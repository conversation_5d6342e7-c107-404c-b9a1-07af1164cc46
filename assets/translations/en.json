{"login": "<PERSON><PERSON>", "register": "Register", "emailAddress": "Email Address", "emailProfile": "Profile email", "enterYourEmail": "Enter your email address", "password": "Password", "rememberMe": "Remember me", "continueText": "Continue", "termsOfUse": "Terms of use", "name": "Name", "enterName": "Enter your name", "lastName": "Last Name", "enterLastName": "Enter your surname", "selectAnOption": "Select an option", "address": "Residential address", "enterYourAddress": "Enter your residential address", "noBeneficiaries": "You have no saved beneficiaries", "send": "Send", "user": "User", "search": "Search", "manageNotification": "Manage notifications", "profileSettings": "Profile Settings", "settings": "Settings", "logout": "Logout", "support": "Help & Support", "aboutUs": "About us", "privacyPolicy": "Privacy policy", "filters": "Filter", "searchResults": "Search results", "deselectAll": "Deselect all", "filter": "Filter", "showMoreOptions": "Show more options", "showLessOptions": "Show less options", "showAll": "Show all", "language": "Language", "languages": "Languages", "copyrightText": "DayOne © {year}.", "loginError": "<PERSON><PERSON> failed, please try again", "signupError": "Registration failed pleae try again.", "anErrorOccured": "An error occured while submitting your request", "retry": "Retry", "oops": "Oops!", "pageNotFound": "Page not found", "weFoundNoPage": "We could not found any page matching the <u>{route}</u> route", "goBack": "Go back", "back": "Return", "telephoneNumber": "Phone number", "dob": "Date of birth", "selectDate": "Select date", "add": "Add", "personalInfo": "Personal information", "avatar": "Avatar", "avatarInstructions": "Choose an image with a neutral background and a semi-serious facial expression. Maximum photo size 2MB", "imageInstructions": "Choose an image without blur. Maximum photo size 2MB", "select": "Select", "provideValidEmail": "Please enter a valid email", "save": "Save", "enter": "<PERSON><PERSON>", "january": "Jan", "february": "Feb", "march": "Mar", "april": "Apr", "may": "May", "june": "Jun", "july": "Jul", "august": "Aug", "september": "Sep", "october": "Oct", "november": "Nov", "december": "Dec", "createdAt": "Created {datetime}", "updatedAt": "Updated {datetime}", "passwordRequired": "Password is required", "invalidPassword": "Invalid password; passwords should have at least 3 characters", "fieldRequired": "This field is required", "fieldsDontMatch": "Fields do not match", "invalidUrl": "Invalid url", "invalidPhone": "Invalid phone number", "invalidDate": "Invalid date", "mustBeNYears": "You need to be at lease {age} years old", "startDateMustBeBeforeEnd": "Start date cannot be after end date", "endMustBeAfterStart": "End date cannot be before start date", "invalidNumber": "Invalid number", "invalidAmount": "Invalid amount", "invalidLink": "Invalid link", "invalidTag": "Invalid tag", "amountMinimum": "The minimum amount you may enter is {amount}", "amountMaximum": "The maximum amount you may enter is {amount}", "changeIt": "Replace it", "fileTooLarge": "File too large", "cropImage": "Crop image", "unknownError": "An unexpected error occurred", "title": "Title", "year": "Year", "years": "Years", "present": "Present", "description": "Description", "forbidden": "Forbidden", "loginToViewPage": "You need to log in to view this page <u>{page}</u>", "started": "Start", "ended": "End", "edit": "Edit", "delete": "Delete", "view": "View", "or": "or", "attach": "Attach", "checkNetwork": "Kindly check your internet connection and try again", "noInternet": "No internet connection", "requestFailedUnexpectedly": "Your request failed due to an unexpected error, please try again", "from": "From", "to": "to", "ellipsis": "...", "bg": "Bulgarian", "cs": "Czech", "da": "Danish", "de": "German", "el": "Greek", "en": "English", "es": "Spanish", "et": "Estonian", "fi": "Finnish", "fr": "French", "ga": "Irish", "hr": "Croatian", "hu": "Hungarian", "it": "Italian", "lt": "Lithuanian", "lv": "Latvian", "mt": "Maltese", "nl": "Dutch", "pl": "Polish", "pt": "Portuguese", "ro": "Romanian", "sk": "Slovak", "sl": "Slovenian", "sv": "Swedish", "deletingItem": "Deleting item...", "deletedItem": "Deleted item successfully", "changedPasswordSuccess": "Changed password successfully", "changedEmailSuccess": "Changed email successfully", "deleteYourAccount": "Delete your account", "deleteAccount": "Delete account", "deleteAccountSuccess": "Account deleted successfully", "deleteAccountConfirmQuestion": "Are you sure you want to delete your account?", "deleteAccountWarning": "Deleting your account will also delete all of your banks and posts. If you do this your data be gone forever.", "changeMailInstruction": "To change your profile email, confirm the link that we will send to your new email address.", "changePassword": "Change Password", "currentPassword": "Current password", "newPassword": "New password", "passwordsDoNotMatch": "Passwords do not match", "retypeNewPassword": "Retype new password", "minimumCharacters": "Minimum {count} characters", "updatePassword": "Update password", "deleteProfile": "Delete profile", "deleteApplication": "Delete application", "areYouSureYouWantToDeleteApplication": "Are you sure you want to delete this application?", "yes": "Yes", "no": "No", "yesDeleteMyProfile": "Yes, delete profile", "doYouWantToDeleteProfile": "Do you want to permanently delete your profile?", "deleteProfileBtnText": "Delete Profile", "newEmailAddress": "New email address", "saving": "Saving", "saved": "Saved", "unsaving": "Unsaving", "unsaved": "Unsaved", "switchTheme": "Dark mode", "doYouWishToProceed": "Do you wish to proceed with this action?", "thisActionIsIrreversible": "This action may be irreversible and cannot be undone.", "defaultEmptyStateText": "Nothing to see here :)", "copiedTextToClipboard": "Copied {text} to clipboard", "data": "Data", "notRequired": "Not required", "noData": "No data", "youHaveNo": "You currently have no {data}", "changeLanguage": "Change language", "thisIsNotificationLanguage": "This is the language you will receive notifications in.", "passwordMustHaveNChars": "Password must be at least {num} characters long.", "minLength": "Field requires at least {num} characters.", "maxLength": "Field requires at most {num} characters.", "passwordShouldBeDifferent": "New password should be different from old password", "category": "Category", "categories": "Categories", "frequentlySearched": "Frequently searched", "showAllCategories": "Show all categories", "updatedNotifLang": "Updated notification language successfully", "closeApp": "Exit app", "closeAppQuestion": "Do you wish to exit the app?", "comments": "Comments", "formInvalid": "Form invalid, some fields have errors", "welcome": "Welcome!", "submit": "Submit", "choosePhoto": "Choose a Photo", "userName": "Username", "hooray": "Hooray!", "browseCommunity": "Browse Community", "skip": "<PERSON><PERSON>", "cancel": "Cancel", "selectPhotosVideos": "Select Photos/Videos", "info": "Info", "photo": "Photo", "updatePhoto": "Update your profile photo", "notifications": "Notifications", "account": "Account", "closeAccount": "Close your account", "chooseNotificationStyle": "Choose when you want to be notified", "sendCode": "Send Code", "verificationCode": "Verification Code", "enterCode": "Enter the verification code we’ve sent to your email address", "changePhoto": "Change Photo", "location": "Location", "updateAddress": "Update your address", "themeMode": "Light/Dark Mode", "light": "Light", "dark": "Dark", "system": "System", "logoutOfAccount": "Log out of your account", "chooseNotificationSchedule": "Choose when you’d like to be notified", "type": "Type", "gender": "Gender", "options": "Options", "noNotifications": "No notifications", "noNotificationsYet": "You have no notifications yet", "suchEmpty": "Such empty", "yesterday": "yesterday", "momentsAgo": "moments ago", "minutesAgo": "{minute} minutes ago", "anHourAgo": "an hour ago", "hoursAgo": "{hour} hours ago", "daysAgo": "{day} days ago", "noMessages": "No Messages", "daysOld": "{age} days", "weeksOld": "{age} weeks", "monthsOld": "{age} months", "yearsOld": "{age} years", "wannaLogout": "Are you sure you want to log out?", "yourLocationIsPrivate": "Your exact location will never be public, only the nearest city will be shown on your profile", "nameOnlyVisibleOnProfile": "Your name is visible on your profile and when you post something", "enterNewEmail": "Enter a new email address if you want to update it", "writeShortDescription": "Write a short description so others can get to know you better", "enterNewEmailToUpdate": "Enter a new email address if you want to update it", "unlockNigeriaWithYourMoney": "Unlock Nigeria, starting with your money", "cardReadyOnLanding": "Your Naira card, ready on landing", "frotKnoxInPocket": "Fort Knox in your pocket", "spendExperienceMore": "Spend smarter, experience more", "yourKeyTonNigerianGems": "Your key to hidden Nigerian gems", "standWhenLanded": "Stand When You Land", "enjoyEasyFundAccess": "Enjoy the easiest access to your funds from the moment you land.", "cardAwaitsYourArrival": "Your debit card awaits your arrival", "enjoyCardConvenience": "Enjoy the convenience of your ready-to-use card immediately when you land in Nigeria.", "spendMoreGetMore": "Spend more,\nget more.", "spendMoreWithCard": "Spend more with your card and unlock exclusive rewards and benefits, from travel perks to cashback offers.", "moneyDataFortified": "Your money & data, fortified", "experienceSecurityUnparalleled": "Experience unparalleled security for all  transactions", "discoverNigeriaFromDay1": "Discover nigeria from day one", "thereMoreFromNaira": "There’s more where the naira came from. Discover places and experiences in Nigeria the day you arrive.", "welcomeToD1": "Welcome to Day One", "createD1Account": "Create your Day One account.", "agreeToTermsByContinuing": "By registering, you have accepted our <sb>Terms of Use</sb> and <sb>Privacy Policy</sb>", "createYourPassword": "Create your password", "choosePassword": "Choose password", "confirmPassword": "Confirm password", "enterPassword": "Enter password", "reEnterPassword": "Re-enter password", "eightCharacters": "8 characters", "aLowerChar": "A lowercase letter", "anUpperChar": "An Uppercase letter", "aSpecialChar": "A special character", "aNumber": "A number", "whatsYourNumber": "What’s your Mobile number", "weWillSendYouACode": "We’ll send you a code to help us keep your account secure.", "yourMobileNumber": "Your Mobile Number", "confrimSignUp": "Confirm Sign Up", "note": "Note", "sms": "SMS", "whatsApp": "WhatsApp", "chooseHowToReceiveOtp": "Choose how you’d like to receive your verification code", "verifyTelephone": "Verify your Mobile number", "weveSentOtp": "We’ve sent a 6 digit code to ****{tel}. Check your SMS and enter it here.", "didntRecieveCodeResend": "I didn’t receive code?  <sb>Resend ({time})</sb>", "verifyAccount": "Verify Account", "maybeLater": "Maybe Later", "enable": "Enable", "makeLoginFastWithBio": "Make your login and transactions faster and more secure with Biometrics enabled", "enableFaceId": "Enable Face ID", "enableBiometricLogin": "Enable Biometric Login", "biomentricAuth": "Biometric ID", "setPin": "Set up your PIN", "createDay1Pin": "Create your Day One App PIN", "enter4DigitPin4Transfer": "Enter a 4-digit code you won’t forget to send money", "done": "Done", "confirmPin": "Confirm PIN", "enableNotifications": "Enable Notifications", "notification": "Notification", "keepYourFinHealthNSecurityInCheck": "Keep your financial health and security in check by being notified of transactions, rewards earned, and upcoming credit payment", "enableLocation": "Enable Location", "shareYourLocation": "Share your location to unlock the full potential of our app. Enjoy personalised offers, local insights, and enhanced security. Your data is safe with us.", "allow": "Allow", "dontAllow": "Don't Allow", "day1NeedBioAccess": "\"Day One\" Would Like to access your Face ID", "day1BioAccessReason": "Day One uses this to securely grant you access", "day1NeedsLocationAccess": "\"Day One\" Would Like to access your Location", "day1LocationAccessReason": "This helps with personalized services, card delivery, and real-time assistance", "day1NeedsNotificationAccess": "\"Day One\" Would Like to send you Notifications", "pinDontMatch": "<PERSON>n do not match", "day1NotificationAccessReason": "Notifications may include alerts, sounds and Icon badges.", "twoFA": "Two Factor Authentication", "enter2FACode": "Enter the code we sent to your phone number <ma>**********{num}</ma>", "resendVia": "Resend via {source}", "enterD1Pin": "Enter your Day One App PIN", "enter4DigitCode": "Enter your 4-digit code", "welcomeBack": "Welcome back!", "welcomeBackUser": "Welcome back, {user}!", "forgotPassword": "Forgot Password?", "notYourAccount": "Not your account?", "money": "Money", "your_balance": "Your balance", "my_account": "My Account", "fund_wallet": "Fund Wallet", "send_money": "Send Money", "amount_to_add_to_wallet": "Amount to add to wallet", "conversion_fee": "Conversion Fee", "send_fee": "Send Fee", "amount_we_will_convert": "Amount we''ll convert", "todays_rate": "Today's rate", "amount_in_denom": "Amount in {denom}", "add_payment_method": "Add Payment Method", "credit_cards_debit_cards_are_supported": "Credit cards, Debit cards are supported", "kindly_complete_your_verification_to_perform_transactions": "Kindly complete your verification to perform transactions", "your_account_verification_is_still_pending_so_you": "Your account verification is still pending, so you won’t be able to carry out transactions on Day One.\n\nKindly complete the process to continue.", "finish_account_setup": "Finish account setup", "nationality": "Nationality", "selectNationality": "Select Your Nationality", "selectNation4Personalisation": "To provide a personalised experience and ensure compliance, we ask for your nationality.", "nonNigerian": "I am not a Nigerian citizen or resident", "forTravellers": "For travelers and expatriates visiting Nigeria.", "aNigerian": "I am a Nigerian citizen or resident", "forResidentsFreqTravellers": "For those living in or frequently returning to Nigeria", "selfieCaptured": "Selfie Capture Complete", "selfieCapturedSuccessfully": "Your selfie capture was successfully taken. You can now proceed.", "verificationRequestSubmitted": "Verification Request Submitted ", "thanksForPatienceYouWillBeNotified": "Thank you for being patient. You’ll be notified once your information has been reviewed", "kycApproved": "Your KYC has been approved", "documentsHavBeenVerified": "Your document submission has been approved and your account verified.", "readyToRoll": "Ready to roll!", "bankDetailsReady": "Your bank details are ready! You can go ahead and start making transactions on your account", "ok": "Ok", "detailsUnderReview": "Your details are under review", "youWillBeNotifiedAfterReview": "You’ll be notified once your information has been reviewed", "stepOfSteps": "STEP {s}/{c}", "takeAPhotoWithId": "Take a photo of yourself holding your ID", "loookInCamera": "Look directly at the camera", "useThis": "Yes, use this", "retakePhoto": "Retake Photo", "takePhoto": "Take Photo", "day1NeedCamAccess": "\"Day One\" would like to Access the Camera", "pleaseGrantCamAccess": "Please grant Day One permission to use your phone camera. Your information will be kept safe and won’t be shared with anyone else.", "selfieCheck": "<PERSON><PERSON>", "check": "Check", "livenessCheck": "Liveness Check", "youAreAlmostThere": "You’re almost there! Keep your face in the center and follow the on-screen steps. Be sure to complete it on your own.", "steps": "Steps", "stayInLitPlace": "Stay in a brightly lit environment ", "removeGlassesEtAl": "Remove glasses, hats, face mask or any other face coverings", "holdPhoneSteady": "Hold your phone steady so the image is clear. Don’t take blurry photos", "visaInfo": "Visa Information", "pleaseProvideVisaInfo": "Please provide the following details from your visa:", "visaNumber": "Visa number", "enterVisaNumber": "Enter your visa number", "visaType": "Visa type", "visaIssueDate": "Visa Issue date", "visaExpireDate": "Visa expiration date", "passportInfo": "Passport Information", "provideInfoOnYourAmericanPass": "Please provide the following details as it appears on your American passport", "passIssuingCountry": "Passport Issuing Country", "unitedStates": "United states", "passNumber": "Passport  number", "enterPassNumber": "Enter your passport number", "expirationDate": "Expiration date", "enterExpirationDate": "Enter expiration date", "fullName": "Full name", "enterFullName": "Enter full name", "onlyAmericanPassportsSupported": "Currently, only American passports are accepted. Support for passports from other countries will be available soon.", "personalInfoCap": "Personal Information", "tellUsAbtU": "Tell us more about you", "pleaseProvideInfoAsInYourId": "Please provide the following details as it appears on your Identification card.", "bvnNum": "BVN number", "ninNum": "NIN number", "enterBvnNum": "Enter your BVN number", "enterNinNum": "Enter your NIN number", "nigerianAddress": "Nigerian address", "enterNigerianAddress": "Enter your nigerian address", "weOnlyHaveAccessTo": "We will only have access to your:", "bvnDoesntGiveAccessTo": "Your BVN does not give us access to your bank account or transactions.", "dialToCheckBvn": "Dial <ma href=\"tel:*565*0#\">*565*0#</ma> on your registered number to get your BVN", "kycSetup": "KYC Set Up", "completKycInQuickSteps": "Complete your verification in quick steps", "passVerification": "Passport Verification", "provideRequiredPassData": "Please provide the required identification based on your selected nationality.", "addVisaInfo": "Add Visa information", "weUseSelfieToVerifyIdentity": "We’ll use this to verify your identity. Please follow the instruction below", "picturesGoIn90Days": "You pictures will only be used to verify your identity. They’ll be securely stored and deleted after 90days", "bvnAndNinCheck": "BVN & NIN Verification", "kycVerification": "KYC Verification", "weRecommendThat": "We recommend that you:", "enterFullnameLikePassport": "Enter full name as it appears on your passport", "visaReferenceNum": "Visa reference number", "enterVisaReferenceNum": "Enter visa reference number", "verifyingIdentity": "Verifying Identity", "acctStatement": "Account Statement", "proofOfAcct": "Proof of Account", "acctName": "Account Name", "acctNumber": "Account Number", "shareAcctDetails": "Share account details", "shareVia": "Share Via", "downloadAcctStatement": "Download Statement of Account", "downloadProofOfAcct": "Download Proof of Account", "getUrProofOfAcct": "Get your Proof of Account", "downloadPoaSecurely": "Securely download your account verification for  any transaction, reference or verification.", "yourPoaData": "Your Proof of account will be dated {date}", "formatType": "Format type", "startDate": "Start date", "endDate": "End date", "invalidCardNumber": "Invalid card number", "invalidCvv": "Invalid CVV number", "paymentMethod": "Payment method", "addNewCard": "Add new card", "addNewPaymentMethod": "Add new payment method", "walletFundingMethod": "How do you want to fund your wallet?", "addCard": "Add Debit / Credit card", "payWithStripe": "Pay with Stripe", "fundWithCard": "Fund with card", "fundWithPaypal": "Fund with PayPal", "payWithCard": "Pay with card", "addCardDetails": "Add your card details", "cardNum": "Card number", "expiryDate": "Expiry date", "securityCode": "Security code", "addDebitCard": "Add debit card", "saveCardForFuture": "Save this card for future payments", "redirectionMessage": "Redirection message", "youWillBeRedirectedToCardIssuer": "You will be redirected to your card issuer's verification page to complete this payment", "proceed": "Proceed", "cardAddedSuccessfully": "You’ve successfully added your credit card", "welldoneUser": "Well done, {name}", "returnToFundWallet": "Return to Fund Wallet", "availablePaymentMethods": "Available payment methods", "creditCard": "Credit Card", "amountAddedToWallet": "Amount added to wallet", "totalAmunt": "Total amount", "addAmount": "Add {amount}", "confirmAmount": "Confirm amount", "youAddedAmountToWallet": "You’ve added {amount} to your wallet", "amountToSend": "Amount to send", "estimatedDollarValue": "Estimated value in dollars", "accountDetails": "Account Details", "selectAcctToSendMoney": "Select account to send money", "sendMoneyToFavs": "Send money to recent or favourites", "recent": "Recent", "searchByNameOrAcct": "Search by name or account details", "viewAll": "View All", "transfer": "Transfer", "searchByName": "Search by name", "matchedBank": "Matched Bank", "verifyingAccountDetails": "Verifying account details", "reviewDetails": "Review details", "exactAmoountWillReachRecipient": "An exact amount of  <sb>{amount}</sb> will reach {user} in <sb>seconds</sb>.", "accountName": "Account name", "bankName": "Bank name", "accountNumber": "Account number", "enterAccountNumber": "Enter account number", "narration": "Narration", "transferNarration": "Describe transfer purspose (optional)", "sendAmount": "Send {amount}", "enterPin": "Enter your PIN", "completeTransferByPinEntry": "To complete your money transfer, please input your PIN for security", "paymentSuccessful": "Payment Successful!", "youSentFundToUserSuccesfully": "You have successfully sent <sb>{amount}</sb> to {user}", "saveBeneficiary": "Save Beneficiary", "shareReceipt": "Share Receipt", "transactionReceipt": "Transaction Receipt", "transactionDetails": "Transaction Details", "downloadAsPdf": "Download as PDF", "downloadAsImage": "Download as Image", "status": "Status", "date": "Date", "transactionType": "Transaction type", "senderName": "Sender’s name", "receiverName": "Receiver’s name", "receiverAcctNum": "Receiver’s account number", "refNum": "Reference No", "selectBank": "Select bank", "paymentWillArriveImmediately": "Payment will arrive Instantly", "receipt": "Receipt", "success": "Success", "all": "All", "favourites": "Favourites", "sureYouWannaDeleteBeneficiary": "Are you sure you want to delete this beneficiary?", "bankNotFound": "That bank wasn’t found. Please check the name and try again.", "recipientNotFound": "That recipient wasn’t found. Please check the name and try again.", "selectTier": "Select Tier", "tier": "Tier {index}", "originalPricing": "Original Pricing", "pricing": "Pricing", "pricePerMonth": "{price}/month", "pickupInstructions": "Pickup Instructions", "aRepWillMeetYouAtAirport": "A representative will meet you at  the airport  to hand over your card. Please bring your passport to collect your card.", "contactInfo": "Contact Information", "contactUsForAssistance": "For any assistance upon arrival, contact our representative at <a href=\"tel:+************\">+234 800 000 001</a>", "emergencyAssistance": "Emergency Assistance", "contactUsInCaseOfEmergency": "If you experience delays or need to change your pickup location, contact us immediately at <a href=\"tel:+************\">+234 800 000 000</a>", "securityTips": "Security Tips", "securityTipsDetails": "For your safety, please ensure your card is securely stored as soon as you receive it.", "usageInformation": "Usage Information", "usageInformationDetails": "Your card is ready to use immediately for transactions in Nigeria.", "getYourPlan": "Get your plan", "active": "Active", "tierFeatures": "Tier Features", "basic": "Basic", "standard": "Standard", "premium": "Premium", "editProfile": "Edit Profile", "inviteFriends": "Invite friends", "getAmount": "Get {amount}", "accountVerification": "Account verification", "stepVerified": "{step}/3 verified", "changeYourPin": "Change your PIN", "yourDevices": "Your Devices", "firstName": "First Name", "mobileNumber": "Mobile Number", "day1AcctNumber": "Day One Account Number", "accountTier": "Account Tier", "upgrade": "Upgrade", "myProfile": "My Profile", "emailVerification": "Email Verification", "idDocument": "ID Document", "enhancedLoginSecurity": "Enhanced Login Security", "addExtraAuthStep": "Add an extra step to ensure your account stays protected every time you log in.", "setUpNow": "Set up now", "aVerificationCodeWillBeSentToYourNumber": "A verification code will be sent via SMS to your registered phone number:", "your2faSetupIsDone": "Your two-factor authentication has been setup", "twoFaIsOn": "Two-step verification is on.", "yourWillReceiveCodesByText": "You will receive verification codes via text message whenever you perform a transaction.", "turnOff": "Turn Off", "inviteAndEarn": "<PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>", "earnAmountByReferral": "Earn {amount} by sharing your referral code, when your friends sign up and choose the tier that works best for them", "shareCode": "Share referral code", "howDoesItWork": "How does it work?", "shareYourCode": "Share Your Referral Code", "startBySharingYourCode": "Start by sharing your unique referral code with your friends and family.", "friendsSignUp": "Friends Sign Up", "whenFriendsSignupTheySelectTier": "When your friends use your referral code to sign up, they will select an account tier that works for them.", "earnRewards": "<PERSON><PERSON><PERSON>", "youGetAmountAfterTheySelectTier": "After they sign up for an account Tier of choice, you’ll get {amount}! ", "totalEarnings": "Total Earnings", "referralRecord": "Referral Record", "shareNEarnAmount": "Share & Earn {amount}", "viewProfile": "View Profile", "passwordUpdated": "Password Updated", "acctVerificationStatus": "Account verification status", "youHaveCompletedVerificationSteps": "You have completed {step}/3 verification steps", "verified": "Verified", "unVerified": "Unverified", "enterCurrentPin": "Enter your current pin", "enterCurrent4DigitPin": "Enter the 4-digit code you currently use for transfers", "device": "Devices", "inActive": "Inactive", "youHaveCountDevices": "You have {count} devices associated with you account", "howCanWeHelp": "How can we help?", "help": "Help", "message": "Message", "sendUsAMessage": "Send us a message", "searchForHelp": "Search for help", "whatIsServiceFee": "What is Service Fee?", "supportChat": "Support Chat", "howDoIWithDrawFunds": "How do i withdraw the funds in my wallet?", "refundPolicy": "Refund Policy", "letsGetYourCard": "Let's Get Your Day One Card!", "getYourDay1CardAsYouLand": "Get your Day One card before you land and start your Nigerian adventure the moment you touch down.", "orderYourCard": "Order your card", "yourFinancialFreedomInNigeria": "Your Financial Freedom in Nigeria is Ready", "loremIpsum": "Lorem ipsum dolor sit amet consectetur. Faucibus quis penatibus praesent habitasse.", "startSpendingInstantly": "Start spending instantly", "getDiscounts": "Get Discounts", "getYourCard": "Get your card", "heresWhatToDoNext": "Here’s what you need to do next", "travelNPickupCard": "Travel and card pickup details", "setupYourPin": "Set up your PIN", "payForCard": "Pay for your card", "setupYourCardPin": "Set up your Card PIN", "setA4DigitCardPin": "Set a 4-digit Card PIN", "payAmount": "Pay {amount}", "cardRequest": "Card Request", "payment": "Payment", "cardCostBreakdown": "Card cost breakdown", "cardCost": "Card cost", "cardDelivery": "Card delivery", "processingFee": "Processing fee", "freeDelivery": "Free delivery . No fee", "addMoneyToSpendLater": "Add money to spend later", "allDone": "All Done", "congratsCardIsActivated": "Congrats - You’re all done. You can see the status of your card from your account.", "goBackHome": "Go back Home", "goToMyCard": "Go to my card", "card": "Card", "thanksForYourSubmission": "Thank you for your Submission", "safeJourney": "Safe travels, and see you soon!", "expectedTimeOnNigeriaArrival": "Expected arrival date in Nigeria", "arrivalAirport": "Arrival airport", "flightDetails": "Flight details", "enterFlightDetails": "Enter your flight details", "alternativeContactNumber": "Alternative contact number", "cardPickupReminder": "Card Pickup Reminder", "yourCardAwaitsYou": "Your card awaits your arrival", "numDays": "{count} days", "moreInfo": "More Info", "activate": "Activate", "viewDetails": "View details", "freeze": "Freeze", "setting": "Setting", "cardName": "Card Name", "cardStatus": "Card Status", "shareDetails": "Share details", "chooseYourPin": "Choose your PIN", "cardSettings": "Card Settings", "manageCard": "Manage Card", "changePin": "Change PIN", "cardReplacement": "Card replacement", "terminateCard": "Terminate card", "virtualCard": "Virtual card", "comingSoon": "Coming soon", "cardIsFrozen": "Your card is frozen and cannot be used for purchases of any kind.", "unFreeze": "Unfreeze", "freezeCard": "Freeze Card?", "transactionsWillBeDisables": "In-store, online and ATM transactions will be disabled. You can unfreeze your card any time.", "cardOnItsWay": "Your card is on its way!", "yourCardWillBeAvailableON": "Your card will be available for pickup on <sb>Monday, ********</sb>", "hideDetails": "Hide details", "frozen": "Frozen", "purpose": "Purpose", "purposeOptions": "Purpose (optional)", "deliveryMethod": "Delivery Method", "selectDeliveryMethod": "Select delivery method", "selectYourPreferredCardDeliveryMethod": "Select your preferred method of card delivery", "airportPickup": "Airport Pickup", "collectCardAtAirport": "Collect your card at the airport", "placeDelivery": "Delivery to my Place of stay", "receiveCardAtYourPlace": "Receive your card at your place of stay", "getYourCardAtAirport": "Get your card delivered at the Airport", "selectDateOfArrival": "Select your date of arrival", "selectPickupAirport": "Select the Airport where you will pickup your card", "provideTravelInformation": "Provide Travel Information", "placeOfStayDelivery": "Place of Stay Delivery", "getUrCardAtPlaceOfStay": "Get your card delivered at your place of stay", "providePlaceOfStayInfo": "Provide information about your place of stay", "dateOfArrival": "Date of arrival", "stateOfArrival": "State of arrival", "pickupAirport": "Pickup Airport", "expectedArrivalTimeInNigeria": "Expected arrival time in Nigeria", "placeOfStay": "Place of stay", "placeOfStayAddress": "Place of stay Address", "selectArrivalState": "Select your State of arrival", "selectYourPickupAirport": "Select your Pickup airport", "enterYourFlightNumber": "Enter your flight number", "enterPlaceOfStay": "Enter your place of stay", "enterPlaceOfStayAddress": "Enter your place of stay address", "travelNPickupDetails": "Travel and card pickup details", "travleNPickupDetailsRider": "Please share this information so we can have your card ready for pickup and adjust for any changes in your arrival", "travelNDeliveryDetails": "Travel and card delivery details", "travelNDeliveryDetailsRider": "Please share this information so we can have your card ready for delivery and adjust for any changes in your arrival", "flightNumber": "Flight number", "selectTime": "Select time", "cardDeliveryReminer": "Card Delivery Reminder", "pleaseHaveYourPassportAtPickup": "For card pickup at the airport, your passport is needed to verify your identity and share the unique code sent to you via SMS with our representative upon arrival", "pleaseHavePassportAtDelivery": "For card pickup at your place of stay, your passport is needed to verify your identity and share the unique code sent to you via SMS with our representative.", "yourCardWillBeReadyOnArrival": "Your card will be ready for pickup at the designated airport upon your arrival in Nigeria.", "otpWillBeSentForPickup": "<m>A unique one-time code</m> will be sent to you via <m>SMS</m> for extra security during the pickup. Our representative will be there to personally hand your card to you so <m>please have your passport</m> ready for verification during pickup.", "cardWillBDeliveredAtPlaceOfStay": "Your card will be ready for delivery at your designated place of stay in Nigeria.", "otpWillBeSentForDelivery": "<m>A unique one-time code</m> will be sent to you via <m>SMS</m> for extra security during delivery. Our representative will be there to personally hand your card to you so <m>please have your passport</m> ready for verification also.", "transactions": "Transactions", "statistics": "Statistics", "summary": "Summary", "cardPayments": "Card Payments", "onlinePayments": "Online Payments", "distribution": "Distribution", "chart": "Chart", "dailyExpenses": "Daily Expenses", "expenses": "Expenses", "inflow": "Inflow", "shareTransactionReceipt": "Share Transaction Receipt", "reportAProblem": "Report a problem", "transferToUser": "Transfer to\n{user}", "dateTime": "Date & Time", "transactionHistory": "Transaction History", "noTransactionRecord": "No transaction record", "youHaveNoTransactions": "You do not have any transaction record", "noTransactionsOfType": "You do not have any {type} transaction record", "noTransactionsFromPeriod": "You do not have any transaction from this period", "noTransactionsOfTypeFromPeriod": "You do not have any {type} transaction from this period", "in_": "In", "out": "Out", "inAmount": "In {amount}", "outAmount": "Out {amount}", "successful": "Successful", "panding": "Pending", "failed": "Failed", "reversed": "Reversed", "walletFund": "Wallet Fund", "lastNumDays": "Last {num} days", "customRange": "Custom Range", "confirm": "Confirm", "chooseDateRange": "Choose date range", "lastNumWeeks": "Last {num} weeks", "lastNumMonths": "Last {num} months", "lastMonth": "Last month", "lastQuarter": "Last quarter", "periodShouldBelessThanYear": "The selected dates should not be greater than a year", "confirmEmailAddress": "Confirm your  email address", "weHaveSentOtpToYourEmail": "We’ve sent a 6 digit code to {email}, check your email and enter it here.", "resendingOtp": "Resending OTP", "otpResent": "OTP has been resent", "noAccountRegister": "Don’t have a Day One account? <sb>Register Now</sb>", "verifyEmail": "<PERSON><PERSON><PERSON>", "verificationEmailSent": "Verification code sent to your  email"}