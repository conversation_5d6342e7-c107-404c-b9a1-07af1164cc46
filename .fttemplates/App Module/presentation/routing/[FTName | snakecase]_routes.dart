import 'package:flutter/material.dart';
import 'package:day1/day1.dart';

class <FTName | replace(' ', '') | capitalize>Routes implements RouteRegistry {
  static const basePath = "/<FTName | snakecase>";

  const <FTName | replace(' ', '') | capitalize>Routes();

  @override
  Route dynamicRoutes(RouteSettings settings, Route fallbackRoute) {
    return switch (settings.name) {
      _ => fallbackRoute,
    };
  }
}
