# Day One Mobile App Environment Configuration
# Copy this file to .env and fill in your actual values

# API Configuration
API_BASE_URL=https://day-one-api-114876416729.us-central1.run.app
STRIPE_PUBLISHABLE_KEY=pk_test_51NMSiEGJgPsZZ1nzQZFWNT7UrKI2hv3OC12Npy7s6fT6wT5MivY8UFTZ97YY3hCIn8j2dIPwrHn4jiaRjIL7DEfU00Rk52Fwmj
GOOGLE_PLACES_API_KEY=your_google_places_api_key_here

# App Configuration
CONF_LANG_CODE=en
CONF_APP_ID=com.yourcompany.yourapp
CONF_APP_NAME=Your App Name
CONF_TERMS_OF_USE_URL=https://yourapp.com/terms
CONF_ABOUT_US_URL=https://yourapp.com/about
CONF_PRIVACY_URL=https://yourapp.com/privacy
CONF_WEB_HOSTS=yourapp.com,www.yourapp.com
CONF_DIAL_CODE=+1

# Development/Testing Configuration
CONF_IS_MOCK=false
USE_MOCK_CONFIG=false

# Mock Data Configuration (for testing only)
MOCK_TOKEN_EXPIRY=3600000
MOCK_ACCESS_TOKEN=mock-token-for-testing-only
MOCK_USER_EMAIL=<EMAIL>
MOCK_USER_FIRST_NAME=Test
MOCK_USER_LAST_NAME=User
MOCK_USER_COUNTRY_CODE=+1
MOCK_USER_PHONE=1234567890

# Security Note:
# - Never commit actual API keys or secrets to version control
# - Use different values for development, staging, and production
# - Rotate keys regularly
# - Use secure key management services in production
