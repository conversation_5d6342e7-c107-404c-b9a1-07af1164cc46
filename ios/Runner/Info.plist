<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>Day One</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleLocalizations</key>
	<array>
		<string>en</string>
		<string>es</string>
	</array>
	<key>FLTEnableImpeller</key>
	<true/>
	<key>FLTEnableWideGamut</key>
	<true/>
	<key>CFBundleName</key>
	<string>day1</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(FLUTTER_BUILD_NAME)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleVersion</key>
	<string>$(FLUTTER_BUILD_NUMBER)</string>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
	<key>ITSAppUsesNonExemptEncryption</key>
	<false/>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<true/>
	</dict>
    <key>NSLocationWhenInUseUsageDescription</key>
    <string>Day One Would Like to access your Location, This helps with personalized services, card delivery, and real-time assistance</string>
    <key>NSLocationAlwaysUsageDescription</key>
    <string>Day One Would Like to access your Location, This helps with personalized services, card delivery, and real-time assistance</string>
    <key>NSLocationAlwaysAndWhenInUsageDescription</key>
    <string>Day One Would Like to access your Location, This helps with personalized services, card delivery, and real-time assistance</string>
	<key>NSFaceIDUsageDescription</key>
	<string>Day we requires FaceId to authenticate and secure your account</string>
	<key>NSCameraUsageDescription</key>
	<string>We use Camera so you can take and attach photos</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>We use the microphone so you can attach videos and recordings</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>We use photo library so you can attach photos</string>
	<key>UIBackgroundModes</key>
	<array>
		<string>fetch</string>
		<string>remote-notification</string>
	</array>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
	</array>
</dict>
</plist>
