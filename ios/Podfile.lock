PODS:
  - app_links (0.0.1):
    - Flutter
  - DKImagePickerController/Core (4.3.9):
    - DKImagePickerController/ImageDataManager
    - DKImagePickerController/Resource
  - DKImagePickerController/ImageDataManager (4.3.9)
  - DKImagePickerController/PhotoGallery (4.3.9):
    - DKImagePickerController/Core
    - DKPhotoGallery
  - DKImagePickerController/Resource (4.3.9)
  - DKPhotoGallery (0.0.19):
    - DKPhotoGallery/Core (= 0.0.19)
    - DKPhotoGallery/Model (= 0.0.19)
    - DKPhotoGallery/Preview (= 0.0.19)
    - DKPhotoGallery/Resource (= 0.0.19)
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Core (0.0.19):
    - DKPhotoGallery/Model
    - DKPhotoGallery/Preview
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Model (0.0.19):
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Preview (0.0.19):
    - DKPhotoGallery/Model
    - DKPhotoGallery/Resource
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Resource (0.0.19):
    - SDWebImage
    - SwiftyGif
  - file_picker (0.0.1):
    - DKImagePickerController/PhotoGallery
    - Flutter
  - Flutter (1.0.0)
  - image_cropper (0.0.4):
    - Flutter
    - TOCropViewController (~> 2.7.4)
  - local_auth_darwin (0.0.1):
    - Flutter
    - FlutterMacOS
  - ObjectBox (2.0.0)
  - objectbox_flutter_libs (0.0.1):
    - Flutter
    - ObjectBox (= 2.0.0)
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - permission_handler_apple (9.3.0):
    - Flutter
  - SDWebImage (5.21.0):
    - SDWebImage/Core (= 5.21.0)
  - SDWebImage/Core (5.21.0)
  - share_plus (0.0.1):
    - Flutter
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sqflite (0.0.3):
    - Flutter
    - FlutterMacOS
  - Stripe (23.30.0):
    - StripeApplePay (= 23.30.0)
    - StripeCore (= 23.30.0)
    - StripePayments (= 23.30.0)
    - StripePaymentsUI (= 23.30.0)
    - StripeUICore (= 23.30.0)
  - stripe_ios (0.0.1):
    - Flutter
    - Stripe (~> 23.30.0)
    - StripeApplePay (~> 23.30.0)
    - StripeFinancialConnections (~> 23.30.0)
    - StripePayments (~> 23.30.0)
    - StripePaymentSheet (~> 23.30.0)
    - StripePaymentsUI (~> 23.30.0)
  - StripeApplePay (23.30.0):
    - StripeCore (= 23.30.0)
  - StripeCore (23.30.0)
  - StripeFinancialConnections (23.30.0):
    - StripeCore (= 23.30.0)
    - StripeUICore (= 23.30.0)
  - StripePayments (23.30.0):
    - StripeCore (= 23.30.0)
    - StripePayments/Stripe3DS2 (= 23.30.0)
  - StripePayments/Stripe3DS2 (23.30.0):
    - StripeCore (= 23.30.0)
  - StripePaymentSheet (23.30.0):
    - StripeApplePay (= 23.30.0)
    - StripeCore (= 23.30.0)
    - StripePayments (= 23.30.0)
    - StripePaymentsUI (= 23.30.0)
  - StripePaymentsUI (23.30.0):
    - StripeCore (= 23.30.0)
    - StripePayments (= 23.30.0)
    - StripeUICore (= 23.30.0)
  - StripeUICore (23.30.0):
    - StripeCore (= 23.30.0)
  - SwiftyGif (5.4.5)
  - TOCropViewController (2.7.4)
  - url_launcher_ios (0.0.1):
    - Flutter

DEPENDENCIES:
  - app_links (from `.symlinks/plugins/app_links/ios`)
  - file_picker (from `.symlinks/plugins/file_picker/ios`)
  - Flutter (from `Flutter`)
  - image_cropper (from `.symlinks/plugins/image_cropper/ios`)
  - local_auth_darwin (from `.symlinks/plugins/local_auth_darwin/darwin`)
  - objectbox_flutter_libs (from `.symlinks/plugins/objectbox_flutter_libs/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - share_plus (from `.symlinks/plugins/share_plus/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sqflite (from `.symlinks/plugins/sqflite/darwin`)
  - stripe_ios (from `.symlinks/plugins/stripe_ios/ios`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)

SPEC REPOS:
  trunk:
    - DKImagePickerController
    - DKPhotoGallery
    - ObjectBox
    - SDWebImage
    - Stripe
    - StripeApplePay
    - StripeCore
    - StripeFinancialConnections
    - StripePayments
    - StripePaymentSheet
    - StripePaymentsUI
    - StripeUICore
    - SwiftyGif
    - TOCropViewController

EXTERNAL SOURCES:
  app_links:
    :path: ".symlinks/plugins/app_links/ios"
  file_picker:
    :path: ".symlinks/plugins/file_picker/ios"
  Flutter:
    :path: Flutter
  image_cropper:
    :path: ".symlinks/plugins/image_cropper/ios"
  local_auth_darwin:
    :path: ".symlinks/plugins/local_auth_darwin/darwin"
  objectbox_flutter_libs:
    :path: ".symlinks/plugins/objectbox_flutter_libs/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  share_plus:
    :path: ".symlinks/plugins/share_plus/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  sqflite:
    :path: ".symlinks/plugins/sqflite/darwin"
  stripe_ios:
    :path: ".symlinks/plugins/stripe_ios/ios"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"

SPEC CHECKSUMS:
  app_links: c5161ac5ab5383ad046884568b4b91cb52df5d91
  DKImagePickerController: 946cec48c7873164274ecc4624d19e3da4c1ef3c
  DKPhotoGallery: b3834fecb755ee09a593d7c9e389d8b5d6deed60
  file_picker: 9b3292d7c8bc68c8a7bf8eb78f730e49c8efc517
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  image_cropper: 5f162dcf988100dc1513f9c6b7eb42cd6fbf9156
  local_auth_darwin: 553ce4f9b16d3fdfeafce9cf042e7c9f77c1c391
  ObjectBox: f5319bd9ad2ea960796eff7227e86471867e9ef0
  objectbox_flutter_libs: 3df54db2292cc7353271e8be6b66b8c62b5e2135
  path_provider_foundation: 080d55be775b7414fd5a5ef3ac137b97b097e564
  permission_handler_apple: 4ed2196e43d0651e8ff7ca3483a069d469701f2d
  SDWebImage: f84b0feeb08d2d11e6a9b843cb06d75ebf5b8868
  share_plus: de6030e33b4e106470e09322d87cf2a4258d2d1d
  shared_preferences_foundation: 9e1978ff2562383bd5676f64ec4e9aa8fa06a6f7
  sqflite: c35dad70033b8862124f8337cc994a809fcd9fa3
  Stripe: 9757efc154de1d9615cbea4836d590bc4034d3a4
  stripe_ios: ac48e0488f95ac7ddea9475fd30f3d739e0bae52
  StripeApplePay: ca33933601302742623762157d587b79b942d073
  StripeCore: 2af250a2366ff2bbf64d4243c5f9bbf2a98b2aaf
  StripeFinancialConnections: 3ab1ef6182ec44e71c29e9a2100b663f9713ac20
  StripePayments: 658a16bd34d20c8185aa281866227b9e1743300e
  StripePaymentSheet: eac031f76d7fbb4f52df9b9c39be5be671ca4c07
  StripePaymentsUI: 7d7cffb2ecfc0d6b5ac3a4488c02893a5ff6cc77
  StripeUICore: bb102d453b1e1a10a37f810bc0a9aa0675fb17fd
  SwiftyGif: 706c60cf65fa2bc5ee0313beece843c8eb8194d4
  TOCropViewController: 80b8985ad794298fb69d3341de183f33d1853654
  url_launcher_ios: 694010445543906933d732453a59da0a173ae33d

PODFILE CHECKSUM: a57f30d18f102dd3ce366b1d62a55ecbef2158e5

COCOAPODS: 1.16.2
