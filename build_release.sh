#!/bin/bash

# Day One Mobile App Release Build Script
echo "🚀 Building Day One Mobile App for Release..."

# Set environment variables for the build
export API_BASE_URL="https://day-one-api-114876416729.us-central1.run.app"
export STRIPE_PUBLISHABLE_KEY="pk_test_51NMSiEGJgPsZZ1nzQZFWNT7UrKI2hv3OC12Npy7s6fT6wT5MivY8UFTZ97YY3hCIn8j2dIPwrHn4jiaRjIL7DEfU00Rk52Fwmj"
export CONF_LANG_CODE="hr"
export CONF_APP_ID="net.poslovac.poslovac"
export CONF_APP_NAME="PosLovac"
export CONF_DIAL_CODE="+1"
export CONF_IS_MOCK="false"
export GOOGLE_PLACES_API_KEY=""
export CONF_TERMS_OF_USE_URL=""
export CONF_ABOUT_US_URL=""
export CONF_PRIVACY_URL=""
export CONF_WEB_HOSTS=""

echo "📦 Environment variables set"
echo "🏗️  Building Android App Bundle..."

# Clean previous builds
flutter clean

# Get dependencies
flutter pub get

# Build the Android App Bundle
flutter build appbundle --release \
  --dart-define=API_BASE_URL="$API_BASE_URL" \
  --dart-define=STRIPE_PUBLISHABLE_KEY="$STRIPE_PUBLISHABLE_KEY" \
  --dart-define=CONF_LANG_CODE="$CONF_LANG_CODE" \
  --dart-define=CONF_APP_ID="$CONF_APP_ID" \
  --dart-define=CONF_APP_NAME="$CONF_APP_NAME" \
  --dart-define=CONF_DIAL_CODE="$CONF_DIAL_CODE" \
  --dart-define=CONF_IS_MOCK="$CONF_IS_MOCK" \
  --dart-define=GOOGLE_PLACES_API_KEY="$GOOGLE_PLACES_API_KEY" \
  --dart-define=CONF_TERMS_OF_USE_URL="$CONF_TERMS_OF_USE_URL" \
  --dart-define=CONF_ABOUT_US_URL="$CONF_ABOUT_US_URL" \
  --dart-define=CONF_PRIVACY_URL="$CONF_PRIVACY_URL" \
  --dart-define=CONF_WEB_HOSTS="$CONF_WEB_HOSTS"

if [ $? -eq 0 ]; then
    echo "✅ Build successful!"
    echo "📱 AAB file location: build/app/outputs/bundle/release/app-release.aab"
    echo "📊 File size:"
    ls -lh build/app/outputs/bundle/release/app-release.aab
    echo ""
    echo "🎯 Ready to upload to Google Play Console!"
else
    echo "❌ Build failed!"
    exit 1
fi
