# Environment Variables Setup for Day One Mobile App

The Day One mobile app now loads all configuration exclusively from environment variables for enhanced security. This eliminates the need for JSON configuration files that could expose sensitive data.

## Required Environment Variables

### API Configuration (Required)
```bash
API_BASE_URL=https://day-one-api-114876416729.us-central1.run.app
STRIPE_PUBLISHABLE_KEY=pk_test_51NMSiEGJgPsZZ1nzQZFWNT7UrKI2hv3OC12Npy7s6fT6wT5MivY8UFTZ97YY3hCIn8j2dIPwrHn4jiaRjIL7DEfU00Rk52Fwmj
```

### App Configuration (Required)
```bash
CONF_LANG_CODE=hr
CONF_APP_ID=net.poslovac.poslovac
CONF_APP_NAME=PosLovac
CONF_DIAL_CODE=+1
```

### Optional Configuration
```bash
GOOGLE_PLACES_API_KEY=your_google_places_api_key_here
CONF_TERMS_OF_USE_URL=
CONF_ABOUT_US_URL=
CONF_PRIVACY_URL=
CONF_WEB_HOSTS=
```

### Development/Testing Configuration
```bash
CONF_IS_MOCK=false
```

### Mock Data Configuration (for testing only)
```bash
MOCK_TOKEN_EXPIRY=3600000
MOCK_ACCESS_TOKEN=mock-token-for-testing-only
MOCK_USER_EMAIL=<EMAIL>
MOCK_USER_FIRST_NAME=Test
MOCK_USER_LAST_NAME=User
MOCK_USER_COUNTRY_CODE=+1
MOCK_USER_PHONE=1234567890
```

## Setup Instructions

### 1. Create Environment File
Copy the `.env.example` file to `.env`:
```bash
cp .env.example .env
```

### 2. Configure Your Values
Edit the `.env` file with your actual configuration values.

### 3. Flutter Development
The app now uses the `flutter_dotenv` package to automatically load environment variables from the `.env` file. You have several options:

#### Option A: Using .env File (Recommended)
Simply create a `.env` file in the project root with all your configuration:

```
# API Configuration
API_BASE_URL=https://day-one-api-114876416729.us-central1.run.app
STRIPE_PUBLISHABLE_KEY=pk_test_51NMSiEGJgPsZZ1nzQZFWNT7UrKI2hv3OC12Npy7s6fT6wT5MivY8UFTZ97YY3hCIn8j2dIPwrHn4jiaRjIL7DEfU00Rk52Fwmj

# App Configuration
CONF_LANG_CODE=hr
CONF_APP_ID=net.poslovac.poslovac
CONF_APP_NAME=PosLovac
CONF_DIAL_CODE=+1
```

Then run the app normally:
```bash
flutter run
```

The app will automatically load the variables from the `.env` file.

#### Option B: Using System Environment Variables
Set environment variables in your system and run:
```bash
export API_BASE_URL=https://day-one-api-114876416729.us-central1.run.app
export STRIPE_PUBLISHABLE_KEY=pk_test_51NMSiEGJgPsZZ1nzQZFWNT7UrKI2hv3OC12Npy7s6fT6wT5MivY8UFTZ97YY3hCIn8j2dIPwrHn4jiaRjIL7DEfU00Rk52Fwmj
export CONF_LANG_CODE=hr
export CONF_APP_ID=net.poslovac.poslovac
export CONF_APP_NAME=PosLovac
export CONF_DIAL_CODE=+1

flutter run
```

#### Option C: Using launch.json (VS Code)
Create or update `.vscode/launch.json`:
```json
{
    "version": "0.2.0",
    "configurations": [
        {
            "name": "day1",
            "request": "launch",
            "type": "dart",
            "toolArgs": [
                "--dart-define=API_BASE_URL=https://day-one-api-114876416729.us-central1.run.app",
                "--dart-define=STRIPE_PUBLISHABLE_KEY=pk_test_51NMSiEGJgPsZZ1nzQZFWNT7UrKI2hv3OC12Npy7s6fT6wT5MivY8UFTZ97YY3hCIn8j2dIPwrHn4jiaRjIL7DEfU00Rk52Fwmj",
                "--dart-define=CONF_LANG_CODE=hr",
                "--dart-define=CONF_APP_ID=net.poslovac.poslovac",
                "--dart-define=CONF_APP_NAME=PosLovac",
                "--dart-define=CONF_DIAL_CODE=+1"
            ]
        }
    ]
}
```

### 4. Building for Production
For production builds, you have two options:

#### Option A: Using .env.production File
Create a separate `.env.production` file with production values:

```bash
# Create a production environment file
cp .env.example .env.production
# Edit with production values
nano .env.production

# Build with the production environment file
flutter build apk --dart-define=ENV_FILE=.env.production
flutter build ios --dart-define=ENV_FILE=.env.production
```

#### Option B: Using CI/CD Environment Variables
In your CI/CD pipeline, set the environment variables and create a .env file dynamically:

```bash
# In your CI/CD script
echo "API_BASE_URL=$PROD_API_URL" > .env
echo "STRIPE_PUBLISHABLE_KEY=$PROD_STRIPE_KEY" >> .env
echo "CONF_LANG_CODE=hr" >> .env
echo "CONF_APP_ID=net.poslovac.poslovac" >> .env
echo "CONF_APP_NAME=PosLovac" >> .env
echo "CONF_DIAL_CODE=+1" >> .env

# Then build
flutter build apk
flutter build ios
```

## Security Benefits

1. **No Hardcoded Secrets**: All sensitive data is externalized
2. **Environment-Specific Configuration**: Different values for dev/staging/prod
3. **Version Control Safe**: No secrets committed to repository
4. **Runtime Configuration**: Values can be changed without rebuilding
5. **Validation**: App validates required environment variables on startup

## Troubleshooting

### Missing Environment Variables
If you see warnings about missing environment variables:
1. Check that all required variables are set
2. Verify the variable names match exactly
3. Ensure values are not empty for required variables

### App Not Loading Configuration
1. Verify environment variables are passed correctly to Flutter
2. Check the console output for configuration loading messages
3. Ensure the app has been rebuilt after changing environment variables

## Migration from JSON Configuration

The app no longer reads from `d1_config.json` or `mock_config.json` files. All configuration must be provided via environment variables. The JSON files are kept for reference but are not used by the application.

## Best Practices

1. **Never commit `.env` files** to version control
2. **Use different values** for development, staging, and production
3. **Rotate API keys regularly**
4. **Use secure key management** services in production environments
5. **Validate environment variables** before deployment
