// import 'package:firebase_core/firebase_core.dart';
import 'package:day1/day1.dart';
import 'package:objectbox/objectbox.dart';

void setUpAppDI({
  required DoAppConfig appConfig,
  // FirebaseOptions? firebaseOptions,
  required RootRouteRegistry routeRegistry,
  FutureCall<Store>? initialiseStore,
  OnChanged<DoAppConfig>? serviceInjectionCallback,
}) {
  ///[Global Appconfig].
  locator.registerSingleton<DoAppConfig>(appConfig);

  locator.registerLazySingleton<AppFirebaseConfig>(() {
    // if (appConfig.isMock || firebaseOptions == null) {
    return AppFirebaseConfigMock();
    // }
    // return AppFirebaseConfigImpl(firebaseOptions);
  });

  ///[Global Services].
  locator.registerFactory<BaseHttpService>(
    () => BaseHttpService(appConfig.baseUrl),
  );
  locator.registerFactory<AppCrashlyticsService>(() {
    if (appConfig.isMock) return AppCrashlyticsMockService();
    return AppCrashlyticsServiceImpl();
  });
  locator.registerFactory<AppFcmService>(() {
    return AppFcmMockService();
    // if (appConfig.isMock) return AppFcmMockService();
    // return AppFcmServiceImpl(locator());
  });
  locator.registerFactory<AppAnalyticsService>(() {
    if (appConfig.isMock) return AppAnalyticsMockService();
    return AppAnalyticsServiceImpl(locator());
  });
  locator.registerFactory<AppCryptoService>(() {
    if (appConfig.isMock) return AppCryptoServiceMock();
    // TODO: revert to impl before going to prod
    return AppCryptoServiceMock();
  });
  locator.registerFactory<AppPermissionService>(() {
    if (appConfig.isMock) return AppPermissionServiceMock();
    return AppPermissionServiceImpl();
  });
  locator.registerFactory<BiometricAuthService>(() {
    if (appConfig.isMock) return BiometricAuthServiceMock();
    return BiometricAuthServiceImpl();
  });
  locator.registerFactory<StripePaymentService>(() {
    return StripePaymentService(appConfig.stripeKey);
  });
  locator.registerFactory<AppBrowser>(
    () => AppBrowserImpl(),
  );

  ///[Global Commons Injection]
  registerCommonDI(
    locator(),
    routeRegistry: routeRegistry,
    initialiseStore: initialiseStore,
  );

  ///[Global Modules Injection]
  registerModulesDI(locator());

  ///[App Specific DI]
  serviceInjectionCallback?.call(appConfig);
}

void resetAppDI({OnPressed? serviceResetCallback}) {
  serviceResetCallback?.call();
  resetCommonDI();
  resetModulesDI();
}
