import 'package:dio/dio.dart';
import 'package:day1/day1.dart';

class UploadHttpService
    with AppHttpMixin, AppTaskMixin
    implements UploadService {
  final AppHttpService _service;

  UploadHttpService(this._service);

  @override
  NetworkCallResponse<List<UploadResponse>> uploadFiles(
    List<FileUploadParam> data,
  ) async {
    return requestHandler(() async {
      if (data.isEmpty) return [];
      final uploads = await Future.wait(await _uploadFiles(data));

      final failed = uploads.every((it) => it.hasError);

      if (failed) {
        uploads.shuffle();
        throw uploads.first.error!;
      }

      final successes = uploads.whereList((value) => value.hasData);

      final List<UploadResponse> responses = [];

      for (final response in successes) {
        responses.tryAdd(response.data!);
      }
      return responses;
    });
  }

  @override
  NetworkCallResponse<UploadResponse> uploadFile(FileUploadParam media) {
    return _uploadFile(media);
  }

  Future<List<Future<NetworkResponse<UploadResponse>>>> _uploadFiles(
    List<FileUploadParam> data,
  ) async {
    return runThrowableTask(
      () async {
        List<Future<NetworkResponse<UploadResponse>>> requests = [];
        for (final FileUploadParam datum in data) {
          requests.tryAdd(
            _uploadFile(datum),
          );
        }
        return requests;
      },
      onError: () async => [],
    );
  }

  Future<NetworkResponse<UploadResponse>> _uploadFile(
    FileUploadParam data,
  ) async {
    final initResponse = await _initUpload(data);

    if (initResponse.hasError || !initResponse.hasData) {
      return NetworkResponse(
        data: null,
        error: initResponse.error,
      );
    }
    final uploadResponse = await _uploadToSignedUrl(data, initResponse.data!);

    if (uploadResponse.hasError || !uploadResponse.hasData) {
      return NetworkResponse(
        data: null,
        error: uploadResponse.error,
      );
    }

    return _confirmUpload(data);
  }

  Future<NetworkResponse<UploadUrlResponse>> _initUpload(
    FileUploadParam data,
  ) async {
    return requestHandler(() async {
      final response = await _service.post("/media/init", body: data);
      return UploadUrlResponse.fromJson(response.data);
    });
  }

  Future<NetworkResponse<NoResponse>> _uploadToSignedUrl(
    FileUploadParam data,
    UploadUrlResponse initResponse,
  ) async {
    return requestHandler(() async {
      final binary = await data.binary;
      await _service.upload(
        initResponse.url.value,
        data: binary,
        options: Options(
          contentType: data.contentType,
          headers: {
            'Content-Type': data.contentType,
            'Connection': 'keep-alive',
            'Accept': "application/json"
          },
        ),
      );
      return const NoResponse();
    });
  }

  Future<NetworkResponse<UploadResponse>> _confirmUpload(
    FileUploadParam data,
  ) async {
    return await requestHandler(() async {
      final response = await _service.post("/media/confirm-upload", body: data);
      return UploadResponse.fromJson(response.data);
    });
  }
}
