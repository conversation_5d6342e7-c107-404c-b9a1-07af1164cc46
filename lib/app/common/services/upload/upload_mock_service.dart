import 'dart:convert';
import 'dart:io';

import 'package:day1/day1.dart';

class UploadMockService with AppTaskMixin implements UploadService {
  String _getb64Prefix(String mimeType) => "data:"; //"data:$mimeType;base64,";

  Future<String> _getFileDataUrl(File file, {required String mimetype}) async {
    final bytes = await file.readAsBytes();
    final data = base64Encode(bytes);
    return "${_getb64Prefix(mimetype)}$data";
  }

  Future<UploadResponse?> _getUploadResponse(FileUploadParam param) async {
    final now = DateTime.now();
    String? url;

    if (param.data.isUrl) {
      url = param.data.fileUrl;
    }

    if (param.data.isFile) {
      url = await _getFileDataUrl(
        param.data.file!,
        mimetype: param.data.mimeType,
      );
    }

    if (url == null) return null;

    return UploadResponse(
      url: url,
      id: now.millisecondsSinceEpoch.toString(),
      uploadedAt: now.toIso8601String(),
    );
  }

  Future<List<Future<UploadResponse?>>> _getUploadRequests(
    List<FileUploadParam> data,
  ) async {
    return runThrowableTask(
      () async {
        List<Future<UploadResponse?>> requests = [];
        for (final FileUploadParam datum in data) {
          requests.tryAdd(
            _getUploadResponse(datum),
          );
        }
        return requests;
      },
      onError: () async => [],
    );
  }

  @override
  NetworkCallResponse<UploadResponse> uploadFile(FileUploadParam data) async {
    final response = await _getUploadResponse(data);
    return NetworkResponse(data: response);
  }

  @override
  NetworkCallResponse<List<UploadResponse>> uploadFiles(
    List<FileUploadParam> data,
  ) async {
    if (data.isEmpty) return const NetworkResponse(data: []);
    final response = await Future.wait(await _getUploadRequests(data));
    final List<UploadResponse> uploads =
        [...response.where((it) => it == null)].cast();
    return NetworkResponse(data: uploads);
  }
}
