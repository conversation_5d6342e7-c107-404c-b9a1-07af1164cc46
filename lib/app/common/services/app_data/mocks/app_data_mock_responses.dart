class AppDataMockResposes {
  final List<Map> digitalSkills = [
    {"id": 1, "name": "Word"},
    {"id": 3, "name": "php"},
    {"id": 4, "name": "Power Point"},
    {"id": 5, "name": "internet"},
    {"id": 6, "name": "administracija"},
    {"id": 7, "name": "AutoCAD"},
    {"id": 8, "name": "Photoshop"},
    {"id": 2102, "name": "vanda"},
    {"id": 2103, "name": "<PERSON>"},
    {"id": 2104, "name": "Pantheon"}
  ];
  final List<Map> otherSkills = [
    {"id": 9, "name": ".BAT"},
    {"id": 10, "name": ".NET"},
    {"id": 11, "name": ".NET Core"},
    {"id": 12, "name": "2D Graphics"},
    {"id": 13, "name": "3D Graphics"},
    {"id": 14, "name": "3G"},
    {"id": 15, "name": "3rd Generation Partnership Project (3GPP)"},
    {"id": 16, "name": "5S"},
    {"id": 17, "name": "A/B Testing"},
    {"id": 18, "name": "ABAP"}
  ];
  final List<Map> educationLevels = [
    {"id": 1, "name": "Srednja stručna sprema"},
    {"id": 2, "name": "Visoka stručna sprema"},
    {"id": 3, "name": "Kvalificirani"},
    {"id": 4, "name": "Viša stručna sprema"},
    {"id": 5, "name": "Magisterij"}
  ];
  final List<Map> driverLicenses = [
    {"id": 1, "name": "A"},
    {"id": 2, "name": "A1"},
    {"id": 3, "name": "B"},
    {"id": 4, "name": "B+E"},
    {"id": 5, "name": "C"},
    {"id": 6, "name": "C+E"},
    {"id": 7, "name": "C1"},
    {"id": 8, "name": "C1+E"},
    {"id": 9, "name": "D"},
    {"id": 10, "name": "D+E"},
    {"id": 11, "name": "E"},
    {"id": 12, "name": "F"},
    {"id": 13, "name": "G"},
    {"id": 14, "name": "H"},
    {"id": 15, "name": "M"}
  ];
  final List<Map> languages = [
    {"id": "HR", "name": "Hrvatski"},
    {"id": "EN", "name": "Engleski"},
    {"id": "DE", "name": "Njemački"},
    {"id": "IT", "name": "Talijanski"}
  ];
  final List<Map> employmentTypes = [
    {"id": 1, "name": "Na određeno vrijeme"},
    {"id": 2, "name": "Stalni radni odnos"},
    {"id": 3, "name": "Praksa"},
    {"id": 4, "name": "Honorarno"},
    {"id": 5, "name": "Stručno osposobljavanje bez zasnivanja radnog odnosa"},
    {"id": 6, "name": "Javni radovi"},
    {"id": 7, "name": "Sezonski"},
    {"id": 8, "name": "Studentska praksa i poslovi"},
    {"id": 9, "name": "Volontiranje"},
    {"id": 10, "name": "Partnerska suradnja"}
  ];
  final List<Map> languageLevels = [
    {"id": "A1", "name": "Početna razina"},
    {"id": "A2", "name": "Početna razina"},
    {"id": "B1", "name": "Samostalna razina"},
    {"id": "B2", "name": "Samostalna razina"},
    {"id": "C1", "name": "Vrsna razina"},
    {"id": "C2", "name": "Vrsna razina"}
  ];
  final List<Map> institutions = [
    {"id": 1, "name": "Osnovna škola Berek"},
    {"id": 2, "name": "Ekonomska i birotehnička škola"},
    {"id": 3, "name": "Gimnazija Bjelovar"},
    {"id": 4, "name": "Glazbena škola Vatroslava Lisinskog Bjelovar"},
    {"id": 5, "name": "I. osnovna škola Bjelovar"},
    {"id": 6, "name": "II. osnovna škola Bjelovar"},
    {"id": 7, "name": "III. Osnovna škola Bjelovar"},
    {"id": 8, "name": "IV. osnovna škola Bjelovar"},
    {"id": 9, "name": "Komercijalna i trgovačka škola Bjelovar"},
    {"id": 10, "name": "Medicinska škola Bjelovar"}
  ];

  final List<Map> notificationSettings = [
    {"id": "new_connection_request", "name": "New connection request"},
    {"id": "saved_search_new_ad", "name": "Saved search new ad"},
    {"id": "profile_change_education", "name": "Profile change - education"},
    {
      "id": "profile_change_several_changes",
      "name": "Profile change - several changes"
    }
  ];
}
