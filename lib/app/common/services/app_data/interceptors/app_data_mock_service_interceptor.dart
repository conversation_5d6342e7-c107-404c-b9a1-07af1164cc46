import 'package:day1/day1.dart';
import 'package:dio/dio.dart';

class AppDataMockInterceptor extends Interceptor {
  final AppDataMockResposes _mock;

  AppDataMockInterceptor(this._mock);

  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    dynamic responseData = switch (options.path) {
      "/skills?type=digital" => _mock.digitalSkills,
      "/apps/education-levels" => _mock.educationLevels,
      "/apps/institutions" => _mock.institutions,
      "/apps/language-levels" => _mock.languageLevels,
      "/apps/languages" => _mock.languages,
      "/apps/driver-licences" => _mock.driverLicenses,
      "/apps/employment-types" => _mock.employmentTypes,
      "/skills?type=other" => _mock.otherSkills,
      "/apps/user-notifications" => _mock.notificationSettings,
      _ => null
    };

    if (responseData != null) {
      return handler.resolve(
        Response(
          requestOptions: options,
          data: responseData,
          statusCode: 200,
        ),
      );
    }

    super.onRequest(options, handler);
  }
}
