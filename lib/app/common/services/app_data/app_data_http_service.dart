import 'package:day1/day1.dart';

class AppDataHttpService with AppHttpMixin implements AppDataService {
  final AppHttpService _service;

  final _cacheOption = CacheOption.oneWeek;

  AppDataHttpService(
    this._service,
    AppDataMockInterceptor? interceptor,
  ) {
    if (interceptor != null) {
      _service.attachInterceptor(interceptor);
    }
  }

  @override
  NetworkCallResponse<List<Bank>> getBanks([
    String? filter,
  ]) async {
    return requestHandler(() async {
      final response = await _service.get(
        "/banks/list",
        cacheOption: _cacheOption,
      );
      final rawBanks = response.parsedData["banks"];

      if (rawBanks is! Iterable) return [];

      final banks = rawBanks.mapList((it) => Bank.fromJson(it));
      return _filterBanks(filter, banks);
    });
  }

  List<Bank> _filterBanks(String? filter, List<Bank> banks) {
    if (!filter.hasValue) return banks;
    return banks.whereList(
      (it) {
        final hasMatchingName = it.name.includes(filter!);
        final hasMatchingAccronym = it.accronym?.includes(filter) ?? false;

        return hasMatchingName || hasMatchingAccronym;
      },
    );
  }

  @override
  NetworkCallResponse<NoResponse> getAllBaseData() async {
    await Future.wait([
      getBanks(),
    ]);

    return const NetworkResponse(data: NoResponse());
  }
}
