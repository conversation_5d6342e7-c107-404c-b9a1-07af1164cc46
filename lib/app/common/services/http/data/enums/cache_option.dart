import 'package:day1/day1.dart';

enum CacheOption {
  nocache(false, Duration.zero),
  fiveMinutes(true, Duration(minutes: 5)),
  tenMinutes(true, Duration(minutes: 10)),
  oneHour(true, Duration(hours: 1)),
  oneDay(true, Duration(days: 1)),
  oneWeek(true, Duration(days: 7)),
  oneMonth(true, Duration(days: 28));

  const CacheOption(this.cacheable, this.duration);

  factory CacheOption.fromString(String directive) {
    if (CacheOption.fiveMinutes.cacheString.equals(directive)) {
      return CacheOption.fiveMinutes;
    }
    if (CacheOption.tenMinutes.cacheString.equals(directive)) {
      return CacheOption.tenMinutes;
    }
    if (CacheOption.oneHour.cacheString.equals(directive)) {
      return CacheOption.oneHour;
    }
    if (CacheOption.oneDay.cacheString.equals(directive)) {
      return CacheOption.oneDay;
    }
    if (CacheOption.oneWeek.cacheString.equals(directive)) {
      return CacheOption.oneWeek;
    }
    if (CacheOption.oneMonth.cacheString.equals(directive)) {
      return CacheOption.oneMonth;
    }
    return CacheOption.nocache;
  }

  final bool cacheable;
  final Duration duration;

  String? get cacheString {
    if (!cacheable) return null;
    final seconds = duration.inSeconds;
    return "private, max-age=$seconds, immutable";
  }
}
