import 'package:day1/day1.dart';
import 'package:dio/dio.dart';

class LoggerInterceptor with AppAnalyticsMixin implements InterceptorsWrapper {
  const LoggerInterceptor();

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) async {
    // Log error information without exposing sensitive data
    AppLogger.info({
      "uri": err.requestOptions.uri.toString(),
      "statusCode": err.response?.statusCode ?? 400,
      "statusMessage": err.response?.statusMessage,
      "errorType": err.type.toString(),
      // Don't log error response data as it may contain sensitive information
    });
    return handler.next(err);
  }

  @override
  void onRequest(
    RequestOptions options,
    RequestInterceptorHandler handler,
  ) async {
    // Only log non-sensitive information and avoid logging request bodies
    final sanitizedHeaders = Map<String, dynamic>.from(options.headers);
    sanitizedHeaders.remove('Authorization'); // Remove auth tokens from logs
    sanitizedHeaders.remove('<PERSON>ie'); // Remove cookies from logs

    AppLogger.info({
      "url": options.uri.toString(),
      "method": options.method,
      "params": _sanitizeParams(options.queryParameters),
      "headers": sanitizedHeaders,
      // Don't log request body as it may contain sensitive data
    });

    trackEvent(
      AppEvent.apiRequest,
      description: options.method,
      value: "${options.baseUrl}${options.path}",
    );
    return handler.next(options);
  }

  // Sanitize query parameters to avoid logging sensitive data
  Map<String, dynamic> _sanitizeParams(Map<String, dynamic> params) {
    final sanitized = Map<String, dynamic>.from(params);
    const sensitiveKeys = ['password', 'pin', 'token', 'secret', 'key', 'otp'];

    for (final key in sanitized.keys.toList()) {
      if (sensitiveKeys.any((sensitive) =>
          key.toLowerCase().contains(sensitive.toLowerCase()))) {
        sanitized[key] = '[REDACTED]';
      }
    }
    return sanitized;
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) async {
    // Only log response metadata, not the actual data which may contain sensitive info
    AppLogger.info({
      "uri": response.requestOptions.uri.toString(),
      "statusCode": response.statusCode,
      "statusMessage": response.statusMessage,
      "contentLength": response.headers.value('content-length'),
      // Don't log response data as it may contain sensitive information
      // Don't log response headers as they may contain tokens
    });
    return handler.next(response);
  }
}
