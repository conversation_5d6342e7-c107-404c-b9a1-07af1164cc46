import 'package:day1/day1.dart';
import 'package:dio/dio.dart';

class NetworkInterceptor extends Interceptor {
  const NetworkInterceptor();

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) async {
    final connectTimeout = 500.milliDuration;
    final hasConnection = await AppHelpers.hasConnection().timeout(
      connectTimeout,
      onTimeout: () => false,
    );
    if (!hasConnection) {
      return handler.next(
        DioException(
          requestOptions: err.requestOptions,
          response: Response(
            requestOptions: err.requestOptions,
            data: {"message": LocaleKeys.checkNetwork.tr()},
            statusCode: err.response?.statusCode ?? 408,
            statusMessage: LocaleKeys.noInternet.tr(),
          ),
        ),
      );
    }
    return handler.next(err);
  }
}
