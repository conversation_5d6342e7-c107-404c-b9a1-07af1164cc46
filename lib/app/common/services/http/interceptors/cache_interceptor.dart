import 'dart:io';

import 'package:day1/day1.dart';
import 'package:day1/objectbox.g.dart';
import 'package:dio/dio.dart';

class CacheInterceptor extends QueuedInterceptor {
  final bool forDownloads;
  CacheInterceptor({this.forDownloads = false});

  @override
  void onRequest(
    RequestOptions options,
    RequestInterceptorHandler handler,
  ) async {
    if (!options.cacheable) return handler.next(options);
    final data = await options.cacheData;
    if (data == null) return handler.next(options);
    return handler.resolve(
      Response(
        requestOptions: options,
        data: data,
        statusCode: 200,
        statusMessage: "Successful response from cache",
      ),
      true,
    );
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) async {
    if (response.cacheable) response.saveToCache(forDownloads);
    return handler.next(response);
  }
}

extension on RequestOptions {
  AppDbClient get _db => locator();

  Future<CacheCache?> get _cacheData async {
    final now = DateTime.now().millisecondsSinceEpoch;

    final query = _db
        .getCollectionDb<CacheCache>()
        ?.query(CacheCache_.uri
            .equals("$uri")
            .and(CacheCache_.expiresAt.greaterThan(now)))
        .build();

    final cacheItem = await query?.findFirstAsync();
    return cacheItem;
  }

  String get _cacheDirective {
    const key = HttpHeaders.cacheControlHeader;
    return "${headers[key] ?? ''}";
  }

  bool get cacheable {
    if (!method.equals("get")) return false;
    return _cacheDirective.hasValue;
  }

  Future<dynamic> get cacheData async {
    try {
      final data = await _cacheData;
      if (data?.byteData.hasValue ?? false) return data?.asBytes;
      return data?.asJson;
    } catch (e, t) {
      AppLogger.severe("$e", error: e, stackTrace: t);
      return null;
    }
  }
}

extension on Response {
  AppDbClient get _db => locator();

  Uri get uri => requestOptions.uri;

  String get _cacheDirective {
    const key = HttpHeaders.cacheControlHeader;
    final reqHeaders = requestOptions.headers;
    return "${reqHeaders[key] ?? ''}";
  }

  bool get cacheable {
    final method = requestOptions.method.upper;

    if (!method.equals("get")) return false;
    return _cacheDirective.hasValue;
  }

  bool get existsInDb {
    final now = DateTime.now().millisecondsSinceEpoch;
    final query = _db
        .getCollectionDb<CacheCache>()
        ?.query(CacheCache_.uri
            .equals("$uri")
            .and(CacheCache_.expiresAt.greaterThan(now)))
        .build()
      ?..limit = 1;

    final matchCount = query?.count();
    return (matchCount ?? 0) >= 1;
  }

  CacheOption get matchingCacheOption {
    return CacheOption.fromString(_cacheDirective.value);
  }

  bool get hasValidData {
    if (data is Iterable) return (data as Iterable).isNotEmpty;
    if (data is String) return (data as String).hasValue;
    return data != null;
  }

  Future saveToCache(bool forDownloads) async {
    try {
      final notCacheAble =
          !cacheable || matchingCacheOption == CacheOption.nocache;
      final isCacheResponse = statusMessage.equals(
        "Successful response from cache",
      );
      if (notCacheAble || isCacheResponse || !hasValidData) return;
      if (existsInDb) return;
      final expiresAt = DateTime.now().add(matchingCacheOption.duration);
      final jsonData = forDownloads ? null : await AppHelpers.encodeJson(data);
      final byteData = forDownloads ? data : null;
      await _db.setItem<CacheCache>(
        CacheCache(
          data: jsonData,
          byteData: byteData,
          uri: "$uri",
          expiresAt: expiresAt.millisecondsSinceEpoch,
        ),
      );
    } catch (e, t) {
      AppLogger.severe("$e", error: e, stackTrace: t);
    }
  }
}
