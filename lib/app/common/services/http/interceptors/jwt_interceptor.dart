import 'dart:io';

import 'package:day1/day1.dart';
import 'package:dio/dio.dart';

class JwtInterceptor extends QueuedInterceptorsWrapper {
  final AppSessionService _sessionService;
  final BaseHttpService _httpService;

  late final Dio _jwtHttp;

  JwtInterceptor(this._httpService, this._sessionService) {
    _jwtHttp = _httpService.tokenHttp
      ..interceptors.tryAdd(const LoggerInterceptor());
  }

  @override
  void onRequest(
    RequestOptions options,
    RequestInterceptorHandler handler,
  ) async {
    final isLoggedIn = _sessionService.isLoggedIn;
    final isExpired = _sessionService.isExpired;
    String? accessToken = _sessionService.accessToken;
    if (isLoggedIn && isExpired) {
      final token = _sessionService.refreshToken;
      accessToken = await _renewToken(token.value);
    }

    options.headers[HttpHeaders.acceptLanguageHeader] =
        AppTextFormatter.languageCode;

    if (accessToken.hasValue) {
      options.headers["Authorization"] = "Bearer $accessToken";
    }

    return handler.next(options);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    final response = err.response;
    final isUnauthorised = response?.statusCode == 401;

    handler.next(err);

    if (!isUnauthorised) return;

    AppRouter.pushAndRemoveUntil(AuthRoutes.login);
  }

  Future<String?> _renewToken(String token) async {
    try {
      final body = {"refreshToken": token};
      final req = await _jwtHttp.post("/auth/refresh-token", data: body);
      if (req.data != null) {
        final sessionData = AuthResponse.fromJson(req.data);
        final newToken = sessionData.accessToken;
        await _sessionService.createSession(
          authData: sessionData,
          ephemeral: true,
        );
        return newToken.hasValue ? newToken : null;
      }
      return null;
    } catch (e, t) {
      AppLogger.severe("$e", error: e, stackTrace: t);
      return null;
    }
  }
}
