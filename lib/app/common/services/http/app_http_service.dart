import 'dart:io';

import 'package:day1/day1.dart';
import 'package:dio/dio.dart';

abstract class AppHttpService {
  late final Dio _http;
  late final Dio _downloadHttp;

  FormData generateFormData(Map<String, dynamic> data) {
    return FormData.fromMap(data);
  }

  attachInterceptor(Interceptor interceptor) {
    _http.interceptors.tryAdd(interceptor);
  }

  Options? _getCacheOption(
    Options? options,
    CacheOption cacheOption,
  ) {
    if (cacheOption.cacheable && config.allowCaching) {
      final opts = (options ?? Options());
      final headers = opts.headers ?? {};
      headers[HttpHeaders.cacheControlHeader] = cacheOption.cacheString;
      return opts.copyWith(headers: headers);
    }
    return options;
  }

  Future<Response> get(
    String path, {
    Codable? query,
    Options? options,
    CacheOption cacheOption = CacheOption.nocache,
  }) async {
    return await _http.get(
      path,
      queryParameters: query?.toJson(),
      options: _getCacheOption(options, cacheOption),
    );
  }

  Future<Response> download(
    String path, {
    ProgressCallback? onReceiveProgress,
    Codable? query,
    Options? options,
    CacheOption cacheOption = CacheOption.nocache,
  }) async {
    return await _downloadHttp.get(
      path,
      queryParameters: query?.toJson(),
      onReceiveProgress: onReceiveProgress,
      options: _getCacheOption(options, cacheOption),
    );
  }

  Future<Response> upload(
    String path, {
    ProgressCallback? onReceiveProgress,
    Codable? query,
    Options? options,
    dynamic data,
  }) async {
    return await _downloadHttp.put(
      path,
      queryParameters: query?.toJson(),
      onReceiveProgress: onReceiveProgress,
      options: options,
      data: data,
    );
  }

  Future<Response> put(
    String path, {
    Codable? query,
    Codable? body,
    Options? options,
  }) {
    return _http.put(
      path,
      data: body?.toJson(),
      queryParameters: query?.toJson(),
      options: options,
    );
  }

  Future<Response> patch(
    String path, {
    Codable? query,
    Codable? body,
    Options? options,
  }) {
    return _http.patch(
      path,
      data: body?.toJson(),
      queryParameters: query?.toJson(),
      options: options,
    );
  }

  Future<Response> post(
    String path, {
    Codable? query,
    Codable? body,
    Options? options,
  }) {
    return _http.post(
      path,
      data: body?.toJson(),
      queryParameters: query?.toJson(),
      options: options,
    );
  }

  Future<Response> postFile(
    String path, {
    Codable? query,
    FormData? body,
  }) {
    return _http.post(
      path,
      data: body,
      queryParameters: query?.toJson(),
      options: Options(headers: {
        "Content-Type": "multipart/form-data",
      }),
    );
  }

  Future<Response> delete(
    String path, {
    Codable? query,
    Codable? body,
    Options? options,
  }) {
    return _http.delete(
      path,
      data: body?.toJson(),
      queryParameters: query?.toJson(),
      options: options,
    );
  }
}

class AppHttpServiceImpl extends AppHttpService {
  final BaseHttpService _httpService;
  final DoAppConfig _config;
  final AppSessionService _sessionService;

  AppHttpServiceImpl(this._httpService, this._config, this._sessionService) {
    final isMock = _config.isMock;
    _http = _httpService.http
      ..interceptors.addAll(
        [
          if (!isMock) ...[
            const ContentTypeInterceptor(),
            JwtInterceptor(_httpService, _sessionService),
            if (config.allowCaching) CacheInterceptor(),
            const NetworkInterceptor(),
          ],
          const LoggerInterceptor(),
        ],
      );
    _downloadHttp = _httpService.downloadHttp
      ..interceptors.addAll(
        [
          if (!isMock) const NetworkInterceptor(),
          const LoggerInterceptor(),
          CacheInterceptor(forDownloads: true),
        ],
      );
  }
}

extension ResponseExtension on Response {
  dynamic get parsedData {
    try {
      if (data is! Map) return data;
      return AppNetworkData.fromJson(data).data;
    } catch (_) {
      return data;
    }
  }
}
