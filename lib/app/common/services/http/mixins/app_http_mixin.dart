import 'dart:async';
import 'dart:io';

import 'package:day1/day1.dart';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';

mixin AppHttpMixin {
  NetworkError _getParsedError(dynamic error) {
    final errorData = AppHelpers.parseError(
      error,
      defaultMessage: LocaleKeys.requestFailedUnexpectedly.tr(),
    );
    return NetworkError(
      message: errorData["message"] ?? "",
      statusCode: errorData["status"],
      code: errorData["code"],
      error: error,
    );
  }

  _reportError(String tag, Object? error, StackTrace trace) {
    final crashReporter = locator<AppCrashlyticsService>();
    crashReporter.trackError(tag, error: error, trace: trace);
  }

  Future<NetworkResponse<T>> requestHandler<T>(FutureCall<T> func) async {
    AppLogger.info("AppHttpMixin: requestHandler called");
    try {
      final watch = Stopwatch();
      if (kDebugMode) watch.start();
      AppLogger.info("AppHttpMixin: executing network function");
      final result = await func();
      AppLogger.info("AppHttpMixin: network function completed successfully");
      if (kDebugMode) {
        watch.stop();
        AppLogger.info("Request took ${watch.elapsed.inMilliseconds}ms");
      }
      return NetworkResponse(data: result);
    } on SocketException catch (e, t) {
      AppLogger.severe("AppHttpMixin: SocketException: ${e.message}");
      _reportError("DioException: ${e.message}", e, t);
      return NetworkResponse(error: _getParsedError(e));
    } on DioException catch (e, t) {
      AppLogger.severe("AppHttpMixin: DioException: ${e.message}");
      _reportError("DioException: ${e.message}", e, t);
      return NetworkResponse(error: _getParsedError(e));
    } on NetworkError catch (e, t) {
      AppLogger.severe("AppHttpMixin: NetworkError: ${e.message}");
      _reportError("NetworkError: ${e.message}", e, t);
      return NetworkResponse(error: e);
    } on TimeoutException catch (e, t) {
      AppLogger.severe("AppHttpMixin: TimeoutException: ${e.message}");
      _reportError("NetworkTimeout: ${e.message}", e, t);
      return NetworkResponse(error: _getParsedError(e));
    } catch (e, t) {
      AppLogger.severe("AppHttpMixin: UnknownError: $e");
      _reportError("UnknownError: $e", e, t);
      return NetworkResponse(error: _getParsedError(e));
    }
  }
}
