import 'package:day1/day1.dart';

abstract class AppSessionService {
  SessionCache? get sessionData;

  Future createSession({
    required AuthResponse authData,
    bool ephemeral = false,
  });

  Future attachBioAuthData({required AuthResponse authData});

  Future modifySession({
    required SessionCache? update,
    bool ephemeral = false,
  });

  Future closeSession();

  String? get accessToken;

  String? get refreshToken;

  String? get lastEmail;

  String? get onboardingState;

  String? get authPublicKey;

  String? get appKey;

  bool get isExpired;

  bool get hasToken;

  bool get isLoggedIn;

  bool get allowsBiometricAuth;

  bool get requires2fa;

  bool get allowsPinAuth;

  bool get hasCompletedOnboarding;

  OnboardingStep? get onboardingStep;

  bool get hasSession;
}
