import 'package:day1/day1.dart';

class AppSessionServiceImpl implements AppSessionService {
  final AppDbClient _dbClient;
  final AppCryptoService _cryptoService;

  SessionCache? _sessionCache;

  AppSessionServiceImpl(this._dbClient, this._cryptoService);

  @override
  Future createSession({
    required AuthResponse authData,
    bool ephemeral = false,
  }) async {
    final expiresIn = authData.expiresIn * 1000;
    final accessToken = authData.accessToken;
    final userId = authData.user?.id;
    final pubKey = _getEncryptedData(authData.publicKey, userId.value);

    final expiryTime = DateTime.fromMillisecondsSinceEpoch(expiresIn);

    _sessionCache = SessionCache(
      accessToken: accessToken,
      lastEmail: authData.email,
      lastPhoneNumber: authData.phoneNumber,
      userUuid: userId,
      expiresAt: expiryTime.millisecondsSinceEpoch,
      refreshToken: authData.refreshToken,
      hasEnabledPinAuth: authData.user?.hasSetPin ?? false,
      appPublicKey: userId,
      onboardingState: authData.onboardingStep.tag,
      authPublicKey: pubKey,
      hasEnabledBioAuth: authData.user?.hasEnabledBioAuth ?? false,
    );

    if (!ephemeral) await _storeSession(_sessionCache!);
  }

  @override
  Future closeSession() async {
    await _storeSession(SessionCache.loggedOut(_sessionCache));
    _sessionCache = null;
  }

  @override
  SessionCache? get sessionData {
    if (_sessionCache != null) return _sessionCache;
    _sessionCache = _dbClient.getItem<SessionCache>(1);
    return _sessionCache;
  }

  @override
  String? get accessToken => sessionData?.accessToken;

  @override
  String? get refreshToken => sessionData?.refreshToken;

  @override
  bool get isExpired {
    final expiryTime = DateTime.fromMillisecondsSinceEpoch(
      sessionData?.expiresAt ?? 0,
    );
    return expiryTime <= DateTime.now();
  }

  @override
  bool get isLoggedIn {
    return hasToken && !isExpired;
  }

  Future _storeSession(SessionCache cache) async {
    await _dbClient.setItem<SessionCache>(cache);
  }

  @override
  String? get lastEmail => sessionData?.lastEmail;

  @override
  String? get onboardingState => sessionData?.onboardingState;

  String get _cipherKey {
    final userId = sessionData?.userUuid;

    return userId.value;
  }

  @override
  bool get hasCompletedOnboarding {
    return sessionData?.hasCompletedOnboarding ?? false;
  }

  Future _updateSession(SessionCache newSession, bool ephemeral) async {
    if (!hasToken) return;
    _sessionCache = newSession;
    if (!ephemeral) await _storeSession(newSession);
  }

  @override
  bool get hasSession => sessionData != null;

  @override
  String? get authPublicKey {
    final authKey = sessionData?.authPublicKey ?? "";
    if (!authKey.hasValue) return null;
    return _cryptoService.decrypt(key: _cipherKey, value: authKey);
  }

  String? _getEncryptedData(String? data, String key) {
    if (!data.hasValue || !key.hasValue) return null;
    return _cryptoService.encrypt(key: key, value: data!);
  }

  @override
  bool get hasToken {
    final accessToken = sessionData?.accessToken;
    return accessToken.hasValue;
  }

  @override
  bool get allowsBiometricAuth => sessionData?.hasEnabledBioAuth ?? false;

  @override
  bool get allowsPinAuth => sessionData?.hasEnabledPinAuth ?? false;

  @override
  bool get requires2fa => sessionData?.hasEnabled2fa ?? false;

  @override
  Future modifySession({
    required SessionCache? update,
    bool ephemeral = false,
  }) async {
    if (update == null) return;
    _updateSession(update, ephemeral);
  }

  @override
  String? get appKey => sessionData?.appPublicKey;

  @override
  OnboardingStep? get onboardingStep {
    return OnboardingStep.fromString(sessionData?.onboardingState ?? '');
  }

  @override
  Future attachBioAuthData({required AuthResponse authData}) async {
    await modifySession(
      update: sessionData?.copyWith(
        appPublicKey: authData.publicKey,
        authPublicKey: _getEncryptedData(authData.userPublicKey, _cipherKey),
        hasEnabledBioAuth: true,
      ),
    );
  }
}
