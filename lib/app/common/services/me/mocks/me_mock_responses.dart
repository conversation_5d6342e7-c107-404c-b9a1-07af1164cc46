import 'package:day1/day1.dart';

class MeMockResponses {
  String get _defaultAvatar => locator<DoAppConfig>().defaultAvatar;
  Map get meData {
    return {
      "id": 1,
      "email": "<EMAIL>",
      "firstName": "Ante",
      "lastName": "Repica",
      "countryCode": "+355",
      "number": "8100115314",
      "phone": {"countryCode": "+355", "number": "8100115314"},
      "image": {
        "id": 47533,
        "url": _defaultAvatar,
        "name": "Ante_Repica_avatar"
      },
      "location": {
        "id": 1214,
        "placeId": "ChIJKReWBDFCZkcR8LUrhlCtAAQ",
        "name": "",
        "formattedAddress": null,
        "data": {
          "geometry": {
            "location": {"lat": 45.8376352, "lng": 16.5382872},
            "viewport": {
              "east": 16.57277584232513,
              "west": 16.52010342938181,
              "north": 45.8647778019265,
              "south": 45.82005358609035
            }
          },
          "address_components": [
            {
              "types": ["locality", "political"],
              "long_name": "Dubrava",
              "short_name": "Dubrava"
            },
            {
              "types": ["administrative_area_level_2", "political"],
              "long_name": "Općina Dubrava",
              "short_name": "Općina Dubrava"
            },
            {
              "types": ["administrative_area_level_1", "political"],
              "long_name": "Zagrebačka županija",
              "short_name": "Zagrebačka županija"
            },
            {
              "types": ["country", "political"],
              "long_name": "Hrvatska",
              "short_name": "HR"
            },
            {
              "types": ["postal_code"],
              "long_name": "10342",
              "short_name": "10342"
            }
          ]
        }
      },
      "dateOfBirth": "1992-06-23",
      "citizenship": "hrvatsko, slovensko",
      "contactEmail": "<EMAIL>",
      "linkedin": "https://www.linkedin.com/in/jesse-dirisu-14b044115",
      "webSite": "https://github.com/dirisujesse",
      "lastSessionAt": "2023-07-14T07:25:39.012Z"
    };
  }

  updateMe(Map meParam) {
    try {
      if (meParam.containsKey("location")) {
        String? location;

        location = meParam["location"];
        meParam.remove("location");

        if (location is String) {
          meData["location"] = {
            ...meData["location"] as Map,
            "name": location,
          };
        }
      }
      meData.addAll(meParam);
    } catch (_) {}
  }
}
