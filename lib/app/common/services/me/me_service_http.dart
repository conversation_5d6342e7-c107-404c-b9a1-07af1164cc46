import 'package:day1/day1.dart';

class AppMeServiceHttp with AppHttpMixin, AppTaskMixin implements AppMeService {
  final AppHttpService _service;
  final AppDbClient _dbClient;
  final UploadService _uploadService;
  final AppSessionService _sessionService;
  final AppFcmService _fcmService;
  final AppCrashlyticsService _crashlyticsService;
  final AppAnalyticsService _analyticsService;

  MeResponse? _cachedMeData;

  AppMeServiceHttp(
    this._service,
    this._dbClient,
    this._uploadService,
    this._sessionService,
    this._fcmService,
    this._crashlyticsService,
    this._analyticsService,
    AppMeMockInterceptor? interceptor,
  ) {
    if (interceptor != null) {
      _service.attachInterceptor(interceptor);
    }
  }

  @override
  MeResponse? get cachedMeData {
    if (!_sessionService.isLoggedIn) return null;
    return runThrowableTask(
      () {
        if (_cachedMeData == null) {
          final rawCacheData = _dbClient.getItem<UserCache>(1);
          if (rawCacheData == null) return null;
          _cachedMeData = MeResponse.fromCache(rawCacheData);
        }
        return _cachedMeData;
      },
      onError: () => _cachedMeData,
    );
  }

  _subscribeMeToTopics(MeResponse me) {
    if (me.id == null) return;
    _fcmService.watchTopic(topic: "${me.id}");
  }

  _identifyMeErrors(MeResponse me) {
    if (me.id == null) return;
    _crashlyticsService.identifyUser(
      id: me.id,
      email: me.email ?? "",
      name: me.name,
    );
  }

  _identifyMeEvents(MeResponse me) {
    if (me.id == null) return;
    _analyticsService.identifyUser(
      id: me.id,
      email: me.email ?? "",
      name: me.name,
    );
  }

  _updateSession(MeResponse? response) {
    _sessionService.modifySession(
      update: _sessionService.sessionData?.copyWith(
        onboardingState: response?.onboardingState,
        hasEnabledBioAuth: response?.hasEnabledBioAuth,
        hasEnabledPinAuth: response?.hasSetPin,
      ),
    );
  }

  @override
  Future<NetworkResponse<MeResponse>> getMeData({
    bool useCache = false,
  }) async {
    MeResponse? cacheData = cachedMeData;
    if (useCache && cacheData != null) {
      _fetchMeData();
      return NetworkResponse(data: cacheData);
    }
    return _fetchMeData();
  }

  Future<NetworkResponse<MeResponse>> _fetchMeData() {
    return requestHandler(() async {
      final rawResponse = await _service.get("/user");
      final response = MeResponse.fromJson(rawResponse.parsedData["user"]);
      _updateSession(response);
      _subscribeMeToTopics(response);
      _identifyMeErrors(response);
      _identifyMeEvents(response);
      await _cacheMeData(response.toCacheData());
      return response;
    });
  }

  Future _cacheMeData(UserCache cache) async {
    try {
      _cachedMeData = MeResponse.fromCache(cache);
      await _dbClient.setItem(cache);
    } catch (e, t) {
      AppLogger.severe("$e", stackTrace: t, error: e);
    }
  }

  @override
  NetworkCallResponse<NoResponse> updateMe(MeUpdateParam param) async {
    return await requestHandler(() async {
      AppImageData? image = param.photo;

      if (image != null && image.isFile) {
        final upload = await _uploadService.uploadFile(
          FileUploadParam(data: image),
        );
        if (upload.hasError) throw upload.error!;
        param.updateImage(upload.data);
      }

      final rawResponse = await _service.patch("/users", body: param);
      final response = MeResponse.fromJson(rawResponse.parsedData["user"]);
      _updateSession(response);
      await _cacheMeData(response.toCacheData());
      return const NoResponse();
    });
  }

  @override
  NetworkCallResponse<NoResponse> deleteMe() async {
    return await requestHandler(() async {
      await _service.post("/users/delete-account");
      return const NoResponse();
    });
  }
}
