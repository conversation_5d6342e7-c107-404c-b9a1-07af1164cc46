import 'package:day1/day1.dart';
import 'package:dio/dio.dart';

class AppMeMockInterceptor extends Interceptor {
  final MeMockResponses _mock;

  AppMeMockInterceptor(this._mock);

  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    dynamic responseData = switch (options.path) {
      "/me" => _mock.meData,
      _ => null,
    };

    if (options.method.upper == "PATCH") {
      _mock.updateMe(options.data);
      responseData = {};
    }

    if (responseData != null) {
      return handler.resolve(
        Response(
          requestOptions: options,
          data: responseData,
          statusCode: 200,
        ),
      );
    }

    super.onRequest(options, handler);
  }
}
