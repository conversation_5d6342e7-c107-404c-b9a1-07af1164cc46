import 'dart:isolate';

import 'package:day1/day1.dart';

class AppBackgroundTaskDispatchServiceImpl
    implements AppBackgroundTaskDispatchService {
  static void _backgroundCompute(BackgroundMessageData data) async {
    final response = await data.function();
    Isolate.exit(data.port, response);
  }

  @override
  Future<T> runBackgroundTask<T>(FutureCall<T> task) async {
    try {
      final receivePort = ReceivePort();
      final data = BackgroundMessageData(
        function: task,
        port: receivePort.sendPort,
      );

      await Isolate.spawn(
        _backgroundCompute,
        data,
        onError: receivePort.sendPort,
        onExit: receivePort.sendPort,
      );
      return await receivePort.first;
    } on ArgumentError catch (_) {
      return await task();
    } catch (e, t) {
      AppLogger.severe("$e", error: e, stackTrace: t);
      rethrow;
    }
  }
}
