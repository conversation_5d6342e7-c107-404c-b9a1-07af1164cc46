import 'package:day1/day1.dart';
import 'package:objectbox/objectbox.dart';

@Entity()
class ThemeCache {
  @Id(assignable: true)
  int id = 1;
  final bool inDarkMode;
  final bool inLightMode;
  final bool inSystemMode;

  ThemeCache(this.inDarkMode, this.inLightMode, this.inSystemMode) : id = 1;
  ThemeCache._light()
      : inLightMode = true,
        inDarkMode = false,
        inSystemMode = false,
        id = 1;
  ThemeCache._dark()
      : inLightMode = false,
        inDarkMode = true,
        inSystemMode = false,
        id = 1;
  ThemeCache._system()
      : inLightMode = false,
        inDarkMode = false,
        inSystemMode = true,
        id = 1;

  @override
  bool operator ==(Object other) {
    if (other is! ThemeCache) return false;

    return other.inDarkMode == inDarkMode &&
        other.inLightMode == inLightMode &&
        other.inSystemMode == inSystemMode;
  }

  static ThemeCache get dark => ThemeCache._dark();
  static ThemeCache get light => ThemeCache._light();
  static ThemeCache get system => ThemeCache._system();

  @Transient()
  String get name {
    if (inDarkMode) return LocaleKeys.dark.tr();
    if (inSystemMode) return LocaleKeys.system.tr();
    return LocaleKeys.light.tr();
  }

  @Transient()
  bool get isDark => inDarkMode;
  @Transient()
  bool get isLight => inLightMode;
  @Transient()
  bool get isSystem => inSystemMode;

  @override
  int get hashCode =>
      super.hashCode ^
      inDarkMode.hashCode ^
      inLightMode.hashCode ^
      inSystemMode.hashCode;
}
