import 'package:day1/day1.dart';
import 'package:objectbox/objectbox.dart';

@Entity()
class UserCache {
  @Id(assignable: true)
  int id = 1;
  @Index()
  String? uuid;
  String? email;
  String? displayName;
  String? firstName;
  String? description;
  String? language;
  String? lastName;
  String? photo;
  @Transient()
  LocationCache? location;
  String? accountNumber;
  String? plan;
  String? phoneNumber;
  @Property(type: PropertyType.dateNano)
  DateTime? createdAt;
  @Property(type: PropertyType.dateNano)
  DateTime? updatedAt;
  String? pinSetAt;
  String? biometricsSetAt;
  String? emailVerifiedAt;
  String? passwordSetAt;
  String? phoneVerifiedAt;
  String? stripeCustomerId;

  UserCache({
    this.uuid,
    this.email,
    this.firstName,
    this.description,
    this.displayName,
    this.lastName,
    this.language,
    this.photo,
    this.location,
    this.phoneNumber,
    this.createdAt,
    this.pinSetAt,
    this.updatedAt,
    this.biometricsSetAt,
    this.emailVerifiedAt,
    this.passwordSetAt,
    this.phoneVerifiedAt,
    this.stripeCustomerId,
  }) : id = 1 {
    location_.target = location;
  }

  final location_ = ToOne<LocationCache>();

  Map<String, dynamic> toMap() {
    final Map<String, dynamic> data = {};
    data['uuid'] = uuid;
    data['id'] = uuid;
    data['email'] = email;
    data['description'] = description;
    data['displayName'] = displayName;
    data['language'] = language;
    data['firstName'] = firstName;
    data['firstName'] = firstName;
    data['lastName'] = lastName;
    data['photo'] = photo;
    data['location'] = (location ?? location_.target)?.toJson();
    data['phoneNumber'] = phoneNumber;
    data['createdAt'] = createdAt?.toIso8601String();
    data['pinSetAt'] = pinSetAt;
    data['updatedAt'] = updatedAt?.toIso8601String();
    data['biometricsSetAt'] = biometricsSetAt;
    data['emailVerifiedAt'] = emailVerifiedAt;
    data['passwordSetAt'] = passwordSetAt;
    data['phoneVerifiedAt'] = phoneVerifiedAt;
    data['stripeCustomerId'] = stripeCustomerId;
    return data;
  }
}

@Entity()
class ImageCache extends Codable {
  @Id()
  int id = 0;
  @Index()
  String? uuid;
  String? createdAt;
  String? name;
  String? url;
  String? type;

  ImageCache({
    this.uuid,
    this.createdAt,
    this.name,
    this.url,
    this.type,
  });

  @override
  toJson() {
    final data = {
      "id": id,
      "uuid": uuid,
      "createdAt": createdAt,
      "name": name,
      "url": url,
      "type": type,
    };

    data.removeWhere((key, value) => value == null);
    return data;
  }
}

@Entity()
class AppSelectionResponseCache {
  @Index()
  String? id;
  @Id()
  int index = 0;
  bool? isActive;
  String? name;

  AppSelectionResponseCache({this.isActive, this.id, this.name});
}

@Entity()
class TelephoneNumberCache {
  @Id()
  int id = 0;
  String? countryCode;
  String? number;

  TelephoneNumberCache({this.countryCode, this.number});
}

@Entity()
class LocationCache {
  @Id()
  int id = 0;
  String? placeId;
  String? displayName;

  LocationCache({this.placeId, this.displayName});

  Map<String, dynamic> toJson() {
    final data = {
      "id": id,
      "placeId": placeId,
      "displayName": displayName,
    };
    data.removeWhere((key, value) => value == null);
    return data;
  }
}
