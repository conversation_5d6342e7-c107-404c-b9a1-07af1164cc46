import 'package:day1/core/core.dart';
import 'package:objectbox/objectbox.dart';

@Entity()
class SessionCache {
  @Id(assignable: true)
  int id = 1;

  @Index()
  @Unique(onConflict: ConflictStrategy.replace)
  String accessToken;

  @Index()
  String refreshToken;

  int createdAt;
  int expiresAt;
  bool hasEnabledBioAuth;
  bool hasEnabled2fa;
  bool hasEnabledPinAuth;
  String? userUuid;
  String? onboardingState;
  String? lastEmail;
  String? lastPhoneNumber;
  String? authPublicKey;
  String? appPublicKey;

  SessionCache({
    required this.accessToken,
    required this.expiresAt,
    required this.onboardingState,
    required this.refreshToken,
    this.appPublicKey,
    this.hasEnabledBioAuth = false,
    this.hasEnabled2fa = false,
    this.hasEnabledPinAuth = false,
    this.lastEmail,
    this.userUuid,
    this.lastPhoneNumber,
    this.authPublicKey,
  })  : id = 1,
        createdAt = DateTime.now().millisecondsSinceEpoch;

  SessionCache copyWith({
    String? accessToken,
    String? refreshToken,
    int? expiresAt,
    bool? hasEnabledBioAuth,
    bool? hasEnabledPinAuth,
    bool? hasEnabled2fa,
    String? onboardingState,
    String? lastEmail,
    String? lastPhoneNumber,
    String? userUuid,
    String? authPublicKey,
    String? appPublicKey,
  }) {
    return SessionCache(
      accessToken: accessToken ?? this.accessToken,
      lastPhoneNumber: lastPhoneNumber ?? this.lastPhoneNumber,
      hasEnabledPinAuth: hasEnabledPinAuth ?? this.hasEnabledPinAuth,
      expiresAt: expiresAt ?? this.expiresAt,
      onboardingState: onboardingState ?? this.onboardingState,
      refreshToken: refreshToken ?? this.refreshToken,
      hasEnabled2fa: hasEnabled2fa ?? this.hasEnabled2fa,
      hasEnabledBioAuth: hasEnabledBioAuth ?? this.hasEnabledBioAuth,
      lastEmail: lastEmail ?? this.lastEmail,
      userUuid: userUuid ?? this.userUuid,
      authPublicKey: authPublicKey ?? this.authPublicKey,
      appPublicKey: appPublicKey ?? this.appPublicKey,
    );
  }

  factory SessionCache.loggedOut(SessionCache? activeSession) {
    return SessionCache(
      accessToken: "",
      expiresAt: 0,
      onboardingState: activeSession?.onboardingState,
      refreshToken: "",
      lastEmail: activeSession?.lastEmail,
      lastPhoneNumber: activeSession?.lastPhoneNumber,
      authPublicKey: activeSession?.authPublicKey,
      appPublicKey: activeSession?.appPublicKey,
      hasEnabledBioAuth: activeSession?.hasEnabledBioAuth ?? false,
      hasEnabled2fa: activeSession?.hasEnabled2fa ?? false,
      hasEnabledPinAuth: activeSession?.hasEnabledPinAuth ?? false,
    );
  }

  bool get hasCompletedOnboarding {
    return onboardingState?.equals("completed") ?? false;
  }
}
