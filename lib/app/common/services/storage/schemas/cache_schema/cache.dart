import 'dart:typed_data';

import 'package:day1/core/core.dart';
import 'package:objectbox/objectbox.dart';

@Entity()
class CacheCache {
  @Id()
  int id = 0;

  String? data;

  List<int>? byteData;

  @Index(type: IndexType.hash)
  @Unique(onConflict: ConflictStrategy.replace)
  String uri;

  @Index(type: IndexType.value)
  @Property(type: PropertyType.date)
  int expiresAt;

  CacheCache({
    required this.data,
    required this.byteData,
    required this.uri,
    required this.expiresAt,
  });

  bool get hasData {
    if (isExpired) return false;
    final hasJsonData = data.hasValue && data != "null";
    final hasByteData = byteData.hasValue;
    return hasJsonData || hasByteData;
  }

  bool get isExpired {
    return DateTime.now().millisecondsSinceEpoch >= expiresAt;
  }

  @Transient()
  Future<dynamic> get asJson async {
    try {
      if (!data.hasValue) return null;
      return await AppHelpers.parseJson(data!);
    } catch (e, t) {
      AppLogger.severe("$e", error: e, stackTrace: t);
      return null;
    }
  }

  @Transient()
  Uint8List? get asBytes {
    try {
      if (!byteData.hasValue) return null;
      return Uint8List.fromList(byteData.value);
    } catch (e, t) {
      AppLogger.severe("$e", error: e, stackTrace: t);
      return null;
    }
  }
}
