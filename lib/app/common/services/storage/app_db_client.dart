import 'package:objectbox/objectbox.dart';

abstract class AppDbClient {
  Future init();

  T? getItem<T>(int id);
  Future<T?> getItemAsync<T>(int id);
  List<T> getAllItems<T>(List<int> ids);
  Future<List<T>> getAllItemsAsync<T>(List<int> ids);

  Future<int?> setItem<T>(T data);
  Future<List<int>?> setItems<T>(List<T> data);

  Future<bool> removeItem<T>(int id);
  Future<int?> removeAllItems<T>(List<int> ids);

  int length<T>();
  Future<int> lengthAsync<T>();

  Future clearCollection<T>();
  Future clearDb();
  Future closeDb();

  Box<T>? getCollectionDb<T>();
}
