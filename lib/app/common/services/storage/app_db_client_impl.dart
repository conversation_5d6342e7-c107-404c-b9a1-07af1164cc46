import 'package:day1/day1.dart';
import 'package:day1/objectbox.g.dart';
import 'package:path/path.dart';
import 'package:path_provider/path_provider.dart';

class AppDbClientImpl with AppTaskMixin implements AppDbClient {
  final DoAppConfig _config;
  final FutureCall<Store>? _initialiseStore;
  Store? _cursor;

  AppDbClientImpl(this._config, this._initialiseStore);

  String get _dbName {
    return "${_config.dbName}-database";
  }

  @override
  Future init() async {
    if (_initialiseStore != null) {
      _cursor = await _initialiseStore();
      return;
    }
    final documentDir = await getApplicationDocumentsDirectory();
    _cursor = await openStore(
      directory: join(documentDir.path, _dbName),
      queriesCaseSensitiveDefault: false,
    );
  }

  @override
  Box<T>? getCollectionDb<T>() {
    if (_cursor == null) return null;
    return tryRunThrowableTask<Box<T>?>(() {
      return _cursor?.box<T>();
    });
  }

  @override
  Future clearCollection<T>() async {
    await getCollectionDb<T>()?.removeAllAsync();
  }

  @override
  clearDb() async {
    await tryRunThrowableTask(() async {
      final futures = [
        getCollectionDb<SessionCache>()?.removeAllAsync(),
        getCollectionDb<ThemeCache>()?.removeAllAsync(),
        getCollectionDb<UserCache>()?.removeAllAsync(),
        getCollectionDb<CacheCache>()?.removeAllAsync(),
      ];
      futures.removeWhere((it) => it == null);
      await Future.wait<int>(futures as Iterable<Future<int>>);
    });
  }

  @override
  Future closeDb() async => _cursor?.close();

  @override
  List<T> getAllItems<T>(List<int> ids) {
    final items = tryRunThrowableTask<List<T>>(() {
      final collection = getCollectionDb<T>();

      if (collection == null) return [];

      final items = collection.getMany(ids);
      return [...items.where((it) => it != null)].cast();
    });
    return items ?? [];
  }

  @override
  Future<List<T>> getAllItemsAsync<T>(List<int> ids) async {
    final items = await tryRunThrowableTask<Future<List<T>>>(() async {
      final collection = getCollectionDb<T>();

      if (collection == null) return [];

      final items = await collection.getManyAsync(ids);
      return [...items.where((it) => it != null)].cast();
    });
    return items ?? [];
  }

  @override
  T? getItem<T>(int id) {
    return tryRunThrowableTask<T?>(() {
      final collection = getCollectionDb<T>();

      if (collection == null) return null;

      return collection.get(id);
    });
  }

  @override
  Future<T?> getItemAsync<T>(int id) async {
    final item = await tryRunThrowableTask(
      () async {
        final collection = getCollectionDb<T>();

        if (collection == null) return null;

        return collection.getAsync(id);
      },
    );
    return item;
  }

  @override
  int length<T>() {
    final size = tryRunThrowableTask<int?>(() {
      final collection = getCollectionDb<T>();

      if (collection == null) return null;

      return collection.count();
    });
    return size ?? 0;
  }

  @override
  Future<int> lengthAsync<T>() async => length();

  @override
  Future<int?> removeAllItems<T>(List<int> ids) async {
    return tryRunThrowableTask<Future<int?>>(() async {
      final collection = getCollectionDb<T>();

      if (collection == null) return null;

      return await collection.removeManyAsync(ids);
    });
  }

  @override
  Future<bool> removeItem<T>(int id) async {
    return await runThrowableTask<Future<bool>>(
      () async {
        final collection = getCollectionDb<T>();

        if (collection == null) return false;

        return collection.removeAsync(id);
      },
      onError: () async => false,
    );
  }

  @override
  Future<int?> setItem<T>(T data) async {
    return tryRunThrowableTask<Future<int?>>(() async {
      final collection = getCollectionDb<T>();

      if (collection == null) return null;

      return collection.put(data);
    });
  }

  @override
  Future<List<int>?> setItems<T>(List<T> data) async {
    return tryRunThrowableTask<Future<List<int>?>>(() async {
      final collection = getCollectionDb<T>();

      if (collection == null) return null;

      return await collection.putManyAsync(data);
    });
  }
}
