import 'package:day1/day1.dart';

class AppValidators {
  static bool _isEmpty(String? text) => !text.hasValue;

  static String? passwordValidator(
    String? password, {
    bool isRequired = true,
    int minLength = 8,
    int maxLength = 128, // Add maximum length for security
  }) {
    final isEmpty = _isEmpty(password);

    if (!isRequired && isEmpty) return null;

    if (isEmpty) {
      return LocaleKeys.passwordRequired.tr();
    }

    // Trim the password to prevent whitespace attacks
    final trimmedPassword = password?.trim();

    // Check for minimum length
    if ((trimmedPassword?.length ?? 0) < minLength) {
      return LocaleKeys.passwordMustHaveNChars.tr({"num": "$minLength"});
    }

    // Check for maximum length to prevent DoS attacks
    if ((trimmedPassword?.length ?? 0) > maxLength) {
      return LocaleKeys.passwordTooLong.tr({"num": "$maxLength"});
    }

    // Validate password complexity
    if (!AppRegex.passwordRegEx.hasMatch(trimmedPassword!)) {
      return LocaleKeys.invalidPassword.tr();
    }

    return null;
  }

  static String? minLength(
    String? text, {
    bool isRequired = true,
    int length = 6,
  }) {
    final isEmpty = _isEmpty(text?.trim());

    if (!isRequired && isEmpty) return null;

    if (isEmpty) {
      return LocaleKeys.fieldRequired.tr();
    }

    if ((text?.trim().length ?? 0) < length) {
      return LocaleKeys.minLength.tr({"num": "$length"});
    }

    return null;
  }

  static String? maxLength(
    String? text, {
    bool isRequired = true,
    int length = 6,
  }) {
    final isEmpty = _isEmpty(text);

    if (!isRequired && isEmpty) return null;

    if (isEmpty) {
      return LocaleKeys.fieldRequired.tr();
    }

    if ((text?.length ?? 0) > length) {
      return LocaleKeys.maxLength.tr({"num": "$length"});
    }

    return null;
  }

  static String? length(
    String? text, {
    bool isRequired = true,
    int length = 3,
  }) {
    final isEmpty = _isEmpty(text);

    if (!isRequired && isEmpty) return null;

    if (isEmpty) {
      return LocaleKeys.fieldRequired.tr();
    }

    if ((text?.length ?? 0) > length) {
      return LocaleKeys.maxLength.tr({"num": "$length"});
    }

    if ((text?.length ?? 0) < length) {
      return LocaleKeys.minLength.tr({"num": "$length"});
    }

    return null;
  }

  static String? matchValidator(
    String? base,
    String? comparison, {
    String? message,
    bool isRequired = true,
  }) {
    final isEmpty = _isEmpty(base);

    if (!isRequired && isEmpty) return null;

    if (isEmpty) {
      return LocaleKeys.fieldRequired.tr();
    }

    if (base != comparison) return message ?? LocaleKeys.fieldsDontMatch.tr();

    return null;
  }

  static String? emailValidator(String? email, {bool isRequired = true}) {
    final isEmpty = _isEmpty(email);

    if (!isRequired && isEmpty) return null;

    if (isEmpty) {
      return LocaleKeys.fieldRequired.tr();
    }

    // Trim and normalize email
    final trimmedEmail = email?.trim().toLowerCase();

    // Check email length to prevent DoS attacks
    if ((trimmedEmail?.length ?? 0) > 254) {
      return LocaleKeys.emailTooLong.tr();
    }

    if (!AppRegex.mailRegEx.hasMatch(trimmedEmail!)) {
      return LocaleKeys.provideValidEmail.tr();
    }

    return null;
  }

  static String? urlValidator(String? url, {bool isRequired = true}) {
    final isEmpty = _isEmpty(url);

    if (!isRequired && isEmpty) return null;

    if (isEmpty) {
      return LocaleKeys.fieldRequired.tr();
    }

    if (!AppRegex.urlRegex.hasMatch(url!)) {
      return LocaleKeys.invalidUrl.tr();
    }

    return null;
  }

  static String? phoneValidator(
    String? tel, {
    String countryCode = "",
    bool isRequired = true,
  }) {
    final isEmpty = _isEmpty(tel);

    if (!isRequired && isEmpty) return null;

    if (isEmpty) {
      return LocaleKeys.fieldRequired.tr();
    }

    if (!AppRegex.phoneRegex.hasMatch("$countryCode$tel")) {
      return LocaleKeys.invalidPhone.tr();
    }
    return null;
  }

  static String? dobValidator(
    String? date, {
    int minAge = 18,
    bool isRequired = true,
  }) {
    final isEmpty = _isEmpty(date);

    if (!isRequired && isEmpty) return null;

    if (isEmpty) {
      return LocaleKeys.fieldRequired.tr();
    }

    final parsedDate = DateTime.tryParse(date ?? '');

    if (parsedDate == null) return LocaleKeys.invalidDate.tr();

    final now = DateTime.now();

    final yearsSinceDate = (now.difference(parsedDate).inDays / 365);

    if (yearsSinceDate < minAge) {
      return LocaleKeys.mustBeNYears.tr({"age": "$minAge"});
    }

    return null;
  }

  static String? cardExpiryValidator(String? text) {
    if (!text.hasValue) return LocaleKeys.fieldRequired.tr();

    DateTime now = DateTime.now();
    now = DateTime(now.year, now.month);
    final parsedDate = text?.fromCardExpiryTextToDate;

    if (parsedDate == null) return LocaleKeys.invalidDate.tr();

    final isAfterNow = parsedDate >= now;

    if (!isAfterNow) return LocaleKeys.invalidDate.tr();

    return null;
  }

  static String? dateValidator(
    String? date, {
    String? otherDate,
    bool shouldBeGreaterThan = true,
    String? comparisonErrorMessage,
    bool isRequired = true,
  }) {
    final isEmpty = _isEmpty(date);

    if (!isRequired && isEmpty) return null;

    if (isEmpty) {
      return LocaleKeys.fieldRequired.tr();
    }

    final parsedDate = DateTime.tryParse(date ?? '');

    if (parsedDate == null) return LocaleKeys.invalidDate.tr();

    if (!_isEmpty(otherDate)) {
      final parsedOtherDate = DateTime.tryParse(otherDate ?? '');

      if (parsedOtherDate == null) return null;

      final defaultErrorMessage =
          comparisonErrorMessage ?? LocaleKeys.invalidDate.tr();

      String? message;

      if (shouldBeGreaterThan) {
        final isGreaterThan = parsedDate >= parsedOtherDate;

        message = isGreaterThan ? null : defaultErrorMessage;
      }

      if (!shouldBeGreaterThan) {
        final isLessThan = parsedDate <= parsedOtherDate;

        message = isLessThan ? null : defaultErrorMessage;
      }

      return message;
    }

    return null;
  }

  static String? required(String? data) {
    bool isEmpty = _isEmpty(data);

    if (isEmpty) return LocaleKeys.fieldRequired.tr();

    return null;
  }

  static String? requiredList(List? data) {
    final isEmpty = !data.hasValue;

    if (isEmpty) {
      return LocaleKeys.fieldRequired.tr();
    }

    return null;
  }

  static String? nameValidator(String? name, {bool isRequired = true}) {
    final isEmpty = _isEmpty(name);

    if (!isRequired && isEmpty) return null;

    if (isEmpty) {
      return LocaleKeys.fieldRequired.tr();
    }

    return null;
  }

  static String? cardNumberValidator(String? text, {bool isRequired = true}) {
    final number = text.value.withoutWhiteSpaceAndSpecialChar;
    final isEmpty = _isEmpty(number);

    if (!isRequired && isEmpty) return null;

    if (isEmpty) return LocaleKeys.fieldRequired.tr();

    if (AppRegex.masterRegex.hasMatch(number)) return null;
    if (AppRegex.visaRegex.hasMatch(number)) return null;
    if (AppRegex.maestroRegex.hasMatch(number)) return null;
    if (AppRegex.jcbRegex.hasMatch(number)) return null;
    if (AppRegex.unionpayRegex.hasMatch(number)) return null;
    if (AppRegex.amexRegex.hasMatch(number)) return null;

    return LocaleKeys.invalidCardNumber.tr();
  }

  static String? cvvValidator(String? text, {bool isRequired = true}) {
    final number = text.value.withoutWhiteSpaceAndSpecialChar;
    final isEmpty = _isEmpty(number);

    if (!isRequired && isEmpty) return null;

    if (isEmpty) return LocaleKeys.fieldRequired.tr();

    if (AppRegex.cvvRegex.hasMatch(number)) return null;

    return LocaleKeys.invalidCvv.tr();
  }

  static String? amountValidator(
    String? value, {
    num? minAmount,
    num? maxAmount,
    bool isRequired = true,
  }) {
    final isEmpty = _isEmpty(value);

    if (!isRequired && isEmpty) return null;

    if (isEmpty) {
      return LocaleKeys.fieldRequired.tr();
    }

    final num? amount = AppHelpers.extractAmount(value);
    if (amount == null) {
      return LocaleKeys.invalidAmount.tr();
    }
    if (minAmount != null && amount < minAmount) {
      final min = AppTextFormatter.formatCurrency(minAmount);
      return LocaleKeys.amountMinimum.tr({"amount": min});
    }
    if (maxAmount != null && amount > maxAmount) {
      final max = AppTextFormatter.formatCurrency(maxAmount);
      return LocaleKeys.amountMaximum.tr({"amount": max});
    }
    return null;
  }

  static String? digitValidator(String? value, {bool isRequired = true}) {
    final isEmpty = _isEmpty(value);

    if (!isRequired && isEmpty) return null;

    if (isEmpty) {
      return LocaleKeys.fieldRequired.tr();
    }
    final num? amount = AppHelpers.extractAmount(value);
    if (amount == null) {
      return LocaleKeys.invalidNumber.tr();
    }
    return null;
  }
}
