import 'package:day1/day1.dart';

mixin AppAnalyticsMixin {
  trackEvent(
    AppEvent event, {
    String? description,
    dynamic value,
  }) async {
    final analytics = locator<AppAnalyticsService>();
    analytics.trackEvent(
      AppAnalyticsData(
        event,
        description: description,
        value: value,
      ),
    );
  }

  trackNavigation(String path, {String? widgetClass}) async {
    final analytics = locator<AppAnalyticsService>();
    analytics.trackNavigation(path, widgetClass: widgetClass ?? path);
  }
}
