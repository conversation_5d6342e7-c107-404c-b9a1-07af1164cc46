import 'package:day1/day1.dart';
import 'package:flutter/widgets.dart'
    show
        BuildContext,
        MediaQuery,
        MediaQueryData,
        EdgeInsets,
        RenderBox,
        Size,
        Offset;

class AppDimension {
  late MediaQueryData _queryData;
  late ScreenType screenType;

  AppDimension(BuildContext context) {
    _queryData = MediaQuery.of(context);
    screenType = context.screenType;
  }

  double get breakPointFraction {
    if (screenType.isMonitor) return 0.27;
    if (screenType.isLaptop) return 0.32;
    if (screenType.isTablet) return 0.53;
    return 1;
  }

  double get topInset {
    return _queryData.viewInsets.top;
  }

  double get bottomInset {
    return _queryData.viewInsets.bottom;
  }

  double get shortestSide {
    return _queryData.size.shortestSide;
  }

  double get longestSide {
    return _queryData.size.longestSide;
  }

  double get width {
    return _queryData.size.width;
  }

  double get height {
    return _queryData.size.height;
  }

  double get pixelRatio {
    return _queryData.devicePixelRatio;
  }

  double _resolveFraction(double value) => value * breakPointFraction;

  double setLongestSide(double percentage) {
    if (percentage == 0) return 0;
    return _resolveFraction(longestSide * (percentage / 100));
  }

  double setShortestSide(double percentage) {
    if (percentage == 0) return 0;
    return _resolveFraction(shortestSide * (percentage / 100));
  }

  double setHeight(double percentage) {
    if (percentage == 0) return 0;
    return _resolveFraction(height * (percentage / 100));
  }

  double setWidth(double percentage) {
    if (percentage == 0) return 0;
    return _resolveFraction(width * (percentage / 100));
  }
}

class AppFontSizer {
  late num _scale;

  double _x(BuildContext context) {
    final x = MediaQuery.sizeOf(context).shortestSide;
    return x > 428 ? 428 : x;
  }

  double _y(BuildContext context) {
    final y = MediaQuery.sizeOf(context).longestSide;
    return y > 926 ? 926 : y;
  }

  AppFontSizer(BuildContext context) {
    final y = _y(context);
    final x = _x(context);
    _scale = (x + y) / 4;
  }

  double sp(double? percentage) {
    return (_scale * ((percentage ?? 40) / 1000)).floorToDouble();
  }

  double px(double? pixels) {
    return (1000 * ((pixels ?? 14)) / _scale).toDouble();
  }
}

class AppInsets {
  late AppDimension _sizer;
  late AppFontSizer _fontSizer;

  AppInsets(BuildContext context) {
    _sizer = AppDimension(context);
    _fontSizer = AppFontSizer(context);
  }

  EdgeInsets get zero {
    return EdgeInsets.zero;
  }

  EdgeInsets all(double inset) {
    return EdgeInsets.all(_sizer.setShortestSide(inset));
  }

  EdgeInsets get defaultHorizontalInsets {
    return symmetricSp(horizontal: AppFontSizes.px12);
  }

  EdgeInsets get defaultVerticalInsets {
    return symmetricSp(vertical: AppFontSizes.px12);
  }

  EdgeInsets get defaultAllInsets {
    return allSp(AppFontSizes.px12);
  }

  EdgeInsets get defaultCardInsets {
    return allSp(AppFontSizes.px32);
  }

  EdgeInsets get defaultCardHInsets {
    return symmetricSp(horizontal: AppFontSizes.px32);
  }

  EdgeInsets only({
    double left = 0,
    double top = 0,
    double right = 0,
    double bottom = 0,
  }) {
    return EdgeInsets.only(
      top: _sizer.setHeight(top),
      left: _sizer.setWidth(left),
      bottom: _sizer.setHeight(bottom),
      right: _sizer.setWidth(right),
    );
  }

  EdgeInsets fromLTRB(
    double left,
    double top,
    double right,
    double bottom,
  ) {
    return EdgeInsets.fromLTRB(
      _sizer.setWidth(left),
      _sizer.setHeight(top),
      _sizer.setWidth(right),
      _sizer.setHeight(bottom),
    );
  }

  EdgeInsets symmetric({
    double vertical = 0,
    double horizontal = 0,
  }) {
    return EdgeInsets.symmetric(
      vertical: _sizer.setHeight(vertical),
      horizontal: _sizer.setWidth(horizontal),
    );
  }

  EdgeInsets allSp(double inset) {
    return EdgeInsets.all(_fontSizer.sp(inset));
  }

  EdgeInsets onlySp({
    double left = 0,
    double top = 0,
    double right = 0,
    double bottom = 0,
  }) {
    return EdgeInsets.only(
      top: _fontSizer.sp(top),
      left: _fontSizer.sp(left),
      bottom: _fontSizer.sp(bottom),
      right: _fontSizer.sp(right),
    );
  }

  EdgeInsets fromLTRBSp(
    double left,
    double top,
    double right,
    double bottom,
  ) {
    return EdgeInsets.fromLTRB(
      _fontSizer.sp(left),
      _fontSizer.sp(top),
      _fontSizer.sp(right),
      _fontSizer.sp(bottom),
    );
  }

  EdgeInsets symmetricSp({
    double vertical = 0,
    double horizontal = 0,
  }) {
    return EdgeInsets.symmetric(
      vertical: _fontSizer.sp(vertical),
      horizontal: _fontSizer.sp(horizontal),
    );
  }
}

class AppScaleUtil {
  final BuildContext context;

  AppScaleUtil.of(this.context);

  AppDimension get sizer => AppDimension(context);
  AppFontSizer get fontSizer => AppFontSizer(context);
  AppInsets get insets => AppInsets(context);

  Offset get position {
    final RenderBox box = context.findRenderObject() as RenderBox;
    return box.localToGlobal(Offset.zero);
  }

  Size get size {
    final RenderBox box = context.findRenderObject() as RenderBox;
    return box.size;
  }
}

class ScreenType {
  final BuildContext _context;

  ScreenType(this._context);

  static const _kTabletBreakPoint = 672.0;
  static const _kLaptopBreakPoint = 1120.0;
  static const _kMonitorBreakPoint = 1312.0;

  double get width => MediaQuery.sizeOf(_context).width;

  bool get isMobile => width < _kTabletBreakPoint;

  bool get isTablet =>
      width >= _kTabletBreakPoint && width < _kLaptopBreakPoint;

  bool get isLaptop =>
      width >= _kLaptopBreakPoint && width < _kMonitorBreakPoint;

  bool get isMonitor => width >= _kMonitorBreakPoint;
}
