import 'package:day1/day1.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

extension TestWidgetExtensions on Widget {
  Widget preventPopAtRoot() {
    return AppRootPopScope(child: this);
  }
}

extension WidgetExtensions on Widget {
  List<Widget> operator *(int count) {
    return List.generate(count, (_) => this);
  }

  DoAppConfig get config => locator<DoAppConfig>();
  AppConfigRadii get radii => config.radii;
  AppConfigButtonSizes get buttonSizes => config.buttonSizes;
  AppConfigFonts get fonts => config.fonts;
  AppConfigSpacing get spacing => config.spacing;

  setAppBarColor(Color color) {
    SystemChrome.setSystemUIOverlayStyle(
      SystemUiOverlayStyle(statusBarColor: color),
    );
  }

  setNavBarColor(Color color) {
    SystemChrome.setSystemUIOverlayStyle(
      SystemUiOverlayStyle(systemNavigationBarColor: color),
    );
  }

  App<PERSON>rowser get browser {
    return locator<AppBrowser>();
  }

  launchUrl(String url) {
    try {
      browser.openUrl(url);
    } catch (e, t) {
      AppLogger.severe("$e", error: e, stackTrace: t);
    }
  }
}

extension WidgetStateExtensions<T extends StatefulWidget> on State<T> {
  DoAppConfig get config => locator<DoAppConfig>();
  AppConfigRadii get radii => config.radii;
  AppConfigFonts get fonts => config.fonts;
  AppConfigSpacing get spacing => config.spacing;
  AppConfigButtonSizes get buttonSizes => config.buttonSizes;

  AppBrowser get browser {
    return locator<AppBrowser>();
  }

  setAppBarColor(Color color) {
    SystemChrome.setSystemUIOverlayStyle(
      SystemUiOverlayStyle(statusBarColor: color),
    );
  }

  setNavBarColor(Color color) {
    SystemChrome.setSystemUIOverlayStyle(
      SystemUiOverlayStyle(systemNavigationBarColor: color),
    );
  }

  launchUrl(String url) {
    try {
      browser.openUrl(url);
    } catch (e, t) {
      AppLogger.severe("$e", error: e, stackTrace: t);
    }
  }
}
