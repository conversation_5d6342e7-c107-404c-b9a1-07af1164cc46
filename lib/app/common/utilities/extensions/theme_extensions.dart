import 'package:flutter/cupertino.dart';
import 'package:day1/day1.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

extension ThemeExtension on BuildContext {
  observeTheme() => watch<ThemeState>();
  ThemeState get themeProvider {
    return read<ThemeState>();
  }

  String get font => theme.font;

  CupertinoThemeData? get currentThemeIos {
    return inDarkMode ? theme.darkThemeIos : theme.lightThemeIos;
  }

  ThemeData get currentTheme {
    return inDarkMode ? theme.darkTheme : theme.lightTheme;
  }

  String get themeName => themeProvider.mode.name;

  bool get inDarkMode => themeProvider.inDarkMode(this);

  ThemeData get activeTheme => currentTheme;

  AppTheme get theme => config.theme(inDarkMode);

  AppTextStyles get textStyle => theme.textStyles(this);

  AppInputStyles get inputStyle => theme.inputStyle(this);

  AppShadows get shadows => theme.shadows(this);

  AppGradients get gradients => theme.gradients(this);

  Color get activeIndicatorColor => theme.activeIndicatorColor;

  Color get activeIndicatorBorderColor => theme.activeIndicatorBorderColor;

  Color get inActiveIndicatorColor => theme.inActiveIndicatorColor;

  Color get appBarBgColor {
    return Theme.of(this).appBarTheme.backgroundColor ?? scaffoldBgColor;
  }

  Color get inActiveIndicatorBorderColor => theme.inActiveIndicatorBorderColor;

  Color get transparent => theme.transparent;

  Color get errorColor => theme.errorColor;

  Color get notificationBackgroundColor => theme.notificationBackgroundColor;

  Color get errorNotificationBackgroundColor =>
      theme.errorNotificationBackgroundColor;

  Color get infoNotificationBackgroundColor =>
      theme.infoNotificationBackgroundColor;

  Color get warningNotificationBackgroundColor =>
      theme.warningNotificationBackgroundColor;

  Color get notificationTextColor => theme.notificationTextColor;

  Color get warningNotificationTextColor => theme.warningNotificationTextColor;

  Color get infoNotificationTextColor => theme.infoNotificationTextColor;

  Color get errorNotificationTextColor => theme.errorNotificationTextColor;

  Color get scaffoldBgColor => theme.scaffoldBgColor;

  Color get shadowColor => theme.shadowColor;

  Color get barrierColor => theme.barrierColor;

  Color get dividerColor => theme.dividerColor;

  Color get switchTrackColor => theme.switchTrackColor;

  Color get switchTrackInactiveColor => theme.switchTrackInactiveColor;

  Color get switchThumbInactiveColor => theme.switchThumbInactiveColor;

  Color get switchThumbActiveColor => theme.switchThumbActiveColor;

  Color get inputBorderColor => theme.inputBorderColor;

  Color get inputBgColor => theme.inputBgColor;

  Color get disabledInputBorderColor => theme.disabledInputBorderColor;

  Color get activeInputBorderColor => theme.activeInputBorderColor;

  Color get successCardColor => theme.successCardColor;

  Color get disabledInputFillColor => theme.disabledInputFillColor;

  Color get textColor => theme.textColor;

  Color get errorTextColor => theme.errorTextColor;

  Color get highlightedTextColor => theme.highlightedTextColor;

  Color get secondaryTextColor => theme.secondaryTextColor;

  Color get hintTextColor => theme.hintTextColor;

  Color get disabledBtnColor => theme.disabledBtnColor;

  Color get disabledBtntextColor => theme.disabledBtntextColor;

  Color get outlineBtnBorderColor => theme.outlineBtnBorderColor;

  Color get outlineBtnAltBorderColor => theme.outlineBtnAltBorderColor;

  double get outlineBtnDisabledOpacity => theme.outlineBtnDisabledOpacity;

  double get outlineBtnAltDisabledOpacity => theme.outlineBtnAltDisabledOpacity;

  Color get outlineBtnBgColor => theme.outlineBtnBgColor;

  Color get outlineBtnAltBgColor => theme.outlineBtnAltBgColor;

  Color get outlineBtnFocusedBgColor => theme.outlineBtnFocusedBgColor;

  Color get outlineBtnAltFocusedBgColor => theme.outlineBtnAltFocusedBgColor;

  Color get outlineBtnTextColor => theme.outlineBtnTextColor;

  Color get raisedBtnBorderColor => theme.raisedBtnBorderColor;

  Color get raisedBtnDisabledBorderColor => theme.raisedBtnDisabledBorderColor;

  Color get raisedBtnBgColor => theme.raisedBtnBgColor;

  Color get raisedBtnBBgColor => theme.raisedBtnBBgColor;

  Color get raisedBtnCBgColor => theme.raisedBtnCBgColor;

  Color get raisedBtnTextColor => theme.raisedBtnTextColor;

  Color get raisedBtnFocusedBgColor => theme.raisedBtnFocusedBgColor;

  Color get raisedBtnBFocusedBgColor => theme.raisedBtnBFocusedBgColor;

  Color get raisedBtnCFocusedBgColor => theme.raisedBtnCFocusedBgColor;

  double get raisedBtnDisabledOpacity => theme.raisedBtnDisabledOpacity;

  Color get textBtnTextColor => theme.textBtnTextColor;

  Color get textBtnFocusedTextColor => theme.textBtnFocusedTextColor;

  Color get textBtnDisabledTextColor => theme.textBtnDisabledTextColor;

  Color get appleBtnTextColor => theme.appleBtnTextColor;

  Color get neutralTextColor => theme.neutralTextColor;

  Color get appleBtnColor => theme.appleBtnColor;

  Color get appleBtnBorderColor => theme.appleBtnBorderColor;

  Color get googleBtnTextColor => theme.googleBtnTextColor;

  Color get googleBtnColor => theme.googleBtnColor;

  Color get googleBtnBorderColor => theme.googleBtnBorderColor;

  Color get inactiveIndicatorColor => theme.inactiveIndicatorColor;

  Color get cardColor => theme.cardColor;

  Color get iconColor => theme.iconColor;

  Color get highlightedIconColor => theme.highlightedIconColor;

  Color get cardBorderColor => theme.cardBorderColor;

  Color get pillTextColor => theme.pillTextColor;

  Color get errorInputBorderColor => theme.errorInputBorderColor;

  Color get infoNotificationBorderColor => theme.infoNotificationBorderColor;

  Color get warningNotificationBorderColor =>
      theme.warningNotificationBorderColor;

  Color get notificationBorderColor => theme.notificationBorderColor;

  Color get errorNotificationBorderColor => theme.errorNotificationBorderColor;
}
