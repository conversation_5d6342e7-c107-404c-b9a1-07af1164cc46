import 'dart:io';
import 'package:day1/day1.dart';
import 'package:easy_localization/easy_localization.dart'
    hide StringTranslateExtension;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

extension BuildContextExtension on BuildContext {
  AppScaleUtil get _scaler => AppScaleUtil.of(this);

  AppDimension get sizer => _scaler.sizer;

  AppFontSizer get fontSizer => _scaler.fontSizer;

  AppInsets get insets => _scaler.insets;

  double sp(double? size) => fontSizer.sp(size);

  double fractionalHeight(double percentage) => sizer.setHeight(percentage);
  double fractionalLongSide(double percentage) =>
      sizer.setLongestSide(percentage);
  double fractionalWidth(double percentage) => sizer.setWidth(percentage);
  double fractionalShortSide(double percentage) =>
      sizer.setShortestSide(percentage);

  DoAppConfig get _config => locator();

  AppConfigRadii get _radii => _config.radii;
  AppConfigButtonSizes get _btnSizes => _config.buttonSizes;

  double get xxsRadius => sp(_radii.xxsRadius);
  double get xsRadius => sp(_radii.xsRadius);
  double get smRadius => sp(_radii.smRadius);
  double get mdRadius => sp(_radii.mdRadius);
  double get lgRadius => sp(_radii.lgRadius);
  double get xlRadius => sp(_radii.xlRadius);
  double get xxlRadius => sp(_radii.xxlRadius);
  double get xxxlRadius => sp(_radii.xxxlRadius);
  double get buttonRadius => sp(_radii.buttonRadius);

  double get smButtonHeight => sp(_btnSizes.smButtonHeight);
  double get mdButtonHeight => sp(_btnSizes.mdButtonHeight);
  double get lgButtonHeight => sp(_btnSizes.lgButtonHeight);

  copyText(String? value) {
    if (!value.hasValue) return;
    Clipboard.setData(ClipboardData(text: value!));
    showNotification(LocaleKeys.copiedTextToClipboard.tr({"text": value}));
  }

  BorderRadius get zeroBorderRadius {
    return BorderRadius.zero;
  }

  BorderRadius get xxsBorderRadius {
    return BorderRadius.circular(xxsRadius);
  }

  BorderRadius get xsBorderRadius {
    return BorderRadius.circular(xsRadius);
  }

  BorderRadius get smBorderRadius {
    return BorderRadius.circular(smRadius);
  }

  BorderRadius get mdBorderRadius {
    return BorderRadius.circular(mdRadius);
  }

  BorderRadius get lgBorderRadius {
    return BorderRadius.circular(lgRadius);
  }

  BorderRadius get xlBorderRadius {
    return BorderRadius.circular(xlRadius);
  }

  BorderRadius get xxlBorderRadius {
    return BorderRadius.circular(xxlRadius);
  }

  BorderRadius get xxxlBorderRadius {
    return BorderRadius.circular(xxxlRadius);
  }

  BorderRadius get btnBorderRadius {
    return BorderRadius.circular(buttonRadius);
  }

  Size get size => _scaler.size;

  Offset get offset => _scaler.position;

  resetFocus() => requestFocus(FocusNode());

  requestFocus(FocusNode node) {
    FocusScope.of(this).requestFocus(node);
  }

  ScreenType get screenType => ScreenType(this);

  bool get isMobile => screenType.isMobile;

  bool get isTab => screenType.isTablet;

  bool get isDesktop => screenType.isLaptop;

  bool get isMonitor => screenType.isMonitor;

  TargetPlatform get _platform {
    return Theme.of(this).platform;
  }

  bool get isIos => _platform == TargetPlatform.iOS;

  bool get isAndroid => _platform == TargetPlatform.android;

  bool get isFucshia => _platform == TargetPlatform.fuchsia;

  bool get isLinux => _platform == TargetPlatform.linux;

  bool get isWindows => _platform == TargetPlatform.windows;

  bool get isMacos => _platform == TargetPlatform.macOS;

  updateLocale(Locale locale) {
    AppTextFormatter.locale = locale;
    setLocale(locale);
  }

  AppBrowser get browser {
    return locator<AppBrowser>();
  }

  Locale get currentLocale {
    return locale;
  }

  String get currentLocaleString {
    return "$locale";
  }

  List<Locale> get locales {
    return supportedLocales;
  }

  List<SelectionData<Locale>> get localeSelections {
    final supportedLocales = locales;
    return List.generate(
      supportedLocales.length,
      (index) {
        final locale = supportedLocales[index];
        return SelectionData(
          selection: locale,
          label: locale.languageName,
        );
      },
    );
  }

  List<SelectionData<AppSelectionResponse>> get extendedLocaleSelections {
    final supportedLocales = locales;
    return List.generate(
      supportedLocales.length,
      (index) {
        final locale = supportedLocales[index];
        return SelectionData(
          selection: AppSelectionResponse(
            id: locale.languageCode,
            name: locale.languageName,
          ),
          label: locale.languageName,
        );
      },
    );
  }

  launchUrl(String url) {
    try {
      browser.openUrl(url);
    } catch (e, t) {
      AppLogger.severe("$e", error: e, stackTrace: t);
    }
  }

  bool validateForm(
    GlobalKey<FormState> formKey, {
    bool notifyOnFailure = false,
  }) {
    final isValid = (formKey.currentState?.validate() ?? false);
    if (notifyOnFailure && !isValid) {
      showWarningNotification(LocaleKeys.formInvalid.tr());
    }
    return isValid;
  }

  showInfoNotification(
    String message, {
    int duration = 5000,
    String? title,
  }) {
    notifications.show(
      message,
      title: title,
      type: NotificationType.info,
      duration: duration,
    );
  }

  showWarningNotification(
    String message, {
    int duration = 5000,
    String? title,
  }) {
    notifications.show(
      message,
      title: title,
      type: NotificationType.warning,
      duration: duration,
    );
  }

  showErrorNotification(
    String message, {
    int duration = 5000,
    String? title,
  }) {
    notifications.show(
      message,
      title: title,
      type: NotificationType.error,
      duration: duration,
    );
  }

  showNotification(
    String message, {
    int duration = 5000,
    String? title,
  }) {
    notifications.show(
      message,
      title: title,
      duration: duration,
    );
  }

  shareText(String text, {String? title}) {
    AppSharePlugin.shareText(this, text: text, title: title);
  }

  shareFile(List<File> files, {String? title, String? text}) {
    AppSharePlugin.shareFile(this, files: files, text: text, title: title);
  }

  AppNotficationOverlay get notifications => AppNotficationOverlay.of(this);
  AppTooltipOverlay get tooltips => AppTooltipOverlay.of(this);

  makeVisible() {
    Scrollable.ensureVisible(
      this,
      alignment: .5,
      duration: 300.milliDuration,
      curve: Curves.decelerate,
    );
  }
}
