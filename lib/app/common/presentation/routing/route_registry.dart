import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

abstract class RouteRegistry {
  // Map<String, Widget Function(BuildContext)> get staticRoutes;
  Route<dynamic> dynamicRoutes(RouteSettings settings, Route fallbackRoute);
}

abstract class RootRouteRegistry with AppAnalyticsMixin {
  AppSessionService get sessionService;
  Map<String, Widget Function(BuildContext)> get staticRoutes;
  Route<dynamic>? dynamicRoutes(RouteSettings settings);
  List<Route<dynamic>> initialRoutes(String path);
}

mixin RootRouteRegistryMixin on RootRouteRegistry {
  logNavigation(RouteSettings? settings) {
    if (settings == null || settings.name == null) return;
    final route = settings.name;
    AppLogger.info("NAVIGATING TO -> $route");
  }

  registerRoute(String route) {
    if (!route.hasValue) return;
    if (route != AppRouter.currentRoute) {
      AppRouter.setCurrentRoute(route);
      trackNavigation(route);
      WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
        AppOverlay.closeCurrentOverlays();
      });
    }
  }

  bool canActivateRoute(RouteSettings? settings) {
    final route = settings?.name;
    final session = sessionService;
    final isLoggedIn = session.isLoggedIn;
    final routes = config.unguardedRoutes;
    final isGuardedRoute = !routes.contains(route);
    if (!isLoggedIn && isGuardedRoute) return false;

    return true;
  }
}
