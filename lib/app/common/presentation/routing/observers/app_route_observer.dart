import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class AppRouteObserver extends RouteObserver<PageRoute<dynamic>>
    with AppAnalyticsMixin {
  _logNavigation() {
    AppOverlay.closeCurrentOverlays();
    final route = AppRouter.currentRoute;
    if (!route.hasValue) return;
    AppLogger.info("NAVIGATING BACK TO: $route");
    trackNavigation(route!);
  }

  @override
  void didPop(Route<dynamic> route, Route<dynamic>? previousRoute) {
    super.didPop(route, previousRoute);
    AppRouter.removeLastRoute();
    _logNavigation();
  }
}
