import 'package:day1/core/core.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class AppRouter {
  AppRouter._();

  static final List<String> _routeStack = [];

  static GlobalKey<NavigatorState> navigatorKey = GlobalKey();

  static String? get currentRoute => _routeStack.tryLast;

  static bool get hasPreviousRoute => _routeStack.length >= 2;

  static setCurrentRoute(String route) {
    _routeStack.add(route);
  }

  static openedModal([String? title]) {
    AppLogger.info("OPENING A MODAL IN: $currentRoute");
    _routeStack.add(
      "${title.hasValue ? "$title->" : ''}$currentRoute?withmodal=true",
    );
  }

  static removeLastRoute() {
    if (_routeStack.isEmpty) return;
    _routeStack.removeLast();
  }

  static NavigatorState? _navigator(BuildContext? context) {
    return context == null ? navigatorKey.currentState : Navigator.of(context);
  }

  static bool canPop([BuildContext? context]) {
    // if (!hasPreviousRoute) return false;
    if (context == null) {
      return navigatorKey.currentState?.canPop() ?? false;
    }
    return _navigator(context)?.canPop() ?? false;
  }

  static void closeApp() {
    SystemNavigator.pop(animated: true);
  }

  static void popToFirst({BuildContext? context}) {
    _navigator(context)?.popUntil((route) => route.isFirst);
  }

  static void popUntil(String routeName, {BuildContext? context}) {
    _navigator(context)?.popUntil(
      (route) => route.settings.name == routeName,
    );
  }

  static void popView({BuildContext? context, Object? result}) {
    _navigator(context)?.maybePop(result);
  }

  static void forcePopView({BuildContext? context, Object? result}) {
    _navigator(context)?.pop(result);
  }

  static pushReplacementNamed(
    String routeName, {
    BuildContext? context,
    Object? arguments,
  }) {
    _navigator(context)?.pushReplacementNamed(
      routeName,
      arguments: arguments,
    );
  }

  static pushNamed(
    String routeName, {
    BuildContext? context,
    Object? arguments,
  }) {
    _navigator(context)?.pushNamed(
      routeName,
      arguments: arguments,
    );
  }

  static popAndPushNamed(
    String routeName, {
    BuildContext? context,
    Object? arguments,
  }) {
    _navigator(context)?.popAndPushNamed(
      routeName,
      arguments: arguments,
    );
  }

  static pushAndRemoveUntil(
    String routeName, {
    Object? arguments,
    String? removeUntilRoute,
    BuildContext? context,
  }) {
    _navigator(context)?.pushNamedAndRemoveUntil(
      routeName,
      ModalRoute.withName(removeUntilRoute ?? routeName),
      arguments: arguments,
    );
  }
}
