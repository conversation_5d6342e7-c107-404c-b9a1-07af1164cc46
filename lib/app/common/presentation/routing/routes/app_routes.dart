import 'package:flutter/material.dart';
import 'package:day1/day1.dart';

class AppRoutes extends RootRouteRegistry with RootRouteRegistryMixin {
  static const Widget launchScreen = LaunchScreen();

  static const _authRoutes = AuthRoutes();
  static const _onboardingRoutes = OnboardingRoutes();
  static const _signupRoutes = SignupRoutes();
  static const _dashRoutes = DashboardRoutes();
  static const _kycRoutes = KycRoutes();
  static const _accountRoutes = AccountRoutes();
  static const _paymentRoutes = PaymentRoutes();
  static const _settingsRoutes = SettingsRoutes();
  static const _cardRoutes = PhysicalcardRoutes();
  static const _transRoutes = TransactionsRoutes();
  static const _regexRoutes = RegexRoutes();

  static const launcher = "/";
  static const forbidden = "/forbidden";
  static const androidDeeplinkBasePath = "/deeplink";

  @override
  Map<String, Widget Function(BuildContext)> staticRoutes = {
    launcher: (context) => launchScreen
  };

  @override
  Route<dynamic>? dynamicRoutes(RouteSettings settings) {
    logNavigation(settings);

    String routeName = settings.name ?? '';

    if (routeName.startsWith(androidDeeplinkBasePath)) {
      routeName = routeName.replaceAll(androidDeeplinkBasePath, "");
    }

    final fallbackRoute = MaterialPageRoute(
      builder: (context) => NotFoundScreen(destination: routeName),
    );
    final forbiddenRoute = MaterialPageRoute(
      builder: (context) {
        context.showWarningNotification(
          LocaleKeys.loginToViewPage.tr({'page': routeName}),
        );
        return NotFoundScreen(destination: settings.name.value);
      },
    );

    if (!canActivateRoute(settings)) {
      registerRoute(forbidden);
      return forbiddenRoute;
    }

    registerRoute(routeName);

    if (routeName.startsWith(AuthRoutes.basePath)) {
      return _authRoutes.dynamicRoutes(settings, fallbackRoute);
    }

    if (routeName.startsWith(OnboardingRoutes.basePath)) {
      return _onboardingRoutes.dynamicRoutes(settings, fallbackRoute);
    }

    if (routeName.startsWith(SignupRoutes.basePath)) {
      return _signupRoutes.dynamicRoutes(settings, fallbackRoute);
    }

    if (routeName.startsWith(DashboardRoutes.basePath)) {
      return _dashRoutes.dynamicRoutes(settings, fallbackRoute);
    }

    if (routeName.startsWith(KycRoutes.basePath)) {
      return _kycRoutes.dynamicRoutes(settings, fallbackRoute);
    }

    if (routeName.startsWith(AccountRoutes.basePath)) {
      return _accountRoutes.dynamicRoutes(settings, fallbackRoute);
    }

    if (routeName.startsWith(PaymentRoutes.basePath)) {
      return _paymentRoutes.dynamicRoutes(settings, fallbackRoute);
    }

    if (routeName.startsWith(SettingsRoutes.basePath)) {
      return _settingsRoutes.dynamicRoutes(settings, fallbackRoute);
    }

    if (routeName.startsWith(PhysicalcardRoutes.basePath)) {
      return _cardRoutes.dynamicRoutes(settings, fallbackRoute);
    }

    if (routeName.startsWith(TransactionsRoutes.basePath)) {
      return _transRoutes.dynamicRoutes(settings, fallbackRoute);
    }

    return _regexRoutes.dynamicRoutes(settings, fallbackRoute);
  }

  @override
  List<Route> initialRoutes(String path) {
    return [MaterialPageRoute(builder: (context) => launchScreen)];
  }

  @override
  AppSessionService get sessionService => locator();
}
