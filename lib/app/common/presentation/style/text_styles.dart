import 'package:day1/day1.dart';
import 'package:flutter/widgets.dart';

class DoTextStyles implements AppTextStyles {
  final BuildContext context;
  late final ScreenType screenType;

  DoTextStyles(this.context) {
    screenType = context.screenType;
  }

  // ignore: unused_element
  int get _screenTypeIndex {
    if (screenType.isMonitor) return 3;
    if (screenType.isLaptop) return 2;
    if (screenType.isTablet) return 1;
    return 0;
  }

  @override
  TextStyle d0({
    TextDecoration? decoration,
    Color? color,
  }) {
    return TextStyle(
      fontFamily: config.fonts.secondaryFontFamily,
      fontWeight: FontWeight.w600,
      fontSize: 64,
      decoration: decoration,
      height: 0.02,
      letterSpacing: -3.20,
      color: color ?? context.textColor,
    );
  }

  @override
  TextStyle d1({
    TextDecoration? decoration,
    Color? color,
  }) {
    return TextStyle(
      fontFamily: config.fonts.secondaryFontFamily,
      fontWeight: FontWeight.w600,
      fontSize: 56,
      decoration: decoration,
      height: 1.2,
      letterSpacing: -3.20,
      color: color ?? context.textColor,
    );
  }

  @override
  TextStyle d2({
    TextDecoration? decoration,
    FontWeight? weight,
    Color? color,
  }) {
    return TextStyle(
      fontFamily: config.fonts.secondaryFontFamily,
      fontWeight: FontWeight.w600,
      fontSize: 52,
      decoration: decoration,
      height: 1,
      letterSpacing: -1.04,
      color: color ?? context.textColor,
    );
  }

  @override
  TextStyle d3({
    TextDecoration? decoration,
    FontWeight? weight,
    Color? color,
  }) {
    return TextStyle(
      fontFamily: config.fonts.secondaryFontFamily,
      fontWeight: FontWeight.w600,
      fontSize: 48,
      decoration: decoration,
      height: 1.2,
      letterSpacing: -1.04,
      color: color ?? context.textColor,
    );
  }

  @override
  TextStyle d4({
    TextDecoration? decoration,
    FontWeight? weight,
    Color? color,
  }) {
    return TextStyle(
      fontFamily: config.fonts.secondaryFontFamily,
      fontWeight: FontWeight.w600,
      fontSize: 40,
      decoration: decoration,
      height: 1.2,
      letterSpacing: -1.04,
      color: color ?? context.textColor,
    );
  }

  @override
  TextStyle b5({
    TextDecoration? decoration,
    FontWeight? weight,
    Color? color,
  }) {
    return TextStyle(
      fontFamily: context.font,
      fontWeight: weight,
      fontSize: 10,
      decoration: decoration,
      height: 1.5,
      letterSpacing: -0.20,
      color: color ?? context.textColor,
    );
  }

  @override
  TextStyle b4({
    TextDecoration? decoration,
    FontWeight? weight,
    Color? color,
  }) {
    return TextStyle(
      fontFamily: context.font,
      fontWeight: weight,
      fontSize: 12,
      decoration: decoration,
      height: 1.5,
      letterSpacing: -0.24,
      color: color ?? context.textColor,
    );
  }

  @override
  TextStyle b3({
    TextDecoration? decoration,
    FontWeight? weight,
    Color? color,
  }) {
    return TextStyle(
      fontFamily: context.font,
      fontWeight: weight,
      fontSize: 14,
      decoration: decoration,
      height: 1.5,
      letterSpacing: -0.35,
      color: color ?? context.textColor,
    );
  }

  @override
  TextStyle b2({
    TextDecoration? decoration,
    FontWeight? weight,
    Color? color,
  }) {
    return TextStyle(
      fontFamily: context.font,
      fontWeight: weight,
      fontSize: 16,
      decoration: decoration,
      height: 1.5,
      letterSpacing: -0.40,
      color: color ?? context.textColor,
    );
  }

  @override
  TextStyle b1({
    TextDecoration? decoration,
    FontWeight? weight,
    Color? color,
  }) {
    return TextStyle(
      fontFamily: context.font,
      fontWeight: weight,
      fontSize: 18,
      decoration: decoration,
      height: 1.5,
      letterSpacing: -0.63,
      color: color ?? context.textColor,
    );
  }

  @override
  TextStyle h6({TextDecoration? decoration, Color? color}) {
    return TextStyle(
      fontFamily: context.font,
      fontWeight: FontWeight.w500,
      fontSize: 18,
      height: 1.4,
      letterSpacing: -0.63,
      decoration: decoration,
      color: color ?? context.textColor,
    );
  }

  @override
  TextStyle h5({TextDecoration? decoration, Color? color}) {
    return TextStyle(
      fontFamily: context.font,
      fontWeight: FontWeight.w500,
      fontSize: 20,
      height: 1.4,
      letterSpacing: -0.80,
      decoration: decoration,
      color: color ?? context.textColor,
    );
  }

  @override
  TextStyle h4({TextDecoration? decoration, Color? color}) {
    return TextStyle(
      fontFamily: context.font,
      fontWeight: FontWeight.w500,
      fontSize: 24,
      height: 1.4,
      letterSpacing: -0.96,
      decoration: decoration,
      color: color ?? context.textColor,
    );
  }

  @override
  TextStyle h3({TextDecoration? decoration, Color? color}) {
    return TextStyle(
      fontFamily: context.font,
      fontWeight: FontWeight.w500,
      fontSize: 32,
      height: 1.2,
      letterSpacing: -1.44,
      decoration: decoration,
      color: color ?? context.textColor,
    );
  }

  @override
  TextStyle h2({TextDecoration? decoration, Color? color}) {
    return TextStyle(
      fontFamily: context.font,
      fontWeight: FontWeight.w500,
      fontSize: 36,
      height: 1.2,
      letterSpacing: -1.62,
      decoration: decoration,
      color: color ?? context.textColor,
    );
  }

  @override
  TextStyle h1({TextDecoration? decoration, Color? color}) {
    return TextStyle(
      fontFamily: context.font,
      fontWeight: FontWeight.w500,
      fontSize: 40,
      height: 1.2,
      letterSpacing: -1.80,
      decoration: decoration,
      color: color ?? context.textColor,
    );
  }

  @override
  TextStyle hintText({
    TextDecoration? decoration,
    FontWeight? weight,
  }) {
    return b3(
      decoration: decoration,
      weight: weight,
      color: context.hintTextColor,
    );
  }

  @override
  TextStyle labelText({TextDecoration? decoration, FontWeight? weight}) {
    return b3(
      decoration: decoration,
      weight: weight,
      color: context.secondaryTextColor,
    );
  }

  @override
  TextStyle helperText({TextDecoration? decoration, FontWeight? weight}) {
    return b4(
      decoration: decoration,
      weight: weight,
      color: context.secondaryTextColor,
    );
  }

  @override
  TextStyle errorHelperText({TextDecoration? decoration, FontWeight? weight}) {
    return b4(
      decoration: decoration,
      weight: weight ?? FontWeight.w600,
      color: context.errorTextColor.withOpacity(.8),
    );
  }

  @override
  TextStyle raisedBtn() {
    return b2(weight: FontWeight.w600).copyWith(
      color: context.raisedBtnTextColor,
    );
  }

  @override
  TextStyle raisedBtnMd() {
    return b3(weight: FontWeight.w600).copyWith(
      color: context.raisedBtnTextColor,
    );
  }

  @override
  TextStyle raisedBtnSm() {
    return b4(weight: FontWeight.w600).copyWith(
      color: context.raisedBtnTextColor,
    );
  }

  @override
  TextStyle textBtn({
    TextDecoration? decoration,
    double? height,
    FontWeight? weight,
  }) {
    return b2(weight: FontWeight.w600).copyWith(
      color: context.textBtnTextColor,
      decorationColor: context.textBtnTextColor,
      height: 1,
    );
  }

  @override
  TextStyle textBtnMd({
    TextDecoration? decoration,
    double? height,
    FontWeight? weight,
  }) {
    return b3(weight: FontWeight.w600).copyWith(
      color: context.textBtnTextColor,
      decorationColor: context.textBtnTextColor,
      height: 1,
    );
  }

  @override
  TextStyle textBtnSm({
    TextDecoration? decoration,
    double? height,
    FontWeight? weight,
  }) {
    return b4(weight: FontWeight.w600).copyWith(
      color: context.textBtnTextColor,
      decorationColor: context.textBtnTextColor,
      height: 1,
    );
  }
}
