import 'package:flutter/material.dart';
import 'package:day1/day1.dart';

class AppColors {
  static const primary = MaterialColor(
    0xFFFFFFFF,
    <int, Color>{
      50: Color.fromRGBO(8, 59, 47, 0.1),
      100: Color.fromRGBO(8, 59, 47, 0.2),
      200: Color.fromRGBO(8, 59, 47, 0.3),
      300: Color.fromRGBO(8, 59, 47, 0.4),
      400: Color.fromRGBO(8, 59, 47, 0.5),
      500: Color.fromRGBO(8, 59, 47, 0.6),
      600: Color.fromRGBO(8, 59, 47, 0.7),
      700: Color.fromRGBO(8, 59, 47, 0.8),
      800: Color.fromRGBO(8, 59, 47, 0.9),
      900: Color.fromRGBO(8, 59, 47, 1),
    },
  );

  static const secondary = MaterialColor(
    0xFF051C3F,
    <int, Color>{
      50: Color.fromRGBO(40, 40, 40, 0.1),
      100: Color.fromRGBO(40, 40, 40, 0.2),
      200: Color.fromRGBO(40, 40, 40, 0.3),
      300: Color.fromRGBO(40, 40, 40, 0.4),
      400: Color.fromRGBO(40, 40, 40, 0.5),
      500: Color.fromRGBO(40, 40, 40, 0.6),
      600: Color.fromRGBO(40, 40, 40, 0.7),
      700: Color.fromRGBO(40, 40, 40, 0.8),
      800: Color.fromRGBO(40, 40, 40, 0.9),
      900: Color.fromRGBO(40, 40, 40, 1),
    },
  );

  /// GENERAL
  static const transparent = ColorSet(0x00FFFFFF);
  static const white = ColorSet(0xFFFFFFFF);
  static const black = ColorSet(0xFF000000);
  static const error = ColorSet(0xFFFF6565);
  static const warning = ColorSet(0xFFFFD143);
  static const disableBtnText = ColorSet(0xFF4F4F4F);
  static const disableBtn = ColorSet(0xFFDEDEDE);

  /// SEMANTIC
  static const info = ColorSet(0xFF53D5FF);
  static const success = ColorSet(0xFF76F057);
  static const text = ColorSet(0xFF282828);
  static const textNeutral05 = ColorSet(0xFFFAFAFA);

  /// SURFACE
  static const surfacePrimary100 = ColorSet(0xFF456C63);
  static const surfacePrimary120 = ColorSet(0xFF083B2F);
  static const surfaceSecondary120 = ColorSet(0xFFD7F65E);
  static const surfaceSecondary100 = ColorSet(0xFFEEEEEE);
  static const surfaceTertiary2 = ColorSet(0xFFFDC8BF);
  static const surfaceTertiary3 = ColorSet(0xFFBEFBA7);
  static const surfaceTertiary4 = ColorSet(0xFFBEE7FF);

  /// SECONDARY
  static const secondary6 = ColorSet(0xFF532D36);
  static const secondary7 = ColorSet(0xFFFCDAB0);
  static const secondary8 = ColorSet(0xFF6D1214);
  static const secondaryDarkBlue = ColorSet(0xFF061C6B);
  static const secondaryLightBlue = ColorSet(0xFFC1E6FC);
  static const secondaryDarkGreen = ColorSet(0xFF1F3F47);
  static const secondaryLightGreen = ColorSet(0xFFE9FEE2);

  /// SECONDARY
  static const tertiary8 = ColorSet(0xFF1E70E6);

  /// NEUTRAL
  static const neutral6 = ColorSet(0xFF9F9F9F);
  static const neutral8 = ColorSet(0xFF505050);
  static const neutral20 = ColorSet(0xFF1A1C1F);

  /// GREENS
  static const green80 = ColorSet(0xFF657671);
  static const green100 = ColorSet(0xFF465A54);
  static const green120 = ColorSet(0xFF252F2C);

  /// REDS
  static const red10 = ColorSet(0xFFFFF3F2);
  static const red40 = ColorSet(0xFFFFC3BF);
  static const red100 = ColorSet(0xFFFF6A5F);
  static const red120 = ColorSet(0xFFEB5B52);

  /// BLUES
  static const blue80 = ColorSet(0xFF5370A1);
  static const blue100 = ColorSet(0xFFFAFAF9);
  static const blue120 = ColorSet(0xFF1B2940);

  /// LEMONS
  static const lemon10 = ColorSet(0xFFFAFEF2);
  static const lemon20 = ColorSet(0xFFFBFEEF);
  static const lemon30 = ColorSet(0xFFF1FCC9);
  static const lemon40 = ColorSet(0xFFE5FBBF);
  static const lemon100 = ColorSet(0xFFB1E158);
  static const lemon120 = ColorSet(0xFFA0D342);
  static const lemon140 = ColorSet(0xFF85E706);

  /// GREYS
  static const grey40 = ColorSet(0xFFE8E8E8);
  static const grey80 = ColorSet(0xFF5E6B80);
  static const grey100 = ColorSet(0xFF4C5A6F);
  static const grey120 = ColorSet(0xFF262D37);

  /// FUSCHIAS
  static const fuschia10 = ColorSet(0xFFFFF5FB);
  static const fuschia40 = ColorSet(0xFFFED6EF);
  static const fuschia100 = ColorSet(0xFFEA81C2);
  static const fuschia120 = ColorSet(0xFFDC70B3);

  /// INDIGO
  static const indigo80 = ColorSet(0xFF4A5FD5);
  static const indigo100 = ColorSet(0xFF2742DA);
  static const indigo120 = ColorSet(0xFF21253D);

  /// BROWNS
  static const brown40 = ColorSet(0xFFFCFAF8);
  static const brown10 = ColorSet(0xFFF4EAE3);
  static const brown100 = ColorSet(0xFFD6B7A1);
  static const brown120 = ColorSet(0xFFCCA78F);
}

class DoGradients implements AppGradients {
  final BuildContext context;

  DoGradients(this.context);

  @override
  LinearGradient get referralGradient {
    return LinearGradient(
      begin: Alignment.bottomCenter,
      end: Alignment.topCenter,
      stops: const [.395, .998],
      colors: [
        context.raisedBtnBgColor.withOpacity(0),
        context.raisedBtnBgColor
      ],
    );
  }

  @override
  LinearGradient get supportGradient {
    return LinearGradient(
      begin: const Alignment(0.00, -1.00),
      end: const Alignment(0, 1),
      colors: [
        context.raisedBtnBgColor,
        context.raisedBtnBgColor,
        context.cardColor,
        context.cardColor
      ],
    );
  }
}

class DoShadows implements AppShadows {
  final BuildContext context;
  DoShadows(this.context);

  Color get _color => context.shadowColor;

  @override
  List<BoxShadow> get xxSmall {
    return [
      BoxShadow(
        color: _color.withOpacity(.05),
        offset: const Offset(0, 2),
        blurRadius: 2,
        spreadRadius: 0,
      )
    ];
  }

  @override
  List<BoxShadow> get xSmall {
    return [
      BoxShadow(
        color: _color.withOpacity(.02),
        offset: const Offset(0, 2),
        blurRadius: 2,
        spreadRadius: 0,
      ),
      BoxShadow(
        color: _color.withOpacity(.08),
        offset: const Offset(0, 2),
        blurRadius: 4,
        spreadRadius: 0,
      )
    ];
  }

  @override
  List<BoxShadow> get small {
    return [
      BoxShadow(
        color: _color.withOpacity(.06),
        offset: const Offset(0, 2),
        blurRadius: 2,
        spreadRadius: -2,
      ),
      BoxShadow(
        color: _color.withOpacity(.1),
        offset: const Offset(0, 4),
        blurRadius: 8,
        spreadRadius: -2,
      )
    ];
  }

  @override
  List<BoxShadow> get medium {
    return [
      BoxShadow(
        color: _color.withOpacity(.03),
        offset: const Offset(0, 4),
        blurRadius: 2,
        spreadRadius: 0,
      ),
      BoxShadow(
        color: _color.withOpacity(.08),
        offset: const Offset(0, 12),
        blurRadius: 16,
        spreadRadius: -4,
      )
    ];
  }

  @override
  List<BoxShadow> get large {
    return [
      BoxShadow(
        color: _color.withOpacity(.03),
        offset: const Offset(0, 8),
        blurRadius: 8,
        spreadRadius: -4,
      ),
      BoxShadow(
        color: _color.withOpacity(.08),
        offset: const Offset(0, 20),
        blurRadius: 24,
        spreadRadius: -4,
      )
    ];
  }

  @override
  List<BoxShadow> get xLarge {
    return [
      BoxShadow(
        color: _color.withOpacity(.02),
        offset: const Offset(0, 4),
        blurRadius: 8,
        spreadRadius: -12,
      ),
      BoxShadow(
        color: _color.withOpacity(.18),
        offset: const Offset(0, 24),
        blurRadius: 48,
        spreadRadius: -12,
      )
    ];
  }

  @override
  List<BoxShadow> get xxLarge {
    return [
      BoxShadow(
        color: _color.withOpacity(.02),
        offset: const Offset(0, 8),
        blurRadius: 8,
        spreadRadius: -12,
      ),
      BoxShadow(
        color: _color.withOpacity(.18),
        offset: const Offset(0, 32),
        blurRadius: 64,
        spreadRadius: -12,
      )
    ];
  }

  @override
  List<BoxShadow> get bottomNavShaddow {
    return const [
      BoxShadow(
        color: ColorSet(0x05000000),
        blurRadius: 8,
        offset: Offset(0, -8),
        spreadRadius: -12,
      ),
      BoxShadow(
        color: ColorSet(0x2D000000),
        blurRadius: 64,
        offset: Offset(0, -11),
        spreadRadius: -12,
      ),
    ];
  }
}
