import 'package:flutter/material.dart';
import 'package:day1/day1.dart';

class DoInputStyles implements AppInputStyles {
  late final AppFontSizer fontSizer;
  final BuildContext context;

  DoInputStyles(this.context) {
    fontSizer = AppFontSizer(context);
  }

  @override
  InputDecoration outlined(bool disabled) {
    final borderRadius = context.mdBorderRadius;
    final fillColor =
        disabled ? context.disabledInputFillColor : context.inputBgColor;
    final defaultBorderColor = context.inputBorderColor;

    const borderWidth = 2.0;

    return InputDecoration(
      errorMaxLines: 3,
      helperMaxLines: 3,
      hintMaxLines: 10,
      isDense: true,
      filled: true,
      fillColor: fillColor,
      hintStyle: context.textStyle.hintText(),
      helperStyle: context.textStyle.helperText(),
      errorStyle: context.textStyle.errorHelperText(),
      enabledBorder: OutlineInputBorder(
        borderSide: BorderSide(
          color: defaultBorderColor,
          width: borderWidth,
        ),
        borderRadius: borderRadius,
      ),
      errorBorder: OutlineInputBorder(
        borderSide: BorderSide(
          color: context.errorInputBorderColor,
          width: borderWidth,
        ),
        borderRadius: borderRadius,
      ),
      focusedErrorBorder: OutlineInputBorder(
        borderSide: BorderSide(
          color: context.errorInputBorderColor,
          width: borderWidth,
        ),
        borderRadius: borderRadius,
      ),
      disabledBorder: OutlineInputBorder(
        borderSide: BorderSide(
          color: context.disabledInputBorderColor,
          width: borderWidth,
        ),
        borderRadius: borderRadius,
      ),
      focusedBorder: OutlineInputBorder(
        borderSide: BorderSide(
          color: context.activeInputBorderColor,
          width: borderWidth,
        ),
        borderRadius: borderRadius,
      ),
    );
  }

  @override
  InputDecoration get none {
    return const InputDecoration(
      contentPadding: EdgeInsets.zero,
      errorMaxLines: 0,
      helperMaxLines: 0,
      isDense: true,
      filled: false,
      enabledBorder: InputBorder.none,
      errorBorder: InputBorder.none,
      focusedErrorBorder: InputBorder.none,
      disabledBorder: InputBorder.none,
      focusedBorder: InputBorder.none,
      border: InputBorder.none,
    );
  }

  @override
  InputDecoration underlined(bool disabled) {
    return InputDecoration(
      errorMaxLines: 3,
      helperMaxLines: 3,
      hintMaxLines: 10,
      isDense: true,
      filled: false,
      fillColor: AppColors.transparent,
      hintStyle: context.textStyle.hintText(),
      helperStyle: context.textStyle.helperText(),
      enabledBorder: UnderlineInputBorder(
        borderSide: BorderSide(color: context.inputBorderColor, width: 1),
      ),
      errorBorder: UnderlineInputBorder(
        borderSide: BorderSide(color: context.errorInputBorderColor, width: 2),
      ),
      focusedErrorBorder: UnderlineInputBorder(
        borderSide: BorderSide(color: context.errorInputBorderColor, width: 2),
      ),
      disabledBorder: UnderlineInputBorder(
        borderSide: BorderSide(
          color: context.disabledInputBorderColor,
          width: 1,
        ),
      ),
      focusedBorder: UnderlineInputBorder(
        borderSide: BorderSide(
          color: context.activeInputBorderColor,
          width: 2,
        ),
      ),
    );
  }
}
