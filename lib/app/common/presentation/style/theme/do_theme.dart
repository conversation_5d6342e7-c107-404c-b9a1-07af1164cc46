import 'package:flutter/cupertino.dart';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:day1/day1.dart';

class DoTheme implements AppTheme {
  final bool inDarkMode;

  DoTheme(this.inDarkMode);

  @override
  Map<TargetPlatform, PageTransitionsBuilder> get defaultTransitions {
    Map<TargetPlatform, PageTransitionsBuilder> transitionMap = {};
    for (final platform in TargetPlatform.values) {
      transitionMap[platform] = const CupertinoPageTransitionsBuilder();
    }
    return transitionMap;
  }

  @override
  CupertinoThemeData? get darkThemeIos {
    return CupertinoThemeData(
      brightness: Brightness.dark,
      barBackgroundColor: AppColors.black,
      primaryColor: AppColors.surfacePrimary120.dark,
      primaryContrastingColor: AppColors.surfaceSecondary120.dark,
      scaffoldBackgroundColor: AppColors.surfaceSecondary100.dark,
      applyThemeToAll: true,
    );
  }

  @override
  CupertinoThemeData? get lightThemeIos {
    return const CupertinoThemeData(
      brightness: Brightness.light,
      barBackgroundColor: AppColors.white,
      primaryColor: AppColors.surfacePrimary120,
      primaryContrastingColor: AppColors.surfaceSecondary120,
      scaffoldBackgroundColor: AppColors.surfaceSecondary100,
      applyThemeToAll: true,
    );
  }

  @override
  ThemeData get darkTheme {
    final base = ThemeData.dark();
    SystemChrome.setSystemUIOverlayStyle(
      SystemUiOverlayStyle(
        statusBarBrightness: Brightness.dark,
        statusBarIconBrightness: Brightness.light,
        systemStatusBarContrastEnforced: true,
        statusBarColor: AppColors.surfaceSecondary100.dark,
        systemNavigationBarColor: AppColors.white.dark,
      ),
    );
    return base.copyWith(
      splashColor: AppColors.transparent,
      pageTransitionsTheme: PageTransitionsTheme(builders: defaultTransitions),
      visualDensity: VisualDensity.adaptivePlatformDensity,
      splashFactory: NoSplash.splashFactory,
      brightness: Brightness.dark,
      colorScheme: base.colorScheme.copyWith(
        surface: AppColors.surfaceSecondary100.dark,
        error: AppColors.error.dark,
        brightness: Brightness.dark,
      ),
      tabBarTheme: base.tabBarTheme.copyWith(
        unselectedLabelColor: AppColors.text,
        labelColor: AppColors.surfacePrimary120.dark,
        indicatorColor: AppColors.surfacePrimary120.dark,
        splashFactory: NoSplash.splashFactory,
        indicatorSize: TabBarIndicatorSize.tab,
        overlayColor:
            WidgetStatePropertyAll(AppColors.surfaceSecondary120.dark),
        dividerColor: AppColors.disableBtn.dark,
        indicator: UnderlineTabIndicator(
          borderRadius: BorderRadius.zero,
          borderSide: BorderSide(
            color: AppColors.surfacePrimary120.dark,
            width: 1,
          ),
        ),
      ),
      scaffoldBackgroundColor: AppColors.surfaceSecondary100.dark,
      primaryColor: AppColors.surfacePrimary120.dark,
      appBarTheme: AppBarTheme(
        systemOverlayStyle: SystemUiOverlayStyle.light,
        iconTheme: IconThemeData(color: AppColors.text.dark),
        backgroundColor: AppColors.transparent,
      ),
      iconTheme: base.iconTheme.copyWith(color: AppColors.text),
      cardTheme: base.cardTheme.copyWith(
        color: AppColors.surfaceSecondary120.dark,
        elevation: 5,
      ),
      bottomAppBarTheme: base.bottomAppBarTheme.copyWith(
        elevation: 0,
        color: AppColors.surfaceSecondary100.dark,
      ),
      bottomNavigationBarTheme: base.bottomNavigationBarTheme.copyWith(
        backgroundColor: AppColors.transparent,
        elevation: 0,
      ),
      bottomSheetTheme: base.bottomSheetTheme.copyWith(
        backgroundColor: AppColors.transparent,
        elevation: 0,
      ),
      buttonTheme: base.buttonTheme.copyWith(
        buttonColor: AppColors.surfaceSecondary120.dark,
        disabledColor: AppColors.disableBtn.dark,
        textTheme: ButtonTextTheme.normal,
      ),
      cardColor: AppColors.surfaceSecondary120.dark,
      textTheme: base.textTheme.apply(
        displayColor: AppColors.text.dark,
        bodyColor: AppColors.text.dark,
        fontFamily: config.fonts.primaryFontFamily,
      ),
      indicatorColor: AppColors.surfaceSecondary120.dark,
      textSelectionTheme: base.textSelectionTheme.copyWith(
        selectionColor: AppColors.surfaceSecondary120.dark.withOpacity(.4),
        selectionHandleColor: AppColors.surfaceSecondary120.dark,
        cursorColor: AppColors.surfacePrimary120,
      ),
      inputDecorationTheme: InputDecorationTheme(
        errorMaxLines: 3,
        helperMaxLines: 3,
        isDense: true,
        filled: true,
        suffixStyle: TextStyle(color: AppColors.text.dark),
        prefixStyle: TextStyle(color: AppColors.text.dark),
        labelStyle: TextStyle(color: AppColors.text.dark),
        helperStyle: TextStyle(color: AppColors.text.dark),
        hintStyle: TextStyle(color: AppColors.text.dark),
      ),
    );
  }

  @override
  ThemeData get lightTheme {
    final base = ThemeData.light();
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarBrightness: Brightness.light,
        statusBarIconBrightness: Brightness.dark,
        systemStatusBarContrastEnforced: true,
        statusBarColor: AppColors.surfaceSecondary100,
        systemNavigationBarColor: AppColors.white,
        systemNavigationBarIconBrightness: Brightness.dark,
      ),
    );
    return base.copyWith(
      splashColor: AppColors.transparent,
      pageTransitionsTheme: PageTransitionsTheme(builders: defaultTransitions),
      visualDensity: VisualDensity.adaptivePlatformDensity,
      splashFactory: NoSplash.splashFactory,
      brightness: Brightness.light,
      colorScheme: base.colorScheme.copyWith(
        surface: AppColors.surfaceSecondary100,
        error: AppColors.error,
        brightness: Brightness.light,
      ),
      tabBarTheme: base.tabBarTheme.copyWith(
        unselectedLabelColor: AppColors.text,
        labelColor: AppColors.surfacePrimary120,
        indicatorColor: AppColors.surfacePrimary120,
        splashFactory: NoSplash.splashFactory,
        indicatorSize: TabBarIndicatorSize.tab,
        overlayColor: const WidgetStatePropertyAll(
          AppColors.surfaceSecondary120,
        ),
        dividerColor: AppColors.disableBtn,
        indicator: const UnderlineTabIndicator(
          borderRadius: BorderRadius.zero,
          borderSide: BorderSide(
            color: AppColors.surfacePrimary120,
            width: 1,
          ),
        ),
      ),
      scaffoldBackgroundColor: AppColors.surfaceSecondary100,
      primaryColor: AppColors.surfacePrimary120,
      appBarTheme: const AppBarTheme(
        systemOverlayStyle: SystemUiOverlayStyle.light,
        iconTheme: IconThemeData(color: AppColors.text),
        backgroundColor: AppColors.transparent,
      ),
      iconTheme: base.iconTheme.copyWith(color: AppColors.text),
      cardTheme: base.cardTheme.copyWith(
        color: AppColors.surfaceSecondary120,
        elevation: 5,
      ),
      bottomAppBarTheme: base.bottomAppBarTheme.copyWith(
        elevation: 0,
        color: AppColors.transparent,
      ),
      bottomNavigationBarTheme: base.bottomNavigationBarTheme.copyWith(
        backgroundColor: AppColors.transparent,
        elevation: 0,
      ),
      bottomSheetTheme: base.bottomSheetTheme.copyWith(
        backgroundColor: AppColors.transparent,
        elevation: 0,
      ),
      buttonTheme: base.buttonTheme.copyWith(
        buttonColor: AppColors.surfaceSecondary120,
        disabledColor: AppColors.disableBtn,
        textTheme: ButtonTextTheme.normal,
      ),
      cardColor: AppColors.surfaceSecondary120,
      textTheme: base.textTheme.apply(
        displayColor: AppColors.text,
        bodyColor: AppColors.text,
        fontFamily: config.fonts.primaryFontFamily,
      ),
      indicatorColor: AppColors.surfaceSecondary120,
      textSelectionTheme: base.textSelectionTheme.copyWith(
        selectionColor: AppColors.surfaceSecondary120.withOpacity(.4),
        selectionHandleColor: AppColors.surfaceSecondary120,
        cursorColor: AppColors.surfacePrimary120,
      ),
      inputDecorationTheme: const InputDecorationTheme(
        errorMaxLines: 3,
        helperMaxLines: 3,
        isDense: true,
        filled: true,
        suffixStyle: TextStyle(color: AppColors.text),
        prefixStyle: TextStyle(color: AppColors.text),
        labelStyle: TextStyle(color: AppColors.text),
        helperStyle: TextStyle(color: AppColors.text),
        hintStyle: TextStyle(color: AppColors.text),
      ),
    );
  }

  @override
  Color get scaffoldBgColor =>
      AppColors.surfaceSecondary100.forTheme(inDarkMode);

  @override
  Color get shadowColor => const ColorSet(0x05000000).forTheme(inDarkMode);

  @override
  Color get barrierColor => shadowColor.withOpacity(.8);

  @override
  Color get dividerColor => AppColors.disableBtn.forTheme(inDarkMode);

  @override
  Color get switchTrackColor =>
      AppColors.surfacePrimary120.forTheme(inDarkMode);

  @override
  Color get switchTrackInactiveColor => switchTrackColor.withOpacity(.25);

  @override
  Color get switchThumbInactiveColor => switchThumbActiveColor.withOpacity(.25);

  @override
  Color get switchThumbActiveColor => AppColors.white;

  @override
  Color get inputBgColor => AppColors.surfaceSecondary100.forTheme(inDarkMode);

  @override
  Color get inputBorderColor => AppColors.grey40.forTheme(inDarkMode);

  @override
  Color get disabledInputBorderColor => disabledInputFillColor;

  @override
  Color get activeInputBorderColor => AppColors.lemon140.forTheme(inDarkMode);

  @override
  Color get successCardColor => AppColors.surfaceTertiary3.forTheme(inDarkMode);

  @override
  Color get disabledInputFillColor =>
      AppColors.surfaceSecondary100.forTheme(inDarkMode).withOpacity(.75);

  @override
  Color get textColor => AppColors.text.forTheme(inDarkMode);

  @override
  Color get highlightedTextColor =>
      AppColors.surfacePrimary100.forTheme(inDarkMode);

  @override
  Color get secondaryTextColor => textColor.withOpacity(.75);

  @override
  Color get hintTextColor => AppColors.neutral6.forTheme(inDarkMode);

  @override
  Color get inactiveIndicatorColor => dividerColor;

  @override
  Color get cardColor {
    return AppColors.white.forTheme(inDarkMode);
  }

  @override
  Color get cardBorderColor => AppColors.neutral8.forTheme(inDarkMode);

  @override
  Color get pillTextColor =>
      AppColors.surfacePrimary120.forTheme(inDarkMode).withOpacity(.75);

  @override
  Color get pillColor =>
      AppColors.surfacePrimary120.forTheme(inDarkMode).withOpacity(.75);

  @override
  String get font => config.fonts.primaryFontFamily;

  @override
  Color get errorTextColor => errorColor;

  @override
  Color get activeIndicatorBorderColor => AppColors.text.forTheme(inDarkMode);

  @override
  Color get activeIndicatorColor =>
      AppColors.surfacePrimary120.forTheme(inDarkMode);

  @override
  Color get errorColor {
    return AppColors.error;
  }

  @override
  Color get inActiveIndicatorBorderColor => inputBorderColor;

  @override
  Color get inActiveIndicatorColor => AppColors.transparent;

  @override
  AppInputStyles inputStyle(BuildContext context) => DoInputStyles(context);

  @override
  Color get disabledBtnColor => AppColors.disableBtn.forTheme(inDarkMode);

  @override
  Color get disabledBtntextColor =>
      AppColors.disableBtnText.forTheme(inDarkMode);

  @override
  Color get outlineBtnBorderColor =>
      AppColors.surfacePrimary120.forTheme(inDarkMode);

  @override
  Color get outlineBtnAltBorderColor =>
      AppColors.disableBtn.forTheme(inDarkMode);

  @override
  double get outlineBtnDisabledOpacity => 0.5;

  @override
  double get outlineBtnAltDisabledOpacity => 0.65;

  @override
  Color get outlineBtnBgColor => AppColors.transparent;

  @override
  Color get outlineBtnAltBgColor => AppColors.white.forTheme(inDarkMode);

  @override
  Color get outlineBtnFocusedBgColor => AppColors.white.forTheme(inDarkMode);

  @override
  Color get outlineBtnAltFocusedBgColor =>
      outlineBtnFocusedBgColor.withOpacity(0.29);

  @override
  Color get outlineBtnTextColor => AppColors.text.forTheme(inDarkMode);

  @override
  Color get raisedBtnBorderColor =>
      AppColors.surfacePrimary100.forTheme(inDarkMode).withOpacity(0.19);

  @override
  Color get raisedBtnDisabledBorderColor =>
      AppColors.disableBtn.forTheme(inDarkMode);

  @override
  Color get raisedBtnBgColor =>
      AppColors.surfaceSecondary120.forTheme(inDarkMode);

  @override
  Color get raisedBtnBBgColor =>
      AppColors.surfacePrimary100.forTheme(inDarkMode);

  @override
  Color get raisedBtnTextColor => AppColors.white.forTheme(inDarkMode);

  @override
  Color get raisedBtnCBgColor =>
      AppColors.surfacePrimary120.forTheme(inDarkMode);

  @override
  Color get raisedBtnFocusedBgColor => raisedBtnBgColor.withOpacity(.78);

  @override
  Color get raisedBtnBFocusedBgColor => raisedBtnBBgColor.withOpacity(.72);

  @override
  Color get raisedBtnCFocusedBgColor => raisedBtnCBgColor.withOpacity(.72);

  @override
  double get raisedBtnDisabledOpacity => 0.65;

  @override
  Color get textBtnTextColor => AppColors.text.forTheme(inDarkMode);

  @override
  Color get textBtnFocusedTextColor => textBtnTextColor.withOpacity(.75);

  @override
  Color get textBtnDisabledTextColor => textBtnTextColor.withOpacity(.40);

  @override
  Color get appleBtnTextColor {
    return AppColors.white.forTheme(inDarkMode);
  }

  @override
  Color get neutralTextColor {
    return AppColors.textNeutral05.forTheme(inDarkMode);
  }

  @override
  Color get appleBtnColor {
    return AppColors.black.forTheme(inDarkMode);
  }

  @override
  Color get appleBtnBorderColor => appleBtnColor;

  @override
  Color get googleBtnTextColor {
    return const ColorSet(0xFF1F1F1F).forTheme(inDarkMode);
  }

  @override
  Color get googleBtnColor {
    return AppColors.white.forTheme(inDarkMode);
  }

  @override
  Color get googleBtnBorderColor {
    return const ColorSet(0xFF747775).forTheme(inDarkMode);
  }

  @override
  String get secondaryFont => font;

  @override
  AppShadows shadows(BuildContext context) => DoShadows(context);

  @override
  AppGradients gradients(BuildContext context) => DoGradients(context);

  @override
  AppTextStyles textStyles(BuildContext context) => DoTextStyles(context);

  @override
  Color get transparent => AppColors.transparent;

  @override
  Color get errorInputBorderColor => errorColor;

  @override
  Color get highlightedIconColor =>
      AppColors.surfaceSecondary120.forTheme(inDarkMode);

  @override
  Color get iconColor => textColor;

  @override
  Color get errorNotificationBackgroundColor => AppColors.surfaceTertiary2;

  @override
  Color get infoNotificationBackgroundColor => AppColors.surfaceTertiary4;

  @override
  Color get warningNotificationBackgroundColor => AppColors.warning;

  @override
  Color get notificationBackgroundColor => AppColors.surfaceTertiary3;

  @override
  Color get infoNotificationTextColor => notificationTextColor;

  @override
  Color get warningNotificationTextColor => notificationTextColor;

  @override
  Color get notificationTextColor => AppColors.surfacePrimary120;

  @override
  Color get errorNotificationTextColor => AppColors.error;

  @override
  Color get infoNotificationBorderColor => AppColors.tertiary8;

  @override
  Color get warningNotificationBorderColor =>
      warningNotificationBackgroundColor;

  @override
  Color get notificationBorderColor => activeInputBorderColor;

  @override
  Color get errorNotificationBorderColor => errorNotificationTextColor;
}
