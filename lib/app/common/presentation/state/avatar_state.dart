import 'package:day1/day1.dart';

class AvatarState extends StateModel {
  final AppMeService _meService;

  AvatarState(this._meService);

  AppImageData? get avatar {
    final cachedUser = _meService.cachedMeData;

    final imageUrl = cachedUser?.photo;

    if (!imageUrl.hasValue) return null;

    return AppImageData(
      imageData: imageUrl,
      name: cachedUser?.name ?? "",
    );
  }

  forceRebuild() => notifyListeners();
}
