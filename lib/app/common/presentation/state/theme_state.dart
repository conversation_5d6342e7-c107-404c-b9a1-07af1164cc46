import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class ThemeState extends ChangeNotifier with AppTaskMixin {
  final AppDbClient _dbClient;
  ThemeCache? _mode;

  ThemeState(this._dbClient);

  ThemeCache get mode {
    if (_mode != null) return _mode!;
    final theme = _dbClient.getItem<ThemeCache>(1);
    _mode = theme ?? ThemeCache.light;
    return _mode!;
  }

  bool inDarkMode(BuildContext context) {
    if (mode.isSystem) {
      final brightness = MediaQuery.maybePlatformBrightnessOf(context);
      return brightness == Brightness.dark;
    }
    return mode.isDark;
  }

  switchTheme(ThemeCache mode) async {
    final previousMode = _mode;
    if (mode == previousMode) return;
    await runThrowableTask(
      () async {
        await _dbClient.setItem(mode);
        _mode = mode;
        notifyListeners();
      },
      onError: () {
        _mode = previousMode;
        notifyListeners();
      },
    );
  }
}
