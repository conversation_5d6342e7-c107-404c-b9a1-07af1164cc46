import 'package:day1/day1.dart';
import 'package:flutter/foundation.dart' show ValueNotifier;

class CountryState extends StateModel {
  final ValueNotifier<Future<List<Country>>?> _countriesTask =
      ValueNotifier(null);

  ValueNotifier<Future<List<Country>>?> get countriesTask {
    if (_countriesTask.value == null) {
      retry();
    }
    return _countriesTask;
  }

  void retry({String? filter}) async {
    _countriesTask.value = AppHelpers.fetchCountries();
  }
}
