import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

mixin AppModalMixin {
  showSelectionSheet<T>(
    BuildContext context, {
    required List<SelectionData<T>> options,
    required String title,
    required OnChanged<SelectionData> onSelect,
    TextInputAction action = TextInputAction.search,
    SelectionData<T>? selection,
    bool popOnSelect = true,
    required bool allowSearch,
  }) async {
    AppBottomModal.draggable(
      ctx: context,
      title: title,
      initialChildSize: .8,
      maxChildSize: .9,
      minChildSize: .3,
      builder: (controller) {
        return SelectionSheet(
          key: ValueKey("selection-sheet-${selection?.label}"),
          controller: controller,
          textInputAction: action,
          allowSearch: allowSearch,
          onSelect: onSelect,
          title: title,
          options: options,
          selection: selection,
          popOnSelect: popOnSelect,
        );
      },
    );
  }

  confirmAction(
    BuildContext context, {
    required OnPressed onContinue,
    bool isDismissable = true,
    String? title,
    String? description,
    String? allowText,
    String? canceltext,
  }) async {
    showAdaptiveDialog(
      context: context,
      barrierDismissible: isDismissable,
      builder: (context) {
        return AppConfirmDialog(
          key: ValueKey("confirm-action-$title"),
          title: title ?? LocaleKeys.doYouWishToProceed.tr(),
          description: description?.tr(),
          onContinue: onContinue,
          allowText: allowText,
          canceltext: canceltext,
        );
      },
    );
  }

  showAlert(
    BuildContext context, {
    required OnPressed action,
    bool isDismissable = false,
    required String title,
    String? description,
    String? btnText,
  }) async {
    showAdaptiveDialog(
      context: context,
      barrierDismissible: isDismissable,
      builder: (context) {
        return AppAlertDialog(
          key: ValueKey("alert-for-$title"),
          title: title,
          description: description?.tr(),
          action: action,
          btnText: btnText,
        );
      },
    );
  }

  showKycAlert(BuildContext context) {
    showAlert(
      context,
      action: () {
        AppRouter.pushNamed(KycRoutes.nationality);
      },
      title: LocaleKeys
          .kindly_complete_your_verification_to_perform_transactions
          .tr(),
      description:
          LocaleKeys.your_account_verification_is_still_pending_so_you.tr(),
      btnText: LocaleKeys.finish_account_setup.tr(),
    );
  }
}
