import 'dart:math';

import 'package:day1/app/common/common.dart';
import 'package:flutter/material.dart';

class ProgressPainter extends CustomPainter {
  Color color;
  final double extent;

  ProgressPainter({this.color = AppColors.success, this.extent = 0})
      : assert(extent >= 0 && extent <= 1);

  @override
  void paint(Canvas canvas, Size size) {
    Paint paint = Paint()
      ..color = color
      ..strokeWidth = size.height
      ..style = PaintingStyle.fill;

    canvas.drawPath(_strokeButt(size), paint);
  }

  Path _strokeButt(Size size) {
    final path = Path();
    double x = size.width;
    double y = size.height;
    double widthFraction = (x * extent);
    path.addRRect(
      RRect.fromLTRBAndCorners(
        0,
        0,
        widthFraction,
        y,
        topRight: const Radius.circular(30),
        bottomRight: const Radius.circular(30),
      ),
    );

    return path;
  }

  @override
  bool shouldRepaint(ProgressPainter oldDelegate) {
    return oldDelegate.extent != extent;
  }
}

class HistPainter extends CustomPainter {
  Color color;
  final double extent;

  HistPainter({this.color = AppColors.success, this.extent = 0})
      : assert(extent >= 0),
        assert(extent <= 1);

  @override
  void paint(Canvas canvas, Size size) {
    Paint paint = Paint()
      ..color = color
      ..strokeWidth = size.width
      ..style = PaintingStyle.fill;

    canvas.drawPath(_strokeButt(size), paint);
  }

  Path _strokeButt(Size size) {
    final path = Path();
    double x = size.width;
    double y = size.height;
    double heightFraction = (y * extent);
    path.addRRect(
      RRect.fromLTRBAndCorners(
        0,
        size.height - heightFraction,
        x,
        size.height,
        topRight: Radius.circular(size.width * .2),
        topLeft: Radius.circular(size.width * .2),
      ),
    );

    return path;
  }

  @override
  bool shouldRepaint(HistPainter oldDelegate) {
    return oldDelegate.extent != extent || oldDelegate.color != color;
  }
}

class PiePainter extends CustomPainter {
  PieData data;

  PiePainter({required this.data});

  double get sum => data.sum;

  @override
  void paint(Canvas canvas, Size size) {
    double startRadian = 0;
    final items = data.items;
    final radius =
        (size.height / 2) + ((pow(size.width, 2)) / (8 * size.height));
    final center = Offset(size.width * .5, size.height * .5);
    for (var i = 0; i < items.length; i++) {
      final datum = items[i];
      final paint = Paint()
        ..strokeCap = StrokeCap.butt
        ..color = data.itemColor(datum)
        ..style = PaintingStyle.stroke
        ..isAntiAlias = true
        ..strokeWidth = 26;
      final radian = datum.radian(sum);
      canvas.drawArc(
        Rect.fromCircle(
          center: center,
          radius: radius,
        ),
        startRadian,
        radian,
        false,
        paint,
      );
      startRadian += radian;
    }
  }

  @override
  bool shouldRepaint(PiePainter oldDelegate) {
    return oldDelegate.data != data;
  }
}

class BarChartPainter extends CustomPainter {
  final Color color;
  final double extent;

  BarChartPainter({this.color = AppColors.text, this.extent = 0})
      : assert(extent >= 0),
        assert(extent <= 1);

  @override
  void paint(Canvas canvas, Size size) {
    Paint paint = Paint()
      ..color = color.withOpacity(.06)
      ..strokeWidth = size.width
      ..style = PaintingStyle.fill;
    Paint paintFill = Paint()
      ..color = color
      ..strokeWidth = size.width
      ..style = PaintingStyle.fill;

    canvas.drawPath(_strokeBg(size), paint);
    canvas.drawPath(_strokeButt(size), paintFill);
  }

  Path _strokeButt(Size size) {
    final path = Path();
    double x = size.width;
    double y = size.height;
    double heightFraction = (y * extent);
    path.addRRect(
      RRect.fromLTRBR(
        0,
        size.height - heightFraction,
        x,
        size.height,
        const Radius.circular(48),
      ),
    );

    return path;
  }

  Path _strokeBg(Size size) {
    final path = Path();
    double x = size.width;
    double y = size.height;
    path.addRRect(
      RRect.fromLTRBR(0, 0, x, y, const Radius.circular(48)),
    );

    return path;
  }

  @override
  bool shouldRepaint(BarChartPainter oldDelegate) {
    return oldDelegate.extent != extent || oldDelegate.color != color;
  }
}

class LinePainter extends CustomPainter {
  BarChartData data;
  Color color;
  ScrollController? controller;

  LinePainter({
    required this.data,
    this.color = AppColors.text,
    this.controller,
  });

  double get sum => data.sum;
  double get max => data.max;

  @override
  void paint(Canvas canvas, Size size) {
    final items = data.series;
    final paint = Paint()
      ..strokeCap = StrokeCap.round
      ..color = color
      ..style = PaintingStyle.stroke
      ..isAntiAlias = true
      ..strokeWidth = 3;
    final width = size.width;
    final height = size.height;
    final vw = width / data.series.length;
    final path = Path();
    path.moveTo(0, height);

    for (var i = 0; i < items.length; i++) {
      final datum = items[i];
      final fraction = datum.value / max;
      final x = vw * i;
      final y = height - (height * fraction);

      // if (i == 0) {
      //   path.moveTo(x, y);
      //   continue;
      // }
      // final previous = items[i-1];
      // final pfrac = previous.value / max;
      // final px = vw * (i-1);
      // final py = height - (height * pfrac);

      path.lineTo(x, y);
    }

    path.lineTo(width, height);

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(LinePainter oldDelegate) {
    return oldDelegate.data != data;
  }
}
