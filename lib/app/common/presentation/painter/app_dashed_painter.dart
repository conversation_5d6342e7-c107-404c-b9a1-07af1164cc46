import 'dart:math' as math;

import 'package:flutter/material.dart';

class DashedPainter extends CustomPainter {
  Color color;
  double radius;
  double? width;

  DashedPainter({
    required this.color,
    this.radius = 0,
    this.width,
  });

  @override
  void paint(Canvas canvas, Size size) {
    Paint dashedPaint = Paint()
      ..color = color
      ..strokeWidth = width ?? size.width * 0.002
      ..style = PaintingStyle.stroke;

    double x = size.width;
    double y = size.height;

    Path topPath = getDashedPath(
      a: math.Point(0 + radius, 0),
      b: math.Point(x - radius, 0),
      gap: size.width * 0.01,
    );

    Path rightPath = getDashedPath(
      a: math.Point(x, 0 + radius),
      b: math.Point(x, y - radius),
      gap: size.width * 0.01,
    );

    Path bottomPath = getDashedPath(
      a: math.Point(0 + radius, y),
      b: math.Point(x - radius, y),
      gap: size.width * 0.01,
    );

    Path leftPath = getDashedPath(
      a: math.Point(0, 0 + radius),
      b: math.Point(0.001, y - radius),
      gap: size.width * 0.01,
    );

    canvas.drawPath(topPath, dashedPaint);
    canvas.drawPath(rightPath, dashedPaint);
    canvas.drawPath(bottomPath, dashedPaint);
    canvas.drawPath(leftPath, dashedPaint);

    if (radius > 0) {
      Path topLeftArc = getArc(
        a: Offset(0, 0 + radius),
        b: Offset(radius, 0),
      );

      Path topRightArc = getArc(
        a: Offset(x - radius, 0),
        b: Offset(x, 0 + radius),
      );

      Path bottomRightArc = getArc(
        a: Offset(x, y - radius),
        b: Offset(x - radius, y),
      );

      Path bottomLeftArc = getArc(
        a: Offset(0 + radius, y),
        b: Offset(0, y - radius),
      );

      canvas.drawPath(topLeftArc, dashedPaint);
      canvas.drawPath(topRightArc, dashedPaint);
      canvas.drawPath(bottomRightArc, dashedPaint);
      canvas.drawPath(bottomLeftArc, dashedPaint);
    }
  }

  Path getDashedPath({
    required math.Point<double> a,
    required math.Point<double> b,
    required gap,
  }) {
    Size size = Size(b.x - a.x, b.y - a.y);
    Path path = Path();
    path.moveTo(a.x, a.y);
    bool shouldDraw = true;
    math.Point currentPoint = math.Point(a.x, a.y);

    num radians = math.atan(size.height / size.width);

    num dx = math.cos(radians) * gap < 0
        ? math.cos(radians) * gap * -1
        : math.cos(radians) * gap;

    num dy = math.sin(radians) * gap < 0
        ? math.sin(radians) * gap * -1
        : math.sin(radians) * gap;

    while (currentPoint.x <= b.x && currentPoint.y <= b.y) {
      shouldDraw
          ? path.lineTo(currentPoint.x as double, currentPoint.y as double)
          : path.moveTo(currentPoint.x as double, currentPoint.y as double);
      shouldDraw = !shouldDraw;
      currentPoint = math.Point(
        currentPoint.x + dx,
        currentPoint.y + dy,
      );
    }
    return path;
  }

  Path getArc({
    required Offset a,
    required Offset b,
    double gap = 0.01,
  }) {
    Path path = Path();
    path.moveTo(a.dx, a.dy);

    path.arcToPoint(
      b,
      radius: Radius.circular(radius),
    );
    return path;
  }

  @override
  bool shouldRepaint(DashedPainter oldDelegate) {
    return false;
  }
}
