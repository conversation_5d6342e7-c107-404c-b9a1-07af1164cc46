import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class AppFeatureCard extends AppStatelessWidget with AppModalMixin {
  final Widget icon;
  final String title;
  final String decription;
  final String? confirmTitle;
  final String? confirmSubtitle;
  final bool isLoading;
  final bool isDisabled;
  final OnPressed onEnable;
  final OnPressed onSkip;
  final EdgeInsetsGeometry? margin;

  const AppFeatureCard({
    super.key,
    required this.icon,
    required this.title,
    required this.decription,
    required this.onEnable,
    required this.onSkip,
    this.isDisabled = false,
    this.isLoading = false,
    this.confirmSubtitle,
    this.confirmTitle,
    this.margin,
  });

  bool get _showConfirmDialog =>
      confirmSubtitle.hasValue || confirmTitle.hasValue;

  @override
  Widget build(BuildContext context) {
    return AppCard(
      margin: margin,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          const AppGap.y64(),
          const AppGap.y10(),
          icon,
          const AppGap.y32(),
          AppText(title, style: context.textStyle.h5()),
          const AppGap.y8(),
          AppText(
            decription,
            style: context.textStyle.b3(color: context.secondaryTextColor),
          ),
          const AppGap.y48(),
          AppButton(
            text: LocaleKeys.enable.tr(),
            isLoading: isLoading,
            isDisabled: isDisabled || isLoading,
            onPressed: () {
              if (_showConfirmDialog) {
                confirmAction(
                  context,
                  onContinue: onEnable,
                  title: confirmTitle,
                  description: confirmSubtitle,
                );
                return;
              }
              onEnable();
            },
          ),
          const AppGap.y16(),
          AppOutlineButton(
            text: LocaleKeys.maybeLater.tr(),
            onPressed: onSkip,
            isDisabled: isLoading,
          ),
          const AppGap.y64(),
          const AppGap.y10(),
        ],
      ),
    );
  }
}
