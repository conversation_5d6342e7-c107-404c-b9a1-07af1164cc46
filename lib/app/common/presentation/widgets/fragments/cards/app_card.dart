import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

enum CornerStyle {
  continous,
  rounded,
}

class AppCard extends AppStatelessWidget {
  final Color? color;
  final Widget child;
  final BoxConstraints? constraints;
  final EdgeInsetsGeometry? padding;
  final CornerStyle cornerStyle;
  final EdgeInsetsGeometry? margin;
  final List<BoxShadow>? shadows;
  final BorderSide border;
  final BorderRadius? borderRadius;
  final Gradient? gradient;
  final DecorationImage? image;

  const AppCard({
    this.color,
    this.padding,
    this.margin,
    this.borderRadius,
    this.constraints,
    this.shadows,
    this.image,
    this.cornerStyle = CornerStyle.continous,
    this.border = BorderSide.none,
    this.gradient,
    super.key,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    final shape = switch (cornerStyle) {
      CornerStyle.continous => ContinuousRectangleBorder(
          borderRadius: borderRadius ?? context.xxxlBorderRadius,
          side: border,
        ),
      _ => RoundedRectangleBorder(
          borderRadius: borderRadius ?? context.xxxlBorderRadius,
          side: border,
        )
    };
    return AnimatedContainer(
      constraints: constraints,
      decoration: ShapeDecoration(
        color: gradient == null ? color ?? context.cardColor : null,
        shadows: shadows,
        shape: shape,
        gradient: gradient,
        image: image,
      ),
      width: double.infinity,
      margin: margin,
      padding: padding ?? context.insets.defaultCardInsets,
      duration: 500.milliDuration,
      child: child,
    );
  }
}
