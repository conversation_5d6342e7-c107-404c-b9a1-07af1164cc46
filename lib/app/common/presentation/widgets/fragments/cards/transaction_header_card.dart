import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class TransactionHeaderCard extends AppStatelessWidget {
  final String title;
  final String amount;
  final TextStyle? style;

  const TransactionHeaderCard(
    this.title, {
    super.key,
    required this.amount,
    this.style,
  });
  @override
  Widget build(BuildContext context) {
    return AppCard(
      padding: context.insets.symmetricSp(
        horizontal: AppFontSizes.px32,
        vertical: AppFontSizes.px24,
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            child: AppFormHeader(
              title: title,
              titleStyle: style ?? context.textStyle.h5(),
              subTitle: amount,
              subTitleStyle: context.textStyle.h3(
                color: context.raisedBtnBBgColor,
              ),
              padding: EdgeInsets.zero,
              bottomGap: const AppGap.zero(),
            ),
          ),
          const AppGap.h10(),
          const App<PERSON>ogo(iconOnly: true)
        ],
      ),
    );
  }
}
