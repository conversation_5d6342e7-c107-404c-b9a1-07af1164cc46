import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class AppIconInfoTile extends AppStatelessWidget {
  final String? title;
  final String? rider;
  final IconData icon;

  const AppIconInfoTile({
    super.key,
    required this.icon,
    this.title,
    this.rider,
  }) : assert(!(title == null && rider == null));

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        AppIcon(icon, size: AppFontSizes.px20),
        const AppGap.h12(),
        Expanded(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              if (title.hasValue) AppText(title, style: context.textStyle.b2()),
              if (title.hasValue && rider.hasValue) const AppGap.y2(),
              if (rider.hasValue)
                AppText(
                  rider,
                  style: context.textStyle.b3(
                    color: context.secondaryTextColor,
                  ),
                )
            ],
          ),
        )
      ],
    );
  }
}

class IconRowTile extends AppStatelessWidget {
  final IconData icon;
  final String title;
  final String description;
  final EdgeInsetsGeometry? padding;

  const IconRowTile({
    super.key,
    required this.icon,
    required this.title,
    required this.description,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    final yOffSet = context.sp(AppFontSizes.px3);
    return Padding(
      padding: padding ?? context.insets.onlySp(bottom: AppFontSizes.px32),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          AppIcon(icon),
          const AppGap.h16(),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                Transform.translate(
                  offset: Offset(0, -yOffSet),
                  child: AppText(title, style: context.textStyle.h6()),
                ),
                AppRichText(
                  description,
                  textStyle: context.textStyle.b2(
                    color: context.secondaryTextColor,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
