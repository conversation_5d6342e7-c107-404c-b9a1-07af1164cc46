import 'package:day1/day1.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class SelectionTile extends AppStatelessWidget {
  final String title;
  final Widget? icon;
  final bool isSelected;
  final OnPressed? onTap;
  final EdgeInsetsGeometry? tilePadding;
  final EdgeInsetsGeometry? selectionPadding;
  final BorderRadiusGeometry? selectionRadius;

  const SelectionTile({
    super.key,
    required this.title,
    this.icon,
    this.isSelected = false,
    this.selectionPadding,
    this.onTap,
    this.selectionRadius,
    this.tilePadding,
  });

  @override
  Widget build(BuildContext context) {
    final styles = context.textStyle;
    final selectedTextColor = context.textColor;
    final unselectedTextColor = context.disabledBtntextColor;
    final textColor = isSelected ? selectedTextColor : unselectedTextColor;
    final style = styles.b2(color: textColor, weight: FontWeight.w500);
    final padding = tilePadding ?? context.insets.allSp(AppFontSizes.px16);
    final selectedPadding =
        selectionPadding ?? context.insets.allSp(AppFontSizes.px8);

    return InkWell(
      borderRadius: context.mdBorderRadius,
      onTap: () {
        HapticFeedback.selectionClick();
        onTap?.call();
      },
      child: AnimatedContainer(
        duration: 500.milliDuration,
        margin: isSelected ? padding : null,
        padding: isSelected ? selectedPadding : padding,
        decoration: BoxDecoration(
          color: isSelected ? context.scaffoldBgColor : context.transparent,
          borderRadius: selectionRadius ?? context.btnBorderRadius,
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            if (icon != null) ...[
              icon!,
              const AppGap.h12(),
            ],
            Expanded(child: AppText(title, style: style)),
          ],
        ),
      ),
    );
  }
}

class SmallSelectionTile extends AppStatelessWidget {
  final String title;
  final bool isSelected;
  final OnPressed? onTap;
  final EdgeInsetsGeometry padding;

  const SmallSelectionTile({
    super.key,
    required this.title,
    this.isSelected = false,
    this.onTap,
    this.padding = EdgeInsets.zero,
  });

  @override
  Widget build(BuildContext context) {
    final styles = context.textStyle;
    final style =
        styles.b2(weight: isSelected ? FontWeight.w600 : FontWeight.w400);

    return InkWell(
      borderRadius: context.mdBorderRadius,
      onTap: () {
        HapticFeedback.selectionClick();
        onTap?.call();
      },
      child: Padding(
        padding: padding,
        child: AppText(title, style: style),
      ),
    );
  }
}
