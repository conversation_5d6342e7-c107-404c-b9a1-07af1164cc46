import 'package:day1/day1.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class AppSelectionListTile<T> extends AppStatelessWidget {
  final T selection;
  final String title;
  final T? terminalOption;
  final List<T> selections;
  final OnChanged<T> onSelect;
  final bool showBorder;
  final bool showProceedIcon;
  final bool disabled;
  final bool isMultiSelect;

  const AppSelectionListTile({
    required this.selection,
    required this.onSelect,
    required this.selections,
    required this.title,
    this.isMultiSelect = true,
    this.showProceedIcon = false,
    this.showBorder = true,
    this.terminalOption,
    this.disabled = false,
    super.key,
  });

  bool get _isSelected => selections.contains(selection);
  int get _index => selections.indexOf(selection);

  @override
  Widget build(BuildContext context) {
    final styles = context.textStyle;
    final style = styles.h4();
    final icon = _isSelected ? AppIcons.minus : AppIcons.plus;
    final isLast = selection == terminalOption;

    Widget child = InkWell(
      onTap: () {
        HapticFeedback.selectionClick();
        onSelect(selection);
      },
      child: AnimatedOpacity(
        opacity: disabled ? .5 : 1,
        duration: 500.milliDuration,
        child: Container(
          padding: context.insets.symmetricSp(vertical: spacing.spacing12),
          decoration: BoxDecoration(
            border: Border(
              bottom: BorderSide(
                width: 1,
                color: (isLast || !showBorder)
                    ? context.transparent
                    : context.dividerColor,
              ),
            ),
          ),
          child: Row(
            children: [
              Expanded(child: AppText(title, style: style)),
              const AppGap.h4(),
              if (showProceedIcon && !isMultiSelect)
                AppIcon(
                  AppIcons.forward,
                  size: AppFontSizes.px12,
                  color: context.highlightedIconColor,
                ),
              if (isMultiSelect)
                AppAnimatedSwitcher(
                  child: AppIcon(
                    icon,
                    size: AppFontSizes.px16,
                    key: ValueKey("$selection-$icon-$_index"),
                  ),
                ),
              if (!isMultiSelect && _isSelected)
                const AppAnimatedSwitcher(
                  child: AppIcon(
                    Icons.check,
                    size: AppFontSizes.px18,
                    color: AppColors.success,
                  ),
                ),
            ],
          ),
        ),
      ),
    );

    if (disabled) {
      child = IgnorePointer(child: child);
    }

    return child;
  }
}
