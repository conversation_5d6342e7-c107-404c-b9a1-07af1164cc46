import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class AppPill extends AppStatelessWidget {
  final String label;
  final EdgeInsetsGeometry? padding;
  final Color? textColor;
  final Color? bgColor;
  final Color? borderColor;
  final OnPressed? onTap;
  final TextStyle? style;
  final Widget? trailing;

  const AppPill(
    this.label, {
    this.bgColor,
    this.textColor,
    this.padding,
    this.onTap,
    this.borderColor,
    this.trailing,
    this.style,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: AnimatedContainer(
        duration: 500.milliDuration,
        padding: padding ??
            context.insets.symmetricSp(
              horizontal: AppFontSizes.px8,
              vertical: AppFontSizes.px4,
            ),
        curve: Curves.decelerate,
        decoration: BoxDecoration(
          border: Border.all(
            color: borderColor ?? bgColor ?? context.scaffoldBgColor,
          ),
          borderRadius: context.btnBorderRadius,
          color: bgColor ?? context.scaffoldBgColor,
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            AppText(
              label,
              textAlign: TextAlign.center,
              style: style?.copyWith(color: textColor) ??
                  context.textStyle.b5(
                    color: textColor ?? context.pillTextColor,
                    weight: FontWeight.w500,
                  ),
            ),
            if (trailing != null) ...[const AppGap.h8(), trailing!],
          ],
        ),
      ),
    );
  }
}

class AppCopyPill extends AppStatelessWidget {
  final String value;

  const AppCopyPill({
    required this.value,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return AppPill(
      value,
      style: context.textStyle.b4(
        color: context.pillTextColor,
        weight: FontWeight.w500,
      ),
      padding: context.insets.symmetricSp(
        horizontal: AppFontSizes.px12,
        vertical: AppFontSizes.px4,
      ),
      trailing: AppIcon(
        AppIcons.copy,
        color: context.pillTextColor,
      ),
      onTap: () => context.copyText(value),
    );
  }
}

class AppSelectionPill<T> extends AppStatelessWidget {
  final T value;
  final List<T> selections;
  final String label;
  final OnChanged<T> onSelect;
  final EdgeInsetsGeometry? padding;

  const AppSelectionPill(
    this.label, {
    required this.value,
    required this.onSelect,
    this.selections = const [],
    this.padding,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final isSelected = selections.contains(value);
    final textColor =
        isSelected ? context.raisedBtnTextColor : context.textColor;
    final bgColor =
        isSelected ? context.raisedBtnBBgColor : context.transparent;
    final borderColor = isSelected ? bgColor : context.outlineBtnAltBorderColor;

    return AppPill(
      label,
      style: context.textStyle.b4(weight: FontWeight.w500),
      padding: padding ??
          context.insets.symmetricSp(
            horizontal: AppFontSizes.px16,
            vertical: AppFontSizes.px8,
          ),
      textColor: textColor,
      bgColor: bgColor,
      borderColor: borderColor,
      onTap: () => onSelect(value),
    );
  }
}

class AppSelectionPills<T> extends AppStatefulWidget {
  final List<NamedOptionData<T>> values;
  final List<T> selections;
  final OnChanged<T> onSelect;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? pillPading;

  const AppSelectionPills(
    this.values, {
    required this.onSelect,
    this.selections = const [],
    this.padding,
    this.pillPading,
    super.key,
  });

  @override
  State<StatefulWidget> createState() => _AppSelectionPillsState<T>();
}

class _AppSelectionPillsState<T> extends State<AppSelectionPills<T>> {
  late final ScrollController controller;
  late final List<GlobalKey> scrollKeys;

  @override
  void initState() {
    super.initState();
    controller = ScrollController();
    scrollKeys = List.generate(
      widget.values.length,
      (_) => GlobalKey(debugLabel: "$_"),
    );

    if (widget.selections.isNotEmpty) {
      final index = widget.values.tryIndexWhere(
        (it) => widget.selections.contains(it.option),
      );
      if (index == -1) return;
      _ensureVisible(scrollKeys[index]);
    }
  }

  _ensureVisible(GlobalKey key) {
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      (key.currentContext ?? context).makeVisible();
    });
  }

  @override
  Widget build(BuildContext context) {
    final defaultPadding = context.insets.symmetricSp(
      horizontal: AppFontSizes.px32,
      vertical: AppFontSizes.px8,
    );
    final List<Widget> children = [
      for (final (index, pill) in widget.values.indexed)
        AppSelectionPill(
          key: scrollKeys[index],
          pill.name,
          value: pill.option,
          onSelect: (value) {
            widget.onSelect(value);
            _ensureVisible(scrollKeys[index]);
          },
          padding: widget.pillPading,
          selections: widget.selections,
        )
    ];

    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      controller: controller,
      padding: widget.padding ?? defaultPadding,
      child: Row(
        children: children.intersperse(const AppGap.h8()),
      ),
    );
  }
}

class AppValidatorPill extends AppStatelessWidget {
  final String value;
  final ValidationPillData data;

  const AppValidatorPill(this.value, {required this.data, super.key});

  @override
  Widget build(BuildContext context) {
    final isValid = data.isValid(value);
    final color = isValid ? context.raisedBtnCBgColor : context.transparent;
    final borderColor =
        isValid ? context.raisedBtnCBgColor : context.cardBorderColor;
    final textColor =
        isValid ? context.raisedBtnTextColor : context.pillTextColor;

    return AnimatedContainer(
      key: ValueKey(data),
      duration: 500.milliDuration,
      curve: Curves.decelerate,
      padding: context.insets.symmetricSp(
        vertical: AppFontSizes.px4,
        horizontal: AppFontSizes.px12,
      ),
      decoration: BoxDecoration(
        borderRadius: context.btnBorderRadius,
        border: Border.all(color: borderColor),
        color: color,
      ),
      child: AppText(
        data.label.tr(),
        style: context.textStyle.b5(color: textColor),
        textAlign: TextAlign.center,
      ),
    );
  }
}
