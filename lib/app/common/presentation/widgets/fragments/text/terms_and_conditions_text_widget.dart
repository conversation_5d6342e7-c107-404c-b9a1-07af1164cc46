import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class TermsAndConditionTextWidget extends AppStatelessWidget {
  final String? termsOfUseText;
  final TextAlign textAlign;
  final EdgeInsets? padding;

  const TermsAndConditionTextWidget({
    this.termsOfUseText,
    this.textAlign = TextAlign.start,
    this.padding,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final text = termsOfUseText;
    return AppRichText(
      text?.tr(),
      textAlign: textAlign,
      textStyle: context.textStyle.labelText(),
    );
  }
}
