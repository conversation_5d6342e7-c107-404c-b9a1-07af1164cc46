import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class CopyrightTextWidget extends AppStatelessWidget {
  final Color? backgroundColor;
  final EdgeInsetsGeometry? padding;

  const CopyrightTextWidget({
    this.backgroundColor,
    this.padding,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      color: backgroundColor ?? context.scaffoldBgColor,
      width: double.infinity,
      padding: padding ?? context.insets.only(bottom: 2, left: 5, right: 5),
      child: AppText(config.copyrightText, textAlign: TextAlign.center),
    );
  }
}
