import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class GildedTitleText extends AppStatelessWidget {
  final String text;
  final double dividerPadding;
  final int textSpan;

  const GildedTitleText(
    this.text, {
    this.dividerPadding = 5,
    this.textSpan = 3,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final dividerWidget = Flexible(
      child: Padding(
        padding: context.insets.symmetric(horizontal: dividerPadding),
        child: const Center(
          child: AppDivider(size: AppDividerSize.none),
        ),
      ),
    );
    return Row(
      children: [
        dividerWidget,
        Expanded(
          flex: textSpan,
          child: AppText(
            text,
            textAlign: TextAlign.center,
          ),
        ),
        dividerWidget,
      ],
    );
  }
}
