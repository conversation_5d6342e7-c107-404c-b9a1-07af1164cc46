import 'package:day1/day1.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class AppRadio<T> extends AppStatelessWidget {
  final T value;
  final T? groupValue;
  final bool? condition;
  final OnChanged<T> onChanged;
  final Color? activeColor;
  final Color? activeBorderColor;
  final Color? inActiveColor;
  final double size;

  const AppRadio({
    super.key,
    required this.value,
    required this.groupValue,
    required this.onChanged,
    this.activeColor,
    this.activeBorderColor,
    this.size = AppFontSizes.px18,
    this.inActiveColor,
  }) : condition = null;

  const AppRadio.conditional({
    super.key,
    required this.value,
    required this.condition,
    required this.onChanged,
    this.activeColor,
    this.activeBorderColor,
    this.inActiveColor,
    this.size = AppFontSizes.px18,
  }) : groupValue = null;

  bool get _isActive {
    if (condition != null) {
      return condition!;
    }
    return groupValue == value;
  }

  Color _borderColor(BuildContext context) {
    return activeBorderColor ?? context.activeIndicatorBorderColor;
  }

  Color _color(BuildContext context) {
    if (!_isActive) return inActiveColor ?? context.inActiveIndicatorColor;
    return activeColor ?? context.activeIndicatorColor;
  }

  @override
  Widget build(BuildContext context) {
    final borderColor = _borderColor(context);
    final color = _color(context);

    return RepaintBoundary(
      child: InkWell(
        onTap: () {
          HapticFeedback.selectionClick();
          onChanged(value);
        },
        child: Container(
          height: context.sp(size),
          width: context.sp(size),
          constraints: BoxConstraints.tightFor(
            height: context.sp(size),
            width: context.sp(size),
          ),
          decoration: BoxDecoration(
            color: color,
            border: Border.all(color: borderColor, width: 1),
            shape: BoxShape.circle,
          ),
          child: AppAnimatedSwitcher(
            child: Builder(
              builder: (context) {
                if (_isActive) {
                  return Container(
                    width: 8,
                    height: 8,
                    decoration: BoxDecoration(
                      color: context.scaffoldBgColor,
                      shape: BoxShape.circle,
                    ),
                  );
                }
                return const Offstage();
              },
              key: ValueKey<T>(value),
            ),
          ),
        ),
      ),
    );
  }
}

class AppRadioRow<T> extends AppStatelessWidget {
  final T value;
  final T? groupValue;
  final bool? condition;
  final OnChanged<T> onChanged;
  final Color? activeColor;
  final Color? activeBorderColor;
  final Color? inActiveColor;
  final double size;
  final Widget? leading;
  final Widget? trailing;
  final MainAxisAlignment? alignment;
  final MainAxisSize? mainAxisSize;
  final EdgeInsets padding;
  final bool _isConditional;

  const AppRadioRow({
    super.key,
    required this.value,
    required this.groupValue,
    required this.onChanged,
    this.mainAxisSize,
    this.trailing,
    this.leading,
    this.alignment,
    this.activeColor,
    this.activeBorderColor,
    this.size = AppFontSizes.px18,
    this.inActiveColor,
    this.padding = EdgeInsets.zero,
  })  : condition = null,
        _isConditional = false;

  const AppRadioRow.conditional({
    super.key,
    required this.value,
    required this.condition,
    required this.onChanged,
    this.mainAxisSize,
    this.trailing,
    this.leading,
    this.alignment,
    this.activeColor,
    this.activeBorderColor,
    this.inActiveColor,
    this.size = AppFontSizes.px18,
    this.padding = EdgeInsets.zero,
  })  : groupValue = null,
        _isConditional = true;

  bool get _hasLeading => leading != null;
  bool get _hasTrailing => trailing != null;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        HapticFeedback.lightImpact();
        onChanged(value);
      },
      child: Padding(
        padding: padding,
        child: Row(
          mainAxisAlignment: alignment ?? MainAxisAlignment.end,
          mainAxisSize: mainAxisSize ?? MainAxisSize.min,
          children: [
            if (_hasLeading) leading!,
            if (!_isConditional)
              AppRadio(
                value: value,
                groupValue: groupValue,
                onChanged: onChanged,
                activeColor: activeColor,
                activeBorderColor: activeBorderColor,
                size: size,
                inActiveColor: inActiveColor,
              ),
            if (_isConditional)
              AppRadio.conditional(
                value: value,
                condition: condition,
                onChanged: onChanged,
                activeColor: activeColor,
                activeBorderColor: activeBorderColor,
                size: size,
                inActiveColor: inActiveColor,
              ),
            if (_hasTrailing) trailing!,
          ],
        ),
      ),
    );
  }
}

class AppRadioCard<T> extends AppStatelessWidget {
  final T value;
  final T? groupValue;
  final bool? condition;
  final OnChanged<T> onChanged;
  final Color? activeColor;
  final Color? activeBorderColor;
  final Color? inActiveColor;
  final double size;
  final String title;
  final String? rider;
  final Widget? icon;
  final MainAxisAlignment? alignment;
  final CrossAxisAlignment crossAxisAlignment;
  final MainAxisSize? mainAxisSize;
  final EdgeInsets? padding;
  final bool _isConditional;

  const AppRadioCard({
    super.key,
    required this.value,
    required this.groupValue,
    required this.onChanged,
    this.mainAxisSize,
    required this.title,
    this.crossAxisAlignment = CrossAxisAlignment.start,
    this.rider,
    this.icon,
    this.alignment,
    this.activeColor,
    this.activeBorderColor,
    this.size = AppFontSizes.px18,
    this.inActiveColor,
    this.padding,
  })  : condition = null,
        _isConditional = false;

  const AppRadioCard.conditional({
    super.key,
    required this.value,
    required this.condition,
    required this.onChanged,
    this.mainAxisSize,
    required this.title,
    this.crossAxisAlignment = CrossAxisAlignment.start,
    this.rider,
    this.icon,
    this.alignment,
    this.activeColor,
    this.activeBorderColor,
    this.inActiveColor,
    this.size = AppFontSizes.px18,
    this.padding,
  })  : groupValue = null,
        _isConditional = true;

  bool get isActive => value == groupValue;

  @override
  Widget build(BuildContext context) {
    final borderColor = context.inputBorderColor;
    final resolvedBorderColor =
        isActive ? activeBorderColor ?? context.cardBorderColor : borderColor;

    final leading = Expanded(
      child: Row(
        crossAxisAlignment: crossAxisAlignment,
        children: [
          if (icon != null) ...[icon!, const AppGap.h16()],
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                AppText(
                  title,
                  style: context.textStyle.b2(weight: FontWeight.w600),
                ),
                if (rider != null) ...[
                  const AppGap.y4(),
                  AppText(
                    rider,
                    style: context.textStyle.b5(weight: FontWeight.w500),
                  ),
                ]
              ],
            ),
          ),
          const AppGap.h16()
        ],
      ),
    );

    return InkWell(
      onTap: () {
        HapticFeedback.lightImpact();
        onChanged(value);
      },
      child: AppCard(
        padding: padding ?? context.insets.allSp(AppFontSizes.px24),
        border: BorderSide(color: resolvedBorderColor),
        child: Builder(builder: (_) {
          if (!_isConditional) {
            return AppRadioRow(
              value: value,
              groupValue: groupValue,
              onChanged: onChanged,
              leading: leading,
              activeColor: activeColor,
              activeBorderColor: activeBorderColor,
              size: size,
              alignment: alignment ?? MainAxisAlignment.end,
              mainAxisSize: mainAxisSize ?? MainAxisSize.min,
              inActiveColor: inActiveColor,
            );
          }
          return AppRadioRow.conditional(
            value: value,
            condition: condition,
            onChanged: onChanged,
            activeColor: activeColor,
            activeBorderColor: activeBorderColor,
            leading: leading,
            alignment: alignment ?? MainAxisAlignment.end,
            mainAxisSize: mainAxisSize ?? MainAxisSize.min,
            size: size,
            inActiveColor: inActiveColor,
          );
        }),
      ),
    );
  }
}
