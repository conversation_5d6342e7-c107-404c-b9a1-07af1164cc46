import 'package:day1/day1.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class AppCheckBox<T> extends AppStatelessWidget {
  final T value;
  final bool isActive;
  final OnChanged<T> onChanged;
  final Color? activeColor;
  final Color? activeBorderColor;
  final Color? inActiveColor;
  final double size;

  const AppCheckBox({
    required this.value,
    required this.onChanged,
    required this.isActive,
    this.activeColor,
    this.activeBorderColor,
    this.size = AppFontSizes.px18,
    this.inActiveColor,
    super.key,
  });

  Color _borderColor(BuildContext context) {
    return activeBorderColor ?? context.activeIndicatorBorderColor;
  }

  Color _color(BuildContext context) {
    if (!isActive) return inActiveColor ?? context.inActiveIndicatorColor;
    return activeColor ?? context.activeIndicatorColor;
  }

  @override
  Widget build(BuildContext context) {
    final borderColor = _borderColor(context);
    final color = _color(context);

    return RepaintBoundary(
      child: InkWell(
        onTap: () {
          HapticFeedback.selectionClick();
          onChanged(value);
        },
        child: Container(
          alignment: Alignment.center,
          height: context.sp(size),
          width: context.sp(size),
          constraints: BoxConstraints.tightFor(
            height: context.sp(size),
            width: context.sp(size),
          ),
          decoration: BoxDecoration(
            color: color,
            border: Border.all(color: borderColor),
            borderRadius: context.smBorderRadius,
          ),
          child: AppAnimatedSwitcher(
            child: Builder(
              builder: (context) {
                if (isActive) {
                  return AppIcon(
                    AppIcons.check,
                    size: AppFontSizes.px16,
                    color: context.scaffoldBgColor,
                  );
                }
                return const Offstage();
              },
              key: ValueKey<T>(value),
            ),
          ),
        ),
      ),
    );
  }
}

class AppCheckBoxRow<T> extends AppStatelessWidget {
  final T value;
  final bool isActive;
  final OnChanged<T> onChanged;
  final Color? activeColor;
  final Color? activeBorderColor;
  final Color? inActiveColor;
  final double size;
  final Widget? leading;
  final Widget? trailing;
  final MainAxisAlignment? alignment;
  final MainAxisSize? mainAxisSize;
  final EdgeInsets padding;

  const AppCheckBoxRow({
    required this.value,
    required this.onChanged,
    required this.isActive,
    this.mainAxisSize,
    this.trailing,
    this.leading,
    this.alignment,
    this.activeColor,
    this.activeBorderColor,
    this.size = AppFontSizes.px18,
    this.inActiveColor,
    super.key,
    this.padding = EdgeInsets.zero,
  });

  bool get _hasLeading => leading != null;
  bool get _hasTrailing => trailing != null;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        HapticFeedback.lightImpact();
        onChanged(value);
      },
      child: Padding(
        padding: padding,
        child: Row(
          mainAxisSize: mainAxisSize ?? MainAxisSize.min,
          mainAxisAlignment: alignment ?? MainAxisAlignment.end,
          children: [
            if (_hasLeading) leading!,
            if (_hasLeading) const AppGap.h8(),
            AppCheckBox(
              value: value,
              onChanged: onChanged,
              isActive: isActive,
              activeColor: activeColor,
              activeBorderColor: activeBorderColor,
              inActiveColor: inActiveColor,
              size: size,
            ),
            if (_hasTrailing) const AppGap.h8(),
            if (_hasTrailing) trailing!,
          ],
        ),
      ),
    );
  }
}
