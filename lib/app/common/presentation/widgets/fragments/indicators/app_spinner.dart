import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class AppSpinner extends AppStatelessWidget {
  final Color? color;
  final double size;
  final double? value;
  final bool forceCentering;

  const AppSpinner({
    this.color,
    this.size = AppFontSizes.px12,
    this.forceCentering = true,
    super.key,
    this.value,
  });

  @override
  Widget build(BuildContext context) {
    Widget child = SizedBox(
      height: context.sp(size),
      width: context.sp(size),
      child: RepaintBoundary(
        child: CircularProgressIndicator.adaptive(
          value: value,
          strokeWidth: 1.5,
          valueColor: AlwaysStoppedAnimation<Color>(
            color ?? context.textColor,
          ),
        ),
      ),
    );
    if (forceCentering) {
      child = Center(
        child: child,
      );
    }
    return child;
  }
}

class AppProgress extends AppStatelessWidget {
  final Color? color;
  final double size;
  final double? value;

  const AppProgress({
    this.color,
    this.size = AppFontSizes.px2,
    super.key,
    this.value,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: context.sp(size),
      child: LinearProgressIndicator(
        color: color ?? context.highlightedIconColor,
        value: value,
      ),
    );
  }
}
