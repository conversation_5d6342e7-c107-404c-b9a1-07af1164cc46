import 'package:flutter/material.dart';
import 'package:day1/day1.dart';

class AppDots extends StatelessWidget {
  final int len;
  final int? activeIndex;

  const AppDots(this.activeIndex, {this.len = 3, super.key})
      : assert(activeIndex != null);

  @override
  Widget build(BuildContext context) {
    List<Widget> children = List.generate(
      len,
      (index) {
        final isActive = activeIndex == index;
        return _Dot(isActive);
      },
    );
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: children.intersperse(const AppGap.h8()),
    );
  }
}

class AppScaledDots extends StatelessWidget {
  final int len;
  final int? activeIndex;
  final double size;

  const AppScaledDots(
    this.activeIndex, {
    this.len = 3,
    this.size = AppFontSizes.px6,
    super.key,
  }) : assert(activeIndex != null);

  double _calculateSize(int index) {
    if (index == activeIndex) return size;
    final distance = ((activeIndex ?? 0) - index).abs();
    if (distance == 0) return size;
    final distanceFraction = distance / len;
    return size - (size * distanceFraction);
  }

  @override
  Widget build(BuildContext context) {
    final activeColor = context.highlightedIconColor;
    final inActiveColor = context.iconColor;

    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: List.generate(
        len,
        (index) {
          final isActive = activeIndex == index;
          return Container(
            margin: context.insets.symmetricSp(horizontal: spacing.spacing2),
            child: AppSvg(
              AppVectors.dot,
              color: isActive ? activeColor : inActiveColor,
              height: context.sp(_calculateSize(index)),
              width: context.sp(_calculateSize(index)),
            ),
          );
        },
      ),
    );
  }
}

class _Dot extends StatelessWidget {
  final bool active;

  const _Dot(this.active);

  @override
  Widget build(BuildContext context) {
    final color = context.activeIndicatorColor;
    final width = active ? AppFontSizes.px32 : AppFontSizes.px8;

    return AnimatedContainer(
      duration: 300.milliDuration,
      curve: Curves.easeIn,
      height: context.sp(AppFontSizes.px8),
      width: context.sp(width),
      decoration: BoxDecoration(
        color: color.withOpacity(active ? 1 : .38),
        borderRadius: context.btnBorderRadius,
      ),
    );
  }
}
