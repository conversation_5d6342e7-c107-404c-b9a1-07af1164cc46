import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class AppIndicatorAlignedPrefix extends AppStatelessWidget {
  final Alignment alignment;
  final Widget child;

  const AppIndicatorAlignedPrefix({
    this.alignment = Alignment.topLeft,
    required this.child,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Align(
      alignment: alignment,
      child: ClipRRect(borderRadius: context.xsBorderRadius, child: child),
    );
  }
}

class AppIndicatorIconPrefix extends AppStatelessWidget {
  final IconData icon;
  final double iconSize;

  const AppIndicatorIconPrefix({
    required this.icon,
    this.iconSize = AppFontSizes.px32,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final size = context.sp(AppFontSizes.px56);
    return Container(
      alignment: Alignment.center,
      constraints: BoxConstraints.tightFor(width: size, height: size),
      padding: context.insets.allSp(AppFontSizes.px12),
      decoration:
          BoxDecoration(shape: BoxShape.circle, color: context.scaffoldBgColor),
      child: AppIcon(icon, size: iconSize),
    );
  }
}
