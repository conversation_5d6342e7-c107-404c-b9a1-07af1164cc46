import 'package:day1/day1.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class AppSwitchTile extends AppStatelessWidget {
  final bool value;
  final bool disabled;
  final Widget? leading;
  final Widget? trailing;
  final OnChanged<bool> onChanged;
  final MainAxisAlignment? alignment;
  final MainAxisSize? mainAxisSize;
  final EdgeInsets padding;
  final double size;
  final Color? activeColor;
  final Color? thumbColor;

  const AppSwitchTile({
    required this.value,
    required this.onChanged,
    this.mainAxisSize,
    this.disabled = false,
    this.alignment,
    this.leading,
    this.trailing,
    this.size = 24,
    super.key,
    this.padding = EdgeInsets.zero,
    this.activeColor,
    this.thumbColor,
  });

  bool get _hasLeading => leading != null;
  bool get _hasTrailing => trailing != null;

  Color _color(BuildContext context) {
    if (!value) return context.switchTrackInactiveColor;
    return activeColor ?? context.switchTrackColor;
  }

  Color _thumbColor(BuildContext context) {
    if (!value) return context.switchThumbActiveColor;
    return thumbColor ?? context.switchThumbActiveColor;
  }

  @override
  Widget build(BuildContext context) {
    final color = _color(context);
    final thumbColor_ = _thumbColor(context);

    return InkWell(
      onTap: () {
        if (disabled) return;
        HapticFeedback.lightImpact();
        onChanged(!value);
      },
      child: Padding(
        padding: padding,
        child: Row(
          mainAxisSize: mainAxisSize ?? MainAxisSize.min,
          mainAxisAlignment: alignment ?? MainAxisAlignment.end,
          children: [
            if (_hasLeading) ...[leading!, const AppGap.h8()],
            SizedBox(
              height: size,
              child: RepaintBoundary(
                child: Switch(
                  value: value,
                  onChanged: (value) {
                    HapticFeedback.selectionClick();
                    onChanged(value);
                  },
                  activeColor: color,
                  inactiveTrackColor: color,
                  inactiveThumbColor: thumbColor_,
                  thumbColor: WidgetStatePropertyAll(thumbColor_),
                  trackColor: WidgetStatePropertyAll(color),
                  trackOutlineColor: WidgetStatePropertyAll(color),
                  trackOutlineWidth: const WidgetStatePropertyAll(0),
                  // trackColor: value ? color : context.switchTrackColor,
                ),
              ),
            ),
            if (_hasTrailing) ...[const AppGap.h8(), trailing!],
          ],
        ),
      ),
    );
  }
}
