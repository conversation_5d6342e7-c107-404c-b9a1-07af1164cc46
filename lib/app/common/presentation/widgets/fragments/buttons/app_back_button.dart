import 'package:day1/day1.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class AppBackButton extends AppStatelessWidget {
  final OnPressed? action;
  final EdgeInsetsGeometry? padding;
  final AlignmentGeometry alignment;
  final bool routeStackSensitive;

  const AppBackButton({
    super.key,
    this.action,
    this.padding,
    this.routeStackSensitive = true,
    this.alignment = Alignment.center,
  });

  bool get canPop => AppRouter.canPop();

  @override
  Widget build(BuildContext context) {
    if (!canPop && routeStackSensitive) {
      return const Offstage();
    }

    return Material(
      type: MaterialType.transparency,
      child: Hero(
        tag: "app-back-button-for-app",
        child: AppOutlineButton(
          alignment: alignment,
          isDisabled: !canPop && routeStackSensitive,
          minSize: Size.fromWidth(
            context.sp(AppFontSizes.px56),
          ),
          contentPadding: context.insets.symmetricSp(
            vertical: AppFontSizes.px8,
            horizontal: AppFontSizes.px16,
          ),
          onPressed: () {
            HapticFeedback.lightImpact();
            trackEvent(AppEvent.clickedBackBtn);
            if (action != null) {
              action!();
              return;
            }
            if (!canPop) return;

            AppRouter.popView();
          },
          size: ButtonSize.medium,
          variant: OutlineBtnVariant.neutral,
          icon: AppSvg(
            AppVectors.arrowLeft,
            width: context.sp(AppFontSizes.px18),
            color: context.iconColor,
          ),
        ),
      ),
    );
  }
}

class AppMenuButton extends AppStatelessWidget {
  final OnPressed? action;
  final EdgeInsetsGeometry? padding;
  final AlignmentGeometry alignment;

  const AppMenuButton({
    super.key,
    this.action,
    this.padding,
    this.alignment = Alignment.center,
  });

  @override
  Widget build(BuildContext context) {
    return Material(
      type: MaterialType.transparency,
      child: Hero(
        tag: "app-back-button",
        child: AppOutlineButton(
          minSize: Size.fromWidth(
            context.sp(AppFontSizes.px56),
          ),
          contentPadding: context.insets.symmetricSp(
            vertical: AppFontSizes.px8,
            horizontal: AppFontSizes.px16,
          ),
          alignment: alignment,
          onPressed: () {
            action?.call();
          },
          size: ButtonSize.medium,
          variant: OutlineBtnVariant.neutral,
          icon: AppIcon(
            AppIcons.menuScale,
            size: AppFontSizes.px18,
            color: context.iconColor,
          ),
        ),
      ),
    );
  }
}

class AppCloseButton extends AppStatelessWidget {
  final OnPressed? subAction;
  final OnPressed? onTap;
  final AlignmentGeometry alignment;
  final double? size;
  final bool asHero;

  const AppCloseButton({
    super.key,
    this.subAction,
    this.onTap,
    this.alignment = Alignment.center,
    this.size = AppFontSizes.px20,
    this.asHero = false,
  });

  @override
  Widget build(BuildContext context) {
    Widget child = AppOutlineButton(
      text: null,
      onPressed: () {
        HapticFeedback.lightImpact();
        trackEvent(AppEvent.clickedCloseBtn);
        if (subAction != null) {
          subAction!();
        }
        if (onTap != null) {
          return onTap!();
        }
        Navigator.of(context).pop();
      },
      alignment: alignment,
      size: ButtonSize.medium,
      variant: OutlineBtnVariant.neutral,
      icon: AppIcon(
        AppIcons.xmark,
        color: context.textColor,
        size: size,
      ),
    );

    if (asHero) {
      child = Material(
        type: MaterialType.transparency,
        child: Hero(
          tag: "app-cancel-button-hero",
          child: child,
        ),
      );
    }

    return child;
  }
}

class AppCancelButton extends AppStatelessWidget {
  final OnPressed? subAction;
  final OnPressed? onTap;
  final TextAlign alignment;
  final ButtonSize buttonSize;
  final double? size;

  const AppCancelButton({
    super.key,
    this.subAction,
    this.onTap,
    this.buttonSize = ButtonSize.medium,
    this.alignment = TextAlign.end,
    this.size = AppFontSizes.px20,
  });

  @override
  Widget build(BuildContext context) {
    Widget child = AppTextButton(
      text: null,
      onPressed: () {
        HapticFeedback.lightImpact();
        trackEvent(AppEvent.clickedCloseBtn);
        if (subAction != null) {
          subAction!();
        }
        if (onTap != null) {
          return onTap!();
        }
        Navigator.of(context).pop();
      },
      textAlign: alignment,
      size: buttonSize,
      icon: AppIcon(
        AppIcons.xmark,
        color: context.textColor,
        size: size,
      ),
    );

    return child;
  }
}
