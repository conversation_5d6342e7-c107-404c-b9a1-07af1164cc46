import 'package:day1/day1.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class AppIconButton extends AppButtonBase {
  final IconData? icon;
  final Widget? trailingIcon;
  final MaterialTapTargetSize? tapTargetSize;

  const AppIconButton({
    required this.icon,
    required super.onPressed,
    super.minSize,
    super.isDisabled = false,
    super.isLoading = false,
    super.size,
    this.trailingIcon,
    super.alignment,
    this.tapTargetSize,
    super.key,
  });

  @override
  Size maximumSize(BuildContext context) {
    final height = buttonHeight(context);
    return Size.square(height);
  }

  @override
  EdgeInsetsGeometry padding(BuildContext context) {
    final i = context.insets;
    return i.symmetricSp(
      horizontal: AppFontSizes.px16,
      vertical: AppFontSizes.px8,
    );
  }

  @override
  Widget build(BuildContext context) {
    final borderColor = context.outlineBtnAltBorderColor;
    final iconColor = context.outlineBtnTextColor;
    final color = context.outlineBtnAltBgColor;
    final focusColor = context.outlineBtnAltFocusedBgColor;
    final opacity = context.outlineBtnAltDisabledOpacity;

    final style = baseStyle(context);

    Widget child = IconButton(
      icon: isLoading
          ? const AppSpinner(forceCentering: false)
          : Icon(
              icon,
              color: iconColor,
            ),
      style: style.copyWith(
        iconColor: WidgetStateProperty.all(color),
        tapTargetSize: tapTargetSize,
        shape: WidgetStatePropertyAll(
          ContinuousRectangleBorder(borderRadius: context.xlBorderRadius),
        ),
        backgroundColor: WidgetStateProperty.resolveWith((state) {
          if (state.contains(WidgetState.pressed)) {
            return focusColor;
          }
          return color;
        }),
        side: WidgetStateProperty.all(
          BorderSide(color: borderColor),
        ),
      ),
      onPressed: isDisabled
          ? null
          : () {
              if (isLoading) return;
              HapticFeedback.mediumImpact();
              context.resetFocus();
              onPressed();
            },
    );

    if (alignment != null) {
      child = Align(
        alignment: alignment!,
        child: child,
      );
    }
    return AnimatedOpacity(
      opacity: isDisabled ? opacity : 1,
      duration: 100.milliDuration,
      child: child,
    );
  }
}

class AppCopyButton extends AppStatelessWidget {
  final String value;
  final Color? color;
  final TextAlign align;

  const AppCopyButton({
    required this.value,
    this.color,
    this.align = TextAlign.end,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return AppTextButton(
      textAlign: align,
      size: ButtonSize.small,
      onPressed: () => context.copyText(value),
      icon: AppIcon(
        AppIcons.copy,
        color: color,
      ),
    );
  }
}
