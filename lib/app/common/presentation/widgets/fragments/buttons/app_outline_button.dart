import 'package:day1/day1.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class AppOutlineButton extends AppButtonBase {
  final Widget? icon;
  final String? text;
  final OutlineBtnVariant variant;
  final Widget? trailingIcon;
  final Color? bgColor;
  final Color? borderColor;
  final double? height;
  final EdgeInsetsGeometry? contentPadding;

  const AppOutlineButton({
    this.text,
    required super.onPressed,
    super.minSize,
    this.variant = OutlineBtnVariant.normal,
    super.size = ButtonSize.large,
    this.icon,
    this.bgColor,
    this.borderColor,
    this.contentPadding,
    this.height,
    super.isDisabled = false,
    super.isLoading = false,
    this.trailingIcon,
    super.alignment,
    super.key,
  });

  @override
  double buttonHeight(BuildContext context) {
    if (height != null) return height!;
    return super.buttonHeight(context);
  }

  bool get _isAlt => variant == OutlineBtnVariant.neutral;

  @override
  Widget build(BuildContext context) {
    final bordrColor = borderColor ??
        (_isAlt
            ? context.outlineBtnAltBorderColor
            : context.outlineBtnBorderColor);
    final textColor = context.outlineBtnTextColor;
    final color = bgColor ??
        (_isAlt ? context.outlineBtnAltBgColor : context.outlineBtnBgColor);
    final focusColor = _isAlt
        ? context.outlineBtnAltFocusedBgColor
        : context.outlineBtnFocusedBgColor;
    final opacity = _isAlt
        ? context.outlineBtnAltDisabledOpacity
        : context.outlineBtnDisabledOpacity;

    final style = baseStyle(context);

    Widget child = OutlinedButton(
      style: style.copyWith(
        backgroundColor: WidgetStateProperty.resolveWith((state) {
          if (state.contains(WidgetState.pressed)) {
            return focusColor;
          }
          return color;
        }),
        side: WidgetStateProperty.all(
          BorderSide(color: bordrColor),
        ),
        padding: WidgetStatePropertyAll(contentPadding ?? padding(context)),
      ),
      onPressed: isDisabled
          ? null
          : () {
              if (isLoading) return;
              HapticFeedback.mediumImpact();
              context.resetFocus();
              onPressed();
            },
      child: ButtonText(
        text,
        flat: true,
        disabled: isDisabled,
        buttonSize: size,
        icon: isLoading ? const AppSpinner() : icon,
        trailingIcon: trailingIcon,
        alignment: alignment,
        decoration: TextDecoration.none,
        textColor: textColor,
      ),
    );

    if (alignment != null) {
      child = Align(
        alignment: alignment!,
        child: child,
      );
    }

    return AnimatedOpacity(
      opacity: isDisabled ? opacity : 1,
      duration: 100.milliDuration,
      child: child,
    );
  }
}
