import 'package:day1/day1.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class AppButton extends AppButtonBase {
  final String? text;
  final RaisedButtonVariant variant;
  final IconData? icon;
  final IconData? trailingIcon;
  final Color? bgColor;
  final Color? borderColor;
  final EdgeInsetsGeometry? contentPadding;

  const AppButton({
    this.text,
    required super.onPressed,
    super.minSize,
    this.variant = RaisedButtonVariant.a,
    super.size = ButtonSize.large,
    this.icon,
    this.bgColor,
    this.borderColor,
    super.isDisabled = false,
    super.isLoading = false,
    this.contentPadding,
    this.trailingIcon,
    super.alignment,
    super.key,
  });

  Color _textColor(BuildContext context) {
    if (isDisabled) return context.disabledBtntextColor;
    return switch (variant) {
      RaisedButtonVariant.b ||
      RaisedButtonVariant.c =>
        context.raisedBtnTextColor,
      _ => context.textBtnTextColor,
    };
  }

  Color _bgColor(BuildContext context) {
    if (isDisabled) return context.disabledBtnColor;
    if (bgColor != null) return bgColor!;
    return switch (variant) {
      RaisedButtonVariant.b => context.raisedBtnBBgColor,
      RaisedButtonVariant.c => context.raisedBtnCBgColor,
      _ => context.raisedBtnBgColor,
    };
  }

  Color _focusColor(BuildContext context) {
    if (isDisabled) return context.disabledBtnColor;
    return switch (variant) {
      RaisedButtonVariant.b => context.raisedBtnBFocusedBgColor,
      RaisedButtonVariant.c => context.raisedBtnCFocusedBgColor,
      _ => context.raisedBtnFocusedBgColor,
    };
  }

  Color _borderColor(BuildContext context) {
    if (isDisabled) return context.disabledBtnColor;
    if (borderColor != null) return borderColor!;
    return switch (variant) {
      RaisedButtonVariant.b => context.raisedBtnBBgColor,
      RaisedButtonVariant.c => context.raisedBtnCBgColor,
      _ => context.raisedBtnBorderColor,
    };
  }

  @override
  Widget build(BuildContext context) {
    final textColor = _textColor(context);
    final color = _bgColor(context);
    final focusColor = _focusColor(context);
    final border = _borderColor(context);
    final style = baseStyle(context);
    Widget? leading;
    Widget? trailing;

    if (icon != null) {
      leading = AppIcon(icon!, color: textColor);
    }

    if (trailingIcon != null) {
      trailing = AppIcon(trailingIcon!, color: textColor);
    }

    Widget child = ElevatedButton(
      style: style.copyWith(
        backgroundColor: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.pressed)) {
            return focusColor;
          }
          return color;
        }),
        side: WidgetStateProperty.resolveWith(
          (states) {
            if (states.contains(WidgetState.pressed)) {
              return BorderSide(color: color);
            }
            return BorderSide(color: border);
          },
        ),
        padding: WidgetStatePropertyAll(
          contentPadding ?? padding(context),
        ),
      ),
      onPressed: isDisabled
          ? null
          : () {
              if (isLoading) return;
              HapticFeedback.mediumImpact();
              context.resetFocus();
              onPressed();
            },
      child: ButtonText(
        text,
        flat: variant == RaisedButtonVariant.a,
        disabled: isDisabled,
        icon: isLoading ? const AppSpinner() : leading,
        trailingIcon: trailing,
        alignment: alignment,
        buttonSize: size,
      ),
    );

    if (alignment != null) {
      child = Align(
        alignment: alignment!,
        child: child,
      );
    }

    return AnimatedOpacity(
      opacity: isDisabled ? context.raisedBtnDisabledOpacity : 1,
      duration: 100.milliDuration,
      child: child,
    );
  }
}
