import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class AppTextButton extends AppButtonBase {
  final String? text;
  final TextDecoration? decoration;
  final MaterialTapTargetSize? tapTargetSize;
  final TextAlign textAlign;
  final FontWeight? weight;
  final Widget? icon;
  final Widget? trailingIcon;

  const AppTextButton({
    this.text,
    required super.onPressed,
    super.minSize,
    super.color,
    super.isDisabled = false,
    super.isLoading = false,
    super.size,
    this.icon,
    this.trailingIcon,
    this.weight,
    this.decoration,
    this.tapTargetSize,
    this.textAlign = TextAlign.center,
    super.key,
  });

  AlignmentGeometry get _alignment {
    return switch (textAlign) {
      TextAlign.end || TextAlign.right => Alignment.centerRight,
      TextAlign.start || TextAlign.left => Alignment.centerLeft,
      _ => Alignment.center,
    };
  }

  @override
  Widget build(BuildContext context) {
    final style = baseStyle(context);

    if (isLoading) {
      return Align(
        alignment: _alignment,
        child: const AppSpinner(forceCentering: false),
      );
    }

    Widget child = AnimatedOpacity(
      opacity: isDisabled ? .4 : 1,
      duration: 100.milliDuration,
      child: TextButton(
        style: style.copyWith(
          padding: const WidgetStatePropertyAll(EdgeInsets.zero),
          minimumSize: const WidgetStatePropertyAll(Size.zero),
        ),
        onPressed: isDisabled
            ? null
            : () {
                if (isLoading) return;
                onPressed();
              },
        child: ButtonText(
          text,
          decoration: decoration,
          flat: true,
          textColor: color,
          alignment: _alignment,
          disabled: isDisabled,
          buttonSize: size,
          icon: isLoading ? const AppSpinner(forceCentering: false) : icon,
          trailingIcon: trailingIcon,
        ),
      ),
    );

    if (_alignment != Alignment.center) {
      return Align(
        alignment: _alignment,
        child: child,
      );
    }

    return child;
  }
}
