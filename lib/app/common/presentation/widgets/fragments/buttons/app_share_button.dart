import 'dart:io';

import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class AppShare<PERSON>utton extends AppStatelessWidget {
  final String? title;
  final String? value;
  final String? text;
  final bool hasTrailingIcon;
  final List<File>? files;
  final ButtonSize? size;

  const AppShareButton({
    super.key,
    this.files,
    this.text,
    this.title,
    this.value,
    this.size,
    this.hasTrailingIcon = false,
  }) : assert(!(files == null && text == null));

  @override
  Widget build(BuildContext context) {
    const icon = AppIcon(AppIcons.shareIos);
    return AppTextButton(
      onPressed: () {
        if (value.hasValue) context.shareText(value.value, title: title);
        if (files.hasValue) context.shareFile(files.value, title: title);
      },
      icon: hasTrailingIcon ? null : icon,
      trailingIcon: hasTrailingIcon ? icon : null,
      text: text,
      size: size ?? ButtonSize.large,
    );
  }
}
