import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

enum ButtonSize { small, medium, large }

enum RaisedButtonVariant { a, b, c }

enum OutlineBtnVariant { normal, neutral }

abstract class AppButtonBase extends AppStatelessWidget {
  final OnPressed onPressed;
  final ButtonSize size;
  final Size? minSize;
  final bool isDisabled;
  final bool isLoading;
  final Color? color;
  final double? borderRadius;
  final AlignmentGeometry? alignment;

  const AppButtonBase({
    required this.onPressed,
    this.minSize,
    this.size = ButtonSize.large,
    this.color,
    this.borderRadius,
    this.isDisabled = false,
    this.isLoading = false,
    this.alignment,
    super.key,
  });

  double buttonHeight(BuildContext context) {
    return switch (size) {
      ButtonSize.small => context.smButtonHeight,
      ButtonSize.medium => context.mdButtonHeight,
      ButtonSize.large => context.lgButtonHeight,
    };
  }

  Size minimumSize(BuildContext context) {
    final height = buttonHeight(context);
    final width = minSize?.width;
    return switch (size) {
      ButtonSize.small => Size(width ?? context.sp(AppFontSizes.px68), height),
      ButtonSize.medium => Size(width ?? context.sp(AppFontSizes.px80), height),
      ButtonSize.large => Size(width ?? context.sp(AppFontSizes.px120), height),
    };
  }

  Size maximumSize(BuildContext context) {
    final height = buttonHeight(context);
    return switch (size) {
      ButtonSize.small => Size(double.infinity, height),
      ButtonSize.medium => Size(double.infinity, height),
      ButtonSize.large => Size(double.infinity, height),
    };
  }

  EdgeInsetsGeometry padding(BuildContext context) {
    final i = context.insets;
    return switch (size) {
      ButtonSize.small => i.symmetricSp(horizontal: AppFontSizes.px12),
      ButtonSize.medium => i.symmetricSp(horizontal: AppFontSizes.px16),
      ButtonSize.large => i.symmetricSp(horizontal: AppFontSizes.px24),
    };
  }

  ButtonStyle baseStyle(BuildContext context) {
    final height = buttonHeight(context);
    final minSize = minimumSize(context);
    final maxSize = maximumSize(context);
    final hPadding = padding(context);
    final radius = borderRadius != null
        ? BorderRadius.circular(borderRadius!)
        : context.btnBorderRadius;
    final shape = RoundedRectangleBorder(borderRadius: radius);

    return ButtonStyle(
      minimumSize: WidgetStatePropertyAll(minSize),
      fixedSize: WidgetStatePropertyAll(Size.fromHeight(height)),
      maximumSize: WidgetStatePropertyAll(maxSize),
      elevation: WidgetStateProperty.all(0),
      shape: WidgetStateProperty.all(shape),
      padding: WidgetStateProperty.all(hPadding),
      // alignment: alignment,
      tapTargetSize: MaterialTapTargetSize.shrinkWrap,
    );
  }
}
