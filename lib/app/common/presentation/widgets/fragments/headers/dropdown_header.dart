import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class AppDropdownHeader extends AppStatelessWidget {
  final String title;
  final double bottomMargin;

  const AppDropdownHeader({
    required this.title,
    this.bottomMargin = 2,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        const AppBackButton(
          key: Key("selection_sheet_back_button"),
        ),
        const AppGap.h4(),
        Expanded(
          child: AppText(
            title,
            style: context.textStyle.h4(),
          ),
        ),
      ],
    );
  }
}
