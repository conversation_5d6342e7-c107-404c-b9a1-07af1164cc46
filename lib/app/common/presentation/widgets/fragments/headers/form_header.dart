import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class AppFormHeader extends AppStatelessWidget {
  final String title;
  final String? subTitle;
  final TextStyle? subTitleStyle;
  final TextStyle? titleStyle;
  final EdgeInsets? padding;
  final AppGap bottomGap;
  final AppGap topGap;

  const AppFormHeader({
    required this.title,
    this.subTitle,
    this.subTitleStyle,
    this.titleStyle,
    this.padding,
    this.bottomGap = const AppGap.y24(),
    this.topGap = const AppGap.zero(),
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final margin = padding ?? EdgeInsets.zero;
    final style = titleStyle ?? context.textStyle.h4();
    final subStyle = subTitleStyle ?? context.textStyle.labelText();

    return Padding(
      padding: margin,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          topGap,
          AppText(title, style: style),
          if (subTitle != null) ...[
            const AppGap.y4(),
            AppRichText(subTitle, textStyle: subStyle)
          ],
          bottomGap,
        ],
      ),
    );
  }
}
