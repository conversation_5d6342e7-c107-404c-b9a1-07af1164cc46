import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class AppInfoHeader extends AppStatelessWidget {
  final String title;

  const AppInfoHeader({super.key, required this.title});

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Expanded(
          child: AppText(
            title,
            style: context.textStyle.b2(weight: FontWeight.w500),
          ),
        ),
        const AppIcon(AppIcons.infoCircle, size: AppFontSizes.px20),
      ],
    );
  }
}
