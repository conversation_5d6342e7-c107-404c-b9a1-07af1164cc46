import 'package:day1/day1.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class AppPassword<PERSON>ield extends AppStatefulWidget {
  final TextEditingController controller;
  final String? hintText;
  final bool isEnabled;
  final bool autoFocus;
  final FocusNode? focusNode;
  final int minLength;
  final OnValidate<String?>? validator;
  final OnChanged<String?>? onFieldSubmitted;
  final TextInputAction action;

  const AppPasswordField({
    required this.controller,
    this.hintText,
    this.autoFocus = false,
    this.isEnabled = true,
    this.focusNode,
    this.minLength = 8,
    this.validator,
    this.action = TextInputAction.next,
    this.onFieldSubmitted,
    super.key,
  });

  @override
  State<StatefulWidget> createState() => _AppPasswordFieldState();
}

class _AppPasswordFieldState extends State<AppPasswordField> {
  late ValueNotifier<bool> hidePass;

  @override
  void initState() {
    super.initState();
    hidePass = ValueNotifier(true);
  }

  @override
  void dispose() {
    super.dispose();
    hidePass.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BoolListener(
      valueListenable: hidePass,
      builder: (passwordHidden) {
        final icon = passwordHidden ? AppIcons.eyeClosed : AppIcons.eye;
        return AppTextField(
          autoCorrect: false,
          autoFocus: widget.autoFocus,
          focusNode: widget.focusNode,
          hintText: widget.hintText,
          textCapitalization: TextCapitalization.none,
          obscureText: passwordHidden,
          controller: widget.controller,
          textInputAction: widget.action,
          onFieldSubmitted: widget.onFieldSubmitted,
          validator: widget.validator ??
              (password) => AppValidators.passwordValidator(
                    password,
                    minLength: widget.minLength,
                  ),
          suffixIcon: InkWell(
            child: Padding(
              padding: context.insets.onlySp(right: AppFontSizes.px16),
              child: AppAnimatedSwitcher(
                child: AppIcon(
                  icon,
                  key: ValueKey(icon),
                  color: context.hintTextColor,
                ),
              ),
            ),
            onTap: () {
              HapticFeedback.lightImpact();
              hidePass.value = !passwordHidden;
            },
          ),
          keyboardType: TextInputType.visiblePassword,
          isEnabled: widget.isEnabled,
          autofillHints: const [AutofillHints.password],
        );
      },
    );
  }
}
