import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class AppAsyncDropdown extends AppStatefulWidget {
  final ValueNotifier<FutureListData<AppSelectionResponse>> notifier;
  final ValueNotifier<SelectionData<AppSelectionResponse>?> selection;
  final FocusNode? focusNode;
  final bool isEnabled;
  final bool hideEmpty;
  final bool? allowSearch;
  final double bottomMargin;
  final bool showId;
  final String label;
  final String hintText;

  const AppAsyncDropdown({
    required this.notifier,
    required this.selection,
    required this.label,
    required this.hintText,
    this.allowSearch,
    this.bottomMargin = 0,
    this.hideEmpty = false,
    this.showId = false,
    this.isEnabled = true,
    this.focusNode,
    super.key,
  });

  @override
  State<StatefulWidget> createState() => _AppAsyncDropdownState();
}

class _AppAsyncDropdownState extends State<AppAsyncDropdown> {
  @override
  Widget build(BuildContext context) {
    return GenericListener(
      valueListenable: widget.notifier,
      builder: (notifier) {
        Widget? suffix;

        if (notifier.isLoading) {
          suffix = const AppSpinner();
        }

        if (!notifier.hasData && widget.hideEmpty) {
          return const Offstage();
        }

        return Padding(
          padding: context.insets.only(bottom: widget.bottomMargin),
          child: AppDropDownField.withSelection(
            allowSearch: widget.allowSearch ?? notifier.data.length >= 10,
            label: widget.label,
            hintText: widget.hintText,
            selection: widget.selection,
            validator: AppValidators.required,
            suffix: suffix,
            options: [
              for (final item in notifier.data)
                item.selectionData(includeId: widget.showId)
            ],
            focusNode: widget.focusNode,
            isEnabled: widget.isEnabled,
          ),
        );
      },
    );
  }
}
