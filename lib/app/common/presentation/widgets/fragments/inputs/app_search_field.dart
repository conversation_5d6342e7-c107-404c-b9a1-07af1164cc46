import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class AppSearchField<T> extends AppStatefulWidget {
  final TextEditingController? controller;
  final FocusNode? focusNode;
  final bool isEnabled;
  final TextInputAction? textInputAction;
  final Color? fillColor;
  final bool autoFocus;
  final String? hintText;
  final OnValidate<String?>? validator;
  final OnChanged<String?>? onChanged;
  final OnChanged<String?>? onFieldSubmitted;

  const AppSearchField({
    super.key,
    this.focusNode,
    this.textInputAction = TextInputAction.next,
    this.controller,
    this.isEnabled = true,
    this.autoFocus = false,
    this.hintText,
    this.fillColor,
    this.onChanged,
    this.validator,
    this.onFieldSubmitted,
  });

  @override
  State createState() => _AppSearchFieldState();
}

class _AppSearchFieldState extends State<AppSearchField>
    with WidgetsBindingObserver, AppTaskMixin {
  @override
  Widget build(BuildContext context) {
    final prefixPadding = context.insets.onlySp(
      left: AppFontSizes.px16,
      right: AppFontSizes.px12,
    );

    return AppTextField(
      key: const Key("bank_search_input"),
      fillColor: widget.fillColor,
      textInputAction: TextInputAction.search,
      validator: widget.validator,
      prefixIcon: Padding(
        padding: prefixPadding,
        child: AppIcon(
          AppIcons.search,
          color: context.hintTextColor,
        ),
      ),
      autoFocus: widget.autoFocus,
      onChanged: widget.onChanged,
      controller: widget.controller,
      hintText: widget.hintText,
    );
  }
}
