import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class AppAmountField extends AppStatefulWidget {
  final TextEditingController controller;
  final String hint;
  final String? currency;
  final OnChanged<String?>? onChange;
  final bool isEnabled;
  final bool isRequired;
  final Color? fillColor;
  final Widget? helper;
  final num? min;
  final num? max;
  final FocusNode? focusNode;

  const AppAmountField({
    super.key,
    required this.controller,
    this.currency,
    this.hint = "Enter amount",
    this.onChange,
    this.isRequired = true,
    this.focusNode,
    this.helper,
    this.fillColor,
    this.isEnabled = true,
    this.max,
    this.min,
  });
  @override
  State<AppAmountField> createState() => _AppAmountFieldState();
}

class _AppAmountFieldState extends State<AppAmountField> {
  @override
  Widget build(BuildContext context) {
    Widget? suffix;

    if (widget.currency != null) {
      suffix = Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          AppText(
            widget.currency,
            style: context.textStyle.b3(weight: FontWeight.w600),
          ),
          const AppGap.h8(),
          const AppIcon(AppIcons.navArrowDown)
        ],
      );
    }
    return AppTextField(
      focusNode: widget.focusNode,
      isEnabled: widget.isEnabled,
      helper: widget.helper,
      hintText: widget.hint,
      fillColor: widget.fillColor,
      inputFormatters: [AppAmountFormatter()],
      controller: widget.controller,
      suffix: suffix,
      validator: (text) => AppValidators.amountValidator(
        text,
        isRequired: widget.isRequired,
        maxAmount: widget.max,
        minAmount: widget.min,
      ),
      keyboardType: const TextInputType.numberWithOptions(decimal: true),
      onChanged: widget.onChange,
      autofillHints: const [AutofillHints.transactionAmount],
      trackKeyboardState: true,
    );
  }
}
