import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class AppEmail<PERSON>ield extends AppStatefulWidget {
  final TextEditingController controller;
  final String? hintText;
  final bool isRequired;
  final bool isEnabled;
  final bool autoFocus;
  final AutovalidateMode? autovalidateMode;
  final FocusNode? focusNode;
  final TextInputAction action;
  final OnChanged<String?>? onFieldSubmitted;

  const AppEmailField({
    super.key,
    required this.controller,
    this.autovalidateMode,
    this.hintText = "<EMAIL>",
    this.isEnabled = true,
    this.isRequired = true,
    this.autoFocus = false,
    this.action = TextInputAction.next,
    this.onFieldSubmitted,
    this.focusNode,
  });

  @override
  State<AppEmailField> createState() => _AppEmailFieldState();
}

class _AppEmailFieldState extends State<AppEmailField> {
  @override
  Widget build(BuildContext context) {
    return AppText<PERSON>ield(
      autoCorrect: false,
      autovalidateMode: widget.autovalidateMode,
      focusNode: widget.focusNode,
      autoFocus: widget.autoFocus,
      isEnabled: widget.isEnabled,
      controller: widget.controller,
      hintText: widget.hintText,
      keyboardType: TextInputType.emailAddress,
      textInputAction: widget.action,
      onFieldSubmitted: widget.onFieldSubmitted,
      validator: (text) => AppValidators.emailValidator(
        text,
        isRequired: widget.isRequired,
      ),
      prefixIcon: ValueListenableBuilder(
        valueListenable: widget.controller,
        builder: (context, TextEditingValue val, _) {
          final bool hasValue = val.text.hasValue;

          final color = hasValue ? null : context.hintTextColor;

          return Padding(
            padding: context.insets.onlySp(
              left: AppFontSizes.px16,
              right: AppFontSizes.px12,
            ),
            child: AppIcon(AppIcons.mail, color: color),
          );
        },
      ),
      suffixIcon: ValueListenableBuilder(
        valueListenable: widget.controller,
        builder: (context, TextEditingValue val, _) {
          if (!val.text.hasValue) return const Offstage();

          final bool hasMatch = AppRegex.mailRegEx.hasMatch(val.text);

          final icon = hasMatch ? AppIcons.check : AppIcons.warningTriangle;

          return Padding(
            padding: context.insets.onlySp(right: AppFontSizes.px16),
            child: AppAnimatedSwitcher(
              child: AppIcon(
                icon,
                color: hasMatch ? context.textColor : context.errorTextColor,
                key: ValueKey(icon),
              ),
            ),
          );
        },
      ),
      autofillHints: const [AutofillHints.email],
    );
  }
}
