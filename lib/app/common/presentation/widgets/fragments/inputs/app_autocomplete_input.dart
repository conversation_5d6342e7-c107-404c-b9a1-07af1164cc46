import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class AppAutocompleteField<T> extends AppStatefulWidget {
  final bool isEnabled;
  final bool isLoading;
  final Duration debounceTime;
  final InputDecoration? decoration;
  final String hintText;
  final String? label;
  final Widget? prefix;
  final Widget? prefixIcon;
  final Widget? suffix;
  final Widget? suffixIcon;
  final OnValidate<String?>? validator;
  final OnChanged<String?>? onChanged;
  final OnChanged<SelectionData<T>>? onSelect;
  final OnChanged<String?>? onFieldSubmitted;
  final TextStyle? style;
  final TextAlign? textAlign;
  final TextEditingController? controller;
  final String? semanticshintText;
  final ValueNotifier<List<SelectionData<T>>> options;
  final ValueNotifier<SelectionData<T>?>? selection;
  final FocusNode? focusNode;
  final double contentPaddingHorizontal;
  final double contentPaddingVertical;
  final TextInputAction? textInputAction;
  final TextCapitalization? textCapitalization;

  TextEditingController get ctrl {
    return controller ?? TextEditingController(text: selection?.value?.label);
  }

  const AppAutocompleteField({
    super.key,
    this.controller,
    this.suffixIcon,
    this.onSelect,
    this.contentPaddingHorizontal = 3,
    this.contentPaddingVertical = 1.7,
    this.textInputAction,
    this.textCapitalization,
    required this.debounceTime,
    this.semanticshintText,
    this.decoration,
    required this.selection,
    this.isEnabled = true,
    this.isLoading = false,
    required this.hintText,
    this.label,
    this.textAlign = TextAlign.left,
    this.onChanged,
    this.onFieldSubmitted,
    this.prefix,
    this.prefixIcon,
    this.suffix,
    this.validator,
    this.style,
    this.focusNode,
    required this.options,
  });

  @override
  State<AppAutocompleteField> createState() => _AppAutocompleteTextFieldState();

  ValueNotifier<SelectionData<T>?>? get selectionRef => selection;

  static ValueNotifier<SelectionData<K>?> getControllerSelection<K>(
    TextEditingController? ctrl,
    List<SelectionData<K>> options,
  ) {
    final selection = options.tryFirstWhere((it) => it.label == ctrl?.text);
    return ValueNotifier(selection);
  }
}

class _AppAutocompleteTextFieldState extends State<AppAutocompleteField>
    with WidgetsBindingObserver, AppModalMixin {
  late FocusNode _inputFocus;
  late ValueNotifier<SelectionData?> _selectionRef;
  late TextEditingController _controller;
  late OverlayPortalController _overlayController;
  late final Debouncer _debouncer;

  @override
  void initState() {
    super.initState();

    _debouncer = Debouncer(widget.debounceTime);
    _selectionRef = widget.selectionRef ?? ValueNotifier(null);
    _controller = widget.ctrl;
    _overlayController = OverlayPortalController();
    _inputFocus = widget.focusNode ?? FocusNode();

    _selectionRef.addListener(() {
      WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
        _controller.text = _selectionRef.value?.label ?? '';
      });
    });

    _trackController();
  }

  _trackController() {
    widget.options.addListener(() {
      if (widget.options.value.hasValue && _controller.hasValue) {
        _overlayController.show();
      } else {
        _overlayController.hide();
      }
    });
  }

  _onSelect(SelectionData? option) {
    if (option != null) {
      widget.onSelect?.call(option);
    }
    _selectionRef.value = option;
    _overlayController.hide();
  }

  _onChange(String? query) async {
    if (widget.onChanged == null || widget.isLoading) return;
    _debouncer.abort();
    _debouncer.run(() {
      widget.onChanged?.call(query);
    });
  }

  @override
  void dispose() {
    _debouncer.abort();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final xOffset = context.sizer.width * .5;
    final topMargin = context.sp(AppFontSizes.px10);

    return AppOverlayPortal(
      overlayOffSet: Offset(-xOffset, topMargin),
      controller: _overlayController,
      overlayChild: ListenableBuilder(
        listenable: Listenable.merge([_selectionRef, widget.options]),
        builder: (_, __) {
          final options = widget.options.value;
          final selection = _selectionRef.value;
          if (!(options.hasValue && _controller.text.length >= 2)) {
            return const Offstage();
          }
          return Align(
            alignment: Alignment.topCenter,
            child: AutocompleteSuggestionView(
              onSelect: _onSelect,
              suggestions: options,
              selection: selection,
            ),
          );
        },
      ),
      child: AppTextField(
        focusNode: _inputFocus,
        decoration: widget.decoration,
        controller: _controller,
        hintText: widget.hintText,
        label: widget.label,
        prefix: widget.prefix,
        prefixIcon: widget.prefixIcon,
        isEnabled: widget.isEnabled,
        validator: widget.validator,
        textAlign: widget.textAlign,
        style: widget.style,
        onChanged: _onChange,
        onFieldSubmitted: widget.onFieldSubmitted,
        textCapitalization: widget.textCapitalization,
        textInputAction: widget.textInputAction,
        contentPaddingVertical: widget.contentPaddingVertical,
        contentPaddingHorizontal: widget.contentPaddingHorizontal,
        suffix: widget.suffix,
        suffixIcon: Padding(
          padding: context.insets.only(right: 2),
          child: AppAnimatedSwitcher(
            key: ValueKey(widget.suffix != null),
            child: Builder(
              builder: (_) {
                if (widget.isLoading) {
                  return const AppSpinner(forceCentering: false);
                }
                if (widget.suffixIcon != null) return widget.suffixIcon!;
                return const Offstage();
              },
            ),
          ),
        ),
      ),
    );
  }
}

class AutocompleteSuggestionView extends AppStatelessWidget {
  final OnChanged<SelectionData> onSelect;
  final List<SelectionData> suggestions;
  final SelectionData? selection;

  const AutocompleteSuggestionView({
    required this.onSelect,
    required this.suggestions,
    required this.selection,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final paddding = context.insets.defaultHorizontalInsets;

    return Container(
      decoration: BoxDecoration(
        color: context.cardColor,
        border: Border.all(color: context.cardBorderColor),
        borderRadius: context.lgBorderRadius,
      ),
      margin: paddding,
      padding: paddding,
      child: ListView.separated(
        shrinkWrap: true,
        padding: context.insets.symmetricSp(vertical: spacing.spacing12),
        separatorBuilder: (_, index) {
          return InkWell(
            onTap: () => onSelect(suggestions[index]),
            child: const AppDivider(size: AppDividerSize.small),
          );
        },
        itemCount: suggestions.length,
        itemBuilder: (_, index) {
          final option = suggestions[index];
          return SelectionTile(
            icon: option.icon,
            title: option.extendedLabel ?? option.label,
            key: ValueKey([index, option.label]),
            onTap: () => onSelect(option),
          );
        },
      ),
    );
  }
}
