import 'package:day1/day1.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class AppNumberField extends AppStatefulWidget {
  final String? hint;
  final List<TextInputFormatter>? formatters;
  final TextEditingController? controller;
  final Widget? prefixIcon;
  final String? hintText;
  final bool isEnabled;
  final bool isLoading;
  final bool autoFocus;
  final Color? fillColor;
  final int? maxLength;
  final AutovalidateMode? autovalidateMode;
  final OnValidate<String?>? validator;
  final TextInputType? keyboardType;
  final FocusNode? focusNode;
  final TextInputAction action;
  final OnChanged<String?>? onFieldSubmitted;
  final OnChanged<String?>? onChanged;

  const AppNumberField({
    super.key,
    this.controller,
    this.autovalidateMode,
    this.prefixIcon,
    this.maxLength,
    this.fillColor,
    this.formatters,
    this.hintText,
    this.keyboardType,
    this.onChanged,
    this.isEnabled = true,
    this.isLoading = false,
    this.hint,
    this.validator,
    this.autoFocus = false,
    this.action = TextInputAction.next,
    this.onFieldSubmitted,
    this.focusNode,
  });

  @override
  State<AppNumberField> createState() => _AppNumberFieldState();
}

class _AppNumberFieldState extends State<AppNumberField> {
  @override
  Widget build(BuildContext context) {
    Widget? prefix;

    if (widget.isLoading) {
      prefix = Padding(
        padding: context.insets.symmetricSp(horizontal: AppFontSizes.px16),
        child: const AppSpinner(
          forceCentering: false,
        ),
      );
    }

    return AppTextField(
      autoCorrect: false,
      autovalidateMode: widget.autovalidateMode,
      focusNode: widget.focusNode,
      autoFocus: widget.autoFocus,
      isEnabled: widget.isEnabled,
      controller: widget.controller,
      fillColor: widget.fillColor,
      hintText: widget.hintText,
      onChanged: widget.onChanged,
      keyboardType:
          widget.keyboardType ?? const TextInputType.numberWithOptions(),
      textInputAction: widget.action,
      maxLength: widget.maxLength,
      onFieldSubmitted: widget.onFieldSubmitted,
      validator: widget.validator,
      prefixIcon: prefix ?? widget.prefixIcon,
      inputFormatters: [
        FilteringTextInputFormatter.singleLineFormatter,
        ...(widget.formatters ?? [])
      ],
      autofillHints: [if (widget.hint.hasValue) widget.hint!],
    );
  }
}

extension on String? {
  String get cardInputPrefix {
    final number = value.withoutWhiteSpaceAndSpecialChar;
    if (AppRegex.masterPrefixRegex.hasMatch(number)) {
      return AppVectors.mastercard;
    }
    if (AppRegex.visaPrefixRegex.hasMatch(number)) return AppVectors.visa;
    if (AppRegex.maestroPrefixRegex.hasMatch(number)) return AppVectors.maestro;
    if (AppRegex.jcbPrefixRegex.hasMatch(number)) return AppVectors.jcb;
    if (AppRegex.unionpayPrefixRegex.hasMatch(number)) {
      return AppVectors.unionpay;
    }
    if (AppRegex.amexPrefixRegex.hasMatch(number)) return AppVectors.amex;

    return AppVectors.paymentCard;
  }
}

class AppCardNumberField extends AppStatefulWidget {
  final TextEditingController controller;
  final bool isEnabled;
  final bool autoFocus;
  final AutovalidateMode? autovalidateMode;
  final FocusNode? focusNode;
  final TextInputAction action;
  final OnChanged<String?>? onFieldSubmitted;

  const AppCardNumberField({
    super.key,
    required this.controller,
    this.autovalidateMode,
    this.isEnabled = true,
    this.autoFocus = false,
    this.action = TextInputAction.next,
    this.onFieldSubmitted,
    this.focusNode,
  });

  @override
  State createState() => _AppCardNumberFieldState();
}

class _AppCardNumberFieldState extends State<AppCardNumberField> {
  @override
  Widget build(BuildContext context) {
    return AppNumberField(
      autovalidateMode: widget.autovalidateMode,
      focusNode: widget.focusNode,
      autoFocus: widget.autoFocus,
      isEnabled: widget.isEnabled,
      controller: widget.controller,
      hintText: LocaleKeys.cardNum.tr(),
      onFieldSubmitted: widget.onFieldSubmitted,
      validator: AppValidators.cardNumberValidator,
      prefixIcon: GenericListener(
        valueListenable: widget.controller,
        builder: (val) {
          final bool hasValue = val.text.hasValue;

          final color = hasValue ? null : context.hintTextColor;

          return AppAnimatedSwitcher(
            child: Padding(
              key: ValueKey(val.text.cardInputPrefix),
              padding: context.insets.onlySp(
                left: AppFontSizes.px16,
                right: AppFontSizes.px12,
              ),
              child: AppSvg(
                val.text.cardInputPrefix,
                color: color,
                width: context.sp(AppFontSizes.px18),
              ),
            ),
          );
        },
      ),
      formatters: [CardInputFormatter()],
      hint: AutofillHints.creditCardNumber,
    );
  }
}

class AppCvvField extends AppStatefulWidget {
  final TextEditingController controller;
  final bool isEnabled;
  final bool autoFocus;
  final AutovalidateMode? autovalidateMode;
  final FocusNode? focusNode;
  final TextInputAction action;
  final OnChanged<String?>? onFieldSubmitted;

  const AppCvvField({
    super.key,
    required this.controller,
    this.autovalidateMode,
    this.isEnabled = true,
    this.autoFocus = false,
    this.action = TextInputAction.next,
    this.onFieldSubmitted,
    this.focusNode,
  });

  @override
  State createState() => _AppCvvFieldState();
}

class _AppCvvFieldState extends State<AppCvvField> {
  @override
  Widget build(BuildContext context) {
    return AppNumberField(
      autovalidateMode: widget.autovalidateMode,
      focusNode: widget.focusNode,
      autoFocus: widget.autoFocus,
      isEnabled: widget.isEnabled,
      controller: widget.controller,
      hintText: "CVV/CVC",
      maxLength: 3,
      onFieldSubmitted: widget.onFieldSubmitted,
      validator: AppValidators.cvvValidator,
      prefixIcon: Padding(
        padding: context.insets.onlySp(
          left: AppFontSizes.px16,
          right: AppFontSizes.px12,
        ),
        child: AppIcon(AppIcons.mastercardCard, color: context.hintTextColor),
      ),
      formatters: [FilteringTextInputFormatter.digitsOnly],
      hint: AutofillHints.creditCardSecurityCode,
    );
  }
}
