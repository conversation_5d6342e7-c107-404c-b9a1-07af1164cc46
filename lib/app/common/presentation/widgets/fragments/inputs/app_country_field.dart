import 'package:day1/day1.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

class AppCountryField extends AppStatefulWidget {
  final TextEditingController controller;
  final bool isEnabled;
  final InputDecoration? decoration;
  final String hintText;
  final String label;
  final Widget? prefix;
  final OnValidate<String?>? validator;
  final OnChanged<String?>? onChanged;
  final TextStyle? style;
  final TextAlign? textAlign;
  final String? semanticshintText;
  final FocusNode? focusNode;

  const AppCountryField({
    super.key,
    this.semanticshintText,
    this.decoration,
    required this.controller,
    this.isEnabled = true,
    required this.hintText,
    required this.label,
    this.textAlign = TextAlign.left,
    this.onChanged,
    this.prefix,
    this.validator,
    this.style,
    this.focusNode,
  });

  @override
  State<AppCountryField> createState() => _AppCountryFieldState();
}

class _AppCountryFieldState extends State<AppCountryField>
    with WidgetsBindingObserver, AppModalMixin {
  late final FocusNode _inputFocus;
  final ValueNotifier<FocusState> _hasFocus = ValueNotifier(const FocusState());

  @override
  void initState() {
    super.initState();
    _inputFocus = widget.focusNode ?? FocusNode();

    _inputFocus.addListener(
      () {
        _hasFocus.value = FocusState(
          hasText: widget.controller.text.isNotEmpty,
          isFocused: _inputFocus.hasFocus,
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final state = context.read<CountryState>();

    return ValueListenableBuilder<Future<List<Country>>?>(
      valueListenable: state.countriesTask,
      builder: (_, future, __) {
        return FutureBuilder<List<Country>>(
          future: future,
          builder: (_, task) {
            if (task.isLoaded && (task.data?.isNotEmpty ?? false)) {
              return AppDropDownField<String>(
                allowSearch: true,
                hintText: widget.hintText,
                label: widget.label,
                options: [
                  for (final Country country in (task.data ?? []))
                    SelectionData(
                      selection: country.countryName ?? "",
                      label: country.countryName ?? "",
                    )
                ],
                controller: widget.controller,
                isEnabled: widget.isEnabled,
                semanticshintText: widget.semanticshintText,
                decoration: widget.decoration,
                textAlign: widget.textAlign,
                onChanged: widget.onChanged,
                prefix: widget.prefix,
                validator: widget.validator,
                style: widget.style,
                focusNode: widget.focusNode,
              );
            }
            return AppTextField(
              hintText: widget.hintText,
              controller: widget.controller,
              isEnabled: widget.isEnabled,
              decoration: widget.decoration,
              textAlign: widget.textAlign,
              onChanged: widget.onChanged,
              prefix: widget.prefix,
              validator: widget.validator,
              style: widget.style,
              focusNode: widget.focusNode,
            );
          },
        );
      },
    );
  }
}
