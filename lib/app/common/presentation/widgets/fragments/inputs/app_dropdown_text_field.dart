import 'package:day1/day1.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class AppDropDownField<T> extends AppStatefulWidget {
  final TextEditingController? controller;
  final bool isEnabled;
  final bool allowSearch;
  final bool propagateParentControllerValue;
  final InputDecoration? decoration;
  final String hintText;
  final EdgeInsetsGeometry? overlayMargin;
  final String? label;
  final Widget? prefix;
  final Widget? prefixIcon;
  final Widget? suffix;
  final Color? fillColor;
  final OnValidate<String?>? validator;
  final OnChanged<String?>? onChanged;
  final TextStyle? style;
  final TextAlign? textAlign;
  final String? semanticshintText;
  final List<SelectionData<T>> options;
  final ValueNotifier<SelectionData<T>?> selection;
  final double contentPaddingHorizontal;
  final double contentPaddingVertical;
  final FocusNode? focusNode;

  TextEditingController get ctrl {
    return controller ?? TextEditingController(text: selection.value?.label);
  }

  AppDropDownField({
    super.key,
    this.semanticshintText,
    this.decoration,
    this.controller,
    this.isEnabled = true,
    this.fillColor,
    this.overlayMargin,
    this.propagateParentControllerValue = false,
    required this.hintText,
    this.label,
    this.allowSearch = false,
    this.textAlign = TextAlign.left,
    this.onChanged,
    this.prefix,
    this.prefixIcon,
    this.suffix,
    this.validator,
    this.style,
    this.contentPaddingHorizontal = AppFontSizes.px16,
    this.contentPaddingVertical = AppFontSizes.px16,
    this.focusNode,
    required this.options,
  }) : selection = getControllerSelection<T>(controller, options);

  const AppDropDownField.withSelection({
    super.key,
    this.semanticshintText,
    this.decoration,
    required this.selection,
    this.isEnabled = true,
    this.propagateParentControllerValue = false,
    required this.hintText,
    this.label,
    this.overlayMargin,
    this.allowSearch = false,
    this.textAlign = TextAlign.left,
    this.onChanged,
    this.prefix,
    this.prefixIcon,
    this.fillColor,
    this.suffix,
    this.validator,
    this.contentPaddingHorizontal = AppFontSizes.px16,
    this.contentPaddingVertical = AppFontSizes.px16,
    this.style,
    this.focusNode,
    required this.options,
  }) : controller = null;

  @override
  State<AppDropDownField> createState() => _AppDropDownTextFieldState();

  ValueNotifier<SelectionData<T>?> get selectionRef => selection;

  static ValueNotifier<SelectionData<K>?> getControllerSelection<K>(
    TextEditingController? ctrl,
    List<SelectionData<K>> options,
  ) {
    final selection = options.tryFirstWhere((it) => it.label == ctrl?.text);
    return ValueNotifier(selection);
  }
}

class _AppDropDownTextFieldState extends State<AppDropDownField>
    with WidgetsBindingObserver, AppModalMixin {
  late FocusNode _inputFocus;
  final ValueNotifier<FocusState> _hasFocus = ValueNotifier(const FocusState());
  late ValueNotifier<SelectionData?> _selectionRef;
  late TextEditingController _controller;
  late OverlayPortalController _overlayController;

  @override
  void initState() {
    super.initState();

    _selectionRef = widget.selectionRef;
    _controller = widget.ctrl;
    _overlayController = OverlayPortalController();
    _inputFocus = widget.focusNode ?? FocusNode();

    _inputFocus.addListener(
      () {
        if (!_inputFocus.hasFocus) {
          _overlayController.hide();
        }
        _hasFocus.value = FocusState(
          hasText: widget.ctrl.text.isNotEmpty,
          isFocused: _inputFocus.hasFocus,
        );
      },
    );

    _selectionRef.addListener(() {
      WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
        _controller.text = _selectionRef.value?.label ?? '';
      });
    });

    if (widget.propagateParentControllerValue) {
      widget.controller?.addListener(() {
        _controller.text = widget.controller?.text ?? "";
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final xOffset = context.sizer.width * .5;
    final topMargin = context.sp(AppFontSizes.px2);

    return AppOverlayPortal(
      controller: _overlayController,
      overlayOffSet: Offset(-xOffset, topMargin),
      overlayChild: GenericListener(
        valueListenable: _selectionRef,
        builder: (selection) {
          final options = widget.options.value;
          if (!options.hasValue) return const Offstage();

          return Align(
            alignment: Alignment.topCenter,
            child: AppDropdownOptionsOverlay(
              onSelect: (value) {
                _selectionRef.value = value;
                _overlayController.hide();
              },
              margin: widget.overlayMargin ??
                  context.insets.symmetricSp(
                    horizontal: widget.contentPaddingHorizontal,
                  ),
              options: options,
              selection: selection,
            ),
          );
        },
      ),
      child: InkWell(
        focusNode: _inputFocus,
        onTap: () {
          if (!widget.isEnabled) return;
          HapticFeedback.mediumImpact();
          if (widget.allowSearch) {
            showSelectionSheet(
              context,
              options: widget.options,
              title: LocaleKeys.selectAnOption.tr(),
              onSelect: (value) {
                _selectionRef.value = value;
                _overlayController.hide();
              },
              allowSearch: widget.allowSearch,
            );
            return;
          }

          _overlayController.toggle();
          context.requestFocus(_inputFocus);
        },
        child: IgnorePointer(
          child: AppTextField(
            decoration: widget.decoration,
            controller: _controller,
            hintText: widget.hintText,
            prefix: widget.prefix,
            prefixIcon: widget.prefixIcon,
            isEnabled: widget.isEnabled,
            validator: widget.validator,
            textAlign: widget.textAlign,
            style: widget.style,
            onChanged: widget.onChanged,
            contentPaddingHorizontal: widget.contentPaddingHorizontal,
            contentPaddingVertical: widget.contentPaddingVertical,
            fillColor: widget.fillColor,
            suffixIcon: Padding(
              padding: context.insets.onlySp(
                right: widget.contentPaddingHorizontal,
              ),
              child: AppAnimatedSwitcher(
                key: ValueKey(widget.suffix != null),
                child: Builder(
                  builder: (_) {
                    if (widget.suffix != null) return widget.suffix!;
                    return AppIcon(
                      AppIcons.navArrowDown,
                      color: context.hintTextColor,
                    );
                  },
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}

class AppDropdownOptionsOverlay extends AppStatelessWidget {
  final OnChanged<SelectionData> onSelect;
  final List<SelectionData> options;
  final SelectionData? selection;
  final EdgeInsetsGeometry margin;

  const AppDropdownOptionsOverlay({
    required this.onSelect,
    required this.options,
    required this.margin,
    required this.selection,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return AppCard(
      margin: margin,
      borderRadius: context.xlBorderRadius,
      border: BorderSide(
        color: context.inputBorderColor,
        width: 2,
      ),
      shadows: context.shadows.small,
      padding: EdgeInsets.zero,
      child: ListView.builder(
        shrinkWrap: true,
        padding: context.insets.symmetricSp(vertical: spacing.spacing12),
        itemCount: options.length,
        itemBuilder: (_, index) {
          final option = options[index];
          return SelectionTile(
            icon: option.icon,
            title: option.extendedLabel ?? option.label,
            key: ValueKey([index, option.label]),
            isSelected: selection == option,
            onTap: () => onSelect(option),
          );
        },
      ),
    );
  }
}
