import 'package:flutter/foundation.dart';
import 'package:day1/day1.dart';
import 'package:flutter/material.dart';
import 'package:google_places_flutter/google_places_flutter.dart';

class ApplacesAutocompleteField extends AppStatefulWidget {
  final bool isEnabled;
  final bool isLoading;
  final InputDecoration? decoration;
  final String? hintText;
  final Widget? prefix;
  final Widget? prefixIcon;
  final Widget? suffix;
  final OnValidate<SelectionData<AppSelectionResponse>?>? validator;
  final OnChanged<String?>? onChanged;
  final TextStyle? style;
  final TextAlign? textAlign;
  final String? semanticshintText;
  final ValueNotifier<SelectionData<AppSelectionResponse>?> selection;
  final FocusNode? focusNode;
  final double contentPaddingHorizontal;
  final double contentPaddingVertical;

  TextEditingController get ctrl {
    return TextEditingController(text: selection.value?.label);
  }

  const ApplacesAutocompleteField({
    super.key,
    this.semanticshintText,
    this.decoration,
    required this.selection,
    this.isEnabled = true,
    this.isLoading = false,
    this.hintText,
    this.textAlign = TextAlign.left,
    this.onChanged,
    this.prefix,
    this.prefixIcon,
    this.suffix,
    this.validator,
    this.style,
    this.focusNode,
    this.contentPaddingHorizontal = 3,
    this.contentPaddingVertical = 1.7,
  });

  @override
  State<ApplacesAutocompleteField> createState() =>
      _AppAutocompleteTextFieldState();

  ValueNotifier<SelectionData<AppSelectionResponse>?> get selectionRef =>
      selection;

  static ValueNotifier<SelectionData<K>?> getControllerSelection<K>(
    TextEditingController? ctrl,
    List<SelectionData<K>> options,
  ) {
    final selection = options.tryFirstWhere((it) => it.label == ctrl?.text);
    return ValueNotifier(selection);
  }
}

class _AppAutocompleteTextFieldState extends State<ApplacesAutocompleteField>
    with WidgetsBindingObserver, AppModalMixin {
  late FocusNode _inputFocus;
  final ValueNotifier<FocusState> _hasFocus = ValueNotifier(const FocusState());
  late ValueNotifier<SelectionData<AppSelectionResponse>?> _selectionRef;
  late TextEditingController _controller;

  @override
  void initState() {
    super.initState();

    _selectionRef = widget.selectionRef;
    _controller = widget.ctrl;
    _inputFocus = widget.focusNode ?? FocusNode();

    _inputFocus.addListener(
      () {
        _hasFocus.value = FocusState(
          hasText: widget.ctrl.text.isNotEmpty,
          isFocused: _inputFocus.hasFocus,
        );
      },
    );

    _selectionRef.addListener(() {
      WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
        _controller.text = _selectionRef.value?.label ?? '';
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    final style = context.textStyle.b1();
    final hintStyle = context.textStyle.hintText();
    final hPadding = widget.contentPaddingHorizontal;
    final vPadding = widget.contentPaddingVertical;
    final hintPadding = context.insets.fromLTRB(hPadding, 1, hPadding / 2, 0);

    return IgnorePointer(
      ignoring: widget.isLoading || !widget.isEnabled,
      child: GenericListener(
        valueListenable: _selectionRef,
        builder: (selection) {
          return FormField<SelectionData<AppSelectionResponse>>(
            builder: (formState) {
              return Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                mainAxisAlignment: MainAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  GooglePlaceAutoCompleteTextField(
                    textEditingController: _controller,
                    googleAPIKey: config.placesApiKey,
                    showError: kDebugMode,
                    focusNode: _inputFocus,
                    boxDecoration: BoxDecoration(
                      border: Border.all(style: BorderStyle.none),
                    ),
                    itemClick: (prediction) {
                      final value = AppSelectionResponse(
                        id: prediction.placeId,
                        name: prediction.description ?? "",
                      );
                      _selectionRef.value = value.selectionData();
                      context.resetFocus();
                    },
                    isCrossBtnShown: false,
                    textStyle: style,
                    inputDecoration: (widget.decoration ??
                            context.inputStyle.outlined(!widget.isEnabled))
                        .copyWith(
                      hintStyle: hintStyle,
                      labelStyle: hintStyle,
                      helperStyle: context.textStyle.helperText(),
                      errorStyle: context.textStyle.helperText(),
                      suffix: widget.suffix,
                      prefixIcon: widget.prefixIcon,
                      suffixStyle: style,
                      counter: null,
                      hintText: widget.hintText,
                      hintMaxLines: 1,
                      prefixStyle: style,
                      prefixIconConstraints:
                          const BoxConstraints.tightForFinite(),
                      suffixIconConstraints:
                          const BoxConstraints.tightForFinite(),
                      contentPadding: context.insets.symmetric(
                        horizontal: hPadding,
                        vertical: vPadding,
                      ),
                    ),
                  ),
                  if (formState.hasError && !formState.isValid)
                    Padding(
                      padding: hintPadding,
                      child: AppText(
                        formState.errorText ?? "a" * 70,
                        style: context.textStyle.helperText(),
                      ),
                    ),
                ],
              );
            },
            validator: widget.validator,
            initialValue: selection,
            enabled: widget.isEnabled,
          );
        },
      ),
    );
  }
}
