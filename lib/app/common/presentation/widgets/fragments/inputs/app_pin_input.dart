import 'dart:async';

import 'package:day1/day1.dart';
import 'package:flutter/material.dart';
import 'package:pin_code_fields/pin_code_fields.dart';

class AppPinInput extends AppStatefulWidget {
  final TextEditingController? controller;
  final FocusNode? focusNode;
  final bool isEnabled;
  final int length;
  final bool obscureText;
  final bool autoFocus;
  final OnValidate<String?>? validator;
  final TextInputType? keyboardType;
  final OnChanged<String?>? onChanged;
  final OnChanged<String?>? onFieldSubmitted;
  final TextStyle? style;
  final TextAlign? textAlign;
  final String? semanticsLabel;
  final Size fieldSize;

  const AppPinInput({
    super.key,
    this.length = 0,
    this.focusNode,
    this.semanticsLabel,
    this.controller,
    this.validator,
    this.isEnabled = true,
    this.autoFocus = false,
    this.textAlign = TextAlign.left,
    this.onChanged,
    this.obscureText = false,
    this.keyboardType,
    this.style,
    this.onFieldSubmitted,
    this.fieldSize = const Size(50, 88),
  });

  @override
  State<AppPinInput> createState() => _AppPinInputState();
}

class _AppPinInputState extends State<AppPinInput> {
  @override
  Widget build(BuildContext context) {
    final errorController = StreamController<ErrorAnimationType>.broadcast();

    return PinCodeTextField(
      key: const Key("otp_input"),
      autoFocus: widget.autoFocus,
      enabled: widget.isEnabled,
      appContext: context,
      obscuringWidget: AppText(
        "*",
        style: context.textStyle.h5(
          color: context.textColor.withOpacity(.8),
        ),
      ),
      errorAnimationController: errorController,
      focusNode: widget.focusNode,
      length: widget.length,
      controller: widget.controller,
      obscureText: widget.obscureText,
      keyboardType: TextInputType.number,
      textStyle: context.textStyle.hintText(),
      pastedTextStyle: context.textStyle.hintText(),
      hintCharacter: "",
      autoDisposeControllers: false,
      textInputAction: TextInputAction.done,
      animationType: AnimationType.none,
      enableActiveFill: true,
      pinTheme: PinTheme.defaults(
        shape: PinCodeFieldShape.box,
        fieldOuterPadding: EdgeInsets.zero,
        borderRadius: context.mdBorderRadius,
        activeFillColor: context.inputBgColor,
        inactiveFillColor: context.inputBgColor,
        selectedFillColor: context.inputBgColor,
        fieldHeight: widget.fieldSize.height,
        fieldWidth: widget.fieldSize.width,
        errorBorderColor: context.errorColor,
        activeColor: context.activeInputBorderColor,
        disabledColor: context.disabledInputBorderColor,
        inactiveColor: context.inputBorderColor,
        selectedColor: context.activeInputBorderColor,
      ),
      onCompleted: widget.onFieldSubmitted,
      onChanged: widget.onChanged,
      autovalidateMode: AutovalidateMode.disabled,
      validator: widget.validator ??
          (text) => AppValidators.minLength(text, length: widget.length),
    );
  }
}
