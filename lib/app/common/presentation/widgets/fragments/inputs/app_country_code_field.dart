import 'package:cached_network_image/cached_network_image.dart';
import 'package:day1/day1.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

class AppCountryCodeField extends AppStatefulWidget {
  final ValueNotifier<SelectionData<Country>?> controller;
  final bool isEnabled;
  final InputDecoration? decoration;
  final OnValidate<Country?>? validator;
  final OnChanged<Country?>? onChanged;
  final TextStyle? style;
  final TextAlign? textAlign;
  final FocusNode? focusNode;

  const AppCountryCodeField({
    super.key,
    this.decoration,
    required this.controller,
    this.isEnabled = true,
    this.textAlign = TextAlign.center,
    this.onChanged,
    this.validator,
    this.style,
    this.focusNode,
  });

  @override
  State<AppCountryCodeField> createState() => _AppCountryCodeFieldState();
}

class _AppCountryCodeFieldState extends State<AppCountryCodeField>
    with WidgetsBindingObserver, AppModalMixin {
  late final FocusNode _inputFocus;
  final ValueNotifier<FocusState> _hasFocus = ValueNotifier(const FocusState());

  @override
  void initState() {
    super.initState();
    _inputFocus = widget.focusNode ?? FocusNode();

    _inputFocus.addListener(
      () {
        _hasFocus.value = FocusState(
          hasText: widget.controller.value != null,
          isFocused: _inputFocus.hasFocus,
        );
      },
    );
  }

  String get _defaultCode => config.countryCode;

  _populateCountryData(List<Country> data) {
    final controllerIsNull = widget.controller.value == null ||
        widget.controller.value?.selection == null;
    final controllerDial = widget.controller.value?.selection.dial;
    final controllerIsoCode = widget.controller.value?.selection.isoCode;
    final controllerLacksIsoCode = controllerIsoCode == null;

    if (controllerIsNull || controllerLacksIsoCode) {
      final dial = (controllerDial ?? _defaultCode).replaceAll("+", "");
      WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
        final match = data.firstWhere(
          (it) => it.dial == dial,
          orElse: () => data.first,
        );
        widget.controller.value = SelectionData(
          selection: match,
          label: match.countryCode,
        );
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final state = context.read<CountryState>();
    const hPadding = AppFontSizes.px12;

    return ValueListenableBuilder<Future<List<Country>>?>(
      valueListenable: state.countriesTask,
      builder: (_, future, __) {
        return FutureBuilder<List<Country>>(
          future: future,
          builder: (_, task) {
            if (task.isLoaded && (task.data?.isNotEmpty ?? false)) {
              _populateCountryData(task.data!);
            }

            return AppDropDownField<Country>.withSelection(
              allowSearch: true,
              hintText: "",
              label: LocaleKeys.selectAnOption.tr(),
              propagateParentControllerValue: true,
              options: [
                for (final Country country in (task.data ?? []))
                  SelectionData(
                    selection: country,
                    label: country.countryCode,
                    icon: _FlagImage(country.flagImage),
                    extendedLabel: country.combinedNameAndCodeExtended,
                  )
              ],
              isEnabled: widget.isEnabled,
              semanticshintText: _defaultCode,
              decoration: widget.decoration,
              textAlign: widget.textAlign,
              prefixIcon: GenericListener(
                valueListenable: widget.controller,
                builder: (country) {
                  if (country == null) return const Offstage();
                  return Padding(
                    padding: context.insets.onlySp(
                      left: hPadding,
                      right: hPadding / 2,
                    ),
                    child: _FlagImage(country.selection.flagImage),
                  );
                },
              ),
              contentPaddingHorizontal: hPadding,
              onChanged: (_) {
                widget.onChanged?.call(widget.controller.value?.selection);
              },
              suffix: AppIcon(
                AppIcons.navArrowDown,
                color: context.hintTextColor,
              ),
              validator: (_) => widget.validator?.call(
                widget.controller.value?.selection,
              ),
              style: widget.style,
              focusNode: widget.focusNode,
              selection: widget.controller,
            );
          },
        );
      },
    );
  }
}

class _FlagImage extends AppStatelessWidget {
  final String? flag;

  const _FlagImage(this.flag);

  @override
  Widget build(BuildContext context) {
    if (flag == null) return const Offstage();

    final size = context.sp(AppFontSizes.px18);

    return Container(
      height: size,
      width: size,
      decoration: BoxDecoration(
        border: Border.all(width: .5, color: context.inputBorderColor),
        shape: BoxShape.circle,
        image: DecorationImage(
          image: CachedNetworkImageProvider(flag!),
          fit: BoxFit.cover,
        ),
      ),
    );
  }
}
