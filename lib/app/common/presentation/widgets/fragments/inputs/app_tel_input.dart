import 'package:day1/day1.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class AppPhoneField extends AppStatefulWidget {
  final TextEditingController controller;
  final ValueNotifier<SelectionData<Country>?>? countryCode;
  final bool isRequired;
  final bool isEnabled;
  final bool autoFocus;
  final String? hintText;
  final FocusNode? focusNode;
  final FocusNode? dialFocusNode;

  const AppPhoneField({
    super.key,
    required this.controller,
    this.countryCode,
    this.hintText,
    this.autoFocus = false,
    this.isRequired = true,
    this.isEnabled = true,
    this.focusNode,
    this.dialFocusNode,
  });
  @override
  State<AppPhoneField> createState() => _AppPhoneFieldState();
}

class _AppPhoneFieldState extends State<AppPhoneField> {
  String? get _phoneValidationError {
    return AppValidators.phoneValidator(
      widget.controller.textValue,
      isRequired: widget.isRequired,
    );
  }

  String? get _dialValidationError {
    if (!widget.isRequired) return null;
    return AppValidators.required(
      widget.countryCode?.value?.selection.dial,
    );
  }

  @override
  Widget build(BuildContext context) {
    final child = AppTextField(
      focusNode: widget.focusNode,
      isEnabled: widget.isEnabled,
      controller: widget.controller,
      hintText: widget.hintText,
      autoFocus: widget.autoFocus,
      maxLength: 15,
      keyboardType: TextInputType.phone,
      textInputAction: TextInputAction.next,
      trackKeyboardState: true,
      validator: AppValidators.phoneValidator,
      inputFormatters: [FilteringTextInputFormatter.digitsOnly],
      autofillHints: const [AutofillHints.telephoneNumber],
      suffixIcon: ValueListenableBuilder(
        valueListenable: widget.controller,
        builder: (context, TextEditingValue val, _) {
          if (!val.text.hasValue) return const Offstage();

          return IconButton(
            padding: context.insets.onlySp(right: AppFontSizes.px16),
            style: const ButtonStyle(
              tapTargetSize: MaterialTapTargetSize.shrinkWrap,
              padding: WidgetStatePropertyAll(EdgeInsets.zero),
            ),
            onPressed: widget.controller.clear,
            icon: const AppIcon(AppIcons.xmark),
          );
        },
      ),
    );

    if (widget.countryCode != null) {
      return FormField<TextEditingController>(
        builder: (formState) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisSize: MainAxisSize.max,
                children: [
                  Expanded(
                    flex: 2,
                    child: AppCountryCodeField(
                      controller: widget.countryCode!,
                      isEnabled: widget.isEnabled,
                      focusNode: widget.dialFocusNode,
                    ),
                  ),
                  const AppGap.h4(),
                  Expanded(
                    flex: 4,
                    child: child,
                  )
                ],
              ),
              if (formState.hasError && !formState.isValid)
                Padding(
                  padding: context.insets.fromLTRB(3, 1, 3, 0),
                  child: AppText(
                    formState.errorText,
                    style: context.textStyle.helperText(),
                  ),
                ),
            ],
          );
        },
        initialValue: widget.controller,
        validator: (_) => _phoneValidationError ?? _dialValidationError,
      );
    }

    return child;
  }
}
