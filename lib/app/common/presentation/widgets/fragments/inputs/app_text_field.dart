import 'package:day1/day1.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class AppTextField<T> extends AppStatefulWidget {
  final TextEditingController? controller;
  final FocusNode? focusNode;
  final bool isEnabled;
  final TextInputAction? textInputAction;
  final InputDecoration? decoration;
  final TextCapitalization? textCapitalization;
  final String? label;
  final Color? fillColor;
  final bool obscureText;
  final bool trackKeyboardState;
  final bool autoFocus;
  final bool autoCorrect;
  final String? hintText;
  final String? helperText;
  final Widget? suffix;
  final Widget? suffixIcon;
  final Widget? helper;
  final Widget? prefix;
  final PrefixStyle? prefixStyle;
  final String? prefixText;
  final TextStyle? prefixTextStyle;
  final String? suffixText;
  final TextStyle? suffixTextStyle;
  final TextStyle? hintStyle;
  final Widget? prefixIcon;
  final OnValidate<String?>? validator;
  final TextInputType? keyboardType;
  final List<TextInputFormatter>? inputFormatters;
  final int? maxLines;
  final int? minLines;
  final int? maxLength;
  final OnChanged<String?>? onChanged;
  final OnChanged<String?>? onFieldSubmitted;
  final OnPressed? onPrefixTap;
  final TextStyle? style;
  final TextAlign? textAlign;
  final String? semanticsLabel;
  final AutovalidateMode? autovalidateMode;
  final double contentPaddingHorizontal;
  final double contentPaddingVertical;
  final double marginBottom;
  final List<String>? autofillHints;

  TextEditingController get ctrl {
    return controller ?? TextEditingController();
  }

  const AppTextField({
    super.key,
    this.focusNode,
    this.semanticsLabel,
    this.decoration,
    this.textInputAction = TextInputAction.next,
    this.textCapitalization,
    this.controller,
    this.isEnabled = true,
    this.autoFocus = false,
    this.autoCorrect = true,
    this.inputFormatters,
    this.prefixIcon,
    this.hintStyle,
    this.label,
    this.trackKeyboardState = false,
    this.prefixStyle,
    this.hintText,
    this.helperText,
    this.textAlign = TextAlign.left,
    this.maxLines = 1,
    this.maxLength,
    this.fillColor,
    this.onChanged,
    this.onPrefixTap,
    this.obscureText = false,
    this.suffix,
    this.suffixIcon,
    this.prefix,
    this.prefixText,
    this.suffixText,
    this.suffixTextStyle,
    this.prefixTextStyle,
    this.validator,
    this.keyboardType,
    this.style,
    this.helper,
    this.contentPaddingHorizontal = AppFontSizes.px16,
    this.contentPaddingVertical = AppFontSizes.px16,
    this.marginBottom = 0,
    this.onFieldSubmitted,
    this.autofillHints,
    this.autovalidateMode = AutovalidateMode.disabled,
  }) : minLines = 1;

  const AppTextField.multiline({
    super.key,
    this.focusNode,
    this.semanticsLabel,
    this.decoration,
    this.textInputAction = TextInputAction.newline,
    this.textCapitalization,
    this.controller,
    this.isEnabled = true,
    this.autoFocus = false,
    this.autoCorrect = true,
    this.inputFormatters,
    this.label,
    this.fillColor,
    this.prefixIcon,
    this.hintStyle,
    this.prefixStyle,
    this.prefixText,
    this.helper,
    this.prefixTextStyle,
    this.suffixText,
    this.suffixTextStyle,
    this.onPrefixTap,
    this.hintText,
    this.helperText,
    this.textAlign = TextAlign.left,
    this.maxLength,
    this.onChanged,
    this.obscureText = false,
    this.suffix,
    this.suffixIcon,
    this.prefix,
    this.validator,
    this.style,
    this.contentPaddingHorizontal = AppFontSizes.px16,
    this.contentPaddingVertical = AppFontSizes.px16,
    this.onFieldSubmitted,
    this.autovalidateMode = AutovalidateMode.disabled,
    this.maxLines,
    this.minLines = 1,
    this.marginBottom = 0,
    this.autofillHints,
  })  : trackKeyboardState = false,
        keyboardType = TextInputType.multiline;

  @override
  State<AppTextField> createState() => _AppTextFieldState();
}

class _AppTextFieldState extends State<AppTextField>
    with WidgetsBindingObserver, AppTaskMixin {
  final _inputFocus = FocusNode();
  final ValueNotifier<FocusState> _focusNotifier =
      ValueNotifier(const FocusState());

  _AppTextFieldState();

  bool get shouldTrack => widget.trackKeyboardState;

  @override
  void initState() {
    super.initState();
    (widget.focusNode ?? _inputFocus).addListener(
      () {
        try {
          _focusNotifier.value = FocusState(
            hasText: widget.ctrl.text.isNotEmpty,
            isFocused: widget.focusNode?.hasFocus ?? _inputFocus.hasFocus,
          );
        } catch (_) {}
      },
    );
  }

  Color? get _prefixBorderColor {
    if (_focusNotifier.value.hasError ?? false) {
      final isFocused = _focusNotifier.value.isFocused ?? false;
      final border =
          isFocused ? decoration.focusedErrorBorder : decoration.errorBorder;
      return border?.borderSide.color;
    }
    if (_focusNotifier.value.isFocused ?? false) {
      return decoration.focusedBorder?.borderSide.color;
    }
    return (decoration.enabledBorder ?? decoration.border)?.borderSide.color;
  }

  InputDecoration get decoration =>
      widget.decoration ?? context.inputStyle.outlined(!widget.isEnabled);

  @override
  Widget build(BuildContext context) {
    final isMultiLine = widget.keyboardType == TextInputType.multiline;
    final weight = isMultiLine ? FontWeight.normal : FontWeight.w500;
    final style = context.textStyle.b2(weight: weight);

    return Semantics(
      label: widget.semanticsLabel ?? "Input Field",
      child: ValueListenableBuilder(
        valueListenable: _focusNotifier,
        builder: (context, FocusState value, child) {
          return Container(
            margin: context.insets.only(bottom: widget.marginBottom),
            child: Builder(
              builder: (context) {
                if (widget.prefix == null) return child!;
                return Row(
                  textBaseline: TextBaseline.alphabetic,
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.baseline,
                  children: [
                    InkWell(
                      child: Container(
                        alignment: Alignment.center,
                        padding: widget.prefixStyle?.padding ??
                            context.insets.symmetric(
                              horizontal: 1,
                              vertical: .85,
                            ),
                        margin: widget.prefixStyle?.margin,
                        decoration: widget.prefixStyle?.decoration ??
                            BoxDecoration(
                              border: Border(
                                bottom: BorderSide(
                                  color:
                                      _prefixBorderColor ?? context.transparent,
                                  width: (value.isFocused ?? false) ? 1.2 : 1,
                                ),
                              ),
                            ),
                        child: widget.prefix,
                      ),
                      onTap: () {
                        if (widget.onPrefixTap != null) {
                          HapticFeedback.lightImpact();
                          context.resetFocus();
                          widget.onPrefixTap!();
                        }
                      },
                    ),
                    const AppGap.h4(),
                    Expanded(child: child!),
                  ],
                );
              },
            ),
          );
        },
        child: TextFormField(
          autocorrect: widget.autoCorrect,
          autofocus: widget.autoFocus,
          buildCounter: (
            context, {
            required currentLength,
            required isFocused,
            required maxLength,
          }) {
            return const Offstage();
          },
          autovalidateMode: widget.autovalidateMode,
          textDirection: TextDirection.ltr,
          showCursor: true,
          textAlign: widget.textAlign ?? TextAlign.left,
          focusNode: widget.focusNode ?? _inputFocus,
          textInputAction: widget.textInputAction ?? TextInputAction.done,
          textCapitalization:
              widget.textCapitalization ?? TextCapitalization.none,
          style: widget.style ?? style,
          maxLength: widget.maxLength,
          controller: widget.ctrl,
          enabled: widget.isEnabled,
          obscureText: widget.obscureText,
          keyboardType: widget.keyboardType ?? TextInputType.text,
          inputFormatters: widget.inputFormatters,
          maxLines: widget.maxLines,
          minLines: widget.minLines,
          onChanged: widget.onChanged,
          autofillHints: widget.autofillHints,
          onFieldSubmitted: widget.onFieldSubmitted,
          decoration: (widget.decoration ??
                  context.inputStyle.outlined(!widget.isEnabled))
              .copyWith(
            suffix: widget.suffix,
            suffixIcon: widget.suffixIcon,
            prefixIcon: widget.prefixIcon,
            prefixText: widget.prefixText,
            suffixText: widget.suffixText,
            suffixStyle: widget.suffixTextStyle ?? style,
            counter: null,
            hintText: widget.hintText,
            labelText: widget.label,
            helper: widget.helper,
            helperText: widget.helperText,
            hintMaxLines: 1,
            fillColor: widget.fillColor,
            prefixStyle: widget.prefixTextStyle ?? style,
            prefixIconConstraints: const BoxConstraints.tightForFinite(),
            suffixIconConstraints: const BoxConstraints.tightForFinite(),
            contentPadding: context.insets.symmetricSp(
              horizontal: widget.contentPaddingHorizontal,
              vertical: widget.contentPaddingVertical,
            ),
          ),
          validator: (String? text) {
            if (widget.validator != null) {
              final error = widget.validator!(text);
              WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
                _focusNotifier.value = FocusState(
                  hasError: error != null,
                  hasText: _focusNotifier.value.hasText ?? false,
                  isFocused: _focusNotifier.value.isFocused ?? false,
                );
              });
              return error;
            }
            _focusNotifier.value = FocusState(
              hasError: false,
              hasText: _focusNotifier.value.hasText ?? false,
              isFocused: _focusNotifier.value.isFocused ?? false,
            );
            return null;
          },
        ),
      ),
    );
  }
}
