import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class AppSlide extends AppStatelessWidget {
  final SlideData slide;

  const AppSlide(this.slide, {super.key});

  @override
  Widget build(BuildContext context) {
    final inset = context.sp(AppFontSizes.px32);
    Widget child = AppCard(
      color: slide.backgroundColor,
      padding: context.insets.zero,
      child: Stack(
        clipBehavior: Clip.hardEdge,
        children: [
          if (slide.backgroundImage.hasValue)
            Positioned.fill(
              child: AppSlideBackground(slide),
            ),
          Positioned(
            top: inset,
            left: inset,
            right: inset,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                AppText(
                  slide.title,
                  style: (slide.titleStyle ?? context.textStyle.d4()).copyWith(
                    color: slide.headerColor,
                  ),
                ),
                slide.headerSpacing,
                AppText(
                  slide.rider,
                  style: slide.riderStyle ?? context.textStyle.b3(),
                ),
              ],
            ),
          ),
        ],
      ),
    );

    if (slide.painter != null) {
      child = CustomPaint(
        foregroundPainter: slide.painter,
        child: child,
      );
    }

    return Padding(
      padding: context.insets.defaultHorizontalInsets,
      child: child,
    );
  }
}

class AppSlideBackground extends AppStatelessWidget {
  final SlideData slide;

  const AppSlideBackground(this.slide, {super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: slide.backgroundPadding,
      child: ClipRect(
        child: TweenAnimationBuilder(
          tween: Tween<double>(begin: slide.backgroundScale * .7, end: slide.backgroundScale),
          duration: slide.backgroundTransitionDuration ?? 300.milliDuration,
          builder: (_, scale, __) {
            final x = slide.backgroundOffSet.dx;
            final y = slide.backgroundOffSet.dy;
            final matrix = Matrix4.diagonal3Values(scale, scale, 1)
              ..translate(x, y);
            return Transform(
              transform: matrix,
              alignment: slide.backgroundAlignment,
              child: AppImage(
                image: AppImageData(
                  imageData: slide.backgroundImage,
                ),
                alignment: slide.backgroundAlignment,
                fit: slide.backgroundFit,
                useDefaultSize: false,
              ),
            );
          },
        ),
      ),
    );
  }
}
