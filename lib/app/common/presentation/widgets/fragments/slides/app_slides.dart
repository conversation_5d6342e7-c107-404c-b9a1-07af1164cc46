import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class AppSlides extends AppStatefulWidget {
  final List<SlideData> slides;
  final ValueNotifier<int>? activeSlide;
  final Widget? bottom;

  const AppSlides({
    required this.slides,
    this.activeSlide,
    this.bottom,
    super.key,
  });

  @override
  State<AppSlides> createState() => _AppSlidesState();
}

class _AppSlidesState extends State<AppSlides> {
  late final ValueNotifier<int> _activeSlide;
  late final PageController _controller;
  late final Debouncer _debouncer;

  @override
  void initState() {
    super.initState();
    _controller = PageController();
    _activeSlide = widget.activeSlide ?? ValueNotifier(0);
    _debouncer = Debouncer(2.secondDuration);
    _goToNextSlide(_controller.initialPage);
  }

  _updateSlide(int index) {
    if (_debouncer.isActive) _debouncer.abort();
    _activeSlide.value = index;
    _goToNextSlide(index);
  }

  _goToNextSlide(int index) {
    int next = index + 1;
    if (next == widget.slides.length) next = 0;

    _debouncer.run(() {
      if (!_controller.hasClients) {
        _activeSlide.value = next;
        return;
      }

      if (next == 0) {
        _controller.jumpToPage(next);
        return;
      }

      _controller.animateToPage(
        next,
        duration: 300.milliDuration,
        curve: Curves.easeIn,
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    final slides = widget.slides;
    final stackHOffset = context.sp(spacing.spacing32);
    final bottomCardBottomOffset = stackHOffset + context.sp(spacing.spacing12);

    return SafeArea(
      bottom: false,
      child: Padding(
        padding: context.insets.defaultVerticalInsets,
        child: Stack(
          children: [
            Positioned.fill(
              child: PageView.builder(
                controller: _controller,
                key: const PageStorageKey("app_slides"),
                onPageChanged: _updateSlide,
                itemBuilder: (_, index) => AppSlide(slides[index]),
                itemCount: slides.length,
              ),
            ),
            Positioned(
              left: stackHOffset,
              right: stackHOffset,
              bottom: bottomCardBottomOffset,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: context.insets.onlySp(left: AppFontSizes.px4),
                    child: NumberListener(
                      valueListenable: _activeSlide,
                      builder: (index) => AppDots(index, len: slides.length),
                    ),
                  ),
                  if (widget.bottom != null) ...[
                    const AppGap.y32(),
                    widget.bottom!
                  ]
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
