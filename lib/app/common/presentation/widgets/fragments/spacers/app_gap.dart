import 'package:day1/day1.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';

enum GapSpace {
  spacing0,
  spacing2,
  spacing4,
  spacing6,
  spacing8,
  spacing10,
  spacing12,
  spacing16,
  spacing20,
  spacing24,
  spacing32,
  spacing40,
  spacing48,
  spacing64,
  spacing80,
  spacing96,
  spacing160,
}

enum GapType { square, vertical, horizontal }

class AppGap extends AppStatelessWidget {
  final GapSpace _space;
  final GapType _type;

  const AppGap({
    super.key,
    required GapSpace space,
    required GapType type,
  })  : _space = space,
        _type = type;

  const AppGap.zero({super.key})
      : _space = GapSpace.spacing0,
        _type = GapType.square;

  const AppGap.s2({super.key})
      : _space = GapSpace.spacing2,
        _type = GapType.square;
  const AppGap.s4({super.key})
      : _space = GapSpace.spacing4,
        _type = GapType.square;
  const AppGap.s6({super.key})
      : _space = GapSpace.spacing6,
        _type = GapType.square;
  const AppGap.s8({super.key})
      : _space = GapSpace.spacing8,
        _type = GapType.square;
  const AppGap.s10({super.key})
      : _space = GapSpace.spacing10,
        _type = GapType.square;
  const AppGap.s12({super.key})
      : _space = GapSpace.spacing12,
        _type = GapType.square;
  const AppGap.s16({super.key})
      : _space = GapSpace.spacing16,
        _type = GapType.square;
  const AppGap.s20({super.key})
      : _space = GapSpace.spacing20,
        _type = GapType.square;
  const AppGap.s24({super.key})
      : _space = GapSpace.spacing24,
        _type = GapType.square;
  const AppGap.s32({super.key})
      : _space = GapSpace.spacing32,
        _type = GapType.square;
  const AppGap.s40({super.key})
      : _space = GapSpace.spacing40,
        _type = GapType.square;
  const AppGap.s48({super.key})
      : _space = GapSpace.spacing48,
        _type = GapType.square;
  const AppGap.s64({super.key})
      : _space = GapSpace.spacing64,
        _type = GapType.square;
  const AppGap.s80({super.key})
      : _space = GapSpace.spacing80,
        _type = GapType.square;
  const AppGap.s96({super.key})
      : _space = GapSpace.spacing96,
        _type = GapType.square;
  const AppGap.s160({super.key})
      : _space = GapSpace.spacing160,
        _type = GapType.square;

  const AppGap.y2({super.key})
      : _space = GapSpace.spacing2,
        _type = GapType.vertical;
  const AppGap.y4({super.key})
      : _space = GapSpace.spacing4,
        _type = GapType.vertical;
  const AppGap.y6({super.key})
      : _space = GapSpace.spacing6,
        _type = GapType.vertical;
  const AppGap.y8({super.key})
      : _space = GapSpace.spacing8,
        _type = GapType.vertical;
  const AppGap.y10({super.key})
      : _space = GapSpace.spacing10,
        _type = GapType.vertical;
  const AppGap.y12({super.key})
      : _space = GapSpace.spacing12,
        _type = GapType.vertical;
  const AppGap.y16({super.key})
      : _space = GapSpace.spacing16,
        _type = GapType.vertical;
  const AppGap.y20({super.key})
      : _space = GapSpace.spacing20,
        _type = GapType.vertical;
  const AppGap.y24({super.key})
      : _space = GapSpace.spacing24,
        _type = GapType.vertical;
  const AppGap.y32({super.key})
      : _space = GapSpace.spacing32,
        _type = GapType.vertical;
  const AppGap.y40({super.key})
      : _space = GapSpace.spacing40,
        _type = GapType.vertical;
  const AppGap.y48({super.key})
      : _space = GapSpace.spacing48,
        _type = GapType.vertical;
  const AppGap.y64({super.key})
      : _space = GapSpace.spacing64,
        _type = GapType.vertical;
  const AppGap.y80({super.key})
      : _space = GapSpace.spacing80,
        _type = GapType.vertical;
  const AppGap.y96({super.key})
      : _space = GapSpace.spacing96,
        _type = GapType.vertical;
  const AppGap.y160({super.key})
      : _space = GapSpace.spacing160,
        _type = GapType.vertical;

  const AppGap.h2({super.key})
      : _space = GapSpace.spacing2,
        _type = GapType.horizontal;
  const AppGap.h4({super.key})
      : _space = GapSpace.spacing4,
        _type = GapType.horizontal;
  const AppGap.h6({super.key})
      : _space = GapSpace.spacing6,
        _type = GapType.horizontal;
  const AppGap.h8({super.key})
      : _space = GapSpace.spacing8,
        _type = GapType.horizontal;
  const AppGap.h10({super.key})
      : _space = GapSpace.spacing10,
        _type = GapType.horizontal;
  const AppGap.h12({super.key})
      : _space = GapSpace.spacing12,
        _type = GapType.horizontal;
  const AppGap.h16({super.key})
      : _space = GapSpace.spacing16,
        _type = GapType.horizontal;
  const AppGap.h20({super.key})
      : _space = GapSpace.spacing20,
        _type = GapType.horizontal;
  const AppGap.h24({super.key})
      : _space = GapSpace.spacing24,
        _type = GapType.horizontal;
  const AppGap.h32({super.key})
      : _space = GapSpace.spacing32,
        _type = GapType.horizontal;
  const AppGap.h40({super.key})
      : _space = GapSpace.spacing40,
        _type = GapType.horizontal;
  const AppGap.h48({super.key})
      : _space = GapSpace.spacing48,
        _type = GapType.horizontal;
  const AppGap.h64({super.key})
      : _space = GapSpace.spacing64,
        _type = GapType.horizontal;
  const AppGap.h80({super.key})
      : _space = GapSpace.spacing80,
        _type = GapType.horizontal;
  const AppGap.h96({super.key})
      : _space = GapSpace.spacing96,
        _type = GapType.horizontal;
  const AppGap.h160({super.key})
      : _space = GapSpace.spacing160,
        _type = GapType.horizontal;

  double computedGap() {
    return switch (_space) {
      GapSpace.spacing2 => spacing.spacing2,
      GapSpace.spacing4 => spacing.spacing4,
      GapSpace.spacing8 => spacing.spacing8,
      GapSpace.spacing6 => spacing.spacing6,
      GapSpace.spacing10 => spacing.spacing10,
      GapSpace.spacing12 => spacing.spacing12,
      GapSpace.spacing16 => spacing.spacing16,
      GapSpace.spacing20 => spacing.spacing20,
      GapSpace.spacing24 => spacing.spacing24,
      GapSpace.spacing32 => spacing.spacing32,
      GapSpace.spacing40 => spacing.spacing40,
      GapSpace.spacing48 => spacing.spacing48,
      GapSpace.spacing64 => spacing.spacing64,
      GapSpace.spacing80 => spacing.spacing80,
      GapSpace.spacing96 => spacing.spacing96,
      GapSpace.spacing160 => spacing.spacing160,
      _ => 0,
    };
  }

  @override
  Widget build(BuildContext context) {
    final size = computedGap();
    final gap = context.sp(size);

    return switch (_type) {
      GapType.square => SizedBox.square(dimension: gap),
      GapType.horizontal => SizedBox(width: gap),
      _ => SizedBox(height: gap),
    };
  }
}
