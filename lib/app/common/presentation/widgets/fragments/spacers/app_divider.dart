import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

enum AppDividerSize {
  none(0),
  xSmall(AppFontSizes.px8),
  small(AppFontSizes.px16),
  medium(AppFontSizes.px32),
  large(AppFontSizes.px48),
  xlarge(AppFontSizes.px64);

  const AppDividerSize(this.height);

  final double height;
}

class AppDivider extends AppStatelessWidget {
  final AppDividerSize size;
  final Color? color;

  const AppDivider({
    this.size = AppDividerSize.xSmall,
    this.color,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Divider(
      height: context.sp(size.height),
      thickness: 1,
      color: color ?? context.dividerColor,
    );
  }
}
