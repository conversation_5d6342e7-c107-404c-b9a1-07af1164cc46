import 'package:day1/day1.dart';
import 'package:flutter/widgets.dart';

class AppSizedBox extends AppStatelessWidget {
  final double height;
  final double width;
  final Widget? child;

  const AppSizedBox({super.key, this.height = 0, this.width = 0, this.child});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: context.fractionalHeight(height),
      width: context.fractionalWidth(width),
      child: child,
    );
  }
}
