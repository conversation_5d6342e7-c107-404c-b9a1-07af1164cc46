import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class CountDownListener extends AppStatefulWidget {
  final ValueNotifier<Stream<int>> countDownStream;
  final EdgeInsetsGeometry? padding;
  final ValueBuilder<int>? builder;
  final OnPressed? onResend;

  const CountDownListener(
    this.countDownStream, {
    super.key,
    this.padding,
    this.builder,
    this.onResend,
  });

  @override
  State<CountDownListener> createState() => _CountDownListenerState();
}

class _CountDownListenerState extends State<CountDownListener> {
  @override
  Widget build(BuildContext context) {
    return GenericListener(
      valueListenable: widget.countDownStream,
      builder: (stream) {
        return StreamBuilder(
          stream: stream,
          builder: (_, task) {
            final data = task.data ?? 0;
            if (widget.builder != null) return widget.builder!(data);

            final textColor = data == 0
                ? context.secondaryTextColor
                : context.disabledBtntextColor;
            return InkWell(
              onTap: () {
                if (data > 0) return;
                widget.onResend?.call();
              },
              child: Padding(
                padding: widget.padding ??
                    context.insets.symmetricSp(
                      vertical: AppFontSizes.px24,
                    ),
                child: AppRichText(
                  LocaleKeys.didntRecieveCodeResend.tr(
                    {"time": data.asDurationString},
                  ),
                  textStyle: context.textStyle.b2(color: textColor),
                  textAlign: TextAlign.center,
                ),
              ),
            );
          },
        );
      },
    );
  }
}
