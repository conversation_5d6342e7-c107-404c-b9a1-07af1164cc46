import 'package:day1/day1.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';

class ListListener<T> extends AppStatelessWidget {
  final ValueBuilder<List<T>> builder;
  final ValueListenable<List<T>?> valueListenable;

  const ListListener({
    super.key,
    required this.valueListenable,
    required this.builder,
  });

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<List<T>?>(
      valueListenable: valueListenable,
      builder: (context, values, _) => builder(values ?? []),
    );
  }
}
