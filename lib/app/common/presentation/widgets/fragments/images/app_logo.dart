import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class AppLogo extends AppStatelessWidget {
  final double height;
  final bool implyLeading;
  final bool iconOnly;
  final Widget? textWidget;
  final AlignmentGeometry alignment;

  const AppLogo({
    this.height = AppFontSizes.px36,
    this.alignment = Alignment.center,
    this.iconOnly = false,
    this.textWidget,
    this.implyLeading = false,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    Widget child = AppSvg(
      !iconOnly ? config.logo : AppVectors.logoIcon,
      fit: BoxFit.contain,
      alignment: alignment,
      height: context.sp(height),
    );

    if (textWidget != null) {
      child = Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          child,
          const AppGap.h12(),
          textWidget!,
        ],
      );
    }

    if (implyLeading) {
      child = Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          const AppBack<PERSON>utton(),
          const AppGap.h8(),
          Expanded(
            child: Transform.translate(
              offset: Offset(
                0,
                context.sp(AppFontSizes.px2),
              ),
              child: child,
            ),
          ),
        ],
      );
    }

    return child;
  }
}
