import 'package:day1/day1.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';

class AppAvatar extends AppStatelessWidget {
  final AppImageData? avatar;
  final Color? defaultAvatarColor;
  final Color? bgColor;
  final Color? borderColor;
  final double? size;
  final BoxFit? fit;
  final Alignment alignment;
  final GlobalKey<ScaffoldState>? scaffoldKey;
  final OnPressed? onPressed;
  final bool isUserAvatar;
  final String? initials;

  const AppAvatar({
    this.avatar,
    this.scaffoldKey,
    this.bgColor,
    this.borderColor,
    this.defaultAvatarColor,
    super.key,
    this.alignment = Alignment.center,
    this.fit,
    this.size,
    this.onPressed,
    this.initials,
    this.isUserAvatar = false,
  });

  @override
  Widget build(BuildContext context) {
    final state = context.watch<AvatarState>();
    final defaultSize = context.sp(AppFontSizes.px40);

    final resolvedAvatar = isUserAvatar ? (avatar ?? state.avatar) : avatar;

    ImageProvider? image;
    DecorationImage? decoration;

    if (resolvedAvatar == null && isUserAvatar) {
      image = NetworkImage(config.defaultAvatar);
    }

    if (resolvedAvatar != null && resolvedAvatar.isString) {
      image = AssetImage(resolvedAvatar.filePath ?? "");
    }

    if (resolvedAvatar != null && resolvedAvatar.isUrl) {
      image = NetworkImage(resolvedAvatar.fileUrl ?? "");
    }

    if (resolvedAvatar != null && resolvedAvatar.isFile) {
      image = FileImage(resolvedAvatar.file!);
    }

    if (image != null) {
      decoration = DecorationImage(
        image: image,
        fit: fit ?? BoxFit.cover,
        alignment: alignment,
      );
    }

    return Align(
      alignment: alignment,
      child: InkWell(
        onTap: () {
          HapticFeedback.lightImpact();
          if (scaffoldKey != null) {
            trackEvent(AppEvent.viewedEndDrawer);
            scaffoldKey?.currentState?.openEndDrawer();
            return;
          }
          onPressed?.call();
        },
        child: Container(
          width: size ?? defaultSize,
          height: size ?? defaultSize,
          alignment: Alignment.center,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: defaultAvatarColor ?? bgColor,
            image: decoration,
            border: Border.all(
              color: borderColor ?? context.transparent,
              width: borderColor != null ? 1 : 0,
            ),
          ),
          child: AppText(
            initials,
            style: context.textStyle.b2(weight: FontWeight.w500),
            textAlign: TextAlign.center,
          ),
        ),
      ),
    );
  }
}

class AppAvatarStack extends AppStatelessWidget {
  final List<(String, AppImageData?)> _avatars;
  final OnChanged<int>? onSelect;

  AppAvatarStack(
    List<(String?, AppImageData?)> avatars, {
    super.key,
    this.onSelect,
  }) : _avatars = _getValidAvatars(avatars);

  static List<(String, AppImageData?)> _getValidAvatars(
    List<(String?, AppImageData?)> avatars,
  ) {
    final itemsWithIntials = avatars.whereList((it) => it.$1.hasValue);
    return itemsWithIntials.mapList((it) => (it.$1!, it.$2));
  }

  bool get isInExcess => _avatars.length > 3;
  int get excessCount => _avatars.length - 3;
  String get excessText => "+$excessCount";

  List<(String, AppImageData?)> get _presentedAvatars {
    if (!isInExcess) return _avatars;
    return [..._avatars.take(3)];
  }

  @override
  Widget build(BuildContext context) {
    if (_avatars.length == 1) {
      return AppAvatar(
        avatar: _avatars.first.$2,
        initials: _avatars.first.$1,
        bgColor: context.scaffoldBgColor,
        onPressed: () => onSelect?.call(0),
      );
    }

    final offset = context.sp(AppFontSizes.px28);
    final size =
        isInExcess ? _presentedAvatars.length + 1 : _presentedAvatars.length;
    final maxHeight = context.sp(AppFontSizes.px42);
    final widthMultiplier = switch (size) {
      2 => size * 1.3,
      3 => size * 1.1,
      _ => size,
    };
    final maxWidth = context.sp(AppFontSizes.px30) * widthMultiplier;

    return ConstrainedBox(
      constraints: BoxConstraints(maxHeight: maxHeight, maxWidth: maxWidth),
      child: Stack(
        clipBehavior: Clip.antiAlias,
        alignment: Alignment.centerLeft,
        fit: StackFit.passthrough,
        children: <Widget>[
          for (final (index, (initials, avatar)) in _presentedAvatars.indexed)
            Positioned(
              left: offset * index,
              child: AppAvatar(
                initials: initials.value,
                avatar: avatar,
                bgColor: context.scaffoldBgColor,
                onPressed: () => onSelect?.call(index),
              ),
            ),
          if (isInExcess)
            Positioned(
              left: offset * 3,
              child: AppAvatar(
                initials: excessText,
                bgColor: context.scaffoldBgColor,
              ),
            ),
        ],
      ),
    );
  }
}
