import 'package:day1/day1.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

class AppSvg extends AppStatelessWidget {
  final String path;
  final double? width;
  final double? height;
  final BoxFit fit;
  final AlignmentGeometry alignment;
  final WidgetBuilder? placeholderBuilder;
  final String? semanticsLabel;
  final Color? color;
  final BlendMode colorBlendMode;
  final bool _renderAsText;

  const AppSvg(
    this.path, {
    super.key,
    this.width,
    this.height,
    this.fit = BoxFit.contain,
    this.alignment = Alignment.center,
    this.placeholderBuilder,
    this.color,
    this.colorBlendMode = BlendMode.srcIn,
    this.semanticsLabel,
  }) : _renderAsText = false;

  const AppSvg.asText(
    this.path, {
    super.key,
    double? size,
    this.fit = BoxFit.contain,
    this.alignment = Alignment.center,
    this.placeholderBuilder,
    this.colorBlendMode = BlendMode.srcIn,
    this.semanticsLabel,
  })  : width = size,
        height = size,
        color = null,
        _renderAsText = true;

  ColorFilter? _getColor(BuildContext context) {
    if (color == null && !_renderAsText) return null;
    return ColorFilter.mode(color ?? context.textColor, BlendMode.srcATop);
  }

  @override
  Widget build(BuildContext context) {
    context.observeTheme();
    if (path.startsWith('http')) {
      return SvgPicture.network(
        path,
        width: width,
        height: height,
        fit: fit,
        alignment: alignment,
        placeholderBuilder: placeholderBuilder,
        semanticsLabel: semanticsLabel,
        colorFilter: _getColor(context),
      );
    }
    return SvgPicture.asset(
      path,
      width: width,
      height: height,
      fit: fit,
      alignment: alignment,
      placeholderBuilder: placeholderBuilder,
      semanticsLabel: semanticsLabel,
      colorFilter: _getColor(context),
    );
  }
}
