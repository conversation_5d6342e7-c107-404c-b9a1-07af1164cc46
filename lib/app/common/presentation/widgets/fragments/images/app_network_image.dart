import 'dart:convert';

import 'package:day1/day1.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';

class AppNetworkImage extends AppStatelessWidget {
  final String? placeHolderPath;
  final String imageUrl;
  final double? width;
  final double? height;
  final BoxFit? fit;
  final Alignment alignment;
  final Widget? errorWidget;

  const AppNetworkImage(
    this.imageUrl, {
    super.key,
    this.placeHolderPath,
    this.alignment = Alignment.center,
    this.fit,
    this.width,
    this.height,
    this.errorWidget,
  });

  @override
  Widget build(BuildContext context) {
    if (imageUrl.isEmpty) return const Offstage();

    final imageShimmer = AppImageShimmer(
      alignment: alignment,
      fit: fit,
      height: height,
      width: width,
    );

    if (imageUrl.startsWith("data:")) {
      final base64String = imageUrl.replaceAll("data:", "");
      return Image.memory(
        base64Decode(base64String),
        height: height,
        width: width,
        alignment: alignment,
        fit: fit,
      );
    }

    if (imageUrl.endsWith(".svg")) {
      return AppSvg(
        imageUrl,
        height: height,
        width: width,
        alignment: alignment,
        fit: fit ?? BoxFit.contain,
      );
    }

    return CachedNetworkImage(
      imageUrl: imageUrl,
      height: height,
      width: width,
      alignment: alignment,
      fit: fit,
      errorWidget: (_, __, ___) => errorWidget ?? imageShimmer,
      placeholder: (context, _) {
        if (placeHolderPath == null) return imageShimmer;
        return AppAssetImage(
          placeHolderPath!,
          width: width,
          height: height,
          alignment: alignment,
        );
      },
    );
  }
}
