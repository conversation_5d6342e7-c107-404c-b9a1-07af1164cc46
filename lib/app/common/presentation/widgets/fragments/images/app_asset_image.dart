import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class AppAssetImage extends AppStatelessWidget {
  final String imageUrl;
  final double? width;
  final double? height;
  final BoxFit? fit;
  final Alignment alignment;

  const AppAssetImage(
    this.imageUrl, {
    super.key,
    this.alignment = Alignment.center,
    this.fit,
    this.width,
    this.height,
  });

  @override
  Widget build(BuildContext context) {
    if (imageUrl.endsWith("svg")) {
      return AppSvg(
        imageUrl,
        fit: fit ?? BoxFit.contain,
        alignment: alignment,
        width: width,
        height: height,
      );
    }
    return Image.asset(
      imageUrl,
      fit: fit,
      alignment: alignment,
      width: width,
      height: height,
    );
  }
}
