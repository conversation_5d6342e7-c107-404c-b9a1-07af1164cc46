import 'dart:io';

import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class AppFileImage extends AppStatelessWidget {
  final File? imageFile;
  final double? width;
  final double? height;
  final BoxFit? fit;
  final Alignment alignment;

  const AppFileImage(
    this.imageFile, {
    super.key,
    this.alignment = Alignment.center,
    this.fit,
    this.width,
    this.height,
  });

  @override
  Widget build(BuildContext context) {
    if (imageFile == null) {
      return Align(
        alignment: alignment,
        child: FittedBox(
          fit: fit ?? BoxFit.contain,
          child: SizedBox(
            height: height,
            width: width,
          ),
        ),
      );
    }
    return Image.file(
      imageFile!,
      fit: fit,
      alignment: alignment,
      width: width,
      height: height,
    );
  }
}
