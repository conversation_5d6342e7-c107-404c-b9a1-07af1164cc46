import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class AppImage extends AppStatelessWidget {
  final AppImageData? image;
  final double? width;
  final double? height;
  final BoxFit? fit;
  final Alignment alignment;
  final GlobalKey<ScaffoldState>? scaffoldKey;
  final bool useDefaultSize;

  const AppImage({
    this.image,
    this.scaffoldKey,
    super.key,
    this.alignment = Alignment.center,
    this.fit,
    this.width,
    this.height,
    this.useDefaultSize = true,
  });

  @override
  Widget build(BuildContext context) {
    final defaultSize = useDefaultSize ? context.sp(AppFontSizes.px40) : null;
    return Align(
      alignment: alignment,
      child: Builder(builder: (_) {
        if (image?.isUrl ?? false) {
          return AppNetworkImage(
            image?.fileUrl ?? "",
            alignment: Alignment.center,
            width: width ?? defaultSize,
            height: height ?? defaultSize,
            fit: fit ?? BoxFit.cover,
          );
        }
        if (image?.isString ?? false) {
          return AppAssetImage(
            image?.filePath ?? "",
            alignment: Alignment.center,
            width: width ?? defaultSize,
            height: height ?? defaultSize,
            fit: fit ?? BoxFit.cover,
          );
        }
        if (image?.isFile ?? false) {
          return AppFileImage(
            image?.file,
            alignment: Alignment.center,
            width: width ?? defaultSize,
            height: height ?? defaultSize,
            fit: fit ?? BoxFit.cover,
          );
        }
        return Align(
          alignment: alignment,
          child: FittedBox(
            fit: fit ?? BoxFit.cover,
            child: SizedBox(
              width: width ?? defaultSize,
              height: height ?? defaultSize,
            ),
          ),
        );
      }),
    );
  }
}
