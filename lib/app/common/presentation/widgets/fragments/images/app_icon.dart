import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class AppIcon extends AppStatelessWidget {
  final IconData icon;
  final double? size;
  final List<Shadow>? shadows;
  final AlignmentGeometry? alignment;
  final String? semanticsLabel;
  final Color? color;
  final bool useDefaultColor;

  const AppIcon(
    this.icon, {
    super.key,
    this.size,
    this.shadows,
    this.alignment,
    this.color,
    this.semanticsLabel,
    this.useDefaultColor = true,
  });

  @override
  Widget build(BuildContext context) {
    context.observeTheme();
    Color? iconColor = color;
    if (useDefaultColor) {
      iconColor ??= context.iconColor;
    }
    final iconSize = context.sp(size ?? AppFontSizes.px20);

    Widget child = Icon(
      icon,
      size: iconSize,
      color: iconColor,
      semanticLabel: semanticsLabel,
      shadows: shadows,
    );
    if (alignment != null) {
      child = Align(
        alignment: alignment!,
        child: child,
      );
    }
    return child;
  }
}
