import 'package:flutter/material.dart';
import 'package:lottie/lottie.dart';
import 'package:day1/app/common/common.dart';

class AppLottie extends AppStatelessWidget {
  final String path;
  final double? width;
  final double? height;
  final BoxFit fit;
  final bool repeat;
  final AlignmentGeometry alignment;

  const AppLottie(
    this.path, {
    super.key,
    this.width,
    this.height,
    this.repeat = true,
    this.fit = BoxFit.contain,
    this.alignment = Alignment.center,
  });

  @override
  Widget build(BuildContext context) {
    return LottieBuilder.asset(
      path,
      height: height,
      alignment: alignment,
      repeat: repeat,
      fit: fit,
      width: width,
    );
  }
}
