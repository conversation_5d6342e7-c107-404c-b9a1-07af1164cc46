import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class MobileModalBody extends AppStatefulWidget {
  final ScrollController? _controller;
  final Widget modalWidget;
  final ValueBuilder<ScrollController>? _builder;
  final EdgeInsets? padding;
  final BoxShadow? shadow;
  final bool _draggable;
  final String? title;
  final ValueNotifier<bool> showEditor;
  final TextEditingController? controller;

  const MobileModalBody({
    required this.modalWidget,
    this.padding,
    required this.showEditor,
    this.controller,
    this.title,
    this.shadow,
    super.key,
  })  : _builder = null,
        _draggable = false,
        _controller = null;

  const MobileModalBody.draggable({
    required ValueBuilder<ScrollController> builder,
    required ScrollController controller,
    required this.showEditor,
    TextEditingController? textEditingController,
    this.title,
    this.shadow,
    super.key,
    this.padding,
  })  : _builder = builder,
        _controller = controller,
        controller = textEditingController,
        _draggable = true,
        modalWidget = const Offstage();

  @override
  State<StatefulWidget> createState() => _MobileModalBodyState();
}

class _MobileModalBodyState extends State<MobileModalBody> {
  @override
  Widget build(BuildContext context) {
    Widget child = Builder(
      builder: (context) {
        if (widget._draggable) {
          return widget._builder!(widget._controller!);
        }
        return ConstrainedBox(
          constraints: BoxConstraints(
            minHeight: context.fractionalHeight(30),
            maxHeight: context.fractionalHeight(80),
          ),
          child: widget.modalWidget,
        );
      },
    );

    if (widget.title != null) {
      child = Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const AppGap.y12(),
          if (widget.title != null)
            DoAppBar(
              title: widget.title,
              color: context.transparent,
              leading: const AppCloseButton(),
            ),
          Expanded(child: child)
        ],
      );
    }

    return SafeArea(
      child: Align(
        alignment: Alignment.bottomCenter,
        child: AppCard(
          margin: context.insets.defaultAllInsets,
          padding: widget.padding ?? EdgeInsets.zero,
          child: child,
        ),
      ),
    );
  }
}
