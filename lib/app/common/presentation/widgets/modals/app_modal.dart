import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class AppBottomModal {
  ValueBuilder<ScrollController>? builder;
  ValueNotifier<bool> editorPresentableRef = ValueNotifier(false);

  AppBottomModal({
    required this.ctx,
    required this.modalWidget,
    this.isDismissable = true,
    this.canDragToClose = true,
    this.isScrollable = false,
    this.canPop = true,
    this.title,
    this.padding,
    this.barrierColor,
    this.searchController,
    this.shadow,
  })  : builder = null,
        maxChildSize = .9,
        minChildSize = .7,
        initialChildSize = .7,
        _isDraggble = false {
    _showModal = _init(ctx);
  }

  AppBottomModal.draggable({
    required this.ctx,
    required this.builder,
    this.minChildSize = .3,
    this.initialChildSize = .7,
    this.maxChildSize = .9,
    this.title,
    this.padding,
    this.barrierColor,
    this.searchController,
    this.shadow,
  })  : _isDraggble = true,
        modalWidget = const Offstage(),
        isScrollable = true,
        canPop = true,
        canDragToClose = true,
        isDismissable = true,
        assert(maxChildSize >= .1 && maxChildSize <= 1),
        assert(minChildSize >= 0 && minChildSize <= 1),
        assert(initialChildSize >= .1 && initialChildSize <= 1) {
    _showModal = _init(ctx);
  }

  final BuildContext ctx;
  final Color? barrierColor;
  final BoxShadow? shadow;
  final TextEditingController? searchController;
  final Widget modalWidget;
  final String? title;
  final double initialChildSize;
  final double maxChildSize;
  final double minChildSize;
  final EdgeInsets? padding;
  final bool isDismissable;
  final bool canDragToClose;
  final bool _isDraggble;
  final bool canPop;
  final bool isScrollable;

  Future? _showModal;

  Future<dynamic> _init(BuildContext context) async {
    AppOverlay.closeCurrentOverlays();
    AppRouter.openedModal();

    if (!context.isMobile) {
      return showAdaptiveDialog(
        context: context,
        barrierDismissible: isDismissable,
        barrierColor: barrierColor,
        anchorPoint: Offset(
          context.sizer.width * .5,
          context.sizer.height * .5,
        ),
        builder: (context) {
          if (_isDraggble) {
            return DesktopModalBody.draggable(
              builder: builder!,
              maxChildHeightFraction: initialChildSize,
              minChildHeightFraction: minChildSize,
              shadow: shadow,
              key: const Key("draggable_desktop_modal"),
              title: title,
              showEditor: editorPresentableRef,
              controller: searchController,
            );
          }
          return DesktopModalBody(
            modalWidget: modalWidget,
            shadow: shadow,
            key: const Key("fixed_desktop_modal"),
            title: title,
            showEditor: editorPresentableRef,
            controller: searchController,
          );
        },
      );
    }

    return showModalBottomSheet(
      context: context,
      isDismissible: isDismissable,
      enableDrag: canDragToClose,
      isScrollControlled: isScrollable,
      backgroundColor: context.transparent,
      barrierColor: barrierColor,
      builder: (context) {
        if (_isDraggble) {
          return DraggableScrollableSheet(
            initialChildSize: initialChildSize,
            maxChildSize: maxChildSize,
            minChildSize: minChildSize,
            builder: (context, scrollController) {
              return MobileModalBody.draggable(
                shadow: shadow,
                key: const Key("draggable_mobile_bottom_modal"),
                builder: builder!,
                controller: scrollController,
                title: title,
                showEditor: editorPresentableRef,
                textEditingController: searchController,
              );
            },
          );
        }
        return AppPopScope(
          canPop: canPop,
          child: MobileModalBody(
            shadow: shadow,
            key: const Key("fixed_mobile_bottom_modal"),
            modalWidget: modalWidget,
            title: title,
            showEditor: editorPresentableRef,
            controller: searchController,
          ),
        );
      },
    );
  }

  Future? get showModal => _showModal;
}
