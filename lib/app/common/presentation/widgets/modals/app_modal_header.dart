import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class ModalHeader extends AppStatefulWidget {
  final ValueNotifier<bool> showEditor;
  final TextEditingController? controller;
  final String? title;

  const ModalHeader({
    super.key,
    this.title,
    this.controller,
    required this.showEditor,
  });

  @override
  State<StatefulWidget> createState() => _ModalHeaderState();
}

class _ModalHeaderState extends State<ModalHeader> {
  final FocusNode _focusNode = FocusNode();

  @override
  void initState() {
    super.initState();

    widget.showEditor.addListener(() {
      if (widget.showEditor.value == false) {
        widget.controller?.clear();
      } else {
        WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
          context.requestFocus(_focusNode);
        });
      }
    });
  }

  @override
  void dispose() {
    // widget.showEditor.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final borderRadius = BorderRadius.vertical(
      top: Radius.circular(context.lgRadius),
    );
    return Container(
      alignment: Alignment.center,
      padding: context.insets.defaultHorizontalInsets,
      constraints: BoxConstraints.tightFor(
        height: context.sp(AppFontSizes.px52),
      ),
      decoration: BoxDecoration(
        color: context.dividerColor,
        borderRadius: borderRadius,
      ),
      child: Table(
        columnWidths: const {
          0: FlexColumnWidth(1),
          1: FlexColumnWidth(10),
          2: FlexColumnWidth(1),
        },
        defaultVerticalAlignment: TableCellVerticalAlignment.middle,
        children: [
          TableRow(children: [
            if (widget.controller != null)
              BoolListener(
                valueListenable: widget.showEditor,
                builder: (show) {
                  return AppAnimatedSwitcher(
                    child: AppIconButton(
                      key: ValueKey(show),
                      icon: show ? AppIcons.arrowRight : AppIcons.search,
                      alignment: Alignment.centerLeft,
                      onPressed: () {
                        widget.showEditor.value = !widget.showEditor.value;
                      },
                    ),
                  );
                },
              ),
            if (widget.controller == null) const Offstage(),
            BoolListener(
              valueListenable: widget.showEditor,
              builder: (show) {
                return AppAnimatedSwitcher(
                  child: Builder(
                    key: ValueKey(show),
                    builder: (context) {
                      if (!show || widget.controller == null) {
                        return AppText(
                          widget.title,
                          textAlign: TextAlign.center,
                          maxLines: 1,
                          style: context.textStyle.h4(),
                        );
                      }
                      return AppTextField(
                        focusNode: _focusNode,
                        controller: widget.controller,
                        decoration: context.inputStyle.none,
                        autoCorrect: false,
                        textInputAction: TextInputAction.search,
                      );
                    },
                  ),
                );
              },
            ),
            const AppCloseButton(
              size: AppFontSizes.px16,
              alignment: Alignment.centerRight,
            ),
          ])
        ],
      ),
    );
  }
}
