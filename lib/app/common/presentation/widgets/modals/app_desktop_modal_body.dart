import 'dart:math';

import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class DesktopModalBody extends AppStatefulWidget {
  final Widget modalWidget;
  final ValueBuilder<ScrollController>? _builder;
  final EdgeInsets? padding;
  final BoxShadow? shadow;
  final bool _draggable;
  final double maxChildHeightFraction;
  final double minChildHeightFraction;
  final double _widthFraction;
  final String? title;
  final ValueNotifier<bool> showEditor;
  final TextEditingController? controller;

  const DesktopModalBody({
    required this.modalWidget,
    this.padding,
    required this.showEditor,
    this.controller,
    this.title,
    this.shadow,
    super.key,
  })  : _builder = null,
        maxChildHeightFraction = .4,
        minChildHeightFraction = .3,
        _widthFraction = .45,
        _draggable = false;

  const DesktopModalBody.draggable({
    required ValueBuilder<ScrollController> builder,
    this.padding,
    required this.maxChildHeightFraction,
    required this.minChildHeightFraction,
    required this.showEditor,
    this.controller,
    this.title,
    this.shadow,
    super.key,
  })  : _builder = builder,
        _draggable = true,
        _widthFraction = .55,
        modalWidget = const Offstage();
  @override
  State<StatefulWidget> createState() => _DesktopModalBodyState();
}

class _DesktopModalBodyState extends State<DesktopModalBody> {
  @override
  Widget build(BuildContext context) {
    final multipliend = min(widget.maxChildHeightFraction, .7);
    return Center(
      child: ConstrainedBox(
        constraints: BoxConstraints(
          maxWidth: context.sizer.width * widget._widthFraction,
          maxHeight: context.sizer.height * multipliend,
          minHeight: context.sizer.height * widget.minChildHeightFraction,
        ),
        child: ClipRRect(
          borderRadius: context.lgBorderRadius,
          child: Container(
            padding: widget.padding,
            decoration: BoxDecoration(
              color: context.scaffoldBgColor,
              boxShadow: [if (widget.shadow != null) widget.shadow!],
              borderRadius: context.lgBorderRadius,
            ),
            child: Column(
              children: [
                ModalHeader(
                  title: widget.title,
                  controller: widget.controller,
                  showEditor: widget.showEditor,
                  key: const Key("desktop-modal-header"),
                ),
                Expanded(
                  child: Builder(builder: (context) {
                    if (widget._draggable) {
                      return widget._builder!(ScrollController());
                    }
                    return widget.modalWidget;
                  }),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
