import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class AppText extends AppStatelessWidget {
  final String? data;
  final TextStyle? style;
  final TextAlign? textAlign;
  final Locale? locale;
  final StrutStyle? strutStyle;
  final bool? softWrap;
  final int? maxLines;
  final TextDirection? textDirection;
  final TextOverflow? overflow;

  const AppText(
    this.data, {
    this.style,
    this.textAlign,
    this.locale,
    this.maxLines,
    this.overflow,
    this.softWrap,
    this.strutStyle,
    this.textDirection,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final computedStyle = style != null ? style! : context.textStyle.b3();

    return Text(
      data ?? "",
      style: computedStyle,
      strutStyle: strutStyle,
      textAlign: textAlign,
      locale: locale ?? context.currentLocale,
      maxLines: maxLines,
      overflow: overflow,
      softWrap: softWrap,
      textDirection: textDirection,
      textWidthBasis: TextWidthBasis.parent,
    );
  }
}
