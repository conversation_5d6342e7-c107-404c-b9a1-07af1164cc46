import 'package:day1/day1.dart';
import 'package:flutter/material.dart';
import 'package:styled_text/styled_text.dart';

class AppRichText extends AppStatelessWidget {
  final TextAlign? textAlign;
  final TextStyle? textStyle;
  final TextOverflow? textOverflow;
  final String? text;
  final Color? hashTagColor;
  final int? maxLines;
  final OnChanged<String>? onTextTap;

  const AppRichText(
    this.text, {
    this.textStyle,
    this.textAlign,
    this.maxLines,
    this.hashTagColor,
    this.textOverflow,
    this.onTextTap,
    super.key,
  });

  String? get _parsedText {
    String inputText = text.value;
    final hashTags = AppRegex.hashTagRegex.allMatches(inputText);

    if (!hashTags.hasValue) return text;

    final splitText = inputText.split(" ");

    for (var (index, it) in splitText.indexed) {
      final match = AppRegex.hashTagRegex.stringMatch(it);
      if (!match.hasValue) continue;
      splitText[index] = "<ht>$match</ht>";
    }

    return splitText.join(" ");
  }

  @override
  Widget build(BuildContext context) {
    final style = textStyle ?? context.textStyle.b3();
    final content = _parsedText.value;
    final linkColor = context.highlightedTextColor.withOpacity(.75);

    // ignore: no_leading_underscores_for_local_identifiers
    _launchUrl(String? text, Map<String?, String?> attributes) {
      final url = attributes["href"] ?? text.value;
      if (onTextTap != null) {
        onTextTap?.call(url);
        return;
      }
      launchUrl(url);
    }

    return StyledText(
      text: content,
      textAlign: textAlign,
      style: style,
      maxLines: maxLines,
      overflow: textOverflow,
      tags: {
        'p': StyledTextActionTag(
          (text, attributes) => onTextTap?.call(text.value),
          style: style,
        ),
        'ht': StyledTextActionTag(
          (text, attributes) => onTextTap?.call(text.value),
          style: style.copyWith(
            color: hashTagColor ?? context.highlightedTextColor,
          ),
        ),
        'h2': StyledTextActionTag(
          (text, attributes) => onTextTap?.call(text.value),
          style: context.textStyle.h2(),
        ),
        'h3': StyledTextActionTag(
          (text, attributes) => onTextTap?.call(text.value),
          style: context.textStyle.h3(),
        ),
        'h4': StyledTextActionTag(
          (text, attributes) => onTextTap?.call(text.value),
          style: context.textStyle.h4(),
        ),
        'h5': StyledTextActionTag(
          (text, attributes) => onTextTap?.call(text.value),
          style: context.textStyle.h4(),
        ),
        'h6': StyledTextActionTag(
          (text, attributes) => onTextTap?.call(text.value),
          style: context.textStyle.h4(),
        ),
        'b': StyledTextActionTag(
          (text, attributes) => onTextTap?.call(text.value),
          style: style.copyWith(fontWeight: FontWeight.bold),
        ),
        'sb': StyledTextActionTag(
          (text, attributes) => onTextTap?.call(text.value),
          style: style.copyWith(fontWeight: FontWeight.w600),
        ),
        'm': StyledTextActionTag(
          (text, attributes) => onTextTap?.call(text.value),
          style: style.copyWith(fontWeight: FontWeight.w500),
        ),
        'em': StyledTextActionTag(
          (text, attributes) => onTextTap?.call(text.value),
          style: style.copyWith(fontStyle: FontStyle.italic),
        ),
        'strong': StyledTextActionTag(
          (text, attributes) => onTextTap?.call(text.value),
          style: style.copyWith(fontWeight: FontWeight.bold),
        ),
        'u': StyledTextActionTag(
          (text, attributes) => onTextTap?.call(text.value),
          style: style.copyWith(
            decoration: TextDecoration.underline,
            decorationColor: style.color,
          ),
        ),
        'bu': StyledTextActionTag(
          (text, attributes) => onTextTap?.call(text.value),
          style: style.copyWith(
            decoration: TextDecoration.underline,
            decorationColor: style.color,
            fontWeight: FontWeight.bold,
          ),
        ),
        'mu': StyledTextActionTag(
          (text, attributes) => onTextTap?.call(text.value),
          style: style.copyWith(
            decoration: TextDecoration.underline,
            decorationColor: style.color,
            fontWeight: FontWeight.w600,
          ),
        ),
        'a': StyledTextActionTag(
          _launchUrl,
          style: style.copyWith(
            color: linkColor,
            decorationColor: style.color,
          ),
        ),
        'ba': StyledTextActionTag(
          _launchUrl,
          style: style.copyWith(
            color: linkColor,
            fontWeight: FontWeight.w600,
          ),
        ),
        'sba': StyledTextActionTag(
          _launchUrl,
          style: style.copyWith(
            color: linkColor,
            fontWeight: FontWeight.w600,
          ),
        ),
        'ma': StyledTextActionTag(
          _launchUrl,
          style: style.copyWith(
            color: linkColor,
            fontWeight: FontWeight.w500,
          ),
        ),
      },
    );
  }
}
