import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class AppEditableText extends AppStatefulWidget {
  final TextStyle? style;
  final TextAlign? textAlign;
  final Locale? locale;
  final StrutStyle? strutStyle;
  final bool? softWrap;
  final bool autofocus;
  final int? maxLines;
  final TextDirection? textDirection;
  final bool readonly;
  final TextEditingController controller;
  final FocusNode? focusNode;
  final OnChanged<String>? onSubmit;
  final TextCapitalization textCapitalization;

  const AppEditableText({
    this.style,
    this.textAlign,
    this.locale,
    this.maxLines,
    this.softWrap,
    this.strutStyle,
    this.textDirection,
    this.readonly = false,
    this.autofocus = false,
    this.onSubmit,
    required this.controller,
    this.focusNode,
    this.textCapitalization = TextCapitalization.none,
    super.key,
  });

  @override
  State<StatefulWidget> createState() => _AppEditableTextState();
}

class _AppEditableTextState extends State<AppEditableText> {
  @override
  Widget build(BuildContext context) {
    final computedStyle =
        widget.style != null ? widget.style! : context.textStyle.b1();

    return TextField(
      contextMenuBuilder: (context, state) {
        return AdaptiveTextSelectionToolbar.editableText(
          editableTextState: state,
        );
      },
      showCursor: true,
      onSubmitted: widget.onSubmit,
      onEditingComplete: () {
        if (widget.onSubmit == null) return;
        widget.onSubmit!(widget.controller.text);
      },
      readOnly: widget.readonly,
      controller: widget.controller,
      focusNode: widget.focusNode ?? FocusNode(),
      cursorColor: Theme.of(context).indicatorColor,
      style: computedStyle,
      strutStyle: widget.strutStyle,
      textAlign: widget.textAlign ?? TextAlign.start,
      maxLines: widget.maxLines,
      textDirection: widget.textDirection,
      textInputAction: TextInputAction.done,
      autofocus: widget.autofocus,
      textCapitalization: widget.textCapitalization,
      decoration: context.inputStyle.none.copyWith(
        hintText: LocaleKeys.enter.tr(),
      ),
    );
  }
}
