import 'package:day1/day1.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class AppUrlText extends AppStatelessWidget {
  final String? data;
  final String url;
  final TextStyle? style;
  final TextAlign? textAlign;
  final StrutStyle? strutStyle;
  final TextDirection? textDirection;
  final TextOverflow? overflow;
  final OnPressed? onTap;
  final EdgeInsets padding;

  const AppUrlText(
    this.data, {
    required this.url,
    this.style,
    this.textAlign,
    this.overflow,
    this.strutStyle,
    this.textDirection,
    this.onTap,
    super.key,
    this.padding = EdgeInsets.zero,
  });

  @override
  Widget build(BuildContext context) {
    final computedStyle = style != null ? style! : context.textStyle.b1();

    return InkWell(
      onTap: () {
        HapticFeedback.lightImpact();
        onTap?.call();
        if (url.isNotEmpty) {
          trackEvent(AppEvent.openedLink, value: url);
          context.launchUrl(url);
        }
      },
      child: Padding(
        padding: padding,
        child: AppText(
          data,
          style: computedStyle.copyWith(decoration: TextDecoration.underline),
          textAlign: textAlign,
          strutStyle: strutStyle,
          textDirection: textDirection,
          overflow: overflow,
        ),
      ),
    );
  }
}
