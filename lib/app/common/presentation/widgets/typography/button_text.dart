import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class ButtonText extends AppStatelessWidget {
  final Widget? icon;
  final Widget? trailingIcon;
  final String? text;
  final TextDecoration? decoration;
  final ButtonSize buttonSize;
  final AlignmentGeometry? alignment;
  final bool disabled;
  final bool _flat;
  final Color? textColor;

  const ButtonText(
    this.text, {
    super.key,
    bool flat = false,
    required this.disabled,
    required this.buttonSize,
    required this.alignment,
    this.icon,
    this.trailingIcon,
    this.decoration,
    this.textColor,
  }) : _flat = flat;

  TextStyle _textStyle(BuildContext context) {
    final textStyle = context.textStyle;
    if (_flat) {
      return switch (buttonSize) {
        ButtonSize.small => textStyle.textBtnSm(decoration: decoration),
        ButtonSize.medium => textStyle.textBtnMd(decoration: decoration),
        _ => textStyle.textBtn(decoration: decoration)
      };
    }
    return switch (buttonSize) {
      ButtonSize.small => textStyle.raisedBtnSm(),
      ButtonSize.medium => textStyle.raisedBtnMd(),
      _ => textStyle.raisedBtn()
    };
  }

  @override
  Widget build(BuildContext context) {
    TextStyle btnStyle = _textStyle(context);
    if (textColor != null) {
      btnStyle = btnStyle.copyWith(
        color: textColor,
        decorationColor: textColor,
      );
    }
    if (disabled) {
      btnStyle = btnStyle.copyWith(
        color: context.disabledBtntextColor,
        decoration: decoration,
        decorationColor: context.disabledBtntextColor,
      );
    }
    if (icon != null || trailingIcon != null) {
      return _ButtonTextWithIcon(
        text: text,
        alignment: alignment,
        style: btnStyle,
        leadingIcon: icon,
        trailingIcon: trailingIcon,
        buttonSize: buttonSize,
      );
    }
    return _ButtonText(
      text: text ?? LocaleKeys.submit.tr(),
      style: btnStyle,
    );
  }
}

class _ButtonTextWithIcon extends AppStatelessWidget {
  final String? text;
  final TextStyle? style;
  final Widget? leadingIcon;
  final Widget? trailingIcon;
  final AlignmentGeometry? alignment;
  final ButtonSize buttonSize;

  const _ButtonTextWithIcon({
    required this.text,
    required this.buttonSize,
    this.alignment,
    this.style,
    this.leadingIcon,
    this.trailingIcon,
  });

  @override
  Widget build(BuildContext context) {
    Widget? child;

    if (text.hasValue) {
      child = _ButtonText(
        text: text!,
        style: style,
      );
    }

    child = Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment: child != null
          ? MainAxisAlignment.spaceBetween
          : MainAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        if (leadingIcon != null) ...[
          Flexible(
            child: _ButtonIconContainer(
              icon: leadingIcon,
              buttonSize: buttonSize,
            ),
          ),
          if (child != null || trailingIcon != null) const AppGap.h12(),
        ],
        if (child != null) child,
        if (trailingIcon != null) ...[
          if (child != null || leadingIcon != null) const AppGap.h12(),
          Flexible(
            child: _ButtonIconContainer(
              icon: trailingIcon,
              buttonSize: buttonSize,
            ),
          )
        ]
      ],
    );

    if (alignment != null) {
      child = Align(
        alignment: alignment!,
        child: child,
      );
    }

    return child;
  }
}

class _ButtonIconContainer extends AppStatelessWidget {
  final Widget? icon;
  final ButtonSize buttonSize;

  const _ButtonIconContainer({
    required this.icon,
    required this.buttonSize,
  });

  double _iconSize(BuildContext context) {
    return switch (buttonSize) {
      ButtonSize.small => context.sp(AppFontSizes.px20),
      _ => context.sp(AppFontSizes.px24),
    };
  }

  @override
  Widget build(BuildContext context) {
    if (icon == null) return const Offstage();
    final size = _iconSize(context);
    return SizedBox.square(
      dimension: size,
      child: SizedBox.shrink(child: icon),
    );
  }
}

class _ButtonText extends AppStatelessWidget {
  final String text;
  final TextStyle? style;

  const _ButtonText({
    required this.text,
    this.style,
  });

  @override
  Widget build(BuildContext context) {
    return AppText(
      text,
      textAlign: TextAlign.center,
      style: style,
      maxLines: 1,
    );
  }
}
