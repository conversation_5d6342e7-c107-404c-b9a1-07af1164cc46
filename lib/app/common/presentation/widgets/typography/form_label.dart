import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class FormLabel extends AppStatelessWidget {
  final String label;
  final bool isRequired;

  const FormLabel({super.key, required this.label, this.isRequired = false});

  bool get _isRequired => isRequired || label.value.endsWith("*");

  String get _label {
    return label.trim().replaceAll(RegExp(r'\*$'), '');
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: context.insets.onlySp(bottom: AppFontSizes.px8),
      child: Text.rich(
        TextSpan(
          text: _label,
          style: context.textStyle.labelText(weight: FontWeight.w500),
          children: [
            if (_isRequired)
              TextSpan(
                text: "*",
                style: context.textStyle.helperText(),
              )
          ],
        ),
      ),
    );
  }
}
