import 'package:flutter/material.dart';

class AppIconData extends IconData {
  static const _font = "Iconoir";
  const AppIconData(super.codePoint) : super(fontFamily: _font);
}

class AppIcons {
  AppIcons._();
  static const AppIconData accessibilitySign = AppIconData(0xea01);
  static const AppIconData accessibilityTech = AppIconData(0xea02);
  static const AppIconData accessibility = AppIconData(0xea03);
  static const AppIconData activity = AppIconData(0xea04);
  static const AppIconData adobeAfterEffects = AppIconData(0xea05);
  static const AppIconData adobeIllustrator = AppIconData(0xea06);
  static const AppIconData adobeIndesign = AppIconData(0xea07);
  static const AppIconData adobeLightroom = AppIconData(0xea08);
  static const AppIconData adobePhotoshop = AppIconData(0xea09);
  static const AppIconData adobeXd = AppIconData(0xea0a);
  static const AppIconData africanTree = AppIconData(0xea0b);
  static const AppIconData agile = AppIconData(0xea0c);
  static const AppIconData airConditioner = AppIconData(0xea0d);
  static const AppIconData airplaneHelix45deg = AppIconData(0xea0e);
  static const AppIconData airplaneHelix = AppIconData(0xea0f);
  static const AppIconData airplaneOff = AppIconData(0xea10);
  static const AppIconData airplaneRotation = AppIconData(0xea11);
  static const AppIconData airplane = AppIconData(0xea12);
  static const AppIconData airplay = AppIconData(0xea13);
  static const AppIconData alarm = AppIconData(0xea14);
  static const AppIconData albumCarousel = AppIconData(0xea15);
  static const AppIconData albumList = AppIconData(0xea16);
  static const AppIconData albumOpen = AppIconData(0xea17);
  static const AppIconData album = AppIconData(0xea18);
  static const AppIconData alignBottomBox = AppIconData(0xea19);
  static const AppIconData alignCenter = AppIconData(0xea1a);
  static const AppIconData alignHorizontalCenters = AppIconData(0xea1b);
  static const AppIconData alignHorizontalSpacing = AppIconData(0xea1c);
  static const AppIconData alignJustify = AppIconData(0xea1d);
  static const AppIconData alignLeftBox = AppIconData(0xea1e);
  static const AppIconData alignLeft = AppIconData(0xea1f);
  static const AppIconData alignRightBox = AppIconData(0xea20);
  static const AppIconData alignRight = AppIconData(0xea21);
  static const AppIconData alignTopBox = AppIconData(0xea22);
  static const AppIconData alignVerticalCenters = AppIconData(0xea23);
  static const AppIconData alignVerticalSpacing = AppIconData(0xea24);
  static const AppIconData angleTool = AppIconData(0xea25);
  static const AppIconData antennaOff = AppIconData(0xea26);
  static const AppIconData antennaSignalTag = AppIconData(0xea27);
  static const AppIconData antennaSignal = AppIconData(0xea28);
  static const AppIconData antenna = AppIconData(0xea29);
  static const AppIconData appNotification = AppIconData(0xea2a);
  static const AppIconData appStore = AppIconData(0xea2b);
  static const AppIconData appWindow = AppIconData(0xea2c);
  static const AppIconData appleHalf = AppIconData(0xea2d);
  static const AppIconData appleImac2021Side = AppIconData(0xea2e);
  static const AppIconData appleImac2021 = AppIconData(0xea2f);
  static const AppIconData appleMac = AppIconData(0xea30);
  static const AppIconData appleShortcuts = AppIconData(0xea31);
  static const AppIconData appleSwift = AppIconData(0xea32);
  static const AppIconData appleWallet = AppIconData(0xea33);
  static const AppIconData apple = AppIconData(0xea34);
  static const AppIconData arTag = AppIconData(0xea35);
  static const AppIconData arc3dCenterPoint = AppIconData(0xea36);
  static const AppIconData arc3d = AppIconData(0xea37);
  static const AppIconData arcade = AppIconData(0xea38);
  static const AppIconData archeryMatch = AppIconData(0xea39);
  static const AppIconData archery = AppIconData(0xea3a);
  static const AppIconData archive = AppIconData(0xea3b);
  static const AppIconData areaSearch = AppIconData(0xea3c);
  static const AppIconData arrowArchery = AppIconData(0xea3d);
  static const AppIconData arrowDownCircle = AppIconData(0xea3e);
  static const AppIconData arrowDownLeftCircle = AppIconData(0xea3f);
  static const AppIconData arrowDownLeftSquare = AppIconData(0xea40);
  static const AppIconData arrowDownLeft = AppIconData(0xea41);
  static const AppIconData arrowDownRightCircle = AppIconData(0xea42);
  static const AppIconData arrowDownRightSquare = AppIconData(0xea43);
  static const AppIconData arrowDownRight = AppIconData(0xea44);
  static const AppIconData arrowDownTag = AppIconData(0xea45);
  static const AppIconData arrowDown = AppIconData(0xea46);
  static const AppIconData arrowEmailForward = AppIconData(0xea47);
  static const AppIconData arrowEnlargeTag = AppIconData(0xea48);
  static const AppIconData arrowLeftCircle = AppIconData(0xea49);
  static const AppIconData arrowLeftTag = AppIconData(0xea4a);
  static const AppIconData arrowLeft = AppIconData(0xea4b);
  static const AppIconData arrowReduceTag = AppIconData(0xea4c);
  static const AppIconData arrowRightCircle = AppIconData(0xea4d);
  static const AppIconData arrowRightTag = AppIconData(0xea4e);
  static const AppIconData arrowRight = AppIconData(0xea4f);
  static const AppIconData arrowSeparateVertical = AppIconData(0xea50);
  static const AppIconData arrowSeparate = AppIconData(0xea51);
  static const AppIconData arrowUnionVertical = AppIconData(0xea52);
  static const AppIconData arrowUnion = AppIconData(0xea53);
  static const AppIconData arrowUpCircle = AppIconData(0xea54);
  static const AppIconData arrowUpLeftCircle = AppIconData(0xea55);
  static const AppIconData arrowUpLeftSquare = AppIconData(0xea56);
  static const AppIconData arrowUpLeft = AppIconData(0xea57);
  static const AppIconData arrowUpRightCircle = AppIconData(0xea58);
  static const AppIconData arrowUpRightSquare = AppIconData(0xea59);
  static const AppIconData arrowUpRight = AppIconData(0xea5a);
  static const AppIconData arrowUpTag = AppIconData(0xea5b);
  static const AppIconData arrowUp = AppIconData(0xea5c);
  static const AppIconData arrowsUpFromLine = AppIconData(0xea5d);
  static const AppIconData asana = AppIconData(0xea5e);
  static const AppIconData asterisk = AppIconData(0xea5f);
  static const AppIconData atSignCircle = AppIconData(0xea60);
  static const AppIconData atSign = AppIconData(0xea61);
  static const AppIconData atom = AppIconData(0xea62);
  static const AppIconData attachment = AppIconData(0xea63);
  static const AppIconData augmentedReality = AppIconData(0xea64);
  static const AppIconData autoFlash = AppIconData(0xea65);
  static const AppIconData aviFormat = AppIconData(0xea66);
  static const AppIconData axes = AppIconData(0xea67);
  static const AppIconData backward15Seconds = AppIconData(0xea68);
  static const AppIconData badgeCheck = AppIconData(0xea69);
  static const AppIconData bag = AppIconData(0xea6a);
  static const AppIconData balcony = AppIconData(0xea6b);
  static const AppIconData bank = AppIconData(0xea6c);
  static const AppIconData barcode = AppIconData(0xea6d);
  static const AppIconData basketballField = AppIconData(0xea6e);
  static const AppIconData basketball = AppIconData(0xea6f);
  static const AppIconData bathroom = AppIconData(0xea70);
  static const AppIconData battery25 = AppIconData(0xea71);
  static const AppIconData battery50 = AppIconData(0xea72);
  static const AppIconData battery75 = AppIconData(0xea73);
  static const AppIconData batteryCharging = AppIconData(0xea74);
  static const AppIconData batteryEmpty = AppIconData(0xea75);
  static const AppIconData batteryFull = AppIconData(0xea76);
  static const AppIconData batteryIndicator = AppIconData(0xea77);
  static const AppIconData batterySlash = AppIconData(0xea78);
  static const AppIconData batteryWarning = AppIconData(0xea79);
  static const AppIconData bbq = AppIconData(0xea7a);
  static const AppIconData beachBag = AppIconData(0xea7b);
  static const AppIconData bedReady = AppIconData(0xea7c);
  static const AppIconData bed = AppIconData(0xea7d);
  static const AppIconData behanceTag = AppIconData(0xea7e);
  static const AppIconData behance = AppIconData(0xea7f);
  static const AppIconData bellNotification = AppIconData(0xea80);
  static const AppIconData bellOff = AppIconData(0xea81);
  static const AppIconData bell = AppIconData(0xea82);
  static const AppIconData bicycle = AppIconData(0xea83);
  static const AppIconData binFull = AppIconData(0xea84);
  static const AppIconData binHalf = AppIconData(0xea85);
  static const AppIconData binMinusIn = AppIconData(0xea86);
  static const AppIconData binPlusIn = AppIconData(0xea87);
  static const AppIconData bin = AppIconData(0xea88);
  static const AppIconData binocular = AppIconData(0xea89);
  static const AppIconData birthdayCake = AppIconData(0xea8a);
  static const AppIconData bishop = AppIconData(0xea8b);
  static const AppIconData bitbucket = AppIconData(0xea8c);
  static const AppIconData bitcoinCircle = AppIconData(0xea8d);
  static const AppIconData bitcoinRotateOut = AppIconData(0xea8e);
  static const AppIconData bluetoothTag = AppIconData(0xea8f);
  static const AppIconData bluetooth = AppIconData(0xea90);
  static const AppIconData boldSquare = AppIconData(0xea91);
  static const AppIconData bold = AppIconData(0xea92);
  static const AppIconData bonfire = AppIconData(0xea93);
  static const AppIconData bookLock = AppIconData(0xea94);
  static const AppIconData bookStack = AppIconData(0xea95);
  static const AppIconData book = AppIconData(0xea96);
  static const AppIconData bookmarkBook = AppIconData(0xea97);
  static const AppIconData bookmarkCircle = AppIconData(0xea98);
  static const AppIconData bookmark = AppIconData(0xea99);
  static const AppIconData borderBl = AppIconData(0xea9a);
  static const AppIconData borderBottom = AppIconData(0xea9b);
  static const AppIconData borderBr = AppIconData(0xea9c);
  static const AppIconData borderInner = AppIconData(0xea9d);
  static const AppIconData borderLeft = AppIconData(0xea9e);
  static const AppIconData borderOut = AppIconData(0xea9f);
  static const AppIconData borderRight = AppIconData(0xeaa0);
  static const AppIconData borderTl = AppIconData(0xeaa1);
  static const AppIconData borderTop = AppIconData(0xeaa2);
  static const AppIconData borderTr = AppIconData(0xeaa3);
  static const AppIconData bounceLeft = AppIconData(0xeaa4);
  static const AppIconData bounceRight = AppIconData(0xeaa5);
  static const AppIconData bowlingBall = AppIconData(0xeaa6);
  static const AppIconData box3dCenter = AppIconData(0xeaa7);
  static const AppIconData box3dPoint = AppIconData(0xeaa8);
  static const AppIconData box3dThreePoints = AppIconData(0xeaa9);
  static const AppIconData boxIso = AppIconData(0xeaaa);
  static const AppIconData box = AppIconData(0xeaab);
  static const AppIconData boxingGlove = AppIconData(0xeaac);
  static const AppIconData brainElectricity = AppIconData(0xeaad);
  static const AppIconData brainResearch = AppIconData(0xeaae);
  static const AppIconData brainWarning = AppIconData(0xeaaf);
  static const AppIconData brain = AppIconData(0xeab0);
  static const AppIconData breadSlice = AppIconData(0xeab1);
  static const AppIconData bridge3d = AppIconData(0xeab2);
  static const AppIconData bridgeSurface = AppIconData(0xeab3);
  static const AppIconData brightCrown = AppIconData(0xeab4);
  static const AppIconData brightStar = AppIconData(0xeab5);
  static const AppIconData brightnessWindow = AppIconData(0xeab6);
  static const AppIconData brightness = AppIconData(0xeab7);
  static const AppIconData bubbleDownload = AppIconData(0xeab8);
  static const AppIconData bubbleIncome = AppIconData(0xeab9);
  static const AppIconData bubbleOutcome = AppIconData(0xeaba);
  static const AppIconData bubbleSearch = AppIconData(0xeabb);
  static const AppIconData bubbleStar = AppIconData(0xeabc);
  static const AppIconData bubbleUpload = AppIconData(0xeabd);
  static const AppIconData bubbleWarning = AppIconData(0xeabe);
  static const AppIconData bubbleXmark = AppIconData(0xeabf);
  static const AppIconData building = AppIconData(0xeac0);
  static const AppIconData busGreen = AppIconData(0xeac1);
  static const AppIconData busStop = AppIconData(0xeac2);
  static const AppIconData bus = AppIconData(0xeac3);
  static const AppIconData cSquare = AppIconData(0xeac4);
  static const AppIconData cableTag = AppIconData(0xeac5);
  static const AppIconData calculator = AppIconData(0xeac6);
  static const AppIconData calendarMinus = AppIconData(0xeac7);
  static const AppIconData calendarPlus = AppIconData(0xeac8);
  static const AppIconData calendar = AppIconData(0xeac9);
  static const AppIconData camera = AppIconData(0xeaca);
  static const AppIconData candlestickChart = AppIconData(0xeacb);
  static const AppIconData car = AppIconData(0xeacc);
  static const AppIconData cardLock = AppIconData(0xeacd);
  static const AppIconData cardNoAccess = AppIconData(0xeace);
  static const AppIconData cardReader = AppIconData(0xeacf);
  static const AppIconData cardShield = AppIconData(0xead0);
  static const AppIconData cardWallet = AppIconData(0xead1);
  static const AppIconData cartAlt = AppIconData(0xead2);
  static const AppIconData cartMinus = AppIconData(0xead3);
  static const AppIconData cartPlus = AppIconData(0xead4);
  static const AppIconData cart = AppIconData(0xead5);
  static const AppIconData cash = AppIconData(0xead6);
  static const AppIconData cell2x2 = AppIconData(0xead7);
  static const AppIconData cellar = AppIconData(0xead8);
  static const AppIconData centerAlign = AppIconData(0xead9);
  static const AppIconData chatBubbleCheck = AppIconData(0xeada);
  static const AppIconData chatBubbleEmpty = AppIconData(0xeadb);
  static const AppIconData chatBubbleQuestion = AppIconData(0xeadc);
  static const AppIconData chatBubbleTranslate = AppIconData(0xeadd);
  static const AppIconData chatBubbleWarning = AppIconData(0xeade);
  static const AppIconData chatBubbleXmark = AppIconData(0xeadf);
  static const AppIconData chatBubble = AppIconData(0xeae0);
  static const AppIconData chatLines = AppIconData(0xeae1);
  static const AppIconData chatMinusIn = AppIconData(0xeae2);
  static const AppIconData chatPlusIn = AppIconData(0xeae3);
  static const AppIconData checkCircle = AppIconData(0xeae4);
  static const AppIconData checkSquare = AppIconData(0xeae5);
  static const AppIconData check = AppIconData(0xeae6);
  static const AppIconData chocolate = AppIconData(0xeae7);
  static const AppIconData chromecastActive = AppIconData(0xeae8);
  static const AppIconData chromecast = AppIconData(0xeae9);
  static const AppIconData churchSide = AppIconData(0xeaea);
  static const AppIconData church = AppIconData(0xeaeb);
  static const AppIconData cigaretteSlash = AppIconData(0xeaec);
  static const AppIconData cinemaOld = AppIconData(0xeaed);
  static const AppIconData circleSpark = AppIconData(0xeaee);
  static const AppIconData circle = AppIconData(0xeaef);
  static const AppIconData city = AppIconData(0xeaf0);
  static const AppIconData clipboardCheck = AppIconData(0xeaf1);
  static const AppIconData clockRotateRight = AppIconData(0xeaf2);
  static const AppIconData clock = AppIconData(0xeaf3);
  static const AppIconData closedCaptionsTag = AppIconData(0xeaf4);
  static const AppIconData closet = AppIconData(0xeaf5);
  static const AppIconData cloudBookmark = AppIconData(0xeaf6);
  static const AppIconData cloudCheck = AppIconData(0xeaf7);
  static const AppIconData cloudDesync = AppIconData(0xeaf8);
  static const AppIconData cloudDownload = AppIconData(0xeaf9);
  static const AppIconData cloudSquare = AppIconData(0xeafa);
  static const AppIconData cloudSunny = AppIconData(0xeafb);
  static const AppIconData cloudSync = AppIconData(0xeafc);
  static const AppIconData cloudUpload = AppIconData(0xeafd);
  static const AppIconData cloudXmark = AppIconData(0xeafe);
  static const AppIconData cloud = AppIconData(0xeaff);
  static const AppIconData codeBracketsSquare = AppIconData(0xeb00);
  static const AppIconData codeBrackets = AppIconData(0xeb01);
  static const AppIconData code = AppIconData(0xeb02);
  static const AppIconData codepen = AppIconData(0xeb03);
  static const AppIconData coffeeCup = AppIconData(0xeb04);
  static const AppIconData coinSlash = AppIconData(0xeb05);
  static const AppIconData coinsSwap = AppIconData(0xeb06);
  static const AppIconData coins = AppIconData(0xeb07);
  static const AppIconData collageFrame = AppIconData(0xeb08);
  static const AppIconData collapse = AppIconData(0xeb09);
  static const AppIconData colorFilter = AppIconData(0xeb0a);
  static const AppIconData colorPicker = AppIconData(0xeb0b);
  static const AppIconData colorWheel = AppIconData(0xeb0c);
  static const AppIconData combine = AppIconData(0xeb0d);
  static const AppIconData commodity = AppIconData(0xeb0e);
  static const AppIconData community = AppIconData(0xeb0f);
  static const AppIconData compAlignBottom = AppIconData(0xeb10);
  static const AppIconData compAlignLeft = AppIconData(0xeb11);
  static const AppIconData compAlignRight = AppIconData(0xeb12);
  static const AppIconData compAlignTop = AppIconData(0xeb13);
  static const AppIconData compactDisc = AppIconData(0xeb14);
  static const AppIconData compass = AppIconData(0xeb15);
  static const AppIconData component = AppIconData(0xeb16);
  static const AppIconData compressLines = AppIconData(0xeb17);
  static const AppIconData compress = AppIconData(0xeb18);
  static const AppIconData computer = AppIconData(0xeb19);
  static const AppIconData constrainedSurface = AppIconData(0xeb1a);
  static const AppIconData consumable = AppIconData(0xeb1b);
  static const AppIconData contactless = AppIconData(0xeb1c);
  static const AppIconData controlSlider = AppIconData(0xeb1d);
  static const AppIconData cookie = AppIconData(0xeb1e);
  static const AppIconData coolingSquare = AppIconData(0xeb1f);
  static const AppIconData copy = AppIconData(0xeb20);
  static const AppIconData copyright = AppIconData(0xeb21);
  static const AppIconData cornerBottomLeft = AppIconData(0xeb22);
  static const AppIconData cornerBottomRight = AppIconData(0xeb23);
  static const AppIconData cornerTopLeft = AppIconData(0xeb24);
  static const AppIconData cornerTopRight = AppIconData(0xeb25);
  static const AppIconData cpuWarning = AppIconData(0xeb26);
  static const AppIconData cpu = AppIconData(0xeb27);
  static const AppIconData crackedEgg = AppIconData(0xeb28);
  static const AppIconData creativeCommons = AppIconData(0xeb29);
  static const AppIconData creditCardSlash = AppIconData(0xeb2a);
  static const AppIconData creditCard = AppIconData(0xeb2b);
  static const AppIconData creditCards = AppIconData(0xeb2c);
  static const AppIconData crib = AppIconData(0xeb2d);
  static const AppIconData cropRotateBl = AppIconData(0xeb2e);
  static const AppIconData cropRotateBr = AppIconData(0xeb2f);
  static const AppIconData cropRotateTl = AppIconData(0xeb30);
  static const AppIconData cropRotateTr = AppIconData(0xeb31);
  static const AppIconData crop = AppIconData(0xeb32);
  static const AppIconData crownCircle = AppIconData(0xeb33);
  static const AppIconData crown = AppIconData(0xeb34);
  static const AppIconData css3 = AppIconData(0xeb35);
  static const AppIconData cubeBandage = AppIconData(0xeb36);
  static const AppIconData cubeCutWithCurve = AppIconData(0xeb37);
  static const AppIconData cubeHole = AppIconData(0xeb38);
  static const AppIconData cubeReplaceFace = AppIconData(0xeb39);
  static const AppIconData cube = AppIconData(0xeb3a);
  static const AppIconData cursorPointer = AppIconData(0xeb3b);
  static const AppIconData curveArray = AppIconData(0xeb3c);
  static const AppIconData cut = AppIconData(0xeb3d);
  static const AppIconData cutlery = AppIconData(0xeb3e);
  static const AppIconData cycling = AppIconData(0xeb3f);
  static const AppIconData cylinder = AppIconData(0xeb40);
  static const AppIconData dashFlag = AppIconData(0xeb41);
  static const AppIconData dashboardDots = AppIconData(0xeb42);
  static const AppIconData dashboardSpeed = AppIconData(0xeb43);
  static const AppIconData dashboard = AppIconData(0xeb44);
  static const AppIconData dataTransferBoth = AppIconData(0xeb45);
  static const AppIconData dataTransferCheck = AppIconData(0xeb46);
  static const AppIconData dataTransferDown = AppIconData(0xeb47);
  static const AppIconData dataTransferUp = AppIconData(0xeb48);
  static const AppIconData dataTransferWarning = AppIconData(0xeb49);
  static const AppIconData databaseBackup = AppIconData(0xeb4a);
  static const AppIconData databaseCheck = AppIconData(0xeb4b);
  static const AppIconData databaseExport = AppIconData(0xeb4c);
  static const AppIconData databaseMonitor = AppIconData(0xeb4d);
  static const AppIconData databaseRestore = AppIconData(0xeb4e);
  static const AppIconData databaseScriptMinus = AppIconData(0xeb4f);
  static const AppIconData databaseScriptPlus = AppIconData(0xeb50);
  static const AppIconData databaseScript = AppIconData(0xeb51);
  static const AppIconData databaseSearch = AppIconData(0xeb52);
  static const AppIconData databaseSettings = AppIconData(0xeb53);
  static const AppIconData databaseStar = AppIconData(0xeb54);
  static const AppIconData databaseStats = AppIconData(0xeb55);
  static const AppIconData databaseTag = AppIconData(0xeb56);
  static const AppIconData databaseWarning = AppIconData(0xeb57);
  static const AppIconData databaseXmark = AppIconData(0xeb58);
  static const AppIconData database = AppIconData(0xeb59);
  static const AppIconData deCompress = AppIconData(0xeb5a);
  static const AppIconData deliveryTruck = AppIconData(0xeb5b);
  static const AppIconData delivery = AppIconData(0xeb5c);
  static const AppIconData depth = AppIconData(0xeb5d);
  static const AppIconData designNib = AppIconData(0xeb5e);
  static const AppIconData designPencil = AppIconData(0xeb5f);
  static const AppIconData desk = AppIconData(0xeb60);
  static const AppIconData developer = AppIconData(0xeb61);
  static const AppIconData dewPoint = AppIconData(0xeb62);
  static const AppIconData dialpad = AppIconData(0xeb63);
  static const AppIconData diameter = AppIconData(0xeb64);
  static const AppIconData diceFive = AppIconData(0xeb65);
  static const AppIconData diceFour = AppIconData(0xeb66);
  static const AppIconData diceOne = AppIconData(0xeb67);
  static const AppIconData diceSix = AppIconData(0xeb68);
  static const AppIconData diceThree = AppIconData(0xeb69);
  static const AppIconData diceTwo = AppIconData(0xeb6a);
  static const AppIconData dimmerSwitch = AppIconData(0xeb6b);
  static const AppIconData directorChair = AppIconData(0xeb6c);
  static const AppIconData discord = AppIconData(0xeb6d);
  static const AppIconData dishwasher = AppIconData(0xeb6e);
  static const AppIconData display4k = AppIconData(0xeb6f);
  static const AppIconData divideThree = AppIconData(0xeb70);
  static const AppIconData divide = AppIconData(0xeb71);
  static const AppIconData dna = AppIconData(0xeb72);
  static const AppIconData dns = AppIconData(0xeb73);
  static const AppIconData docMagnifyingGlassIn = AppIconData(0xeb74);
  static const AppIconData docMagnifyingGlass = AppIconData(0xeb75);
  static const AppIconData docStarIn = AppIconData(0xeb76);
  static const AppIconData docStar = AppIconData(0xeb77);
  static const AppIconData dogecoinCircle = AppIconData(0xeb78);
  static const AppIconData dogecoinRotateOut = AppIconData(0xeb79);
  static const AppIconData dollarCircle = AppIconData(0xeb7a);
  static const AppIconData dollar = AppIconData(0xeb7b);
  static const AppIconData domoticWarning = AppIconData(0xeb7c);
  static const AppIconData donate = AppIconData(0xeb7d);
  static const AppIconData dotArrowDown = AppIconData(0xeb7e);
  static const AppIconData dotArrowLeft = AppIconData(0xeb7f);
  static const AppIconData dotArrowRight = AppIconData(0xeb80);
  static const AppIconData dotArrowUp = AppIconData(0xeb81);
  static const AppIconData doubleCheck = AppIconData(0xeb82);
  static const AppIconData downloadCircle = AppIconData(0xeb83);
  static const AppIconData downloadDataWindow = AppIconData(0xeb84);
  static const AppIconData downloadSquare = AppIconData(0xeb85);
  static const AppIconData download = AppIconData(0xeb86);
  static const AppIconData dragHandGesture = AppIconData(0xeb87);
  static const AppIconData drag = AppIconData(0xeb88);
  static const AppIconData drawer = AppIconData(0xeb89);
  static const AppIconData dribbble = AppIconData(0xeb8a);
  static const AppIconData droneChargeFull = AppIconData(0xeb8b);
  static const AppIconData droneChargeHalf = AppIconData(0xeb8c);
  static const AppIconData droneChargeLow = AppIconData(0xeb8d);
  static const AppIconData droneCheck = AppIconData(0xeb8e);
  static const AppIconData droneLanding = AppIconData(0xeb8f);
  static const AppIconData droneRefresh = AppIconData(0xeb90);
  static const AppIconData droneTakeOff = AppIconData(0xeb91);
  static const AppIconData droneXmark = AppIconData(0xeb92);
  static const AppIconData drone = AppIconData(0xeb93);
  static const AppIconData dropletCheck = AppIconData(0xeb94);
  static const AppIconData dropletHalf = AppIconData(0xeb95);
  static const AppIconData droplet = AppIconData(0xeb96);
  static const AppIconData easeCurveControlPoints = AppIconData(0xeb97);
  static const AppIconData easeInControlPoint = AppIconData(0xeb98);
  static const AppIconData easeInOut = AppIconData(0xeb99);
  static const AppIconData easeIn = AppIconData(0xeb9a);
  static const AppIconData easeOutControlPoint = AppIconData(0xeb9b);
  static const AppIconData easeOut = AppIconData(0xeb9c);
  static const AppIconData ecologyBook = AppIconData(0xeb9d);
  static const AppIconData editPencil = AppIconData(0xeb9e);
  static const AppIconData edit = AppIconData(0xeb9f);
  static const AppIconData egg = AppIconData(0xeba0);
  static const AppIconData eject = AppIconData(0xeba1);
  static const AppIconData electronicsChip = AppIconData(0xeba2);
  static const AppIconData electronicsTransistor = AppIconData(0xeba3);
  static const AppIconData elevator = AppIconData(0xeba4);
  static const AppIconData ellipse3dThreePoints = AppIconData(0xeba5);
  static const AppIconData ellipse3d = AppIconData(0xeba6);
  static const AppIconData emojiBall = AppIconData(0xeba7);
  static const AppIconData emojiBlinkLeft = AppIconData(0xeba8);
  static const AppIconData emojiBlinkRight = AppIconData(0xeba9);
  static const AppIconData emojiLookDown = AppIconData(0xebaa);
  static const AppIconData emojiLookLeft = AppIconData(0xebab);
  static const AppIconData emojiLookRight = AppIconData(0xebac);
  static const AppIconData emojiLookUp = AppIconData(0xebad);
  static const AppIconData emojiPuzzled = AppIconData(0xebae);
  static const AppIconData emojiQuite = AppIconData(0xebaf);
  static const AppIconData emojiReally = AppIconData(0xebb0);
  static const AppIconData emojiSad = AppIconData(0xebb1);
  static const AppIconData emojiSatisfied = AppIconData(0xebb2);
  static const AppIconData emojiSingLeftNote = AppIconData(0xebb3);
  static const AppIconData emojiSingLeft = AppIconData(0xebb4);
  static const AppIconData emojiSingRightNote = AppIconData(0xebb5);
  static const AppIconData emojiSingRight = AppIconData(0xebb6);
  static const AppIconData emojiSurpriseAlt = AppIconData(0xebb7);
  static const AppIconData emojiSurprise = AppIconData(0xebb8);
  static const AppIconData emojiTalkingAngry = AppIconData(0xebb9);
  static const AppIconData emojiTalkingHappy = AppIconData(0xebba);
  static const AppIconData emojiThinkLeft = AppIconData(0xebbb);
  static const AppIconData emojiThinkRight = AppIconData(0xebbc);
  static const AppIconData emoji = AppIconData(0xebbd);
  static const AppIconData emptyPage = AppIconData(0xebbe);
  static const AppIconData energyUsageWindow = AppIconData(0xebbf);
  static const AppIconData enlarge = AppIconData(0xebc0);
  static const AppIconData erase = AppIconData(0xebc1);
  static const AppIconData ethereumCircle = AppIconData(0xebc2);
  static const AppIconData ethereumRotateOut = AppIconData(0xebc3);
  static const AppIconData euroSquare = AppIconData(0xebc4);
  static const AppIconData euro = AppIconData(0xebc5);
  static const AppIconData evChargeAlt = AppIconData(0xebc6);
  static const AppIconData evCharge = AppIconData(0xebc7);
  static const AppIconData evPlugCharging = AppIconData(0xebc8);
  static const AppIconData evPlugXmark = AppIconData(0xebc9);
  static const AppIconData evPlug = AppIconData(0xebca);
  static const AppIconData evStation = AppIconData(0xebcb);
  static const AppIconData evTag = AppIconData(0xebcc);
  static const AppIconData exclude = AppIconData(0xebcd);
  static const AppIconData expandLines = AppIconData(0xebce);
  static const AppIconData expand = AppIconData(0xebcf);
  static const AppIconData extrude = AppIconData(0xebd0);
  static const AppIconData eyeClosed = AppIconData(0xebd1);
  static const AppIconData eye = AppIconData(0xebd2);
  static const AppIconData fSquare = AppIconData(0xebd3);
  static const AppIconData face3dDraft = AppIconData(0xebd4);
  static const AppIconData faceId = AppIconData(0xebd5);
  static const AppIconData facebookTag = AppIconData(0xebd6);
  static const AppIconData facebook = AppIconData(0xebd7);
  static const AppIconData facetime = AppIconData(0xebd8);
  static const AppIconData farm = AppIconData(0xebd9);
  static const AppIconData fastArrowDownSquare = AppIconData(0xebda);
  static const AppIconData fastArrowDown = AppIconData(0xebdb);
  static const AppIconData fastArrowLeftSquare = AppIconData(0xebdc);
  static const AppIconData fastArrowLeft = AppIconData(0xebdd);
  static const AppIconData fastArrowRightSquare = AppIconData(0xebde);
  static const AppIconData fastArrowRight = AppIconData(0xebdf);
  static const AppIconData fastArrowUpSquare = AppIconData(0xebe0);
  static const AppIconData fastArrowUp = AppIconData(0xebe1);
  static const AppIconData fastDownCircle = AppIconData(0xebe2);
  static const AppIconData fastLeftCircle = AppIconData(0xebe3);
  static const AppIconData fastRightCircle = AppIconData(0xebe4);
  static const AppIconData fastUpCircle = AppIconData(0xebe5);
  static const AppIconData favouriteBook = AppIconData(0xebe6);
  static const AppIconData favouriteWindow = AppIconData(0xebe7);
  static const AppIconData female = AppIconData(0xebe8);
  static const AppIconData figma = AppIconData(0xebe9);
  static const AppIconData fileNotFound = AppIconData(0xebea);
  static const AppIconData fillColor = AppIconData(0xebeb);
  static const AppIconData fillet3d = AppIconData(0xebec);
  static const AppIconData filterAlt = AppIconData(0xebed);
  static const AppIconData filterListCircle = AppIconData(0xebee);
  static const AppIconData filterList = AppIconData(0xebef);
  static const AppIconData filter = AppIconData(0xebf0);
  static const AppIconData finder = AppIconData(0xebf1);
  static const AppIconData fingerprintCheckCircle = AppIconData(0xebf2);
  static const AppIconData fingerprintCircle = AppIconData(0xebf3);
  static const AppIconData fingerprintLockCircle = AppIconData(0xebf4);
  static const AppIconData fingerprintScan = AppIconData(0xebf5);
  static const AppIconData fingerprintSquare = AppIconData(0xebf6);
  static const AppIconData fingerprintWindow = AppIconData(0xebf7);
  static const AppIconData fingerprintXmarkCircle = AppIconData(0xebf8);
  static const AppIconData fingerprint = AppIconData(0xebf9);
  static const AppIconData fireFlame = AppIconData(0xebfa);
  static const AppIconData fish = AppIconData(0xebfb);
  static const AppIconData fishing = AppIconData(0xebfc);
  static const AppIconData flare = AppIconData(0xebfd);
  static const AppIconData flashOff = AppIconData(0xebfe);
  static const AppIconData flash = AppIconData(0xebff);
  static const AppIconData flask = AppIconData(0xec00);
  static const AppIconData flipReverse = AppIconData(0xec01);
  static const AppIconData flip = AppIconData(0xec02);
  static const AppIconData floppyDiskArrowIn = AppIconData(0xec03);
  static const AppIconData floppyDiskArrowOut = AppIconData(0xec04);
  static const AppIconData floppyDisk = AppIconData(0xec05);
  static const AppIconData flower = AppIconData(0xec06);
  static const AppIconData fog = AppIconData(0xec07);
  static const AppIconData folderMinus = AppIconData(0xec08);
  static const AppIconData folderPlus = AppIconData(0xec09);
  static const AppIconData folderSettings = AppIconData(0xec0a);
  static const AppIconData folderWarning = AppIconData(0xec0b);
  static const AppIconData folder = AppIconData(0xec0c);
  static const AppIconData fontQuestion = AppIconData(0xec0d);
  static const AppIconData footballBall = AppIconData(0xec0e);
  static const AppIconData football = AppIconData(0xec0f);
  static const AppIconData forward15Seconds = AppIconData(0xec10);
  static const AppIconData forwardMessage = AppIconData(0xec11);
  static const AppIconData forward = AppIconData(0xec12);
  static const AppIconData frameAltEmpty = AppIconData(0xec13);
  static const AppIconData frameAlt = AppIconData(0xec14);
  static const AppIconData frameMinusIn = AppIconData(0xec15);
  static const AppIconData framePlusIn = AppIconData(0xec16);
  static const AppIconData frameSelect = AppIconData(0xec17);
  static const AppIconData frameSimple = AppIconData(0xec18);
  static const AppIconData frameTool = AppIconData(0xec19);
  static const AppIconData frame = AppIconData(0xec1a);
  static const AppIconData fridge = AppIconData(0xec1b);
  static const AppIconData friends = AppIconData(0xec1c);
  static const AppIconData fxTag = AppIconData(0xec1d);
  static const AppIconData fx = AppIconData(0xec1e);
  static const AppIconData gamepad = AppIconData(0xec1f);
  static const AppIconData garage = AppIconData(0xec20);
  static const AppIconData gasTankDroplet = AppIconData(0xec21);
  static const AppIconData gasTank = AppIconData(0xec22);
  static const AppIconData gas = AppIconData(0xec23);
  static const AppIconData gifFormat = AppIconData(0xec24);
  static const AppIconData gift = AppIconData(0xec25);
  static const AppIconData gitBranch = AppIconData(0xec26);
  static const AppIconData gitCherryPickCommit = AppIconData(0xec27);
  static const AppIconData gitCommit = AppIconData(0xec28);
  static const AppIconData gitCompare = AppIconData(0xec29);
  static const AppIconData gitFork = AppIconData(0xec2a);
  static const AppIconData gitMerge = AppIconData(0xec2b);
  static const AppIconData gitPullRequestClosed = AppIconData(0xec2c);
  static const AppIconData gitPullRequest = AppIconData(0xec2d);
  static const AppIconData githubCircle = AppIconData(0xec2e);
  static const AppIconData github = AppIconData(0xec2f);
  static const AppIconData gitlabFull = AppIconData(0xec30);
  static const AppIconData glassEmpty = AppIconData(0xec31);
  static const AppIconData glassFragile = AppIconData(0xec32);
  static const AppIconData glassHalfAlt = AppIconData(0xec33);
  static const AppIconData glassHalf = AppIconData(0xec34);
  static const AppIconData glasses = AppIconData(0xec35);
  static const AppIconData globe = AppIconData(0xec36);
  static const AppIconData golf = AppIconData(0xec37);
  static const AppIconData googleCircle = AppIconData(0xec38);
  static const AppIconData googleDocs = AppIconData(0xec39);
  static const AppIconData googleDriveCheck = AppIconData(0xec3a);
  static const AppIconData googleDriveSync = AppIconData(0xec3b);
  static const AppIconData googleDriveWarning = AppIconData(0xec3c);
  static const AppIconData googleDrive = AppIconData(0xec3d);
  static const AppIconData googleHome = AppIconData(0xec3e);
  static const AppIconData googleOne = AppIconData(0xec3f);
  static const AppIconData google = AppIconData(0xec40);
  static const AppIconData gps = AppIconData(0xec41);
  static const AppIconData graduationCap = AppIconData(0xec42);
  static const AppIconData graphDown = AppIconData(0xec43);
  static const AppIconData graphUp = AppIconData(0xec44);
  static const AppIconData gridMinus = AppIconData(0xec45);
  static const AppIconData gridPlus = AppIconData(0xec46);
  static const AppIconData gridXmark = AppIconData(0xec47);
  static const AppIconData group = AppIconData(0xec48);
  static const AppIconData gym = AppIconData(0xec49);
  static const AppIconData hSquare = AppIconData(0xec4a);
  static const AppIconData halfCookie = AppIconData(0xec4b);
  static const AppIconData halfMoon = AppIconData(0xec4c);
  static const AppIconData hammer = AppIconData(0xec4d);
  static const AppIconData handBrake = AppIconData(0xec4e);
  static const AppIconData handCard = AppIconData(0xec4f);
  static const AppIconData handCash = AppIconData(0xec50);
  static const AppIconData handContactless = AppIconData(0xec51);
  static const AppIconData handbag = AppIconData(0xec52);
  static const AppIconData hardDrive = AppIconData(0xec53);
  static const AppIconData hashtag = AppIconData(0xec54);
  static const AppIconData hat = AppIconData(0xec55);
  static const AppIconData hdDisplay = AppIconData(0xec56);
  static const AppIconData hd = AppIconData(0xec57);
  static const AppIconData hdr = AppIconData(0xec58);
  static const AppIconData headsetBolt = AppIconData(0xec59);
  static const AppIconData headsetHelp = AppIconData(0xec5a);
  static const AppIconData headsetWarning = AppIconData(0xec5b);
  static const AppIconData headset = AppIconData(0xec5c);
  static const AppIconData healthShield = AppIconData(0xec5d);
  static const AppIconData healthcare = AppIconData(0xec5e);
  static const AppIconData heartArrowDown = AppIconData(0xec5f);
  static const AppIconData heart = AppIconData(0xec60);
  static const AppIconData heatingSquare = AppIconData(0xec61);
  static const AppIconData heavyRain = AppIconData(0xec62);
  static const AppIconData helpCircle = AppIconData(0xec63);
  static const AppIconData helpSquare = AppIconData(0xec64);
  static const AppIconData heptagon = AppIconData(0xec65);
  static const AppIconData hexagonDice = AppIconData(0xec66);
  static const AppIconData hexagonPlus = AppIconData(0xec67);
  static const AppIconData hexagon = AppIconData(0xec68);
  static const AppIconData historicShieldAlt = AppIconData(0xec69);
  static const AppIconData historicShield = AppIconData(0xec6a);
  static const AppIconData homeAltSlimHoriz = AppIconData(0xec6b);
  static const AppIconData homeAltSlim = AppIconData(0xec6c);
  static const AppIconData homeAlt = AppIconData(0xec6d);
  static const AppIconData homeHospital = AppIconData(0xec6e);
  static const AppIconData homeSale = AppIconData(0xec6f);
  static const AppIconData homeSecure = AppIconData(0xec70);
  static const AppIconData homeShield = AppIconData(0xec71);
  static const AppIconData homeSimpleDoor = AppIconData(0xec72);
  static const AppIconData homeSimple = AppIconData(0xec73);
  static const AppIconData homeTable = AppIconData(0xec74);
  static const AppIconData homeTemperatureIn = AppIconData(0xec75);
  static const AppIconData homeTemperatureOut = AppIconData(0xec76);
  static const AppIconData homeUser = AppIconData(0xec77);
  static const AppIconData home = AppIconData(0xec78);
  static const AppIconData horizDistributionLeft = AppIconData(0xec79);
  static const AppIconData horizDistributionRight = AppIconData(0xec7a);
  static const AppIconData horizontalMerge = AppIconData(0xec7b);
  static const AppIconData horizontalSplit = AppIconData(0xec7c);
  static const AppIconData hospitalCircle = AppIconData(0xec7d);
  static const AppIconData hospital = AppIconData(0xec7e);
  static const AppIconData hotAirBalloon = AppIconData(0xec7f);
  static const AppIconData hourglass = AppIconData(0xec80);
  static const AppIconData houseRooms = AppIconData(0xec81);
  static const AppIconData html5 = AppIconData(0xec82);
  static const AppIconData iceCream = AppIconData(0xec83);
  static const AppIconData iconoir = AppIconData(0xec84);
  static const AppIconData import = AppIconData(0xec85);
  static const AppIconData inclination = AppIconData(0xec86);
  static const AppIconData industry = AppIconData(0xec87);
  static const AppIconData infinite = AppIconData(0xec88);
  static const AppIconData infoCircle = AppIconData(0xec89);
  static const AppIconData inputField = AppIconData(0xec8a);
  static const AppIconData inputOutput = AppIconData(0xec8b);
  static const AppIconData inputSearch = AppIconData(0xec8c);
  static const AppIconData instagram = AppIconData(0xec8d);
  static const AppIconData internet = AppIconData(0xec8e);
  static const AppIconData intersectAlt = AppIconData(0xec8f);
  static const AppIconData intersect = AppIconData(0xec90);
  static const AppIconData iosSettings = AppIconData(0xec91);
  static const AppIconData ipAddressTag = AppIconData(0xec92);
  static const AppIconData irisScan = AppIconData(0xec93);
  static const AppIconData italicSquare = AppIconData(0xec94);
  static const AppIconData italic = AppIconData(0xec95);
  static const AppIconData jellyfish = AppIconData(0xec96);
  static const AppIconData journalPage = AppIconData(0xec97);
  static const AppIconData journal = AppIconData(0xec98);
  static const AppIconData jpegFormat = AppIconData(0xec99);
  static const AppIconData jpgFormat = AppIconData(0xec9a);
  static const AppIconData kanbanBoard = AppIconData(0xec9b);
  static const AppIconData keyBack = AppIconData(0xec9c);
  static const AppIconData keyCommand = AppIconData(0xec9d);
  static const AppIconData keyMinus = AppIconData(0xec9e);
  static const AppIconData keyPlus = AppIconData(0xec9f);
  static const AppIconData keyXmark = AppIconData(0xeca0);
  static const AppIconData key = AppIconData(0xeca1);
  static const AppIconData keyframeAlignCenter = AppIconData(0xeca2);
  static const AppIconData keyframeAlignHorizontal = AppIconData(0xeca3);
  static const AppIconData keyframeAlignVertical = AppIconData(0xeca4);
  static const AppIconData keyframeMinusIn = AppIconData(0xeca5);
  static const AppIconData keyframeMinus = AppIconData(0xeca6);
  static const AppIconData keyframePlusIn = AppIconData(0xeca7);
  static const AppIconData keyframePlus = AppIconData(0xeca8);
  static const AppIconData keyframePosition = AppIconData(0xeca9);
  static const AppIconData keyframe = AppIconData(0xecaa);
  static const AppIconData keyframesCouple = AppIconData(0xecab);
  static const AppIconData keyframesMinus = AppIconData(0xecac);
  static const AppIconData keyframesPlus = AppIconData(0xecad);
  static const AppIconData keyframes = AppIconData(0xecae);
  static const AppIconData label = AppIconData(0xecaf);
  static const AppIconData lamp = AppIconData(0xecb0);
  static const AppIconData language = AppIconData(0xecb1);
  static const AppIconData laptopCharging = AppIconData(0xecb2);
  static const AppIconData laptopDevMode = AppIconData(0xecb3);
  static const AppIconData laptopFix = AppIconData(0xecb4);
  static const AppIconData laptopWarning = AppIconData(0xecb5);
  static const AppIconData laptop = AppIconData(0xecb6);
  static const AppIconData layoutLeft = AppIconData(0xecb7);
  static const AppIconData layoutRight = AppIconData(0xecb8);
  static const AppIconData leaderboardStar = AppIconData(0xecb9);
  static const AppIconData leaderboard = AppIconData(0xecba);
  static const AppIconData leaf = AppIconData(0xecbb);
  static const AppIconData learning = AppIconData(0xecbc);
  static const AppIconData lensPlus = AppIconData(0xecbd);
  static const AppIconData lens = AppIconData(0xecbe);
  static const AppIconData lifebelt = AppIconData(0xecbf);
  static const AppIconData lightBulbOff = AppIconData(0xecc0);
  static const AppIconData lightBulbOn = AppIconData(0xecc1);
  static const AppIconData lightBulb = AppIconData(0xecc2);
  static const AppIconData lineSpace = AppIconData(0xecc3);
  static const AppIconData linear = AppIconData(0xecc4);
  static const AppIconData linkSlash = AppIconData(0xecc5);
  static const AppIconData linkXmark = AppIconData(0xecc6);
  static const AppIconData link = AppIconData(0xecc7);
  static const AppIconData linkedin = AppIconData(0xecc8);
  static const AppIconData linux = AppIconData(0xecc9);
  static const AppIconData listSelect = AppIconData(0xecca);
  static const AppIconData list = AppIconData(0xeccb);
  static const AppIconData litecoinCircle = AppIconData(0xeccc);
  static const AppIconData litecoinRotateOut = AppIconData(0xeccd);
  static const AppIconData lockSlash = AppIconData(0xecce);
  static const AppIconData lockSquare = AppIconData(0xeccf);
  static const AppIconData lock = AppIconData(0xecd0);
  static const AppIconData loft3d = AppIconData(0xecd1);
  static const AppIconData logIn = AppIconData(0xecd2);
  static const AppIconData logNoAccess = AppIconData(0xecd3);
  static const AppIconData logOut = AppIconData(0xecd4);
  static const AppIconData longArrowDownLeft = AppIconData(0xecd5);
  static const AppIconData longArrowDownRight = AppIconData(0xecd6);
  static const AppIconData longArrowLeftDown = AppIconData(0xecd7);
  static const AppIconData longArrowLeftUp = AppIconData(0xecd8);
  static const AppIconData longArrowRightDown = AppIconData(0xecd9);
  static const AppIconData longArrowRightUp = AppIconData(0xecda);
  static const AppIconData longArrowUpLeft = AppIconData(0xecdb);
  static const AppIconData longArrowUpRight = AppIconData(0xecdc);
  static const AppIconData lotOfCash = AppIconData(0xecdd);
  static const AppIconData lullaby = AppIconData(0xecde);
  static const AppIconData macControlKey = AppIconData(0xecdf);
  static const AppIconData macDock = AppIconData(0xece0);
  static const AppIconData macOptionKey = AppIconData(0xece1);
  static const AppIconData macOsWindow = AppIconData(0xece2);
  static const AppIconData magicWand = AppIconData(0xece3);
  static const AppIconData magnetEnergy = AppIconData(0xece4);
  static const AppIconData magnet = AppIconData(0xece5);
  static const AppIconData mailIn = AppIconData(0xece6);
  static const AppIconData mailOpen = AppIconData(0xece7);
  static const AppIconData mailOut = AppIconData(0xece8);
  static const AppIconData mail = AppIconData(0xece9);
  static const AppIconData male = AppIconData(0xecea);
  static const AppIconData mapPinMinus = AppIconData(0xeceb);
  static const AppIconData mapPinPlus = AppIconData(0xecec);
  static const AppIconData mapPinXmark = AppIconData(0xeced);
  static const AppIconData mapPin = AppIconData(0xecee);
  static const AppIconData mapXmark = AppIconData(0xecef);
  static const AppIconData map = AppIconData(0xecf0);
  static const AppIconData mapsArrowDiagonal = AppIconData(0xecf1);
  static const AppIconData mapsArrowXmark = AppIconData(0xecf2);
  static const AppIconData mapsArrow = AppIconData(0xecf3);
  static const AppIconData mapsGoStraight = AppIconData(0xecf4);
  static const AppIconData mapsTurnBack = AppIconData(0xecf5);
  static const AppIconData mapsTurnLeft = AppIconData(0xecf6);
  static const AppIconData mapsTurnRight = AppIconData(0xecf7);
  static const AppIconData maskSquare = AppIconData(0xecf8);
  static const AppIconData mastercardCard = AppIconData(0xecf9);
  static const AppIconData mastodon = AppIconData(0xecfa);
  static const AppIconData mathBook = AppIconData(0xecfb);
  static const AppIconData maximize = AppIconData(0xecfc);
  static const AppIconData medal1st = AppIconData(0xecfd);
  static const AppIconData medal = AppIconData(0xecfe);
  static const AppIconData mediaImageFolder = AppIconData(0xecff);
  static const AppIconData mediaImageList = AppIconData(0xed00);
  static const AppIconData mediaImagePlus = AppIconData(0xed01);
  static const AppIconData mediaImageXmark = AppIconData(0xed02);
  static const AppIconData mediaImage = AppIconData(0xed03);
  static const AppIconData mediaVideoFolder = AppIconData(0xed04);
  static const AppIconData mediaVideoList = AppIconData(0xed05);
  static const AppIconData mediaVideoPlus = AppIconData(0xed06);
  static const AppIconData mediaVideoXmark = AppIconData(0xed07);
  static const AppIconData mediaVideo = AppIconData(0xed08);
  static const AppIconData medium = AppIconData(0xed09);
  static const AppIconData megaphone = AppIconData(0xed0a);
  static const AppIconData menuScale = AppIconData(0xed0b);
  static const AppIconData menu = AppIconData(0xed0c);
  static const AppIconData messageAlert = AppIconData(0xed0d);
  static const AppIconData messageText = AppIconData(0xed0e);
  static const AppIconData message = AppIconData(0xed0f);
  static const AppIconData meterArrowDownRight = AppIconData(0xed10);
  static const AppIconData metro = AppIconData(0xed11);
  static const AppIconData microphoneCheck = AppIconData(0xed12);
  static const AppIconData microphoneMinus = AppIconData(0xed13);
  static const AppIconData microphoneMute = AppIconData(0xed14);
  static const AppIconData microphonePlus = AppIconData(0xed15);
  static const AppIconData microphoneSpeaking = AppIconData(0xed16);
  static const AppIconData microphoneWarning = AppIconData(0xed17);
  static const AppIconData microphone = AppIconData(0xed18);
  static const AppIconData microscope = AppIconData(0xed19);
  static const AppIconData minusCircle = AppIconData(0xed1a);
  static const AppIconData minusHexagon = AppIconData(0xed1b);
  static const AppIconData minusSquareDashed = AppIconData(0xed1c);
  static const AppIconData minusSquare = AppIconData(0xed1d);
  static const AppIconData minus = AppIconData(0xed1e);
  static const AppIconData mirror = AppIconData(0xed1f);
  static const AppIconData mobileDevMode = AppIconData(0xed20);
  static const AppIconData mobileFingerprint = AppIconData(0xed21);
  static const AppIconData mobileVoice = AppIconData(0xed22);
  static const AppIconData modernTv4k = AppIconData(0xed23);
  static const AppIconData modernTv = AppIconData(0xed24);
  static const AppIconData moneySquare = AppIconData(0xed25);
  static const AppIconData moonSat = AppIconData(0xed26);
  static const AppIconData moreHorizCircle = AppIconData(0xed27);
  static const AppIconData moreHoriz = AppIconData(0xed28);
  static const AppIconData moreVertCircle = AppIconData(0xed29);
  static const AppIconData moreVert = AppIconData(0xed2a);
  static const AppIconData motorcycle = AppIconData(0xed2b);
  static const AppIconData mouseButtonLeft = AppIconData(0xed2c);
  static const AppIconData mouseButtonRight = AppIconData(0xed2d);
  static const AppIconData mouseScrollWheel = AppIconData(0xed2e);
  static const AppIconData movie = AppIconData(0xed2f);
  static const AppIconData mpegFormat = AppIconData(0xed30);
  static const AppIconData multiBubble = AppIconData(0xed31);
  static const AppIconData multiMacOsWindow = AppIconData(0xed32);
  static const AppIconData multiWindow = AppIconData(0xed33);
  static const AppIconData multiplePagesEmpty = AppIconData(0xed34);
  static const AppIconData multiplePagesMinus = AppIconData(0xed35);
  static const AppIconData multiplePagesPlus = AppIconData(0xed36);
  static const AppIconData multiplePagesXmark = AppIconData(0xed37);
  static const AppIconData multiplePages = AppIconData(0xed38);
  static const AppIconData musicDoubleNotePlus = AppIconData(0xed39);
  static const AppIconData musicDoubleNote = AppIconData(0xed3a);
  static const AppIconData musicNotePlus = AppIconData(0xed3b);
  static const AppIconData musicNote = AppIconData(0xed3c);
  static const AppIconData nSquare = AppIconData(0xed3d);
  static const AppIconData navArrowDown = AppIconData(0xed3e);
  static const AppIconData navArrowLeft = AppIconData(0xed3f);
  static const AppIconData navArrowRight = AppIconData(0xed40);
  static const AppIconData navArrowUp = AppIconData(0xed41);
  static const AppIconData navigatorAlt = AppIconData(0xed42);
  static const AppIconData navigator = AppIconData(0xed43);
  static const AppIconData neighbourhood = AppIconData(0xed44);
  static const AppIconData networkLeft = AppIconData(0xed45);
  static const AppIconData networkReverse = AppIconData(0xed46);
  static const AppIconData networkRight = AppIconData(0xed47);
  static const AppIconData network = AppIconData(0xed48);
  static const AppIconData newTab = AppIconData(0xed49);
  static const AppIconData nintendoSwitch = AppIconData(0xed4a);
  static const AppIconData noSmokingCircle = AppIconData(0xed4b);
  static const AppIconData nonBinary = AppIconData(0xed4c);
  static const AppIconData notes = AppIconData(0xed4d);
  static const AppIconData npmSquare = AppIconData(0xed4e);
  static const AppIconData npm = AppIconData(0xed4f);
  static const AppIconData number0Square = AppIconData(0xed50);
  static const AppIconData number1Square = AppIconData(0xed51);
  static const AppIconData number2Square = AppIconData(0xed52);
  static const AppIconData number3Square = AppIconData(0xed53);
  static const AppIconData number4Square = AppIconData(0xed54);
  static const AppIconData number5Square = AppIconData(0xed55);
  static const AppIconData number6Square = AppIconData(0xed56);
  static const AppIconData number7Square = AppIconData(0xed57);
  static const AppIconData number8Square = AppIconData(0xed58);
  static const AppIconData number9Square = AppIconData(0xed59);
  static const AppIconData numberedListLeft = AppIconData(0xed5a);
  static const AppIconData numberedListRight = AppIconData(0xed5b);
  static const AppIconData oSquare = AppIconData(0xed5c);
  static const AppIconData octagon = AppIconData(0xed5d);
  static const AppIconData offTag = AppIconData(0xed5e);
  static const AppIconData oilIndustry = AppIconData(0xed5f);
  static const AppIconData okrs = AppIconData(0xed60);
  static const AppIconData onTag = AppIconData(0xed61);
  static const AppIconData oneFingerSelectHandGesture = AppIconData(0xed62);
  static const AppIconData onePointCircle = AppIconData(0xed63);
  static const AppIconData openBook = AppIconData(0xed64);
  static const AppIconData openInBrowser = AppIconData(0xed65);
  static const AppIconData openInWindow = AppIconData(0xed66);
  static const AppIconData openNewWindow = AppIconData(0xed67);
  static const AppIconData openSelectHandGesture = AppIconData(0xed68);
  static const AppIconData openVpn = AppIconData(0xed69);
  static const AppIconData orangeHalf = AppIconData(0xed6a);
  static const AppIconData orangeSliceAlt = AppIconData(0xed6b);
  static const AppIconData orangeSlice = AppIconData(0xed6c);
  static const AppIconData organicFoodSquare = AppIconData(0xed6d);
  static const AppIconData organicFood = AppIconData(0xed6e);
  static const AppIconData orthogonalView = AppIconData(0xed6f);
  static const AppIconData packageLock = AppIconData(0xed70);
  static const AppIconData package = AppIconData(0xed71);
  static const AppIconData packages = AppIconData(0xed72);
  static const AppIconData pacman = AppIconData(0xed73);
  static const AppIconData pageDown = AppIconData(0xed74);
  static const AppIconData pageEdit = AppIconData(0xed75);
  static const AppIconData pageFlip = AppIconData(0xed76);
  static const AppIconData pageLeft = AppIconData(0xed77);
  static const AppIconData pageMinusIn = AppIconData(0xed78);
  static const AppIconData pageMinus = AppIconData(0xed79);
  static const AppIconData pagePlusIn = AppIconData(0xed7a);
  static const AppIconData pagePlus = AppIconData(0xed7b);
  static const AppIconData pageRight = AppIconData(0xed7c);
  static const AppIconData pageSearch = AppIconData(0xed7d);
  static const AppIconData pageStar = AppIconData(0xed7e);
  static const AppIconData pageUp = AppIconData(0xed7f);
  static const AppIconData page = AppIconData(0xed80);
  static const AppIconData palette = AppIconData(0xed81);
  static const AppIconData panoramaEnlarge = AppIconData(0xed82);
  static const AppIconData panoramaReduce = AppIconData(0xed83);
  static const AppIconData pantsPockets = AppIconData(0xed84);
  static const AppIconData pants = AppIconData(0xed85);
  static const AppIconData parking = AppIconData(0xed86);
  static const AppIconData passwordCheck = AppIconData(0xed87);
  static const AppIconData passwordCursor = AppIconData(0xed88);
  static const AppIconData passwordXmark = AppIconData(0xed89);
  static const AppIconData pasteClipboard = AppIconData(0xed8a);
  static const AppIconData pathArrow = AppIconData(0xed8b);
  static const AppIconData pauseWindow = AppIconData(0xed8c);
  static const AppIconData pause = AppIconData(0xed8d);
  static const AppIconData paypal = AppIconData(0xed8e);
  static const AppIconData pcCheck = AppIconData(0xed8f);
  static const AppIconData pcFirewall = AppIconData(0xed90);
  static const AppIconData pcMouse = AppIconData(0xed91);
  static const AppIconData pcNoEntry = AppIconData(0xed92);
  static const AppIconData pcWarning = AppIconData(0xed93);
  static const AppIconData peaceHand = AppIconData(0xed94);
  static const AppIconData peerlist = AppIconData(0xed95);
  static const AppIconData penConnectBluetooth = AppIconData(0xed96);
  static const AppIconData penConnectWifi = AppIconData(0xed97);
  static const AppIconData penTabletConnectUsb = AppIconData(0xed98);
  static const AppIconData penTabletConnectWifi = AppIconData(0xed99);
  static const AppIconData penTablet = AppIconData(0xed9a);
  static const AppIconData pentagon = AppIconData(0xed9b);
  static const AppIconData peopleTag = AppIconData(0xed9c);
  static const AppIconData percentRotateOut = AppIconData(0xed9d);
  static const AppIconData percentageCircle = AppIconData(0xed9e);
  static const AppIconData percentageSquare = AppIconData(0xed9f);
  static const AppIconData percentage = AppIconData(0xeda0);
  static const AppIconData perspectiveView = AppIconData(0xeda1);
  static const AppIconData pharmacyCrossCircle = AppIconData(0xeda2);
  static const AppIconData pharmacyCrossTag = AppIconData(0xeda3);
  static const AppIconData phoneDisabled = AppIconData(0xeda4);
  static const AppIconData phoneIncome = AppIconData(0xeda5);
  static const AppIconData phoneMinus = AppIconData(0xeda6);
  static const AppIconData phoneOutcome = AppIconData(0xeda7);
  static const AppIconData phonePaused = AppIconData(0xeda8);
  static const AppIconData phonePlus = AppIconData(0xeda9);
  static const AppIconData phoneXmark = AppIconData(0xedaa);
  static const AppIconData phone = AppIconData(0xedab);
  static const AppIconData piggyBank = AppIconData(0xedac);
  static const AppIconData pillow = AppIconData(0xedad);
  static const AppIconData pinSlash = AppIconData(0xedae);
  static const AppIconData pin = AppIconData(0xedaf);
  static const AppIconData pineTree = AppIconData(0xedb0);
  static const AppIconData pinterest = AppIconData(0xedb1);
  static const AppIconData pipe3d = AppIconData(0xedb2);
  static const AppIconData pizzaSlice = AppIconData(0xedb3);
  static const AppIconData planetAlt = AppIconData(0xedb4);
  static const AppIconData planetSat = AppIconData(0xedb5);
  static const AppIconData planet = AppIconData(0xedb6);
  static const AppIconData planimetry = AppIconData(0xedb7);
  static const AppIconData play = AppIconData(0xedb8);
  static const AppIconData playlistPlay = AppIconData(0xedb9);
  static const AppIconData playlistPlus = AppIconData(0xedba);
  static const AppIconData playlist = AppIconData(0xedbb);
  static const AppIconData playstationGamepad = AppIconData(0xedbc);
  static const AppIconData plugTypeA = AppIconData(0xedbd);
  static const AppIconData plugTypeC = AppIconData(0xedbe);
  static const AppIconData plugTypeG = AppIconData(0xedbf);
  static const AppIconData plugTypeL = AppIconData(0xedc0);
  static const AppIconData plusCircle = AppIconData(0xedc1);
  static const AppIconData plusSquareDashed = AppIconData(0xedc2);
  static const AppIconData plusSquare = AppIconData(0xedc3);
  static const AppIconData plus = AppIconData(0xedc4);
  static const AppIconData pngFormat = AppIconData(0xedc5);
  static const AppIconData pocket = AppIconData(0xedc6);
  static const AppIconData podcast = AppIconData(0xedc7);
  static const AppIconData pokeball = AppIconData(0xedc8);
  static const AppIconData polarSh = AppIconData(0xedc9);
  static const AppIconData positionAlign = AppIconData(0xedca);
  static const AppIconData position = AppIconData(0xedcb);
  static const AppIconData post = AppIconData(0xedcc);
  static const AppIconData potion = AppIconData(0xedcd);
  static const AppIconData pound = AppIconData(0xedce);
  static const AppIconData precisionTool = AppIconData(0xedcf);
  static const AppIconData presentation = AppIconData(0xedd0);
  static const AppIconData printer = AppIconData(0xedd1);
  static const AppIconData printingPage = AppIconData(0xedd2);
  static const AppIconData priorityDown = AppIconData(0xedd3);
  static const AppIconData priorityHigh = AppIconData(0xedd4);
  static const AppIconData priorityMedium = AppIconData(0xedd5);
  static const AppIconData priorityUp = AppIconData(0xedd6);
  static const AppIconData privacyPolicy = AppIconData(0xedd7);
  static const AppIconData privateWifi = AppIconData(0xedd8);
  static const AppIconData profileCircle = AppIconData(0xedd9);
  static const AppIconData prohibition = AppIconData(0xedda);
  static const AppIconData projectCurve3d = AppIconData(0xeddb);
  static const AppIconData puzzle = AppIconData(0xeddc);
  static const AppIconData qrCode = AppIconData(0xeddd);
  static const AppIconData questionMark = AppIconData(0xedde);
  static const AppIconData quoteMessage = AppIconData(0xeddf);
  static const AppIconData quote = AppIconData(0xede0);
  static const AppIconData radiation = AppIconData(0xede1);
  static const AppIconData radius = AppIconData(0xede2);
  static const AppIconData rain = AppIconData(0xede3);
  static const AppIconData rawFormat = AppIconData(0xede4);
  static const AppIconData receiveDollars = AppIconData(0xede5);
  static const AppIconData receiveEuros = AppIconData(0xede6);
  static const AppIconData receivePounds = AppIconData(0xede7);
  static const AppIconData receiveYens = AppIconData(0xede8);
  static const AppIconData redoAction = AppIconData(0xede9);
  static const AppIconData redoCircle = AppIconData(0xedea);
  static const AppIconData redo = AppIconData(0xedeb);
  static const AppIconData reduce = AppIconData(0xedec);
  static const AppIconData refreshCircle = AppIconData(0xeded);
  static const AppIconData refreshDouble = AppIconData(0xedee);
  static const AppIconData refresh = AppIconData(0xedef);
  static const AppIconData reloadWindow = AppIconData(0xedf0);
  static const AppIconData reminderHandGesture = AppIconData(0xedf1);
  static const AppIconData repeatOnce = AppIconData(0xedf2);
  static const AppIconData repeat = AppIconData(0xedf3);
  static const AppIconData replyToMessage = AppIconData(0xedf4);
  static const AppIconData reply = AppIconData(0xedf5);
  static const AppIconData reportColumns = AppIconData(0xedf6);
  static const AppIconData reports = AppIconData(0xedf7);
  static const AppIconData repository = AppIconData(0xedf8);
  static const AppIconData restart = AppIconData(0xedf9);
  static const AppIconData rewind = AppIconData(0xedfa);
  static const AppIconData rhombusArrowRight = AppIconData(0xedfb);
  static const AppIconData rhombus = AppIconData(0xedfc);
  static const AppIconData rings = AppIconData(0xedfd);
  static const AppIconData rocket = AppIconData(0xedfe);
  static const AppIconData rook = AppIconData(0xedff);
  static const AppIconData rotateCameraLeft = AppIconData(0xee00);
  static const AppIconData rotateCameraRight = AppIconData(0xee01);
  static const AppIconData roundFlask = AppIconData(0xee02);
  static const AppIconData roundedMirror = AppIconData(0xee03);
  static const AppIconData rssFeedTag = AppIconData(0xee04);
  static const AppIconData rssFeed = AppIconData(0xee05);
  static const AppIconData rubikCube = AppIconData(0xee06);
  static const AppIconData rulerArrows = AppIconData(0xee07);
  static const AppIconData rulerCombine = AppIconData(0xee08);
  static const AppIconData rulerMinus = AppIconData(0xee09);
  static const AppIconData rulerPlus = AppIconData(0xee0a);
  static const AppIconData ruler = AppIconData(0xee0b);
  static const AppIconData running = AppIconData(0xee0c);
  static const AppIconData safari = AppIconData(0xee0d);
  static const AppIconData safeArrowLeft = AppIconData(0xee0e);
  static const AppIconData safeArrowRight = AppIconData(0xee0f);
  static const AppIconData safeOpen = AppIconData(0xee10);
  static const AppIconData safe = AppIconData(0xee11);
  static const AppIconData sandals = AppIconData(0xee12);
  static const AppIconData scaleFrameEnlarge = AppIconData(0xee13);
  static const AppIconData scaleFrameReduce = AppIconData(0xee14);
  static const AppIconData scanBarcode = AppIconData(0xee15);
  static const AppIconData scanQrCode = AppIconData(0xee16);
  static const AppIconData scanning = AppIconData(0xee17);
  static const AppIconData scarf = AppIconData(0xee18);
  static const AppIconData scissorAlt = AppIconData(0xee19);
  static const AppIconData scissor = AppIconData(0xee1a);
  static const AppIconData screenshot = AppIconData(0xee1b);
  static const AppIconData seaAndSun = AppIconData(0xee1c);
  static const AppIconData seaWaves = AppIconData(0xee1d);
  static const AppIconData searchEngine = AppIconData(0xee1e);
  static const AppIconData searchWindow = AppIconData(0xee1f);
  static const AppIconData search = AppIconData(0xee20);
  static const AppIconData secureWindow = AppIconData(0xee21);
  static const AppIconData securityPass = AppIconData(0xee22);
  static const AppIconData selectEdge3d = AppIconData(0xee23);
  static const AppIconData selectFace3d = AppIconData(0xee24);
  static const AppIconData selectPoint3d = AppIconData(0xee25);
  static const AppIconData selectWindow = AppIconData(0xee26);
  static const AppIconData selectiveTool = AppIconData(0xee27);
  static const AppIconData sendDiagonal = AppIconData(0xee28);
  static const AppIconData sendDollars = AppIconData(0xee29);
  static const AppIconData sendEuros = AppIconData(0xee2a);
  static const AppIconData sendMail = AppIconData(0xee2b);
  static const AppIconData sendPounds = AppIconData(0xee2c);
  static const AppIconData sendYens = AppIconData(0xee2d);
  static const AppIconData send = AppIconData(0xee2e);
  static const AppIconData serverConnection = AppIconData(0xee2f);
  static const AppIconData server = AppIconData(0xee30);
  static const AppIconData settingsProfiles = AppIconData(0xee31);
  static const AppIconData settings = AppIconData(0xee32);
  static const AppIconData shareAndroid = AppIconData(0xee33);
  static const AppIconData shareIos = AppIconData(0xee34);
  static const AppIconData shieldAlert = AppIconData(0xee35);
  static const AppIconData shieldAlt = AppIconData(0xee36);
  static const AppIconData shieldBroken = AppIconData(0xee37);
  static const AppIconData shieldCheck = AppIconData(0xee38);
  static const AppIconData shieldDownload = AppIconData(0xee39);
  static const AppIconData shieldEye = AppIconData(0xee3a);
  static const AppIconData shieldLoading = AppIconData(0xee3b);
  static const AppIconData shieldMinus = AppIconData(0xee3c);
  static const AppIconData shieldPlusIn = AppIconData(0xee3d);
  static const AppIconData shieldQuestion = AppIconData(0xee3e);
  static const AppIconData shieldSearch = AppIconData(0xee3f);
  static const AppIconData shieldUpload = AppIconData(0xee40);
  static const AppIconData shieldXmark = AppIconData(0xee41);
  static const AppIconData shield = AppIconData(0xee42);
  static const AppIconData shirtTankTop = AppIconData(0xee43);
  static const AppIconData shirt = AppIconData(0xee44);
  static const AppIconData shopFourTilesWindow = AppIconData(0xee45);
  static const AppIconData shopFourTiles = AppIconData(0xee46);
  static const AppIconData shopWindow = AppIconData(0xee47);
  static const AppIconData shop = AppIconData(0xee48);
  static const AppIconData shoppingBagArrowDown = AppIconData(0xee49);
  static const AppIconData shoppingBagArrowUp = AppIconData(0xee4a);
  static const AppIconData shoppingBagCheck = AppIconData(0xee4b);
  static const AppIconData shoppingBagMinus = AppIconData(0xee4c);
  static const AppIconData shoppingBagPlus = AppIconData(0xee4d);
  static const AppIconData shoppingBagPocket = AppIconData(0xee4e);
  static const AppIconData shoppingBagWarning = AppIconData(0xee4f);
  static const AppIconData shoppingBag = AppIconData(0xee50);
  static const AppIconData shoppingCodeCheck = AppIconData(0xee51);
  static const AppIconData shoppingCodeXmark = AppIconData(0xee52);
  static const AppIconData shoppingCode = AppIconData(0xee53);
  static const AppIconData shortPantsPockets = AppIconData(0xee54);
  static const AppIconData shortPants = AppIconData(0xee55);
  static const AppIconData shortcutSquare = AppIconData(0xee56);
  static const AppIconData shuffle = AppIconData(0xee57);
  static const AppIconData sidebarCollapse = AppIconData(0xee58);
  static const AppIconData sidebarExpand = AppIconData(0xee59);
  static const AppIconData sigmaFunction = AppIconData(0xee5a);
  static const AppIconData simpleCart = AppIconData(0xee5b);
  static const AppIconData sineWave = AppIconData(0xee5c);
  static const AppIconData singleTapGesture = AppIconData(0xee5d);
  static const AppIconData skateboard = AppIconData(0xee5e);
  static const AppIconData skateboarding = AppIconData(0xee5f);
  static const AppIconData skipNext = AppIconData(0xee60);
  static const AppIconData skipPrev = AppIconData(0xee61);
  static const AppIconData slashSquare = AppIconData(0xee62);
  static const AppIconData slash = AppIconData(0xee63);
  static const AppIconData sleeperChair = AppIconData(0xee64);
  static const AppIconData slips = AppIconData(0xee65);
  static const AppIconData smallLampAlt = AppIconData(0xee66);
  static const AppIconData smallLamp = AppIconData(0xee67);
  static const AppIconData smartphoneDevice = AppIconData(0xee68);
  static const AppIconData smoking = AppIconData(0xee69);
  static const AppIconData snapchat = AppIconData(0xee6a);
  static const AppIconData snowFlake = AppIconData(0xee6b);
  static const AppIconData snow = AppIconData(0xee6c);
  static const AppIconData soap = AppIconData(0xee6d);
  static const AppIconData soccerBall = AppIconData(0xee6e);
  static const AppIconData sofa = AppIconData(0xee6f);
  static const AppIconData soilAlt = AppIconData(0xee70);
  static const AppIconData soil = AppIconData(0xee71);
  static const AppIconData sortDown = AppIconData(0xee72);
  static const AppIconData sortUp = AppIconData(0xee73);
  static const AppIconData sort = AppIconData(0xee74);
  static const AppIconData soundHigh = AppIconData(0xee75);
  static const AppIconData soundLow = AppIconData(0xee76);
  static const AppIconData soundMin = AppIconData(0xee77);
  static const AppIconData soundOff = AppIconData(0xee78);
  static const AppIconData spades = AppIconData(0xee79);
  static const AppIconData spark = AppIconData(0xee7a);
  static const AppIconData sparks = AppIconData(0xee7b);
  static const AppIconData sphere = AppIconData(0xee7c);
  static const AppIconData spiral = AppIconData(0xee7d);
  static const AppIconData splitArea = AppIconData(0xee7e);
  static const AppIconData splitSquareDashed = AppIconData(0xee7f);
  static const AppIconData spockHandGesture = AppIconData(0xee80);
  static const AppIconData spotify = AppIconData(0xee81);
  static const AppIconData square3dCornerToCorner = AppIconData(0xee82);
  static const AppIconData square3dFromCenter = AppIconData(0xee83);
  static const AppIconData square3dThreePoints = AppIconData(0xee84);
  static const AppIconData squareCursor = AppIconData(0xee85);
  static const AppIconData squareDashed = AppIconData(0xee86);
  static const AppIconData squareWave = AppIconData(0xee87);
  static const AppIconData square = AppIconData(0xee88);
  static const AppIconData stackoverflow = AppIconData(0xee89);
  static const AppIconData starDashed = AppIconData(0xee8a);
  static const AppIconData starHalfDashed = AppIconData(0xee8b);
  static const AppIconData star = AppIconData(0xee8c);
  static const AppIconData statDown = AppIconData(0xee8d);
  static const AppIconData statUp = AppIconData(0xee8e);
  static const AppIconData statsDownSquare = AppIconData(0xee8f);
  static const AppIconData statsReport = AppIconData(0xee90);
  static const AppIconData statsUpSquare = AppIconData(0xee91);
  static const AppIconData strategy = AppIconData(0xee92);
  static const AppIconData stretching = AppIconData(0xee93);
  static const AppIconData strikethrough = AppIconData(0xee94);
  static const AppIconData stroller = AppIconData(0xee95);
  static const AppIconData styleBorder = AppIconData(0xee96);
  static const AppIconData submitDocument = AppIconData(0xee97);
  static const AppIconData substract = AppIconData(0xee98);
  static const AppIconData suggestion = AppIconData(0xee99);
  static const AppIconData suitcase = AppIconData(0xee9a);
  static const AppIconData sunLight = AppIconData(0xee9b);
  static const AppIconData svgFormat = AppIconData(0xee9c);
  static const AppIconData sweep3d = AppIconData(0xee9d);
  static const AppIconData swimming = AppIconData(0xee9e);
  static const AppIconData swipeDownGesture = AppIconData(0xee9f);
  static const AppIconData swipeLeftGesture = AppIconData(0xeea0);
  static const AppIconData swipeRightGesture = AppIconData(0xeea1);
  static const AppIconData swipeTwoFingersDownGesture = AppIconData(0xeea2);
  static const AppIconData swipeTwoFingersLeftGesture = AppIconData(0xeea3);
  static const AppIconData swipeTwoFingersRightGesture = AppIconData(0xeea4);
  static const AppIconData swipeTwoFingersUpGesture = AppIconData(0xeea5);
  static const AppIconData swipeUpGesture = AppIconData(0xeea6);
  static const AppIconData switchOff = AppIconData(0xeea7);
  static const AppIconData switchOn = AppIconData(0xeea8);
  static const AppIconData systemRestart = AppIconData(0xeea9);
  static const AppIconData systemShut = AppIconData(0xeeaa);
  static const AppIconData table2Columns = AppIconData(0xeeab);
  static const AppIconData tableRows = AppIconData(0xeeac);
  static const AppIconData table = AppIconData(0xeead);
  static const AppIconData taskList = AppIconData(0xeeae);
  static const AppIconData telegramCircle = AppIconData(0xeeaf);
  static const AppIconData telegram = AppIconData(0xeeb0);
  static const AppIconData temperatureDown = AppIconData(0xeeb1);
  static const AppIconData temperatureHigh = AppIconData(0xeeb2);
  static const AppIconData temperatureLow = AppIconData(0xeeb3);
  static const AppIconData temperatureUp = AppIconData(0xeeb4);
  static const AppIconData tennisBallAlt = AppIconData(0xeeb5);
  static const AppIconData tennisBall = AppIconData(0xeeb6);
  static const AppIconData terminalTag = AppIconData(0xeeb7);
  static const AppIconData terminal = AppIconData(0xeeb8);
  static const AppIconData testTube = AppIconData(0xeeb9);
  static const AppIconData textArrowsUpDown = AppIconData(0xeeba);
  static const AppIconData textBox = AppIconData(0xeebb);
  static const AppIconData textMagnifyingGlass = AppIconData(0xeebc);
  static const AppIconData textSize = AppIconData(0xeebd);
  static const AppIconData textSquare = AppIconData(0xeebe);
  static const AppIconData text = AppIconData(0xeebf);
  static const AppIconData threads = AppIconData(0xeec0);
  static const AppIconData threePointsCircle = AppIconData(0xeec1);
  static const AppIconData threeStars = AppIconData(0xeec2);
  static const AppIconData thumbsDown = AppIconData(0xeec3);
  static const AppIconData thumbsUp = AppIconData(0xeec4);
  static const AppIconData thunderstorm = AppIconData(0xeec5);
  static const AppIconData tifFormat = AppIconData(0xeec6);
  static const AppIconData tiffFormat = AppIconData(0xeec7);
  static const AppIconData tiktok = AppIconData(0xeec8);
  static const AppIconData timeZone = AppIconData(0xeec9);
  static const AppIconData timerOff = AppIconData(0xeeca);
  static const AppIconData timer = AppIconData(0xeecb);
  static const AppIconData tools = AppIconData(0xeecc);
  static const AppIconData tournament = AppIconData(0xeecd);
  static const AppIconData towerCheck = AppIconData(0xeece);
  static const AppIconData towerNoAccess = AppIconData(0xeecf);
  static const AppIconData towerWarning = AppIconData(0xeed0);
  static const AppIconData tower = AppIconData(0xeed1);
  static const AppIconData trademark = AppIconData(0xeed2);
  static const AppIconData train = AppIconData(0xeed3);
  static const AppIconData tram = AppIconData(0xeed4);
  static const AppIconData transitionDown = AppIconData(0xeed5);
  static const AppIconData transitionLeft = AppIconData(0xeed6);
  static const AppIconData transitionRight = AppIconData(0xeed7);
  static const AppIconData transitionUp = AppIconData(0xeed8);
  static const AppIconData translate = AppIconData(0xeed9);
  static const AppIconData trash = AppIconData(0xeeda);
  static const AppIconData treadmill = AppIconData(0xeedb);
  static const AppIconData tree = AppIconData(0xeedc);
  static const AppIconData trekking = AppIconData(0xeedd);
  static const AppIconData trello = AppIconData(0xeede);
  static const AppIconData triangleFlagCircle = AppIconData(0xeedf);
  static const AppIconData triangleFlagTwoStripes = AppIconData(0xeee0);
  static const AppIconData triangleFlag = AppIconData(0xeee1);
  static const AppIconData triangle = AppIconData(0xeee2);
  static const AppIconData trophy = AppIconData(0xeee3);
  static const AppIconData truckGreen = AppIconData(0xeee4);
  static const AppIconData truckLength = AppIconData(0xeee5);
  static const AppIconData truck = AppIconData(0xeee6);
  static const AppIconData tunnel = AppIconData(0xeee7);
  static const AppIconData tvFix = AppIconData(0xeee8);
  static const AppIconData tvWarning = AppIconData(0xeee9);
  static const AppIconData tv = AppIconData(0xeeea);
  static const AppIconData twitter = AppIconData(0xeeeb);
  static const AppIconData twoPointsCircle = AppIconData(0xeeec);
  static const AppIconData twoSeaterSofa = AppIconData(0xeeed);
  static const AppIconData type = AppIconData(0xeeee);
  static const AppIconData uTurnArrowLeft = AppIconData(0xeeef);
  static const AppIconData uTurnArrowRight = AppIconData(0xeef0);
  static const AppIconData umbrella = AppIconData(0xeef1);
  static const AppIconData underlineSquare = AppIconData(0xeef2);
  static const AppIconData underline = AppIconData(0xeef3);
  static const AppIconData undoAction = AppIconData(0xeef4);
  static const AppIconData undoCircle = AppIconData(0xeef5);
  static const AppIconData undo = AppIconData(0xeef6);
  static const AppIconData unionAlt = AppIconData(0xeef7);
  static const AppIconData unionHorizAlt = AppIconData(0xeef8);
  static const AppIconData union = AppIconData(0xeef9);
  static const AppIconData unity5 = AppIconData(0xeefa);
  static const AppIconData unity = AppIconData(0xeefb);
  static const AppIconData unjoin3d = AppIconData(0xeefc);
  static const AppIconData uploadDataWindow = AppIconData(0xeefd);
  static const AppIconData uploadSquare = AppIconData(0xeefe);
  static const AppIconData upload = AppIconData(0xeeff);
  static const AppIconData usb = AppIconData(0xef00);
  static const AppIconData userBadgeCheck = AppIconData(0xef01);
  static const AppIconData userBag = AppIconData(0xef02);
  static const AppIconData userCart = AppIconData(0xef03);
  static const AppIconData userCircle = AppIconData(0xef04);
  static const AppIconData userCrown = AppIconData(0xef05);
  static const AppIconData userLove = AppIconData(0xef06);
  static const AppIconData userPlus = AppIconData(0xef07);
  static const AppIconData userScan = AppIconData(0xef08);
  static const AppIconData userSquare = AppIconData(0xef09);
  static const AppIconData userStar = AppIconData(0xef0a);
  static const AppIconData userXmark = AppIconData(0xef0b);
  static const AppIconData user = AppIconData(0xef0c);
  static const AppIconData veganCircle = AppIconData(0xef0d);
  static const AppIconData veganSquare = AppIconData(0xef0e);
  static const AppIconData vegan = AppIconData(0xef0f);
  static const AppIconData vehicleGreen = AppIconData(0xef10);
  static const AppIconData verticalMerge = AppIconData(0xef11);
  static const AppIconData verticalSplit = AppIconData(0xef12);
  static const AppIconData vials = AppIconData(0xef13);
  static const AppIconData videoCameraOff = AppIconData(0xef14);
  static const AppIconData videoCamera = AppIconData(0xef15);
  static const AppIconData videoProjector = AppIconData(0xef16);
  static const AppIconData view360 = AppIconData(0xef17);
  static const AppIconData viewColumns2 = AppIconData(0xef18);
  static const AppIconData viewColumns3 = AppIconData(0xef19);
  static const AppIconData viewGrid = AppIconData(0xef1a);
  static const AppIconData viewStructureDown = AppIconData(0xef1b);
  static const AppIconData viewStructureUp = AppIconData(0xef1c);
  static const AppIconData voiceCheck = AppIconData(0xef1d);
  static const AppIconData voiceCircle = AppIconData(0xef1e);
  static const AppIconData voiceLockCircle = AppIconData(0xef1f);
  static const AppIconData voiceScan = AppIconData(0xef20);
  static const AppIconData voiceSquare = AppIconData(0xef21);
  static const AppIconData voiceXmark = AppIconData(0xef22);
  static const AppIconData voice = AppIconData(0xef23);
  static const AppIconData vrTag = AppIconData(0xef24);
  static const AppIconData vueJs = AppIconData(0xef25);
  static const AppIconData waist = AppIconData(0xef26);
  static const AppIconData walking = AppIconData(0xef27);
  static const AppIconData wallet = AppIconData(0xef28);
  static const AppIconData warningCircle = AppIconData(0xef29);
  static const AppIconData warningHexagon = AppIconData(0xef2a);
  static const AppIconData warningSquare = AppIconData(0xef2b);
  static const AppIconData warningTriangle = AppIconData(0xef2c);
  static const AppIconData warningWindow = AppIconData(0xef2d);
  static const AppIconData wash = AppIconData(0xef2e);
  static const AppIconData washingMachine = AppIconData(0xef2f);
  static const AppIconData wateringSoil = AppIconData(0xef30);
  static const AppIconData webWindowEnergyConsumption = AppIconData(0xef31);
  static const AppIconData webWindowXmark = AppIconData(0xef32);
  static const AppIconData webWindow = AppIconData(0xef33);
  static const AppIconData webpFormat = AppIconData(0xef34);
  static const AppIconData weightAlt = AppIconData(0xef35);
  static const AppIconData weight = AppIconData(0xef36);
  static const AppIconData whiteFlag = AppIconData(0xef37);
  static const AppIconData wifiOff = AppIconData(0xef38);
  static const AppIconData wifiSignalNone = AppIconData(0xef39);
  static const AppIconData wifiTag = AppIconData(0xef3a);
  static const AppIconData wifiWarning = AppIconData(0xef3b);
  static const AppIconData wifiXmark = AppIconData(0xef3c);
  static const AppIconData wifi = AppIconData(0xef3d);
  static const AppIconData wind = AppIconData(0xef3e);
  static const AppIconData windowCheck = AppIconData(0xef3f);
  static const AppIconData windowLock = AppIconData(0xef40);
  static const AppIconData windowNoAccess = AppIconData(0xef41);
  static const AppIconData windowTabs = AppIconData(0xef42);
  static const AppIconData windowXmark = AppIconData(0xef43);
  static const AppIconData windows = AppIconData(0xef44);
  static const AppIconData wolf = AppIconData(0xef45);
  static const AppIconData wrapText = AppIconData(0xef46);
  static const AppIconData wrench = AppIconData(0xef47);
  static const AppIconData wristwatch = AppIconData(0xef48);
  static const AppIconData www = AppIconData(0xef49);
  static const AppIconData xSquare = AppIconData(0xef4a);
  static const AppIconData x = AppIconData(0xef4b);
  static const AppIconData xboxA = AppIconData(0xef4c);
  static const AppIconData xboxB = AppIconData(0xef4d);
  static const AppIconData xboxX = AppIconData(0xef4e);
  static const AppIconData xboxY = AppIconData(0xef4f);
  static const AppIconData xmarkCircle = AppIconData(0xef50);
  static const AppIconData xmarkSquare = AppIconData(0xef51);
  static const AppIconData xmark = AppIconData(0xef52);
  static const AppIconData xrayView = AppIconData(0xef53);
  static const AppIconData ySquare = AppIconData(0xef54);
  static const AppIconData yelp = AppIconData(0xef55);
  static const AppIconData yenSquare = AppIconData(0xef56);
  static const AppIconData yen = AppIconData(0xef57);
  static const AppIconData yoga = AppIconData(0xef58);
  static const AppIconData youtube = AppIconData(0xef59);
  static const AppIconData zSquare = AppIconData(0xef5a);
  static const AppIconData zoomIn = AppIconData(0xef5b);
  static const AppIconData zoomOut = AppIconData(0xef5c);
}
