import 'package:day1/day1.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

class AppStateWrapper extends AppStatelessWidget {
  final Widget child;
  final List<ChangeNotifierProvider> providers;
  final String? path;

  const AppStateWrapper({
    super.key,
    required this.child,
    required this.providers,
  }) : path = null;

  const AppStateWrapper.forPath({
    super.key,
    required this.child,
    required this.providers,
    required String initialRoute,
  }) : path = initialRoute;

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: providers,
      child: child,
    );
  }
}
