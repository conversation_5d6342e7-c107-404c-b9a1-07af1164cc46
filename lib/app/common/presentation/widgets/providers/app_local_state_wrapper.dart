import 'package:day1/day1.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

class AppLocalStateWrapper<T extends ChangeNotifier> extends AppStatefulWidget {
  final ValueBuilder<T> builder;
  final T? notifier;

  const AppLocalStateWrapper({
    required this.builder,
    this.notifier,
    super.key,
  });

  @override
  State<StatefulWidget> createState() => _AppLocalStateWrapperState<T>();
}

class _AppLocalStateWrapperState<T extends ChangeNotifier>
    extends State<AppLocalStateWrapper<T>> {
  late final T state;
  @override
  void initState() {
    super.initState();
    state = widget.notifier ?? locator<T>();
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider<T>.value(
      value: state,
      child: Consumer<T>(
        builder: (context, value, child) {
          return widget.builder(value);
        },
      ),
    );
  }
}
