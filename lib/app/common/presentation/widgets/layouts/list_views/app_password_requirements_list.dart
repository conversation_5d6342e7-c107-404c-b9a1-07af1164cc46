import 'package:flutter/material.dart';
import 'package:day1/day1.dart';

class PasswordRequirementsList extends AppStatelessWidget {
  final TextEditingController controller;

  const PasswordRequirementsList(this.controller, {super.key});

  static final _expectations = [
    ValidationPillData(
      LocaleKeys.eightCharacters,
      pattern: AppRegex.eightCharRegEx,
    ),
    ValidationPillData(
      LocaleKeys.aLowerChar,
      pattern: AppRegex.lowercaseRegEx,
    ),
    ValidationPillData(
      LocaleKeys.anUpperChar,
      pattern: AppRegex.uppercaseRegEx,
    ),
    ValidationPillData(
      LocaleKeys.aSpecialChar,
      pattern: AppRegex.specialCharRegEx,
    ),
    ValidationPillData(
      LocaleKeys.aNumber,
      pattern: AppRegex.digitRegEx,
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return GenericListener(
      valueListenable: controller,
      builder: (value) {
        return Padding(
          padding: context.insets.onlySp(right: AppFontSizes.px44),
          child: Wrap(
            alignment: WrapAlignment.start,
            crossAxisAlignment: WrapCrossAlignment.start,
            runAlignment: WrapAlignment.start,
            spacing: context.sp(AppFontSizes.px12),
            runSpacing: context.sp(AppFontSizes.px12),
            children: [
              for (final expectation in _expectations)
                AppValidatorPill(
                  value.text.tr(),
                  data: expectation,
                ),
            ],
          ),
        );
      },
    );
  }
}
