import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class AppInfiniteScrollView<T> extends AppStatefulWidget {
  final PaginatedData data;
  final OnChangedAsync<OnPressed> onScrollEnd;
  final OnPressedAsync onRefresh;
  final EdgeInsetsGeometry? padding;
  final Widget child;
  final ScrollController controller;
  final double indicatorOffset;

  const AppInfiniteScrollView({
    required this.data,
    required this.onScrollEnd,
    required this.onRefresh,
    required this.child,
    required this.controller,
    this.indicatorOffset = 0,
    this.padding,
    super.key,
  });

  @override
  State<StatefulWidget> createState() => _AppInfiniteScrollViewState();
}

class _AppInfiniteScrollViewState extends State<AppInfiniteScrollView> {
  double lastPosition = 0;
  PaginatedData get data => widget.data;
  EdgeInsetsGeometry? get padding => widget.padding;
  ScrollController get controller => widget.controller;
  double get indicatorOffset => widget.indicatorOffset;

  @override
  void initState() {
    super.initState();
    controller.addListener(_scrollObserver);
  }

  _scrollObserver() {
    if (!controller.hasClients) return;
    if (data.isLoading || !data.hasNext) return;

    final offset = controller.position.pixels;
    final maxOffset = controller.position.maxScrollExtent;

    bool isScrollingDown = offset > lastPosition;
    lastPosition = offset;

    if (!isScrollingDown) return;

    double gapToMax = maxOffset - offset;

    if (gapToMax > 100) return;

    widget.onScrollEnd(() {
      if (!controller.hasClients) return;
      final position = controller.position;
      controller.animateTo(
        position.pixels + context.fractionalHeight(20),
        duration: 500.milliDuration,
        curve: Curves.decelerate,
      );
    });
  }

  @override
  void dispose() {
    controller.removeListener(_scrollObserver);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    Widget body = RefreshIndicator.adaptive(
      onRefresh: widget.onRefresh,
      edgeOffset: indicatorOffset,
      child: widget.child,
    );

    if (padding != null) {
      body = Padding(padding: padding!, child: body);
    }

    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        Expanded(child: body),
        if (data.isLoading && data.hasData) const AppProgress()
      ],
    );
  }
}
