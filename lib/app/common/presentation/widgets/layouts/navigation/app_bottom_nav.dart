import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class AppBottomNavBar extends AppStatelessWidget {
  final PageController controller;
  final List<NavPageData> pages;
  final ValueNotifier<int> pageIndex;

  const AppBottomNavBar(
    this.pages, {
    super.key,
    required this.pageIndex,
    required this.controller,
  });

  @override
  Widget build(BuildContext context) {
    const hPad = AppFontSizes.px48;
    EdgeInsetsGeometry padding = context.insets.symmetricSp(
      horizontal: hPad,
      vertical: AppFontSizes.px16,
    );

    if (context.isIos) {
      padding = padding.add(
        context.insets.onlySp(bottom: AppFontSizes.px16),
      );
    }

    return Container(
      padding: padding,
      decoration: ShapeDecoration(
        color: context.cardColor,
        shape: ContinuousRectangleBorder(
          borderRadius: BorderRadius.vertical(
            top: Radius.circular(context.buttonRadius),
          ),
        ),
        shadows: context.shadows.bottomNavShaddow,
      ),
      child: ValueListenableBuilder(
        valueListenable: pageIndex,
        builder: (context, int index, _) {
          return Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              for (final (idx, item) in pages.indexed)
                BottomNavIcon(
                  icon: item.icon,
                  isActive: index == idx,
                  label: item.label,
                  onPressed: () {
                    if (item.onTap != null) {
                      item.onTap?.call();
                      return;
                    }
                    controller.jumpToPage(idx);
                  },
                )
            ],
          );
        },
      ),
    );
  }
}
