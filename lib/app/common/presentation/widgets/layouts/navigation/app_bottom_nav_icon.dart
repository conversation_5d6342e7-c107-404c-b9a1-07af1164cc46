import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class BottomNavIcon extends AppStatelessWidget {
  final IconData icon;
  final bool isActive;
  final Color? activeColor;
  final OnPressed onPressed;
  final double? size;
  final String? label;
  final ButtonSize? buttonSize;

  const BottomNavIcon({
    required this.icon,
    required this.isActive,
    required this.onPressed,
    this.label,
    this.activeColor,
    this.buttonSize,
    this.size,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final insets = context.insets;
    final size = Size.fromWidth(
      context.sp(AppFontSizes.px56),
    );

    final padding = buttonSize == null
        ? insets.symmetricSp(
            vertical: AppFontSizes.px8,
            horizontal: AppFontSizes.px16,
          )
        : null;

    Widget child = Builder(
      key: ValueKey(isActive),
      builder: (_) {
        if (!isActive) {
          return AppOutlineButton(
            onPressed: onPressed,
            size: buttonSize ?? ButtonSize.medium,
            icon: AppIcon(
              icon,
              color: context.textColor,
            ),
            text: label,
            contentPadding: padding,
            minSize: size,
            variant: OutlineBtnVariant.neutral,
          );
        }
        return AppButton(
          onPressed: onPressed,
          borderColor: activeColor,
          variant: RaisedButtonVariant.c,
          size: buttonSize ?? ButtonSize.medium,
          icon: icon,
          text: label,
          contentPadding: padding,
          bgColor: activeColor,
          minSize: size,
        );
      },
    );

    if (!label.hasValue) {
      child = AppAnimatedSlider(child: child);
    }

    return child;
  }
}
