import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class AppRootPopScope extends AppStatefulWidget {
  final Widget child;

  const AppRootPopScope({super.key, required this.child});

  @override
  State<AppRootPopScope> createState() => _AppRootPopScopeState();
}

class _AppRootPopScopeState extends State<AppRootPopScope> with AppModalMixin {
  @override
  Widget build(BuildContext context) {
    if (!Navigator.of(context).canPop()) {
      return PopScope(
        canPop: false,
        onPopInvokedWithResult: (_, __) {
          confirmAction(
            AppRouter.navigatorKey.currentContext ?? context,
            onContinue: AppRouter.closeApp,
            title: LocaleKeys.closeApp.tr(),
            description: LocaleKeys.closeAppQuestion.tr(),
          );
        },
        child: widget.child,
      );
    }
    return widget.child;
  }
}

class AppPopScope extends AppStatefulWidget {
  final Widget child;
  final bool canPop;
  final OnChanged2<bool, Object?>? onPopRequest;

  const AppPopScope({
    super.key,
    required this.child,
    this.canPop = true,
    this.onPopRequest,
  });

  @override
  State<AppPopScope> createState() => _AppopScopeState();
}

class _AppopScopeState extends State<AppPopScope> {
  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: widget.canPop,
      onPopInvokedWithResult: widget.onPopRequest,
      child: widget.child,
    );
  }
}
