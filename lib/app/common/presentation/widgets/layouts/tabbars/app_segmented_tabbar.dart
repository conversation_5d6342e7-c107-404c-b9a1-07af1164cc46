import 'package:day1/day1.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class AppSegmentedTabBar<T> extends AppStatelessWidget {
  final T selection;
  final List<Widget> tabs;
  final Color? bgColor;

  const AppSegmentedTabBar(
    this.selection, {
    required this.tabs,
    this.bgColor,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    List<Widget> items = [for (final tab in tabs) tab];
    return Container(
      padding: context.insets.allSp(AppFontSizes.px8),
      decoration: BoxDecoration(
        borderRadius: context.btnBorderRadius,
        color: bgColor ?? context.scaffoldBgColor,
      ),
      child: Row(children: [
        for (final item in items.intersperse(const AppGap.h4())) item
      ]),
    );
  }
}

class AppSegmentedTab<T> extends AppStatelessWidget {
  final T activeValue;
  final T value;
  final String label;
  final OnChanged<T> onSelect;
  final EdgeInsetsGeometry? padding;
  final Color? inActiveBorder;
  final BoxConstraints? constraints;

  const AppSegmentedTab({
    required this.value,
    required this.activeValue,
    required this.label,
    required this.onSelect,
    this.constraints,
    this.inActiveBorder,
    this.padding,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final isSelected = activeValue == value;
    final color = isSelected ? context.raisedBtnTextColor : context.textColor;
    final style = context.textStyle.b3(
      color: color,
      weight: FontWeight.w600,
    );

    return InkWell(
      key: ValueKey("type-tab-$isSelected"),
      onTap: () {
        HapticFeedback.lightImpact();

        onSelect(value);
      },
      child: AnimatedContainer(
        duration: 500.milliDuration,
        constraints: constraints,
        curve: Curves.decelerate,
        decoration: BoxDecoration(
          color: isSelected ? context.raisedBtnCBgColor : context.transparent,
          borderRadius: context.btnBorderRadius,
          border: Border.all(
            color: isSelected
                ? context.raisedBtnCBgColor
                : inActiveBorder ?? context.transparent,
          ),
        ),
        padding: padding ?? context.insets.allSp(AppFontSizes.px12),
        child: AppText(
          label,
          style: style,
          textAlign: TextAlign.center,
        ),
      ),
    );
  }
}
