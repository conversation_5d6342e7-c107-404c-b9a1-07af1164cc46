import 'package:day1/day1.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class App<PERSON>eyPad extends AppStatelessWidget {
  final TextEditingController controller;
  final OnPressed? onBioAuth;
  final int limit;
  final AlignmentGeometry alignment;

  const AppKeyPad({
    required this.controller,
    required this.limit,
    this.onBioAuth,
    this.alignment = Alignment.center,
    super.key,
  });

  _updateValue(String char) {
    AppHelpers.updateValue(char, controller, limit: limit);
  }

  @override
  Widget build(BuildContext context) {
    return Align(
      key: const PageStorageKey("app-key-pad"),
      alignment: alignment,
      child: Table(
        defaultVerticalAlignment: TableCellVerticalAlignment.middle,
        children: [
          for (final keyRow in AppKeyCellData.values)
            TableRow(
              children: List.generate(
                keyRow.length,
                (index) => AppKeyCell(
                  data: keyRow[index],
                  onSelected: _updateValue,
                  onBioAuth: onBioAuth,
                ),
              ),
            ),
        ],
      ),
    );
  }
}

class AppKeyCell extends AppStatelessWidget {
  final AppKeyCellData data;
  final OnChanged<String> onSelected;
  final OnPressed? onBioAuth;

  const AppKeyCell({
    required this.data,
    required this.onSelected,
    this.onBioAuth,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    if (data.value == "bio" && onBioAuth == null) {
      return const Offstage();
    }

    final color = context.highlightedTextColor;
    final padding = context.insets.symmetricSp(vertical: AppFontSizes.px18);

    Widget child = AppText(
      data.value,
      style: context.textStyle.b2(
        color: color,
        weight: FontWeight.w600,
      ),
      textAlign: TextAlign.center,
    );

    if (data.value == "bio" && onBioAuth != null) {
      child = AppIcon(
        data.icon!,
        size: AppFontSizes.px19,
        color: color,
      );
    }

    if (data.value == 'x') {
      child = AppSvg(
        AppVectors.arrowLeft,
        width: context.sp(AppFontSizes.px18),
        color: color,
      );
    }

    return TableRowInkWell(
      onTap: () {
        HapticFeedback.heavyImpact();
        if (data.value == "bio") {
          return onBioAuth?.call();
        }
        onSelected(data.value);
      },
      child: Padding(padding: padding, child: child),
    );
  }
}
