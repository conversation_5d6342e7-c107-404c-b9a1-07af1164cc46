import 'package:flutter/material.dart';

class MaxSizeContainer extends StatelessWidget {
  final Widget child;
  final double width;
  final double height;

  const MaxSizeContainer({
    required this.child,
    this.width = 544,
    required this.height,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: ConstrainedBox(
        constraints: BoxConstraints(
          maxWidth: width,
          maxHeight: height,
        ),
        child: child,
      ),
    );
  }
}
