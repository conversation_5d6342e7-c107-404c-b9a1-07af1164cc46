import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class AppAlertDialog extends AppStatelessWidget {
  final String title;
  final String? btnText;
  final String? description;
  final OnPressed action;

  const AppAlertDialog({
    super.key,
    this.description,
    this.btnText,
    required this.title,
    required this.action,
  });

  @override
  Widget build(BuildContext context) {
    AppRouter.openedModal();
    return Material(
      type: MaterialType.transparency,
      child: Padding(
        padding: context.insets.defaultAllInsets,
        child: Center(
          child: AppCard(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              mainAxisSize: MainAxisSize.min,
              children: [
                const AppTextButton(
                  textAlign: TextAlign.end,
                  icon: AppIcon(
                    AppIcons.xmark,
                    size: AppFontSizes.px24,
                  ),
                  size: ButtonSize.small,
                  onPressed: AppRouter.popView,
                ),
                const AppGap.y12(),
                const AppIcon(
                  AppIcons.bellNotification,
                  size: AppFontSizes.px24,
                  alignment: Alignment.centerLeft,
                ),
                const AppGap.y16(),
                AppText(title, style: context.textStyle.h5()),
                if (description.hasValue) ...[
                  const AppGap.y8(),
                  AppText(
                    description,
                    style: context.textStyle.b2(
                      color: context.secondaryTextColor,
                    ),
                  ),
                  const AppGap.y12(),
                ],
                const AppGap.y16(),
                AppButton(
                  text: btnText ?? LocaleKeys.continueText.tr(),
                  onPressed: action,
                )
              ],
            ),
          ),
        ),
      ),
    );
  }
}
