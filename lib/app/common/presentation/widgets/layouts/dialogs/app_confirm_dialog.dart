import 'package:day1/day1.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

class AppConfirmDialog extends AppStatelessWidget {
  final String title;
  final String? description;
  final String? allowText;
  final String? canceltext;
  final OnPressed onContinue;

  const AppConfirmDialog({
    super.key,
    this.description,
    this.allowText,
    this.canceltext,
    required this.title,
    required this.onContinue,
  });

  @override
  Widget build(BuildContext context) {
    AppRouter.openedModal();
    Widget? content;
    if (description != null) {
      content = AppText(
        description,
        style: context.textStyle.b3(),
      );
    }
    return AlertDialog.adaptive(
      title: AppText(title, style: context.textStyle.h6()),
      content: content,
      actions: [
        AppConfirmDialogAction(
          key: const Key("app-form-cancel-btn"),
          text: canceltext ?? LocaleKeys.dontAllow.tr(),
          onTap: AppRouter.popView,
          destructive: true,
        ),
        AppConfirmDialogAction(
          key: const Key("app-form-done-btn"),
          text: allowText ?? LocaleKeys.allow.tr(),
          onTap: () {
            Navigator.of(context).pop();
            onContinue.call();
          },
        ),
      ],
    );
  }
}

class AppConfirmDialogAction extends AppStatelessWidget {
  final scaffoldKey = GlobalKey<ScaffoldState>();
  final String text;
  final bool destructive;
  final OnPressed onTap;

  AppConfirmDialogAction({
    super.key,
    required this.text,
    this.destructive = false,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final textColor = destructive
        ? context.errorTextColor
        : context.isIos
            ? AppColors.tertiary8
            : context.textColor;
    final child = AppText(
      text,
      style: context.textStyle.b1(color: textColor),
    );
    if (context.isIos) {
      return CupertinoDialogAction(
        onPressed: onTap,
        isDefaultAction: !destructive,
        isDestructiveAction: destructive,
        child: child,
      );
    }
    return TextButton(onPressed: onTap, child: child);
  }
}
