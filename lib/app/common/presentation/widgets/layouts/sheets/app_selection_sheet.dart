import 'package:day1/day1.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class SelectionSheet<T> extends AppStatefulWidget {
  final SelectionData<T>? selection;
  final List<SelectionData<T>> options;
  final ScrollController controller;
  final TextInputAction textInputAction;
  final String title;
  final OnChanged<SelectionData<T>> onSelect;
  final bool popOnSelect;
  final bool allowSearch;

  const SelectionSheet({
    super.key,
    this.selection,
    this.popOnSelect = true,
    this.allowSearch = true,
    this.textInputAction = TextInputAction.search,
    required this.controller,
    required this.onSelect,
    required this.title,
    required this.options,
  });

  @override
  State<StatefulWidget> createState() => _SelectionSheetState<T>();
}

class _SelectionSheetState<T> extends State<SelectionSheet> {
  late ValueNotifier<SelectionData?> _choiceOption;
  late ValueNotifier<List<SelectionData>?> _choicesRef;
  late TextEditingController _termCtrl;

  @override
  initState() {
    super.initState();
    _choiceOption = ValueNotifier(widget.selection);
    _choicesRef = ValueNotifier(widget.options);
    _termCtrl = TextEditingController();
  }

  _filterOptions(String? term) {
    if (!term.hasValue) {
      _choicesRef.value = [...widget.options];
      return;
    }
    final options = widget.options.where(
      (it) =>
          it.label.includes(term!) ||
          (it.extendedLabel?.includes(term) ?? false),
    );
    _choicesRef.value = [...options];
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: true,
      backgroundColor: context.transparent,
      body: ListListener(
        valueListenable: _choicesRef,
        builder: (options) {
          return Scrollbar(
            controller: widget.controller,
            child: Padding(
              padding: context.insets.defaultHorizontalInsets,
              child: CustomScrollView(
                controller: widget.controller,
                slivers: [
                  if (widget.allowSearch)
                    SliverList.list(
                      children: [
                        const AppGap.y16(),
                        AppTextField(
                          onChanged: _filterOptions,
                          controller: _termCtrl,
                          hintText: LocaleKeys.search.tr(),
                          textInputAction: widget.textInputAction,
                        ),
                      ],
                    ),
                  const SliverToBoxAdapter(child: AppGap.y16()),
                  SliverList.builder(
                    itemBuilder: (context, index) {
                      final option = options[index];
                      return ValueListenableBuilder<SelectionData?>(
                        valueListenable: _choiceOption,
                        builder: (_, val, __) {
                          return SelectionTile(
                            icon: option.icon,
                            title: option.extendedLabel ?? option.label,
                            key: ValueKey([index, option.label]),
                            onTap: () {
                              HapticFeedback.selectionClick();
                              widget.onSelect(option as SelectionData<T>);
                              if (widget.popOnSelect) {
                                AppRouter.popView();
                              }
                            },
                            isSelected: option == widget.selection,
                          );
                        },
                      );
                    },
                    itemCount: options.length,
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
