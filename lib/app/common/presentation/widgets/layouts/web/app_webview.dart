// import 'dart:io';

// import 'package:flutter/material.dart';
// import 'package:flutter_inappwebview/flutter_inappwebview.dart';
// import 'package:day1/day1.dart';

// class AppWebView extends AppStatefulWidget {
//   final Uri? initialUri;
//   final String? initialData;
//   final OnPressed? onTapApplicationLink;
//   final OnChanged<double>? onHeightChange;

//   const AppWebView({
//     super.key,
//     this.onTapApplicationLink,
//     this.initialUri,
//     this.initialData,
//     this.onHeightChange,
//   });

//   @override
//   State<AppWebView> createState() => _AppWebViewState();
// }

// class _AppWebViewState extends State<AppWebView> {
//   late final PullToRefreshController _pullToRefreshController;
//   InAppWebViewController? _webViewController;

//   @override
//   void initState() {
//     super.initState();

//     _pullToRefreshController = PullToRefreshController(
//       settings: PullToRefreshSettings(color: context.btnColor),
//       onRefresh: () async {
//         if (Platform.isIOS) {
//           final request = URLRequest(url: await _webViewController?.getUrl());
//           _webViewController?.loadUrl(urlRequest: request);
//           return;
//         }
//         _webViewController?.reload();
//       },
//     );
//   }

//   @override
//   void dispose() {
//     super.dispose();
//   }

//   _setContentHeight(InAppWebViewController controller) async {
//     try {
//       final height = (await controller.getContentHeight())?.toDouble();
//       if (height == null) return;
//       widget.onHeightChange?.call(height);
//     } catch (_) {}
//   }

//   Future<NavigationActionPolicy> _handleHyperLinks(
//     InAppWebViewController _,
//     NavigationAction navigationAction,
//   ) async {
//     final uri = navigationAction.request.url;
//     final isBlank = "$uri".includes("about:blank");
//     if (initialWebUri == null && (initialData != null && isBlank)) {
//       return NavigationActionPolicy.ALLOW;
//     }
//     if (uri == initialWebUri) return NavigationActionPolicy.ALLOW;

//     if (uri != null) {
//       final isApplicationLink = AppRegex.jobDetailRegex.hasMatch("$uri");
//       if (isApplicationLink) {
//         widget.onTapApplicationLink?.call();
//         return NavigationActionPolicy.CANCEL;
//       }

//       context.launchUrl("$uri");
//     }

//     return NavigationActionPolicy.CANCEL;
//   }

//   WebUri? get initialWebUri {
//     if (widget.initialUri == null) return null;
//     return WebUri.uri(widget.initialUri!);
//   }

//   InAppWebViewInitialData? get initialData {
//     if (widget.initialData == null) return null;
//     return InAppWebViewInitialData(data: widget.initialData!);
//   }

//   @override
//   Widget build(BuildContext context) {
//     return RepaintBoundary(
//       child: InAppWebView(
//         key: ValueKey(widget.initialUri),
//         initialUrlRequest: URLRequest(url: initialWebUri),
//         initialData: initialData,
//         shouldOverrideUrlLoading: _handleHyperLinks,
//         initialSettings: InAppWebViewSettings(
//           useShouldOverrideUrlLoading: true,
//           mediaPlaybackRequiresUserGesture: false,
//           verticalScrollBarEnabled: false,
//           horizontalScrollBarEnabled: false,
//           allowFileAccessFromFileURLs: true,
//           allowUniversalAccessFromFileURLs: true,
//           disableVerticalScroll: true,
//           disableHorizontalScroll: true,
//           javaScriptCanOpenWindowsAutomatically: true,
//           cacheEnabled: true,
//           allowsInlineMediaPlayback: true,
//           useHybridComposition: true,
//         ),
//         pullToRefreshController: _pullToRefreshController,
//         onWebViewCreated: (ctrl) => _webViewController = ctrl,
//         onTitleChanged: (ctrl, _) => _setContentHeight(ctrl),
//         onPermissionRequest: (_, request) async {
//           return PermissionResponse(
//             resources: request.resources,
//             action: PermissionResponseAction.GRANT,
//           );
//         },
//         onReceivedError: (controller, _, __) {
//           _pullToRefreshController.endRefreshing();
//         },
//       ),
//     );
//   }
// }
