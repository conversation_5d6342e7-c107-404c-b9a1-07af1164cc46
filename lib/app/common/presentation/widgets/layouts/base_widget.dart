import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class BaseWidget extends AppStatelessWidget {
  final Widget? child;

  const BaseWidget({super.key, required this.child});

  @override
  Widget build(BuildContext context) {
    context.observeTheme();

    MediaQueryData mediaQuery = MediaQuery.of(context);
    config.windowSize = mediaQuery.size;

    final isIos = context.isIos || context.isMacos;

    mediaQuery = mediaQuery.copyWith(
      textScaler: mediaQuery.textScaler.clamp(
        minScaleFactor: isIos ? 1 : 0.8,
        maxScaleFactor: isIos ? 1.12 : 1.1,
      ),
    );

    return MediaQuery(
      key: ValueKey(context.inDarkMode),
      data: mediaQuery,
      child: GestureDetector(
        onTap: context.resetFocus,
        child: child ?? const Offstage(),
      ),
    );
  }
}
