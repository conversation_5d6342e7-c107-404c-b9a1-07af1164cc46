import 'package:flutter/material.dart';
import 'package:day1/day1.dart';

class DoAppBar extends AppStatelessWidget implements PreferredSizeWidget {
  final bool showLogo;
  final bool implyLeading;
  final Widget? leading;
  final String? title;
  final double? toolbarHeight;
  final Color? color;
  final Widget? trailing;
  final EdgeInsetsGeometry? padding;

  const DoAppBar({
    this.implyLeading = true,
    this.leading,
    this.trailing,
    this.title,
    this.color,
    this.toolbarHeight,
    this.padding,
    super.key,
  }) : showLogo = false;

  const DoAppBar.withLogo({
    this.trailing,
    this.color,
    this.toolbarHeight,
    this.padding,
    super.key,
  })  : showLogo = true,
        title = null,
        leading = null,
        implyLeading = false;

  bool get _shouldImplyLeading {
    return leading == null && (implyLeading && AppRouter.canPop());
  }

  @override
  Widget build(BuildContext context) {
    final bgColor = color ?? context.appBarBgColor;
    final toolbarHeight = MediaQuery.paddingOf(context).top;
    const spacer = AppGap.y12();

    Widget? center;

    if (title != null) {
      center = AppText(
        title,
        style: context.textStyle.h5(),
        textAlign: TextAlign.center,
      );
    }

    return Material(
      color: bgColor,
      child: Container(
        padding: (padding ?? context.insets.defaultHorizontalInsets).add(
          EdgeInsets.only(top: toolbarHeight),
        ),
        decoration: BoxDecoration(color: bgColor),
        child: Table(
          defaultVerticalAlignment: TableCellVerticalAlignment.middle,
          columnWidths: const {
            0: FlexColumnWidth(3),
            1: FlexColumnWidth(12),
            2: FlexColumnWidth(3),
          },
          children: [
            TableRow(children: [
              if (_shouldImplyLeading)
                const Align(
                  alignment: Alignment.centerLeft,
                  child: AppBackButton(),
                )
              else
                leading ?? const Offstage(),
              if (showLogo)
                const AppLogo(
                  height: AppFontSizes.px50,
                )
              else
                center ?? const Offstage(),
              if (trailing != null)
                Align(
                  alignment: Alignment.centerRight,
                  child: trailing,
                )
              else
                const Offstage(),
            ]),
            TableRow(children: spacer * 3)
          ],
        ),
      ),
    );
  }

  @override
  Size get preferredSize {
    return Size.fromHeight(toolbarHeight ?? kToolbarHeight);
  }
}

class DoSearchAppBar<T> extends AppStatefulWidget
    implements PreferredSizeWidget {
  final OnChanged<String?> onChanged;
  final OnChanged<SelectionData<T>>? onSelect;
  final OnChanged<String?>? onSubmit;
  final Duration debounceTime;
  final FocusNode? focusNode;
  final TextEditingController? controller;
  final ValueNotifier<List<SelectionData<T>>>? options;
  final ValueNotifier<SelectionData<T>?>? selection;
  final String? hintText;
  final bool enabled;

  const DoSearchAppBar({
    required this.onChanged,
    required this.debounceTime,
    this.onSelect,
    this.options,
    this.selection,
    this.onSubmit,
    this.controller,
    this.focusNode,
    this.hintText,
    this.enabled = true,
    super.key,
  });

  @override
  Size get preferredSize {
    return const Size.fromHeight(kToolbarHeight);
  }

  @override
  State<StatefulWidget> createState() => _DoSearchAppBarState<T>();
}

class _DoSearchAppBarState<T> extends State<DoSearchAppBar> {
  late final ValueNotifier<List<SelectionData<T>>> _options;
  late final TextEditingController _controller;

  @override
  void initState() {
    super.initState();
    _options = widget.options as ValueNotifier<List<SelectionData<T>>>? ??
        ValueNotifier([]);
    _controller = widget.controller ?? TextEditingController();
  }

  @override
  Widget build(BuildContext context) {
    final color = context.scaffoldBgColor;
    final toolbarHeight =
        MediaQuery.paddingOf(context).top + context.sp(AppFontSizes.px10);

    return Material(
      color: color,
      child: Container(
        padding: context.insets.defaultHorizontalInsets.add(
          EdgeInsets.only(top: toolbarHeight),
        ),
        decoration: BoxDecoration(color: color),
        child: ListListener(
          valueListenable: _options,
          builder: (options) {
            return AppAutocompleteField<T>(
              focusNode: widget.focusNode,
              controller: _controller,
              onChanged: widget.onChanged,
              onSelect: widget.onSelect,
              options: _options,
              selection: widget.selection as ValueNotifier<SelectionData<T>?>?,
              debounceTime: widget.debounceTime,
              hintText: widget.hintText.value,
              isEnabled: widget.enabled,
              textCapitalization: TextCapitalization.none,
              textInputAction: TextInputAction.search,
              onFieldSubmitted: widget.onSubmit,
              suffixIcon: GenericListener(
                valueListenable: _controller,
                builder: (value) {
                  if (!value.text.hasValue) return const Offstage();
                  return FittedBox(
                    fit: BoxFit.scaleDown,
                    child: Align(
                      alignment: Alignment.centerRight,
                      child: Padding(
                        padding: context.insets.only(left: 2),
                        child: AppCloseButton(
                          asHero: true,
                          alignment: Alignment.centerRight,
                          size: AppFontSizes.px12,
                          onTap: () {
                            _controller.clear();
                            widget.onChanged(_controller.textValue);
                          },
                        ),
                      ),
                    ),
                  );
                },
              ),
            );
          },
        ),
      ),
    );
  }
}
