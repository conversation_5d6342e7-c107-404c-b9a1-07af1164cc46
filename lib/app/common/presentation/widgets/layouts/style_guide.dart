import 'package:flutter/material.dart';
import 'package:day1/day1.dart';

class AppStyleGuide extends AppStatelessWidget {
  const AppStyleGuide({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Padding(
        padding: context.insets.defaultHorizontalInsets,
        child: CustomScrollView(
          slivers: [
            const SliverToBoxAdapter(
              child: DoAppBar.withLogo(),
            ),
            SliverList.list(children: [
              AppText(
                "Typography",
                style: context.textStyle.h1(),
              ),
              const AppGap.y16(),
              AppText("Display 1 (Bevellier)", style: context.textStyle.d1()),
              AppText("Display 2 (Bevellier)", style: context.textStyle.d2()),
              AppText("Display 3 (Bevellier)", style: context.textStyle.d3()),
              const AppGap.y16(),
              AppText("H1 (Archivo)", style: context.textStyle.h1()),
              AppText("H2 (Archivo)", style: context.textStyle.h2()),
              AppText("H3 (Archivo)", style: context.textStyle.h3()),
              AppText("H4 (Archivo)", style: context.textStyle.h4()),
              AppText("H5 (Archivo)", style: context.textStyle.h5()),
              AppText("H6 (Archivo)", style: context.textStyle.h6()),
              const AppGap.y24(),
              for (final weight in FontWeight.values.reversed)
                AppText(
                  "Body 1 (Archivo | $weight)",
                  style: context.textStyle.b1(weight: weight),
                ),
              const AppGap.y12(),
              for (final weight in FontWeight.values.reversed)
                AppText(
                  "Body 2 (Archivo | $weight)",
                  style: context.textStyle.b2(weight: weight),
                ),
              const AppGap.y12(),
              for (final weight in FontWeight.values.reversed)
                AppText(
                  "Body 3 (Archivo | $weight)",
                  style: context.textStyle.b3(weight: weight),
                ),
              const AppGap.y12(),
              for (final weight in FontWeight.values.reversed)
                AppText(
                  "Body 4 (Archivo | $weight)",
                  style: context.textStyle.b4(weight: weight),
                ),
              const AppGap.y12(),
              for (final weight in FontWeight.values.reversed)
                AppText(
                  "Body 5 (Archivo | $weight)",
                  style: context.textStyle.b5(weight: weight),
                ),
              const AppGap.y32(),
              AppText(
                "Buttons",
                style: context.textStyle.h1(),
              ),
              const AppGap.y16(),
              Wrap(
                alignment: WrapAlignment.start,
                runAlignment: WrapAlignment.start,
                spacing: 5,
                runSpacing: 5,
                children: [
                  AppButton(
                    text: "Button Lg",
                    onPressed: () {},
                    icon: AppIcons.arrowLeft,
                    trailingIcon: AppIcons.arrowRight,
                  ),
                  AppButton(
                    text: "Button Md",
                    onPressed: () {},
                    size: ButtonSize.medium,
                    icon: AppIcons.arrowLeft,
                    trailingIcon: AppIcons.arrowRight,
                  ),
                  AppButton(
                    text: "Button Sm",
                    onPressed: () {},
                    size: ButtonSize.small,
                    icon: AppIcons.arrowLeft,
                    trailingIcon: AppIcons.arrowRight,
                  ),
                ],
              ),
              const AppGap.y16(),
              Wrap(
                alignment: WrapAlignment.start,
                runAlignment: WrapAlignment.start,
                spacing: 5,
                runSpacing: 5,
                children: [
                  AppButton(
                    text: "Button Lg",
                    variant: RaisedButtonVariant.b,
                    onPressed: () {},
                    icon: AppIcons.arrowLeft,
                    trailingIcon: AppIcons.arrowRight,
                  ),
                  AppButton(
                    text: "Button Md",
                    variant: RaisedButtonVariant.b,
                    onPressed: () {},
                    size: ButtonSize.medium,
                    icon: AppIcons.arrowLeft,
                    trailingIcon: AppIcons.arrowRight,
                  ),
                  AppButton(
                    text: "Button Sm",
                    variant: RaisedButtonVariant.b,
                    onPressed: () {},
                    size: ButtonSize.small,
                    icon: AppIcons.arrowLeft,
                    trailingIcon: AppIcons.arrowRight,
                  ),
                ],
              ),
              const AppGap.y16(),
              Wrap(
                alignment: WrapAlignment.start,
                runAlignment: WrapAlignment.start,
                spacing: 5,
                runSpacing: 5,
                children: [
                  AppButton(
                    text: "Button Lg",
                    variant: RaisedButtonVariant.c,
                    onPressed: () {},
                    icon: AppIcons.arrowLeft,
                    trailingIcon: AppIcons.arrowRight,
                  ),
                  AppButton(
                    text: "Button Md",
                    variant: RaisedButtonVariant.c,
                    onPressed: () {},
                    size: ButtonSize.medium,
                    icon: AppIcons.arrowLeft,
                    trailingIcon: AppIcons.arrowRight,
                  ),
                  AppButton(
                    text: "Button Sm",
                    variant: RaisedButtonVariant.c,
                    onPressed: () {},
                    size: ButtonSize.small,
                    icon: AppIcons.arrowLeft,
                    trailingIcon: AppIcons.arrowRight,
                  ),
                  AppButton(
                    text: "Button Sm",
                    isDisabled: true,
                    variant: RaisedButtonVariant.c,
                    onPressed: () {},
                    icon: AppIcons.arrowLeft,
                    trailingIcon: AppIcons.arrowRight,
                  ),
                ],
              ),
              const AppGap.y16(),
              AppOutlineButton(
                text: "Button Lg",
                onPressed: () {},
                icon: const AppIcon(AppIcons.arrowLeft),
                trailingIcon: const AppIcon(AppIcons.arrowRight),
              ),
              const AppGap.y8(),
              AppOutlineButton(
                text: "Button Md",
                onPressed: () {},
                size: ButtonSize.medium,
                icon: const AppIcon(AppIcons.arrowLeft),
                trailingIcon: const AppIcon(AppIcons.arrowRight),
              ),
              const AppGap.y8(),
              AppOutlineButton(
                text: "Button Sm",
                onPressed: () {},
                size: ButtonSize.small,
                icon: const AppIcon(AppIcons.arrowLeft),
                trailingIcon: const AppIcon(AppIcons.arrowRight),
              ),
              const AppGap.y8(),
              AppOutlineButton(
                text: "Button Sm",
                isDisabled: true,
                onPressed: () {},
                size: ButtonSize.small,
                icon: const AppIcon(AppIcons.arrowLeft),
                trailingIcon: const AppIcon(AppIcons.arrowRight),
              ),
              const AppGap.y16(),
              AppOutlineButton(
                variant: OutlineBtnVariant.neutral,
                text: "Button Lg",
                onPressed: () {},
                icon: const AppIcon(AppIcons.arrowLeft),
                trailingIcon: const AppIcon(AppIcons.arrowRight),
              ),
              const AppGap.y8(),
              AppOutlineButton(
                variant: OutlineBtnVariant.neutral,
                text: "Button Md",
                onPressed: () {},
                size: ButtonSize.medium,
                icon: const AppIcon(AppIcons.arrowLeft),
                trailingIcon: const AppIcon(AppIcons.arrowRight),
              ),
              const AppGap.y8(),
              AppOutlineButton(
                variant: OutlineBtnVariant.neutral,
                text: "Button Sm",
                onPressed: () {},
                size: ButtonSize.small,
                icon: const AppIcon(AppIcons.arrowLeft),
                trailingIcon: const AppIcon(AppIcons.arrowRight),
              ),
              const AppGap.y8(),
              AppOutlineButton(
                variant: OutlineBtnVariant.neutral,
                isDisabled: true,
                text: "Button Sm",
                onPressed: () {},
                size: ButtonSize.small,
                icon: const AppIcon(AppIcons.arrowLeft),
                trailingIcon: const AppIcon(AppIcons.arrowRight),
              ),
              const AppGap.y16(),
              AppTextButton(
                text: "Button Lg",
                onPressed: () {},
                icon: const AppIcon(AppIcons.arrowLeft),
                trailingIcon: const AppIcon(AppIcons.arrowRight),
              ),
              AppTextButton(
                text: "Button Md",
                onPressed: () {},
                icon: const AppIcon(AppIcons.arrowLeft),
                size: ButtonSize.medium,
                trailingIcon: const AppIcon(AppIcons.arrowRight),
              ),
              AppTextButton(
                text: "Button Lg",
                onPressed: () {},
                icon: const AppIcon(AppIcons.arrowLeft),
                size: ButtonSize.small,
                trailingIcon: const AppIcon(AppIcons.arrowRight),
              ),
              AppTextButton(
                text: "Button Sm",
                onPressed: () {},
                isDisabled: true,
                size: ButtonSize.small,
                icon: const AppIcon(AppIcons.arrowLeft),
                trailingIcon: const AppIcon(AppIcons.arrowRight),
              ),
              const AppGap.y16(),
              AppIconButton(
                onPressed: () {},
                icon: AppIcons.userStar,
                alignment: Alignment.center,
                trailingIcon: const AppIcon(AppIcons.arrowRight),
              ),
              const AppGap.y8(),
              AppIconButton(
                onPressed: () {},
                size: ButtonSize.medium,
                icon: AppIcons.userStar,
                alignment: Alignment.center,
                trailingIcon: const AppIcon(AppIcons.arrowRight),
              ),
              const AppGap.y8(),
              AppIconButton(
                onPressed: () {},
                icon: AppIcons.userStar,
                size: ButtonSize.small,
                alignment: Alignment.center,
                trailingIcon: const AppIcon(AppIcons.arrowRight),
              ),
              const AppGap.y8(),
              AppIconButton(
                onPressed: () {},
                icon: AppIcons.userStar,
                isDisabled: true,
                alignment: Alignment.center,
                trailingIcon: const AppIcon(AppIcons.arrowRight),
              ),
              const AppGap.y32(),
              AppText(
                "Inputs",
                style: context.textStyle.h1(),
              ),
              const AppGap.y16(),
              const FormLabel(label: "Input Sample"),
              const AppTextField(
                hintText: "Enter something here",
              ),
              const AppGap.y8(),
              const FormLabel(label: "Input Sample 2"),
              AppTextField(
                controller: TextEditingController(text: "Input"),
                helperText: "This is valid",
              ),
              const AppGap.y8(),
              const FormLabel(label: "Invalid Input Sample"),
              AppEmailField(
                autovalidateMode: AutovalidateMode.always,
                controller: TextEditingController(text: "invalidemail"),
              ),
              const AppGap.y8(),
              const FormLabel(label: "Valid Input Sample"),
              AppEmailField(
                autovalidateMode: AutovalidateMode.always,
                controller: TextEditingController(text: "<EMAIL>"),
              ),
              const AppGap.y8(),
              const FormLabel(label: "Telephone Input Sample"),
              AppPhoneField(
                countryCode: ValueNotifier(null),
                controller: TextEditingController(),
              ),
              const AppGap.y8(),
              const FormLabel(label: "Multiline Input Sample"),
              const AppTextField.multiline(minLines: 3),
              const AppGap.y32(),
              AppText(
                "Indicators",
                style: context.textStyle.h1(),
              ),
              const AppGap.y16(),
              Wrap(
                spacing: 10,
                children: [
                  AppRadioRow(
                    value: 0,
                    groupValue: 2,
                    onChanged: (_) {},
                    trailing: const AppText("Unselected Radio"),
                  ),
                  AppRadioRow(
                    value: 1,
                    groupValue: 1,
                    onChanged: (_) {},
                    trailing: const AppText("Selected Radio"),
                  ),
                ],
              ),
              const AppGap.y16(),
              Wrap(
                spacing: 10,
                children: [
                  AppCheckBoxRow(
                    value: 0,
                    isActive: false,
                    onChanged: (_) {},
                    trailing: const AppText("Unselected Checkbox"),
                  ),
                  AppCheckBoxRow(
                    value: 1,
                    isActive: true,
                    onChanged: (_) {},
                    trailing: const AppText("Selected Checkbox"),
                  ),
                ],
              ),
              const AppGap.y16(),
              Wrap(
                spacing: 10,
                children: [
                  AppSwitchTile(
                    value: false,
                    onChanged: (_) {},
                    trailing: const AppText("Unselected Switch"),
                  ),
                  AppSwitchTile(
                    value: true,
                    onChanged: (_) {},
                    trailing: const AppText("Selected Switch"),
                  ),
                ],
              ),
              const AppGap.y16(),
              Wrap(
                spacing: 10,
                runSpacing: 10,
                runAlignment: WrapAlignment.center,
                crossAxisAlignment: WrapCrossAlignment.center,
                children: [
                  for (final i in [0, 2, 4]) AppDots(i, len: 5),
                ],
              ),
              const AppGap.y32(),
            ]),
          ],
        ),
      ),
    );
  }
}
