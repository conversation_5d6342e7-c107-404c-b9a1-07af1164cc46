import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class AppForm extends AppStatefulWidget {
  final Widget child;
  final bool? canPop;
  final VoidCallback? onChanged;
  final AutovalidateMode autovalidateMode;
  final GlobalKey<FormState> formKey;

  const AppForm({
    super.key,
    this.autovalidateMode = AutovalidateMode.disabled,
    required this.formKey,
    this.onChanged,
    this.canPop,
    required this.child,
  });

  @override
  State<AppForm> createState() => _AppFormState();
}

class _AppFormState extends State<AppForm> {
  @override
  Widget build(BuildContext context) {
    return Form(
      key: widget.formKey,
      canPop: widget.canPop,
      onChanged: widget.onChanged,
      autovalidateMode: widget.autovalidateMode,
      child: AutofillGroup(
        key: Key("AugofillGroup-${widget.formKey}"),
        child: widget.child,
      ),
    );
  }
}

class AppStepForm extends AppStatefulWidget {
  final Widget child;
  final int step;
  final int steps;
  final bool? canPop;
  final VoidCallback? onChanged;
  final AutovalidateMode autovalidateMode;
  final GlobalKey<FormState> formKey;

  const AppStepForm({
    super.key,
    this.autovalidateMode = AutovalidateMode.disabled,
    required this.formKey,
    required this.step,
    required this.steps,
    this.onChanged,
    this.canPop,
    required this.child,
  });

  @override
  State createState() => _AppStepFormState();
}

class _AppStepFormState extends State<AppStepForm> {
  @override
  Widget build(BuildContext context) {
    const hPad = AppFontSizes.px40;
    const topPad = AppFontSizes.px32;
    const bottomPad = AppFontSizes.px24;

    return Form(
      key: widget.formKey,
      canPop: widget.canPop,
      onChanged: widget.onChanged,
      autovalidateMode: widget.autovalidateMode,
      child: AutofillGroup(
        key: Key("AugofillGroup-${widget.formKey}"),
        child: AppCard(
          padding: EdgeInsets.zero,
          color: context.raisedBtnCBgColor,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Material(
                type: MaterialType.transparency,
                child: Hero(
                  tag: widget.steps,
                  child: Padding(
                    padding: context.insets.fromLTRBSp(
                      hPad,
                      topPad,
                      hPad,
                      bottomPad,
                    ),
                    child: AppText(
                      LocaleKeys.stepOfSteps.utr(
                        {"c": "${widget.steps}", "s": "${widget.step}"},
                      ),
                      style: context.textStyle.b4(
                        color: context.raisedBtnTextColor,
                      ),
                    ),
                  ),
                ),
              ),
              AppCard(child: widget.child)
            ],
          ),
        ),
      ),
    );
  }
}
