import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class AppPinForm extends AppStatefulWidget {
  final GlobalKey<FormState> formKey;
  final TextEditingController otpCtrl;
  final String title;
  final String? rider;
  final bool isLoading;
  final bool deferBioLoginToParent;
  final String? btnText;
  final OnChanged<String?> onSubmit;
  final OnChanged<bool> onBioAuth;
  final ValueNotifier<bool>? formStateEmitter;

  const AppPinForm(
    this.formKey, {
    super.key,
    required this.otpCtrl,
    required this.title,
    this.deferBioLoginToParent = true,
    this.rider,
    this.btnText,
    this.isLoading = false,
    required this.onSubmit,
    required this.onBioAuth,
    this.formStateEmitter,
  });

  @override
  State<AppPinForm> createState() => _AppPinFormState();
}

class _AppPinFormState extends State<AppPinForm> {
  Future<bool> _autheticate() {
    return locator<BiometricAuthService>().authenticate(title: widget.title);
  }

  @override
  Widget build(BuildContext context) {
    const p = AppFontSizes.px32;

    return AppForm(
      formKey: widget.formKey,
      child: ListView(
        padding: context.insets.defaultAllInsets,
        children: [
          AppCard(
            padding: context.insets.fromLTRBSp(p, p, p, p / 2),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                AppFormHeader(title: widget.title, subTitle: widget.rider),
                IgnorePointer(
                  child: AppPinInput(
                    length: 4,
                    obscureText: false,
                    controller: widget.otpCtrl,
                    fieldSize: const Size(70, 88),
                    onFieldSubmitted: widget.onSubmit,
                    isEnabled: !widget.isLoading,
                  ),
                )
              ],
            ),
          ),
          const AppGap.y12(),
          AppCard(
            child: AppKeyPad(
              controller: widget.otpCtrl,
              limit: 4,
              onBioAuth: () async {
                if (!widget.deferBioLoginToParent) {
                  return widget.onBioAuth(await _autheticate());
                }
                widget.onBioAuth(true);
              },
            ),
          ),
          if (widget.formStateEmitter != null) ...[
            const AppGap.y12(),
            BoolListener(
              valueListenable: widget.formStateEmitter!,
              builder: (isValid) {
                return Padding(
                  padding: context.insets.symmetricSp(horizontal: p),
                  child: AppButton(
                    text: widget.btnText ?? LocaleKeys.done.tr(),
                    onPressed: () {
                      widget.onSubmit(widget.otpCtrl.textValue);
                    },
                    isDisabled: !isValid || widget.isLoading,
                    isLoading: widget.isLoading,
                  ),
                );
              },
            ),
          ]
        ],
      ),
    );
  }
}
