import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class AppGenericShimmer extends AppStatelessWidget {
  const AppGenericShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    final line = Container(
      height: context.sp(AppFontSizes.px6),
      color: context.disabledInputFillColor,
      margin: context.insets.only(bottom: 1),
    );

    final paragraph = Column(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        ...line * 4,
        Padding(
          padding: context.insets.only(right: 45),
          child: line,
        ),
        const AppGap.y4(),
      ],
    );

    return SingleChildScrollView(
      physics: const NeverScrollableScrollPhysics(),
      padding: context.insets.defaultHorizontalInsets,
      child: AppShimmer(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: paragraph * 10,
        ),
      ),
    );
  }
}
