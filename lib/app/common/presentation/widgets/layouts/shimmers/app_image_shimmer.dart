import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class AppImageShimmer extends AppStatelessWidget {
  final double? width;
  final double? height;
  final BoxFit? fit;
  final Alignment alignment;

  const AppImageShimmer({
    super.key,
    required this.alignment,
    this.fit,
    this.width,
    this.height,
  });

  @override
  Widget build(BuildContext context) {
    return AppShimmer(
      child: FittedBox(
        fit: fit ?? BoxFit.contain,
        child: Container(
          alignment: alignment,
          constraints: BoxConstraints.tightFor(
            height: height ?? width ?? 0,
            width: width ?? height ?? 0,
          ),
          decoration: BoxDecoration(
            color: context.disabledBtnColor,
            borderRadius: context.mdBorderRadius,
          ),
        ),
      ),
    );
  }
}
