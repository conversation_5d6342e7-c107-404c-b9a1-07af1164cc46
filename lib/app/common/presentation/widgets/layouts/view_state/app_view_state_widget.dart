import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class AppViewStateWidget extends AppStatelessWidget {
  final Widget? icon;
  final Widget? button;
  final String? title;
  final String? description;
  final MainAxisAlignment alignment;

  const AppViewStateWidget({
    super.key,
    this.title,
    this.icon,
    this.description,
    this.button,
    this.alignment = MainAxisAlignment.center,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: alignment,
      crossAxisAlignment: CrossAxisAlignment.stretch,
      mainAxisSize: MainAxisSize.min,
      children: [
        if (icon != null) ...[
          icon!,
          const AppGap.y32(),
        ],
        AppRichText(
          title,
          textStyle: context.textStyle.h5(),
          textAlign: TextAlign.start,
        ),
        if (description != null) ...[
          const AppGap.y8(),
          AppRichText(
            description,
            textAlign: TextAlign.start,
            textStyle: context.textStyle.labelText(),
          ),
        ],
        if (button != null) ...[
          const AppGap.y48(),
          button!,
        ],
      ],
    );
  }
}
