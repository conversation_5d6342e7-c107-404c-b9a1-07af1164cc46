import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class AppErrorStateWidget extends AppStatelessWidget {
  final String? title;
  final String? description;
  final String? buttonText;
  final OnPressed? onRetry;

  const AppErrorStateWidget({
    super.key,
    this.title,
    this.buttonText,
    this.description,
    this.onRetry,
  });

  @override
  Widget build(BuildContext context) {
    Widget? button;

    if (onRetry != null) {
      button = AppTextButton(
        text: buttonText ?? LocaleKeys.retry.tr(),
        onPressed: onRetry!,
      );
    }

    return AppViewStateWidget(
      icon: AppIcon(
        AppIcons.cloudXmark,
        size: AppFontSizes.px80,
        color: context.errorColor,
      ),
      title: title ?? LocaleKeys.anErrorOccured.tr(),
      description: description,
      button: button,
    );
  }
}
