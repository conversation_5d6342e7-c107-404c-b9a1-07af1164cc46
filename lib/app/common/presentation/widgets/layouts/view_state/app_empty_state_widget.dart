import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class AppEmptyStateWidget extends AppStatelessWidget {
  final String? title;
  final String? description;
  final String? buttonText;
  final Widget? buttonIcon;
  final Widget? icon;
  final OnPressed? onRetry;
  final EdgeInsetsGeometry? padding;

  const AppEmptyStateWidget({
    super.key,
    this.title,
    this.buttonText,
    this.description,
    this.onRetry,
    this.buttonIcon,
    this.icon,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    Widget? button;
    const iconHeight = AppFontSizes.px80;

    if (onRetry != null) {
      button = AppOutlineButton(
        text: buttonText ?? LocaleKeys.retry.tr(),
        onPressed: onRetry!,
      );
    }

    return Padding(
      padding: padding ?? context.insets.onlySp(top: spacing.spacing64),
      child: AppViewStateWidget(
        icon: icon ??
            Container(
              decoration: BoxDecoration(
                border: Border.all(
                  color: context.errorColor,
                  width: 4,
                ),
                shape: BoxShape.circle,
              ),
              child: AppIcon(
                AppIcons.questionMark,
                size: iconHeight,
                color: context.errorColor,
              ),
            ),
        title: title ?? LocaleKeys.defaultEmptyStateText.tr(),
        description: description,
        button: button,
        alignment: MainAxisAlignment.start,
      ),
    );
  }
}
