import 'package:flutter/material.dart';
import 'package:day1/day1.dart';

class NotFoundScreen extends StatelessWidget {
  final scaffoldKey = GlobalKey<ScaffoldState>();
  final String destination;
  final String? buttonText;
  final OnPressed? onPressed;

  NotFoundScreen({
    super.key,
    this.buttonText,
    this.onPressed,
    required this.destination,
  });

  bool get canPop => AppRouter.canPop();

  handleBtnPress() {
    if (onPressed != null) onPressed!();
    if (canPop) {
      AppRouter.popView();
      return;
    }
    const destination = AuthRoutes.login;
    AppRouter.pushAndRemoveUntil(destination);
  }

  String get btnText {
    if (buttonText != null) return buttonText!;
    return (canPop ? LocaleKeys.goBack : LocaleKeys.login).tr();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const DoAppBar.withLogo(),
      body: Padding(
        padding: context.insets.symmetric(horizontal: 5),
        child: Column(
          mainAxisSize: MainAxisSize.max,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Padding(
              padding: context.insets.symmetric(horizontal: 5),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  AppIcon(
                    Icons.not_interested_rounded,
                    size: AppFontSizes.px120,
                    color: context.errorColor,
                  ),
                  const AppGap.y32(),
                  AppText(
                    LocaleKeys.oops.tr(),
                    style: context.textStyle
                        .h1()
                        .copyWith(color: context.errorColor),
                    textAlign: TextAlign.center,
                  ),
                  const AppGap.y8(),
                  AppText(
                    LocaleKeys.pageNotFound.tr(),
                    style: context.textStyle.h3(),
                    textAlign: TextAlign.center,
                  ),
                  const AppGap.y2(),
                  AppRichText(
                    LocaleKeys.weFoundNoPage.tr({'route': destination}),
                    textAlign: TextAlign.center,
                  ),
                  const AppGap.y16(),
                ],
              ),
            ),
            AppOutlineButton(
              text: btnText,
              onPressed: handleBtnPress,
            )
          ],
        ),
      ),
    );
  }
}
