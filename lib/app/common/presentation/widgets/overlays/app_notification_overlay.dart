import 'dart:async';

import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

enum NotificationType {
  error,
  warning,
  info,
  success,
}

class AppNotficationOverlay extends AppOverlay {
  OverlayEntry? _entry;
  bool _inserted = false;

  AppNotficationOverlay.of(super.context) : super(closableOnNavigation: false);

  show(
    String message, {
    String? title,
    NotificationType type = NotificationType.success,
    int duration = 6000,
  }) {
    Duration timeout = Duration(milliseconds: duration);
    runThrowableTask(() {
      if (_entry != null || _inserted) return;
      super.closeExistingOverlays();

      _entry = OverlayEntry(
        opaque: false,
        builder: (context) {
          return AppNotificationOverlayWidget(
            message,
            close: close,
            title: title,
            type: type,
          );
        },
      );
      _inserted = true;
      super.present(entry: _entry!, instance: this);
      Timer(timeout, close);
    }, onError: () {
      Timer(timeout, close);
    });
  }

  @override
  close() {
    tryRunThrowableTask(() {
      if (_entry != null && _inserted) {
        _entry?.remove();
        _entry = null;
        _inserted = false;
      }
    });
  }
}

class AppNotificationOverlayWidget extends AppStatelessWidget {
  final String message;
  final String? title;
  final OnPressed? close;
  final NotificationType type;
  final EdgeInsetsGeometry? margin;

  const AppNotificationOverlayWidget(
    this.message, {
    this.title,
    this.close,
    required this.type,
    super.key,
    this.margin,
  });

  @override
  Widget build(BuildContext context) {
    Color bgColor = switch (type) {
      NotificationType.error => context.errorNotificationBackgroundColor,
      NotificationType.warning => context.warningNotificationBackgroundColor,
      NotificationType.info => context.infoNotificationBackgroundColor,
      NotificationType.success => context.notificationBackgroundColor,
    };
    Color textColor = switch (type) {
      NotificationType.success => context.notificationTextColor,
      NotificationType.error => context.errorNotificationTextColor,
      NotificationType.warning => context.warningNotificationTextColor,
      NotificationType.info => context.infoNotificationTextColor,
    };
    Color borderColor = switch (type) {
      NotificationType.success => context.notificationBorderColor,
      NotificationType.error => context.errorNotificationBorderColor,
      NotificationType.warning => context.warningNotificationBorderColor,
      NotificationType.info => context.infoNotificationBorderColor,
    };
    IconData icon = switch (type) {
      NotificationType.success => AppIcons.checkCircle,
      NotificationType.error => AppIcons.xmarkCircle,
      NotificationType.warning => AppIcons.warningTriangle,
      NotificationType.info => AppIcons.infoCircle,
    };

    if (title.hasValue) borderColor = bgColor;

    TextStyle textStyle = context.textStyle.b2(
      color: textColor.withOpacity(title.hasValue ? .7 : 1),
      weight: FontWeight.w500,
    );

    final crossAlignment =
        title.hasValue ? CrossAxisAlignment.start : CrossAxisAlignment.center;

    return Material(
      type: MaterialType.transparency,
      child: SafeArea(
        bottom: false,
        child: Align(
          alignment: Alignment.topCenter,
          child: Container(
            padding: context.insets.symmetricSp(
              horizontal: AppFontSizes.px16,
              vertical: title.hasValue ? AppFontSizes.px24 : AppFontSizes.px12,
            ),
            margin: (margin ?? context.insets.defaultHorizontalInsets).add(
              const EdgeInsets.only(top: 4),
            ),
            decoration: ShapeDecoration(
              shape: ContinuousRectangleBorder(
                borderRadius: context.xlBorderRadius,
                side: BorderSide(color: borderColor, width: 2),
              ),
              color: bgColor,
            ),
            child: Row(
              crossAxisAlignment: crossAlignment,
              children: [
                if (title.hasValue)
                  const AppLogo(iconOnly: true)
                else
                  AppIcon(icon, color: textColor),
                const AppGap.h20(),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      if (title.hasValue) ...[
                        AppText(
                          title,
                          style: context.textStyle.h5(
                            color: textColor,
                          ),
                        ),
                      ],
                      AppRichText(message, textStyle: textStyle),
                    ],
                  ),
                ),
                if (close != null) ...[
                  const AppGap.h12(),
                  InkWell(
                    onTap: close,
                    child: const AppIcon(
                      AppIcons.xmark,
                      size: AppFontSizes.px24,
                    ),
                  )
                ]
              ],
            ),
          ),
        ),
      ),
    );
  }
}
