import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

abstract class AppOverlay with AppTaskMixin {
  final BuildContext context;
  final bool closableOnNavigation;

  AppOverlay(this.context, {this.closableOnNavigation = true});

  static final List<AppOverlay> _curentOverlays = [];

  static closeCurrentOverlays() {
    try {
      for (int index = 0; index < _curentOverlays.length; index++) {
        final overlay = _curentOverlays[index];
        if (!overlay.closableOnNavigation) continue;
        overlay.close();
        _curentOverlays.removeAt(index);
      }
    } catch (_) {}
  }

  closeExistingOverlays() => closeCurrentOverlays();

  void close();

  void present({
    required OverlayEntry entry,
    required AppOverlay instance,
  }) {
    tryRunThrowableTask(() {
      Navigator.of(context).overlay?.insert(entry);
      _curentOverlays.tryAdd(instance);
    });
  }
}
