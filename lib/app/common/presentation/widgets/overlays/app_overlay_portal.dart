import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class AppOverlayPortal extends AppStatefulWidget {
  final OverlayPortalController controller;
  final Widget overlayChild;
  final Alignment childAnchor;
  final Alignment overlayAnchor;
  final Offset overlayOffSet;
  final Widget child;

  const AppOverlayPortal({
    required this.controller,
    required this.overlayChild,
    required this.child,
    this.overlayOffSet = Offset.zero,
    this.childAnchor = Alignment.bottomCenter,
    this.overlayAnchor = Alignment.topLeft,
    super.key,
  });

  @override
  State<StatefulWidget> createState() => _AppOverlayPortalState();
}

class _AppOverlayPortalState extends State<AppOverlayPortal> {
  final LayerLink _link = LayerLink();

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return CompositedTransformTarget(
      link: _link,
      child: OverlayPortal(
        controller: widget.controller,
        overlayChildBuilder: (context) {
          return CompositedTransformFollower(
            link: _link,
            targetAnchor: widget.childAnchor,
            followerAnchor: widget.overlayAnchor,
            offset: widget.overlayOffSet,
            child: widget.overlayChild,
          );
        },
        child: widget.child,
      ),
    );
  }
}
