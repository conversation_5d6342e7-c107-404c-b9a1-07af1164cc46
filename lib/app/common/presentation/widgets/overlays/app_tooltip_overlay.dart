import 'dart:async';

import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class AppTooltipOverlay extends AppOverlay {
  OverlayEntry? _entry;
  bool _inserted = false;

  AppTooltipOverlay.of(super.context);

  show(
    String message, {
    required BuildContext anchorContext,
    int duration = 5000,
  }) {
    Duration timeout = Duration(milliseconds: duration);
    runThrowableTask(() {
      if (_entry != null || _inserted) return;
      super.closeExistingOverlays();

      final anchorSize = anchorContext.size ?? const Size(0, 0);
      final anchorOffset = anchorContext.offset;
      final tipRadius = context.sp(AppFontSizes.px1);
      final topOffset = anchorOffset.dy + anchorSize.height + tipRadius;
      final tipSize = context.sp(AppFontSizes.px28);

      _entry = OverlayEntry(
        opaque: false,
        builder: (context) {
          return AppTooltipOverlayWidget(
            message,
            topOffset: topOffset,
            tipSize: tipSize,
          );
        },
      );
      _inserted = true;
      super.present(entry: _entry!, instance: this);
      Timer(timeout, close);
    }, onError: () {
      Timer(timeout, close);
    });
  }

  @override
  close() {
    tryRunThrowableTask(() {
      if (_entry != null && _inserted) {
        _entry?.remove();
        _entry = null;
        _inserted = false;
      }
    });
  }
}

class AppTooltipOverlayWidget extends AppStatelessWidget {
  final String message;
  final double topOffset;
  final double tipSize;

  const AppTooltipOverlayWidget(
    this.message, {
    required this.topOffset,
    required this.tipSize,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    Color textColor = context.notificationTextColor;
    Color bgColor = context.notificationBackgroundColor;
    TextStyle textStyle = context.textStyle.b1().copyWith(color: textColor);
    return Stack(
      children: [
        Positioned(
          top: topOffset,
          left: 0,
          right: 0,
          child: Align(
            alignment: Alignment.topCenter,
            child: Card(
              shape: BeveledRectangleBorder(
                borderRadius: context.xxxlBorderRadius,
              ),
              color: bgColor,
              margin: context.insets.zero,
              child: SizedBox(height: tipSize, width: tipSize),
            ),
          ),
        ),
        Positioned(
          left: 0,
          right: 0,
          top: topOffset,
          child: Material(
            type: MaterialType.transparency,
            child: Container(
              padding: context.insets.symmetric(
                horizontal: 5,
                vertical: 2,
              ),
              margin: context.insets.fromLTRB(5, 1, 5, 0),
              decoration: BoxDecoration(
                borderRadius: context.lgBorderRadius,
                color: bgColor,
              ),
              child: AppText(
                message,
                style: textStyle,
              ),
            ),
          ),
        ),
      ],
    );
  }
}
