import 'package:day1/day1.dart';
import 'package:flutter/cupertino.dart';

class AppKeyboardOverlay extends AppOverlay {
  OverlayEntry? _entry;
  bool _inserted = false;

  AppKeyboardOverlay.of(super.context);

  show() {
    tryRunThrowableTask(
      () {
        if (_entry != null || _inserted) return;
        super.closeExistingOverlays();
        final contextInset = context.sizer.bottomInset;
        final computedInset =
            (contextInset > 0 ? contextInset : context.fractionalLongSide(40));
        _entry = OverlayEntry(
          opaque: false,
          builder: (context) {
            if (!context.isIos) return const Offstage();
            return AppKeyboardOverlayWidget(computedInset, close: close);
          },
        );
        _inserted = true;
        super.present(entry: _entry!, instance: this);
      },
    );
  }

  @override
  close() {
    tryRunThrowableTask(() {
      if (_entry != null && _inserted) {
        _entry?.remove();
        _entry = null;
        _inserted = false;
      }
    });
  }
}

class AppKeyboardOverlayWidget extends AppStatelessWidget {
  final double computedInset;
  final OnPressed close;

  const AppKeyboardOverlayWidget(
    this.computedInset, {
    required this.close,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Positioned(
          right: context.fractionalWidth(5),
          bottom: computedInset,
          child: Align(
            alignment: Alignment.centerRight,
            child: SizedBox(
              width: context.fractionalWidth(20),
              child: AppButton(
                text: "Done",
                size: ButtonSize.small,
                onPressed: close,
              ),
            ),
          ),
        )
      ],
    );
  }
}
