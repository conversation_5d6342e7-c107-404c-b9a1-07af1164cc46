import 'package:day1/day1.dart';
import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';

class AppShimmer extends AppStatelessWidget {
  final Color? baseColor;
  final Color? highlightColor;
  final Widget child;

  const AppShimmer({
    this.baseColor,
    this.highlightColor,
    required this.child,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final color = context.dividerColor;
    return Shimmer.fromColors(
      baseColor: baseColor ?? color,
      highlightColor: highlightColor ?? color.withOpacity(.5),
      child: child,
    );
  }
}
