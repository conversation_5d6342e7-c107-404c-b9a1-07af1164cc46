import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class AppAnimatedSwitcher extends AppStatefulWidget {
  final Widget child;
  final int duration;

  const AppAnimatedSwitcher({
    required this.child,
    this.duration = 300,
    super.key,
  });

  @override
  State<StatefulWidget> createState() => _AppAnimatedSwitcherState();
}

class _AppAnimatedSwitcherState extends State<AppAnimatedSwitcher> {
  @override
  Widget build(BuildContext context) {
    return AnimatedSwitcher(
      transitionBuilder: (Widget child, Animation<double> animation) {
        return ScaleTransition(scale: animation, child: child);
      },
      duration: Duration(milliseconds: widget.duration),
      child: widget.child,
    );
  }
}

class AppAnimatedSlider extends AppStatefulWidget {
  final Widget child;
  final int duration;
  final Axis axis;

  const AppAnimatedSlider({
    required this.child,
    this.duration = 300,
    this.axis = Axis.vertical,
    super.key,
  });

  @override
  State<StatefulWidget> createState() => _AppAnimatedSliderState();
}

class _AppAnimatedSliderState extends State<AppAnimatedSlider> {
  @override
  Widget build(BuildContext context) {
    return AnimatedSwitcher(
      transitionBuilder: (Widget child, Animation<double> animation) {
        return SizeTransition(
          sizeFactor: animation,
          axis: widget.axis,
          child: child,
        );
      },
      duration: Duration(milliseconds: widget.duration),
      reverseDuration: Duration(milliseconds: widget.duration),
      child: widget.child,
    );
  }
}
