import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:day1/day1.dart';

class DoAppConfig {
  Size? windowSize;
  bool? forceMockState;

  // Configuration properties
  late String langCode;
  late String appId;
  late String stripeKey;
  late String appName;
  late String baseUrl;
  late String placesApiKey;
  late bool _isMock;
  late String termsOfUseUrl;
  late String aboutUsUrl;
  late String privacyUrl;
  late String webHosts;
  late String dialCode;

  DoAppConfig({this.forceMockState});

  /// Load configuration from environment variables only
  static Future<DoAppConfig> fromEnvironment() async {
    // Check if a specific env file was specified
    final envFile = const String.fromEnvironment('ENV_FILE', defaultValue: '.env');

    // Load .env file if it exists
    try {
      await dotenv.load(fileName: envFile);
      print('✅ Loaded environment file: $envFile');
    } catch (e) {
      print('⚠️  No $envFile file found, using system environment variables only');
    }

    final config = DoAppConfig();
    config._loadFromEnvironment();
    return config;
  }

  /// Legacy method for backward compatibility - now loads from environment only
  static Future<DoAppConfig> fromConfigFile(String configPath) async {
    print('⚠️  Loading from environment variables only. JSON config file is ignored for security.');
    return fromEnvironment();
  }

  void _loadFromEnvironment() {
    try {
      String getEnvVar(String key, {String defaultValue = ''}) {
        return dotenv.env[key] ??
               Platform.environment[key] ??
               defaultValue;
      }

      bool getEnvBool(String key, {bool defaultValue = false}) {
        final value = getEnvVar(key, defaultValue: defaultValue.toString());
        return value.toLowerCase() == 'true';
      }

      stripeKey = getEnvVar('STRIPE_PUBLISHABLE_KEY');
      baseUrl = getEnvVar('API_BASE_URL');
      placesApiKey = getEnvVar('GOOGLE_PLACES_API_KEY');
      _isMock = getEnvBool('CONF_IS_MOCK');

      langCode = getEnvVar('CONF_LANG_CODE', defaultValue: 'en');
      appId = getEnvVar('CONF_APP_ID');
      appName = getEnvVar('CONF_APP_NAME', defaultValue: 'Day One');
      termsOfUseUrl = getEnvVar('CONF_TERMS_OF_USE_URL');
      aboutUsUrl = getEnvVar('CONF_ABOUT_US_URL');
      privacyUrl = getEnvVar('CONF_PRIVACY_URL');
      webHosts = getEnvVar('CONF_WEB_HOSTS');
      dialCode = getEnvVar('CONF_DIAL_CODE', defaultValue: '+1');

      _validateConfiguration();

      print('✅ Configuration loaded successfully from environment variables');
      print('🏷️ App ID: $appId');
      print('🔧 Mock mode: $_isMock');
      print('📡 Base URL: ${baseUrl.isNotEmpty ? baseUrl : '[NOT SET]'}');
    } catch (e) {
      print('❌ Failed to load configuration from environment: $e');
      _setDefaults();
    }
  }

  void _validateConfiguration() {
    final missingVars = <String>[];

    if (baseUrl.isEmpty) missingVars.add('API_BASE_URL');
    if (stripeKey.isEmpty) missingVars.add('STRIPE_PUBLISHABLE_KEY');
    if (appId.isEmpty) missingVars.add('CONF_APP_ID');

    if (missingVars.isNotEmpty) {
      print('⚠️  Missing required environment variables: ${missingVars.join(', ')}');
      print('📝 Please check your .env file or environment configuration');
    }
  }

  void _setDefaults() {
    langCode = 'en';
    appId = 'com.example.app';
    stripeKey = ''; // Should be set via environment variable
    appName = 'Day One';
    baseUrl = ''; // Should be set via environment variable
    placesApiKey = ''; // Should be set via environment variable
    _isMock = true;
    termsOfUseUrl = '';
    aboutUsUrl = '';
    privacyUrl = '';
    webHosts = '';
    dialCode = '+1';
  }

  Locale get defaultLocale => supportedLocales.first;

  bool get isMock => forceMockState ?? _isMock;

  List<Locale> get supportedLocales {
    return const [Locale('en', 'US'), Locale('es', 'ES')];
  }

  DoTheme theme(bool inDarkMode) {
    return DoTheme(inDarkMode);
  }

  String logo = AppVectors.logo;

  String get copyrightText {
    final year = DateTime.now().year;
    return switch (langCode) {
      _ => LocaleKeys.copyrightText.tr({'year': '$year'}),
    };
  }

  String get termsOfUseUrlConfig => termsOfUseUrl;

  String get aboutUsUrlConfig => aboutUsUrl;

  String get privacyUrlConfig => privacyUrl;

  List<String> get webAppHosts => webHosts.split(',').where((s) => s.isNotEmpty).toList();

  String get countryCode => dialCode;

  String defaultAvatar =
      "https://avatar.iran.liara.run/public/boy?username=Scott";

  String defaultPetAvatar =
      "https://cdn.iconscout.com/icon/premium/png-512-thumb/cat-3420713-2854847.png?f=webp&w=512";

  AppConfigFonts fonts = const AppConfigFonts(
    primaryFontFamily: "Archivo",
    secondaryFontFamily: "Bevellier",
    iconFontFamily: "Icnoir",
  );

  AppConfigButtonSizes get buttonSizes => const AppConfigButtonSizes();

  AppConfigRadii get radii => const AppConfigRadii();

  AppConfigSpacing get spacing => const AppConfigSpacing();

  String get dbName => "day1_store_V1_1";

  String get scheme => "do";

  bool get allowCaching => true;

  List<String> get unguardedRoutes {
    return [
      AuthRoutes.login,
      AuthRoutes.pin,
      AuthRoutes.pinChange,
      AuthRoutes.pinChangeNewPin,
      AuthRoutes.pinChangeConfirm,
      AuthRoutes.passwordChange,
      AuthRoutes.verifyCode,
      AuthRoutes.twoFaSetup,
      AuthRoutes.twoFaPhoneNumber,
      AuthRoutes.twoFaCode,
      AuthRoutes.twoFaSuccess,
      AuthRoutes.twoFaActive,
      OnboardingRoutes.slides,
      SignupRoutes.email,
      SignupRoutes.emailVerification,
      SignupRoutes.password,
      SignupRoutes.phoneNumber,
      SignupRoutes.phoneVerification,
      SignupRoutes.biometrics,
      SignupRoutes.pinSetup,
      SignupRoutes.pinConfirm,
      SignupRoutes.notification,
      SignupRoutes.location,
    ];
  }
}
