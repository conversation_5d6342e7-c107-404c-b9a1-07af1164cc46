import 'package:flutter/material.dart';
import 'package:day1/day1.dart';

class DoAppConfig {
  Size? windowSize;
  bool? forceMockState;

  // Configuration properties
  late String langCode;
  late String appId;
  late String stripeKey;
  late String appName;
  late String baseUrl;
  late String placesApiKey;
  late bool _isMock;
  late String termsOfUseUrl;
  late String aboutUsUrl;
  late String privacyUrl;
  late String webHosts;
  late String dialCode;

  DoAppConfig({this.forceMockState});

  /// Load configuration from environment variables only
  static Future<DoAppConfig> fromEnvironment() async {
    final config = DoAppConfig();
    config._loadFromEnvironment();
    return config;
  }

  /// Legacy method for backward compatibility - now loads from environment only
  static Future<DoAppConfig> fromConfigFile(String configPath) async {
    print('⚠️  Loading from environment variables only. JSON config file is ignored for security.');
    return fromEnvironment();
  }

  void _loadFromEnvironment() {
    try {
      // Load all configuration from environment variables
      stripeKey = const String.fromEnvironment('STRIPE_PUBLISHABLE_KEY', defaultValue: '');
      baseUrl = const String.fromEnvironment('API_BASE_URL', defaultValue: '');
      placesApiKey = const String.fromEnvironment('GOOGLE_PLACES_API_KEY', defaultValue: '');
      _isMock = const bool.fromEnvironment('CONF_IS_MOCK', defaultValue: false);

      // Load app configuration from environment
      langCode = const String.fromEnvironment('CONF_LANG_CODE', defaultValue: 'en');
      appId = const String.fromEnvironment('CONF_APP_ID', defaultValue: '');
      appName = const String.fromEnvironment('CONF_APP_NAME', defaultValue: 'Day One');
      termsOfUseUrl = const String.fromEnvironment('CONF_TERMS_OF_USE_URL', defaultValue: '');
      aboutUsUrl = const String.fromEnvironment('CONF_ABOUT_US_URL', defaultValue: '');
      privacyUrl = const String.fromEnvironment('CONF_PRIVACY_URL', defaultValue: '');
      webHosts = const String.fromEnvironment('CONF_WEB_HOSTS', defaultValue: '');
      dialCode = const String.fromEnvironment('CONF_DIAL_CODE', defaultValue: '+1');

      // Validate required configuration
      _validateConfiguration();

      print('✅ Configuration loaded successfully from environment variables');
      print('🏷️ App ID: $appId');
      print('🔧 Mock mode: $_isMock');
      print('📡 Base URL: ${baseUrl.isNotEmpty ? baseUrl : '[NOT SET]'}');
    } catch (e) {
      print('❌ Failed to load configuration from environment: $e');
      // Set default values as fallback
      _setDefaults();
    }
  }

  void _validateConfiguration() {
    final missingVars = <String>[];

    if (baseUrl.isEmpty) missingVars.add('API_BASE_URL');
    if (stripeKey.isEmpty) missingVars.add('STRIPE_PUBLISHABLE_KEY');
    if (appId.isEmpty) missingVars.add('CONF_APP_ID');

    if (missingVars.isNotEmpty) {
      print('⚠️  Missing required environment variables: ${missingVars.join(', ')}');
      print('📝 Please check your .env file or environment configuration');
    }
  }

  void _setDefaults() {
    langCode = 'en';
    appId = 'com.example.app';
    stripeKey = ''; // Should be set via environment variable
    appName = 'Day One';
    baseUrl = ''; // Should be set via environment variable
    placesApiKey = ''; // Should be set via environment variable
    _isMock = true;
    termsOfUseUrl = '';
    aboutUsUrl = '';
    privacyUrl = '';
    webHosts = '';
    dialCode = '+1';
  }

  Locale get defaultLocale => supportedLocales.first;

  bool get isMock => forceMockState ?? _isMock;

  List<Locale> get supportedLocales {
    return const [Locale('en', 'US'), Locale('es', 'ES')];
  }

  DoTheme theme(bool inDarkMode) {
    return DoTheme(inDarkMode);
  }

  String logo = AppVectors.logo;

  String get copyrightText {
    final year = DateTime.now().year;
    return switch (langCode) {
      _ => LocaleKeys.copyrightText.tr({'year': '$year'}),
    };
  }

  String get termsOfUseUrlConfig => termsOfUseUrl;

  String get aboutUsUrlConfig => aboutUsUrl;

  String get privacyUrlConfig => privacyUrl;

  List<String> get webAppHosts => webHosts.split(',').where((s) => s.isNotEmpty).toList();

  String get countryCode => dialCode;

  String defaultAvatar =
      "https://avatar.iran.liara.run/public/boy?username=Scott";

  String defaultPetAvatar =
      "https://cdn.iconscout.com/icon/premium/png-512-thumb/cat-3420713-2854847.png?f=webp&w=512";

  AppConfigFonts fonts = const AppConfigFonts(
    primaryFontFamily: "Archivo",
    secondaryFontFamily: "Bevellier",
    iconFontFamily: "Icnoir",
  );

  AppConfigButtonSizes get buttonSizes => const AppConfigButtonSizes();

  AppConfigRadii get radii => const AppConfigRadii();

  AppConfigSpacing get spacing => const AppConfigSpacing();

  String get dbName => "day1_store_V1_1";

  String get scheme => "do";

  bool get allowCaching => true;

  List<String> get unguardedRoutes {
    return [
      AuthRoutes.login,
      AuthRoutes.pin,
      AuthRoutes.pinChange,
      AuthRoutes.pinChangeNewPin,
      AuthRoutes.pinChangeConfirm,
      AuthRoutes.passwordChange,
      AuthRoutes.verifyCode,
      AuthRoutes.twoFaSetup,
      AuthRoutes.twoFaPhoneNumber,
      AuthRoutes.twoFaCode,
      AuthRoutes.twoFaSuccess,
      AuthRoutes.twoFaActive,
      OnboardingRoutes.slides,
      SignupRoutes.email,
      SignupRoutes.emailVerification,
      SignupRoutes.password,
      SignupRoutes.phoneNumber,
      SignupRoutes.phoneVerification,
      SignupRoutes.biometrics,
      SignupRoutes.pinSetup,
      SignupRoutes.pinConfirm,
      SignupRoutes.notification,
      SignupRoutes.location,
    ];
  }
}
