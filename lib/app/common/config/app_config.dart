import 'dart:convert';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:day1/day1.dart';

class DoAppConfig {
  Size? windowSize;
  bool? forceMockState;

  // Configuration properties
  late String langCode;
  late String appId;
  late String stripeKey;
  late String appName;
  late String baseUrl;
  late String placesApiKey;
  late bool _isMock;
  late String termsOfUseUrl;
  late String aboutUsUrl;
  late String privacyUrl;
  late String webHosts;
  late String dialCode;

  DoAppConfig({this.forceMockState});

  /// Load configuration from JSON file
  static Future<DoAppConfig> fromConfigFile(String configPath) async {
    final config = DoAppConfig();
    await config._loadFromFile(configPath);
    return config;
  }

  Future<void> _loadFromFile(String configPath) async {
    try {
      final jsonString = await rootBundle.loadString(configPath);
      final Map<String, dynamic> configData = json.decode(jsonString);
      
      // Load all configuration values
      langCode = configData['CONF_LANG_CODE'] ?? 'en';
      appId = configData['CONF_APP_ID'] ?? '';
      stripeKey = configData['CONF_STRIPE_PK'] ?? '';
      appName = configData['CONF_APP_NAME'] ?? 'Day One';
      baseUrl = configData['CONF_BASE_URL'] ?? '';
      placesApiKey = configData['CONF_GOOG_PLACES_API_KEY'] ?? '';
      _isMock = configData['CONF_IS_MOCK'] == 'true';
      termsOfUseUrl = configData['CONF_TERMS_OF_USE_URL'] ?? '';
      aboutUsUrl = configData['CONF_ABOUT_US_URL'] ?? '';
      privacyUrl = configData['CONF_PRIVACY_URL'] ?? '';
      webHosts = configData['CONF_WEB_HOSTS'] ?? '';
      dialCode = configData['CONF_DIAL_CODE'] ?? '+1';
      
      print('✅ Configuration loaded successfully from $configPath');
      print('📡 Base URL: $baseUrl');
      print('🏷️ App ID: $appId');
      print('🔧 Mock mode: $_isMock');
    } catch (e) {
      print('❌ Failed to load configuration from $configPath: $e');
      // Set default values as fallback
      _setDefaults();
    }
  }

  void _setDefaults() {
    langCode = 'en';
    appId = 'com.example.app';
    stripeKey = '';
    appName = 'Day One';
    baseUrl = 'http://localhost:3000';
    placesApiKey = '';
    _isMock = true;
    termsOfUseUrl = '';
    aboutUsUrl = '';
    privacyUrl = '';
    webHosts = '';
    dialCode = '+1';
  }

  Locale get defaultLocale => supportedLocales.first;

  bool get isMock => forceMockState ?? _isMock;

  List<Locale> get supportedLocales {
    return const [Locale('en', 'US'), Locale('es', 'ES')];
  }

  DoTheme theme(bool inDarkMode) {
    return DoTheme(inDarkMode);
  }

  String logo = AppVectors.logo;

  String get copyrightText {
    final year = DateTime.now().year;
    return switch (langCode) {
      _ => LocaleKeys.copyrightText.tr({'year': '$year'}),
    };
  }

  String get termsOfUseUrlConfig => termsOfUseUrl;

  String get aboutUsUrlConfig => aboutUsUrl;

  String get privacyUrlConfig => privacyUrl;

  List<String> get webAppHosts => webHosts.split(',').where((s) => s.isNotEmpty).toList();

  String get countryCode => dialCode;

  String defaultAvatar =
      "https://avatar.iran.liara.run/public/boy?username=Scott";

  String defaultPetAvatar =
      "https://cdn.iconscout.com/icon/premium/png-512-thumb/cat-3420713-2854847.png?f=webp&w=512";

  AppConfigFonts fonts = const AppConfigFonts(
    primaryFontFamily: "Archivo",
    secondaryFontFamily: "Bevellier",
    iconFontFamily: "Icnoir",
  );

  AppConfigButtonSizes get buttonSizes => const AppConfigButtonSizes();

  AppConfigRadii get radii => const AppConfigRadii();

  AppConfigSpacing get spacing => const AppConfigSpacing();

  String get dbName => "day1_store_V1_1";

  String get scheme => "do";

  bool get allowCaching => true;

  List<String> get unguardedRoutes {
    return [
      AuthRoutes.login,
      AuthRoutes.pin,
      AuthRoutes.pinChange,
      AuthRoutes.pinChangeNewPin,
      AuthRoutes.pinChangeConfirm,
      AuthRoutes.passwordChange,
      AuthRoutes.verifyCode,
      AuthRoutes.twoFaSetup,
      AuthRoutes.twoFaPhoneNumber,
      AuthRoutes.twoFaCode,
      AuthRoutes.twoFaSuccess,
      AuthRoutes.twoFaActive,
      OnboardingRoutes.slides,
      SignupRoutes.email,
      SignupRoutes.emailVerification,
      SignupRoutes.password,
      SignupRoutes.phoneNumber,
      SignupRoutes.phoneVerification,
      SignupRoutes.biometrics,
      SignupRoutes.pinSetup,
      SignupRoutes.pinConfirm,
      SignupRoutes.notification,
      SignupRoutes.location,
    ];
  }
}
