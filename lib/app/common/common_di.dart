import 'package:day1/day1.dart';
import 'package:objectbox/objectbox.dart';

void registerCommonDI(
  DoAppConfig config, {
  required RootRouteRegistry routeRegistry,
  FutureCall<Store>? initialiseStore,
}) {
  ///[Routes]
  locator.registerLazySingleton<RootRouteRegistry>(() => routeRegistry);

  if (config.isMock) {
    ///[Mocks]
    locator.registerLazySingleton<AppDataMockResposes>(
      () => AppDataMockResposes(),
    );
    locator.registerLazySingleton<MeMockResponses>(
      () => MeMockResponses(),
    );

    ///[Mock Interceptors]
    locator.registerLazySingleton<AppDataMockInterceptor>(
      () => AppDataMockInterceptor(
        locator(),
      ),
    );
    locator.registerLazySingleton<AppMeMockInterceptor>(
      () => AppMeMockInterceptor(
        locator(),
      ),
    );
  }

  ///[Services]
  locator.registerLazySingleton<AppDbClient>(
    () => AppDbClientImpl(
      locator(),
      initialiseStore,
    ),
  );
  locator.registerLazySingleton<AppBackgroundTaskDispatchService>(
    () => AppBackgroundTaskDispatchServiceImpl(),
  );
  locator.registerLazySingleton<AppSessionService>(() {
    return AppSessionServiceImpl(
      locator(),
      locator(),
    );
  });
  locator.registerLazySingleton<AppHttpService>(() {
    return AppHttpServiceImpl(
      locator(),
      locator(),
      locator(),
    );
  });
  locator.registerLazySingleton<AppMeService>(() {
    return AppMeServiceHttp(
      locator(),
      locator(),
      locator(),
      locator(),
      locator(),
      locator(),
      locator(),
      config.isMock ? locator() : null,
    );
  });
  locator.registerLazySingleton<AppDataService>(() {
    return AppDataHttpService(
      locator(),
      config.isMock ? locator() : null,
    );
  });
  locator.registerLazySingleton<UploadService>(() {
    if (config.isMock) {
      return UploadMockService();
    }
    return UploadHttpService(locator());
  });
  locator.registerLazySingleton<DeeplinkService>(() {
    return DeeplinkServiceImpl(
      config.webAppHosts,
      locator(),
    );
  });

  ///[State]
  locator.registerLazySingleton<ThemeState>(
    () => ThemeState(locator()),
  );

  locator.registerLazySingleton<CountryState>(
    () => CountryState(),
  );

  locator.registerLazySingleton<AvatarState>(
    () => AvatarState(locator()),
  );
}

void resetCommonDI() {
  locator<AvatarState>().reset();
}
