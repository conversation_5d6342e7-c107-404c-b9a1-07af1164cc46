import 'package:day1/day1.dart';

enum OnboardingStep {
  email('email_setup'),
  emailVerification('email_verification'),
  password('password_setup'),
  phone('phone_setup'),
  phoneVerification('phone_verification'),
  bioAuth('bio_auth_setup'),
  pin('pin_setup'),
  notification('notification_setup'),
  location('location_setup'),
  completed('completed');

  const OnboardingStep(this.tag);

  final String tag;

  factory OnboardingStep.fromString(String step) {
    if (!step.hasValue) return OnboardingStep.email;
    return switch (step.lower.trim()) {
      "email_setup" || "email" => OnboardingStep.email,
      "email_verification" ||
      "emailVerification" =>
        OnboardingStep.emailVerification,
      "password_setup" || "password" => OnboardingStep.password,
      "phone_setup" || "phone" => OnboardingStep.phone,
      "phone_verification" ||
      "phoneVerification" =>
        OnboardingStep.phoneVerification,
      "bio_auth_setup" || "bioAuth" => OnboardingStep.bioAuth,
      "pin_setup" || "pin" => OnboardingStep.pin,
      "location_setup" || "location" => OnboardingStep.location,
      "notification_setup" || "notification" => OnboardingStep.notification,
      _ => OnboardingStep.completed,
    };
  }

  String get nextRoutePath {
    return switch (this) {
      OnboardingStep.emailVerification => SignupRoutes.emailVerification,
      OnboardingStep.password => SignupRoutes.password,
      OnboardingStep.phone => SignupRoutes.phoneNumber,
      OnboardingStep.phoneVerification => SignupRoutes.phoneVerification,
      OnboardingStep.bioAuth => SignupRoutes.biometrics,
      OnboardingStep.pin => SignupRoutes.pinSetup,
      OnboardingStep.location => SignupRoutes.location,
      OnboardingStep.notification => SignupRoutes.notification,
      OnboardingStep.email => SignupRoutes.email,
      _ => DashboardRoutes.home,
    };
  }

  bool get hasCompletedOnboarding => this == OnboardingStep.completed;
}
