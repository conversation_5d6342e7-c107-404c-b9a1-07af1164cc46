import 'package:day1/day1.dart';

abstract class _UrlParam extends Codable {
  String get urlParamString;
}

class SuggestionParam implements _UrlParam {
  final List<SuggestionItem>? selections;
  final String suggestionKey;

  const SuggestionParam({this.selections, required this.suggestionKey});

  @override
  String get urlParamString {
    if (!selections.hasValue) return "";

    final selectionIds = selections!
        .where((it) => it.id != null && !it.isUserGenerated)
        .map((it) => it.id)
        .toSet();

    if (selectionIds.isEmpty) return "";

    final concatenatedIds = selectionIds.join("&$suggestionKey=");
    return "$suggestionKey=$concatenatedIds";
  }

  @override
  Map<String, dynamic> toJson() => {};
}

class QueryParam implements _UrlParam {
  final List<SuggestionItem>? queries;
  final String queryKey;

  const QueryParam({this.queries, this.queryKey = "query"});

  @override
  String get urlParamString {
    if (!queries.hasValue) return "";

    final queryStrings = queries!
        .where((it) => it.name != null && it.isUserGenerated)
        .map((it) => it.name.value)
        .toSet();

    if (queryStrings.isEmpty) return "";

    final concatenatedNames = queryStrings.join("&$queryKey=");
    return "$queryKey=$concatenatedNames";
  }

  @override
  Map<String, dynamic> toJson() => {};
}
