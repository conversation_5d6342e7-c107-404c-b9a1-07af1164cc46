import 'dart:io';
import 'dart:typed_data';

import 'package:day1/day1.dart';
import 'package:dio/dio.dart' show FormData, MultipartFile;
import 'package:http_parser/http_parser.dart';

class FileUploadParam extends Codable {
  MediaData data;
  UploadUrlResponse? urlResponse;

  FileUploadParam({required this.data}) : urlResponse = null;
  FileUploadParam.forConfirmation({
    required this.data,
    required this.urlResponse,
  });

  FileUploadParam toConfirmationParam(UploadUrlResponse response) {
    return FileUploadParam.forConfirmation(data: data, urlResponse: response);
  }

  Future<Uint8List?> get binary async {
    try {
      if (!data.isFile) return null;

      File? file = data.file;

      return file?.readAsBytes();
    } catch (e, t) {
      AppLogger.severe("$e", stackTrace: t, error: e);
      return null;
    }
  }

  Future<FormData?> get formData async {
    if (!data.isFile) return null;

    MultipartFile? multiPartFile;

    multiPartFile = await MultipartFile.fromFile(
      data.file!.path,
      filename: data.fileName.lower,
      contentType: MediaType.parse(data.mimeType),
    );

    return FormData.fromMap({"file": multiPartFile});
  }

  String get contentType => data.mimeType;

  String? get contentLength => data.file?.lengthSync().toString();

  @override
  toJson() {
    if (urlResponse != null) {
      final json = {"uuid": urlResponse?.uuid};
      json.removeWhere((key, value) => value == null);
      return json;
    }
    return {"fileName": data.fileName, "mimeType": data.mimeType};
  }
}
