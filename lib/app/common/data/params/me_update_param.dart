import 'package:day1/day1.dart';

class MeUpdateParam extends Codable implements ImageUpdatable {
  String? email;
  String? displayName;
  String? description;
  String? placeId;
  String? language;
  @override
  AppImageData? photo;

  MeUpdateParam({
    this.email,
    this.displayName,
    this.placeId,
    this.description,
    this.photo,
    this.language,
  });

  @override
  updateImage(UploadResponse? uploadedImage) {
    if (uploadedImage == null) return;
    photo = AppImageData(
      imageData: uploadedImage.url ?? uploadedImage.fileName,
      id: uploadedImage.uuid,
    );
  }

  @override
  Map<String, dynamic> toJson() {
    Map<String, dynamic> data = {};
    data['email'] = email?.trim();
    data['displayName'] = displayName?.trim();
    data['placeId'] = placeId?.trim();
    data['description'] = description?.trim();
    data['language'] = language?.trim();
    data['photoUuid'] = photo?.id;

    data.removeWhere((key, value) => value == null);

    return data;
  }
}
