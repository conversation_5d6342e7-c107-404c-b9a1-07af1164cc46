import 'package:day1/day1.dart';

class AppDataSearchParam extends Codable {
  final String? search;
  final int offset;
  final int limit;

  const AppDataSearchParam({this.search, this.offset = 0, this.limit = 10});

  @override
  Map<String, dynamic> toJson() {
    Map<String, dynamic> data = {
      "search": search,
      "offset": offset,
      "limit": limit
    };

    data.removeWhere((key, value) => value == null);

    return data;
  }

  @override
  bool operator ==(other) {
    return other is AppDataSearchParam &&
        other.search == search &&
        other.offset == offset &&
        other.limit == limit;
  }

  @override
  int get hashCode => Object.hash(search, limit, offset);
}
