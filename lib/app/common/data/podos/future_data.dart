import 'package:day1/day1.dart';
import 'package:equatable/equatable.dart';

class FutureData<T extends Equatable> extends Equatable {
  final T? data;
  final bool isLoading;
  final bool isPristine;
  final NetworkError? error;
  final DateTime? updatedAt;

  const FutureData({
    this.data,
    this.isLoading = false,
    this.error,
    this.updatedAt,
  }) : isPristine = false;

  const FutureData.pristine({
    this.data,
    this.isLoading = false,
    this.error,
    this.updatedAt,
  }) : isPristine = true;

  bool get hasError {
    return error != null;
  }

  bool get hasData {
    return data != null;
  }

  @override
  toString() {
    return "error => $error, data => $data, isLoading => $isLoading";
  }

  String get updateTime {
    return updatedAt?.toIso8601String() ?? '';
  }

  FutureData<T> copyWith({
    T? data,
    bool? isLoading,
    NetworkError? error,
  }) {
    return FutureData(
      data: data ?? this.data,
      isLoading: isLoading ?? this.isLoading,
      error: this.error,
      updatedAt: DateTime.now(),
    );
  }

  FutureData<T> reset({required bool isLoading}) {
    return FutureData(
      data: null,
      isLoading: isLoading,
      updatedAt: DateTime.now(),
    );
  }

  @override
  List<Object?> get props => [data, isLoading, error, updatedAt];
}

class FutureListData<T extends Equatable> extends Equatable {
  final List<T> data;
  final bool isLoading;
  final NetworkError? error;
  final DateTime? updatedAt;
  final bool isPristine;

  const FutureListData({
    this.data = const [],
    this.isLoading = false,
    this.error,
    this.updatedAt,
  }) : isPristine = false;

  const FutureListData.pristine({
    this.data = const [],
    this.isLoading = false,
    this.error,
    this.updatedAt,
  }) : isPristine = true;

  bool get hasError {
    return error != null;
  }

  bool get hasData {
    return data.isNotEmpty;
  }

  @override
  toString() {
    return "error => $error, data => $data, isLoading => $isLoading";
  }

  String get updateTime {
    return updatedAt?.toIso8601String() ?? '';
  }

  FutureListData<T> copyWith({
    List<T>? data,
    bool? isLoading,
    NetworkError? error,
  }) {
    return FutureListData(
      data: data ?? this.data,
      isLoading: isLoading ?? this.isLoading,
      error: this.error,
      updatedAt: DateTime.now(),
    );
  }

  @override
  List<Object?> get props => [data, isLoading, error, updatedAt];
}
