import 'package:equatable/equatable.dart';
import 'package:flutter/widgets.dart';

class SelectionData<T> extends Equatable {
  final T selection;
  final String label;
  final Widget? icon;
  final String? extendedLabel;

  const SelectionData({
    required this.selection,
    required this.label,
    this.icon,
    this.extendedLabel,
  });

  static SelectionData<String> fromLabel(String label) {
    return SelectionData<String>(selection: label, label: label);
  }

  @override
  List<Object?> get props => [label, selection, icon, extendedLabel];
}
