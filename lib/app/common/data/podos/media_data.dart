import 'dart:convert';
import 'dart:io';

import 'package:equatable/equatable.dart';
import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

abstract class MediaData<T> extends Codable {
  bool get isValid;
  bool get isUrl;
  bool get isString;
  bool get isFile;
  bool get hasId;
  bool get hasName;

  String get fileName;

  String? get fileUrl;
  String? get filePath;
  File? get file;

  String get mimeType;
}

class AppDocumentData<T> implements MediaData<T> {
  final T document;
  final String? id;
  final String? name;
  final String? createdAt;

  AppDocumentData({
    required this.document,
    this.name,
    this.createdAt,
    this.id,
  });

  @override
  bool get hasName => name != null || (name?.isNotEmpty ?? false);

  bool get _hasData => document != null;

  @override
  bool get hasId => id != null;

  @override
  bool get isValid {
    if (!_hasData) return false;
    return (isString || isUrl || isFile) && (_isDoc || _isPdf || _isDocx);
  }

  @override
  bool get isString {
    if (!_hasData) return false;
    if ("$document".startsWith("data:")) return false;
    return document is String;
  }

  @override
  bool get isUrl {
    if (!isString) return false;
    return AppValidators.urlValidator(document as String) == null;
  }

  @override
  bool get isFile {
    if (!_hasData) return false;
    if ("$document".startsWith("data:")) return true;
    return document is File;
  }

  @override
  String get fileName {
    if (hasName) return name!;
    if (!isValid) return "";
    if (isString) {
      return (document as String);
    }
    return file?.path.split('/').tryLast ?? "";
  }

  @override
  File? get file {
    if (!isFile) return null;
    if ("$document".startsWith("data:")) {
      final base64 = "$document".replaceAll("data:", "");
      final data = base64Decode(base64);
      return File.fromRawPath(data);
    }
    return document as File;
  }

  @override
  String? get filePath {
    if (!isString) return null;
    return document as String;
  }

  bool get _isPdf {
    return file?.path.lower.endsWith(".pdf") ?? false;
  }

  bool get _isDoc {
    return file?.path.lower.endsWith(".doc") ?? false;
  }

  bool get _isDocx {
    return file?.path.lower.endsWith(".docx") ?? false;
  }

  @override
  String get mimeType {
    if (_isPdf) return AppMimeTypes.pdf;
    if (_isDocx) return AppMimeTypes.docx;
    if (_isDoc) return AppMimeTypes.doc;

    return "*/*";
  }

  @override
  String? get fileUrl {
    if (!isUrl) return null;
    return document as String;
  }

  @override
  Map<String, dynamic> toJson() {
    final data = {
      "id": id?.toId(),
      "name": name,
      "url": fileUrl,
      "createdAt": createdAt ?? DateTime.now()
    };
    data.removeWhere((key, value) => value == null);
    return data;
  }
}

class AppImageData<T> extends Equatable implements MediaData<T> {
  final T imageData;
  final String? id;
  final String? name;
  final String? createdAt;

  const AppImageData({
    required this.imageData,
    this.name,
    this.id,
    this.createdAt,
  });

  @override
  bool get hasId => id != null;

  @override
  bool get hasName => name != null || (name?.isNotEmpty ?? false);

  bool get _hasData => imageData != null;

  @override
  bool get isValid {
    if (!_hasData) return false;
    return isString || isUrl || isFile;
  }

  @override
  bool get isString {
    if (!_hasData) return false;
    return imageData is String;
  }

  @override
  @override
  bool get isUrl {
    if (!isString) return false;
    String imageUrl = imageData as String;
    if (imageUrl.startsWith("assets")) return false;
    if (imageUrl.startsWith("data:")) return true;
    return AppValidators.urlValidator(imageData as String) == null;
  }

  @override
  bool get isFile {
    if (!_hasData) return false;
    return imageData is File;
  }

  @override
  String get fileName {
    if (hasName) return name!;
    if (!isValid) return "";
    if (isString) {
      return (imageData as String);
    }
    return file?.path.split('/').tryLast ?? "";
  }

  @override
  File? get file {
    if (!isFile) return null;
    return imageData as File;
  }

  @override
  String? get filePath {
    if (!isString) return null;
    return imageData as String;
  }

  @override
  String? get fileUrl {
    if (!isUrl) return null;
    return imageData as String;
  }

  AssetImage? get stringImageData {
    if (!isString) return null;
    return AssetImage(filePath!);
  }

  NetworkImage? get urlImageData {
    if (!isUrl) return null;
    return NetworkImage(fileUrl!);
  }

  FileImage? get fileImageData {
    if (file == null) return null;
    return FileImage(file!);
  }

  bool get isImage {
    return AppRegex.imageRegex.hasMatch(file?.path ?? "");
  }

  bool get isVideo {
    return AppRegex.videoRegex.hasMatch(file?.path ?? "");
  }

  bool get _isPng {
    return file?.path.lower.endsWith('.png') ?? false;
  }

  bool get _isJpeg {
    return AppRegex.jpegRegex.hasMatch(file?.path ?? "");
  }

  @override
  String get mimeType {
    if (_isPng) return AppMimeTypes.png;
    if (_isJpeg) return AppMimeTypes.jpeg;
    if (isImage) return AppMimeTypes.image;
    if (isVideo) return AppMimeTypes.video;

    return "*/*";
  }

  @override
  Map<String, dynamic> toJson() {
    final data = {
      "id": id?.toId(),
      "name": name,
      "url": fileUrl,
      "createdAt": createdAt ?? DateTime.now()
    };
    data.removeWhere((key, value) => value == null);
    return data;
  }

  @override
  List<Object?> get props => [filePath];
}
