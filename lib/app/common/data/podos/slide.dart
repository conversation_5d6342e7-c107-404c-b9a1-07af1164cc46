import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class SlideData {
  final String title;
  final String rider;
  final TextStyle? titleStyle;
  final TextStyle? riderStyle;
  final Color headerColor;
  final Color backgroundColor;
  final Color? bottomCardColor;
  final Color? bottomCardTextColor;
  final Color? bottomCardButtonColor;
  final String? backgroundImage;
  final CustomPainter? painter;
  final EdgeInsets backgroundPadding;
  final Offset backgroundOffSet;
  final BoxFit backgroundFit;
  final double backgroundScale;
  final AppGap headerSpacing;
  final Duration? backgroundTransitionDuration;
  final Alignment backgroundAlignment;

  const SlideData({
    required this.title,
    required this.rider,
    required this.headerColor,
    required this.backgroundColor,
    this.titleStyle,
    this.riderStyle,
    this.backgroundTransitionDuration,
    this.headerSpacing = const AppGap.y16(),
    this.bottomCardColor,
    this.bottomCardTextColor,
    this.bottomCardButtonColor,
    this.backgroundImage,
    this.backgroundFit = BoxFit.none,
    this.backgroundPadding = EdgeInsets.zero,
    this.backgroundScale = 1,
    this.backgroundOffSet = Offset.zero,
    this.backgroundAlignment = Alignment.center,
    this.painter,
  });
}

extension SlideDataExtension on BuildContext {
  List<SlideData> get onboardingSlides {
    return [
      SlideData(
        title: LocaleKeys.unlockNigeriaWithYourMoney.utr(),
        rider: LocaleKeys.enjoyEasyFundAccess.tr(),
        headerColor: textColor,
        backgroundColor: cardColor,
        backgroundImage: AppVectors.unlockNaija,
        backgroundScale: 1.4,
        backgroundAlignment: Alignment.center,
        bottomCardTextColor: highlightedTextColor,
        bottomCardButtonColor: raisedBtnCBgColor,
        bottomCardColor: AppColors.lemon30,
        backgroundOffSet: Offset(-sp(AppFontSizes.px40), 0),
      ),
      SlideData(
        title: LocaleKeys.cardReadyOnLanding.utr(),
        rider: LocaleKeys.enjoyCardConvenience.tr(),
        headerColor: AppColors.secondary8.forTheme(inDarkMode),
        backgroundColor: AppColors.secondary7.forTheme(inDarkMode),
        backgroundImage: AppVectors.cardOnLanding,
        backgroundScale: 1,
        backgroundAlignment: Alignment.center,
        bottomCardTextColor: highlightedTextColor,
        bottomCardButtonColor: raisedBtnCBgColor,
        backgroundFit: BoxFit.contain,
        bottomCardColor: AppColors.lemon30,
      ),
      SlideData(
        title: LocaleKeys.frotKnoxInPocket.utr(),
        rider: LocaleKeys.spendMoreWithCard.tr(),
        headerColor: AppColors.secondary6.forTheme(inDarkMode),
        backgroundColor: AppColors.surfaceTertiary2.forTheme(inDarkMode),
        backgroundImage: AppVectors.fortKnox,
        backgroundScale: 1.2,
        backgroundFit: BoxFit.contain,
        backgroundAlignment: Alignment.bottomCenter,
        bottomCardTextColor: highlightedTextColor,
        bottomCardButtonColor: raisedBtnCBgColor,
        bottomCardColor: AppColors.lemon20,
        backgroundOffSet: Offset(
          -sp(AppFontSizes.px22),
          fractionalHeight(11),
        ),
      ),
      SlideData(
        title: LocaleKeys.spendExperienceMore.utr(),
        rider: LocaleKeys.experienceSecurityUnparalleled.tr(),
        headerColor: AppColors.secondaryDarkBlue.forTheme(inDarkMode),
        backgroundColor: AppColors.secondaryLightBlue.forTheme(inDarkMode),
        backgroundImage: AppVectors.smartSpender,
        backgroundScale: 1.3,
        backgroundFit: BoxFit.contain,
        backgroundAlignment: Alignment.bottomCenter,
        bottomCardTextColor: highlightedTextColor,
        bottomCardButtonColor: raisedBtnCBgColor,
        bottomCardColor: AppColors.lemon20,
        backgroundOffSet: Offset(
          -sp(AppFontSizes.px20),
          fractionalHeight(13),
        ),
      ),
      SlideData(
        title: LocaleKeys.yourKeyTonNigerianGems.utr(),
        rider: LocaleKeys.thereMoreFromNaira.tr(),
        headerColor: AppColors.secondaryDarkGreen.forTheme(inDarkMode),
        backgroundColor: AppColors.secondaryLightGreen.forTheme(inDarkMode),
        backgroundImage: AppVectors.hiddenGems,
        backgroundFit: BoxFit.contain,
        backgroundScale: .9,
        backgroundAlignment: Alignment.bottomCenter,
        backgroundOffSet: Offset(0, fractionalHeight(5)),
      ),
    ];
  }

  List<SlideData> get cardSlides {
    return [
      SlideData(
        title: LocaleKeys.letsGetYourCard.utr(),
        rider: LocaleKeys.getYourDay1CardAsYouLand.tr(),
        titleStyle: textStyle.d2(),
        riderStyle: textStyle.b2(),
        headerSpacing: const AppGap.y32(),
        headerColor: textColor,
        backgroundColor: cardColor,
        backgroundImage: AppVectors.cardSlide1,
        backgroundFit: BoxFit.contain,
        backgroundAlignment: Alignment.center,
        backgroundOffSet: Offset(
          0,
          fractionalHeight(10),
        ),
      ),
      SlideData(
        title: LocaleKeys.yourFinancialFreedomInNigeria.utr(),
        rider: LocaleKeys.loremIpsum.tr(),
        titleStyle: textStyle.d2(),
        riderStyle: textStyle.b2(),
        headerSpacing: const AppGap.y32(),
        headerColor: textColor,
        backgroundColor: cardColor,
        backgroundScale: .82,
        backgroundImage: AppVectors.cardSlide2,
        backgroundFit: BoxFit.contain,
        backgroundAlignment: Alignment.bottomCenter,
      ),
      SlideData(
        title: LocaleKeys.startSpendingInstantly.utr(),
        rider: LocaleKeys.loremIpsum.tr(),
        titleStyle: textStyle.d2(),
        riderStyle: textStyle.b2(),
        headerColor: textColor,
        backgroundColor: cardColor,
        backgroundImage: AppVectors.cardSlide3,
        backgroundScale: 1.05,
        backgroundFit: BoxFit.contain,
        backgroundAlignment: Alignment.bottomCenter,
        backgroundOffSet: Offset(
          -sp(AppFontSizes.px10),
          fractionalHeight(3.5),
        ),
      ),
      SlideData(
        title: LocaleKeys.getDiscounts.utr(),
        rider: LocaleKeys.loremIpsum.tr(),
        titleStyle: textStyle.d2(),
        riderStyle: textStyle.b2(),
        headerColor: textColor,
        backgroundColor: cardColor,
        backgroundImage: AppVectors.cardSlide4,
        backgroundScale: 1.05,
        backgroundFit: BoxFit.contain,
        backgroundAlignment: Alignment.center,
        backgroundOffSet: Offset(
          -sp(AppFontSizes.px6),
          fractionalHeight(4.5),
        ),
      ),
    ];
  }
}
