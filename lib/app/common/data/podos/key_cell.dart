import 'package:day1/day1.dart';

class AppKeyCellData {
  final AppIconData? icon;
  final String value;

  AppKeyCellData._(this.value, {this.icon});

  static List<List<AppKeyCellData>> get values {
    return [
      [AppKeyCellData._('1'), AppKeyCellData._('2'), AppKeyCellData._('3')],
      [AppKeyCellData._('4'), AppKeyCellData._('5'), AppKeyCellData._('6')],
      [AppKeyCellData._('7'), AppKeyCellData._('8'), AppKeyCellData._('9')],
      [
        AppKeyCellData._('bio', icon: AppIcons.faceId),
        AppKeyCellData._('0'),
        AppKeyCellData._('x', icon: AppIcons.arrowLeftTag)
      ],
    ];
  }
}
