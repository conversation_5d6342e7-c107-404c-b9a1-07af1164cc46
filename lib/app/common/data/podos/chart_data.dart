import 'dart:math';
import 'package:day1/day1.dart';

class PieData {
  final List<PieItem> items;
  final List<ColorSet> pallete;

  const PieData({
    required this.items,
    this.pallete = defaultPallete,
  });

  static const List<ColorSet> defaultPallete = [
    ColorSet(0xFF083B2F, 0xFFFFFFFF),
    ColorSet(0xFF76F057, 0xFF282828),
    ColorSet(0xFFEEEEEE, 0xFF282828),
    ColorSet(0xFF38812F, 0xFFFFFFFF),
    ColorSet(0xFFBDE2B9, 0xFF282828),
    ColorSet(0xFF009596, 0xFFFFFFFF),
    ColorSet(0xFFA2D9D9, 0xFF282828),
    ColorSet(0xFF5752D1, 0xFFFFFFFF),
    ColorSet(0xFFB2B0EA, 0xFF282828),
    ColorSet(0xFFF0AB00, 0xFF282828),
    ColorSet(0xFFF6D173, 0xFF282828),
    ColorSet(0xFFC46100, 0xFFFFFFFF),
    ColorSet(0xFFF4B678, 0xFF282828),
  ];

  ColorSet itemColor(PieItem item) {
    if (item.color != null) return item.color!;
    int index = items.indexOf(item);
    if (index == -1) return pallete[0];
    index = index % pallete.length;
    return pallete[index];
  }

  double get sum {
    if (!items.hasValue) return 0;
    return items.map((it) => it.value).reduce((p, n) => p + n);
  }

  double get max {
    if (!items.hasValue) return 0;
    final values = items.mapList((it) => it.value);
    values.sort();
    return (values.last).ceilToDouble();
  }

  @override
  bool operator ==(other) {
    if (other is! PieData) return false;
    return items == other.items && pallete == other.pallete;
  }

  @override
  int get hashCode => items.hashCode ^ pallete.hashCode ^ super.hashCode;
}

class PieItem {
  final ColorSet? color;
  final double value;
  final String label;

  const PieItem({
    this.color,
    required this.value,
    required this.label,
  });

  double fraction(double sum) {
    if (value <= 0 || sum <= 0) return 0;
    return value / sum;
  }

  double radian(double sum) {
    return value * 2 * pi / sum;
  }

  @override
  bool operator ==(other) {
    if (other is! PieItem) return false;
    return value == other.value && label == other.label && color == other.color;
  }

  @override
  int get hashCode => value.hashCode ^ label.hashCode ^ super.hashCode;
}

class BarChartData {
  final List<BarChartBarData> series;
  final String legend;
  final String period;

  const BarChartData({
    required this.series,
    required this.legend,
    required this.period,
  });

  factory BarChartData.empty() {
    return BarChartData(
      period: "",
      legend: "",
      series: List.generate(7, (index) {
        return BarChartBarData(value: 0, label: "$index");
      }),
    );
  }

  List<double> get _sortedSeriesValue {
    if (series.isEmpty) return [];
    final data = [...series.map((it) => it.value)];
    data.sort();
    return data;
  }

  double get sum {
    if (_sortedSeriesValue.isEmpty) return 0;
    return _sortedSeriesValue.reduce((prev, next) => prev + next);
  }

  double get min {
    final seriesMinimum = _sortedSeriesValue.first;
    return seriesMinimum < 0 ? seriesMinimum : 0;
  }

  double get median {
    return (max / 2).ceilToDouble();
  }

  double get max {
    final seriesMax =
        _sortedSeriesValue.isEmpty ? 100.0 : _sortedSeriesValue.last;
    return seriesMax;
  }
}

class BarChartBarData {
  final double value;
  final String label;

  const BarChartBarData({required this.value, required this.label});

  double fraction(double max) {
    if (value <= 0 || max <= 0) return 0;
    final fraction = value / max;
    return fraction > 1 ? 1 : fraction;
  }
}
