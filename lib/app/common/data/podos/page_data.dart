class PageData {
  final int totalPages;
  final int total;
  final List items;
  final PageSearch? search;

  PageData({
    required this.totalPages,
    required this.total,
    required this.items,
    this.search,
  });

  factory PageData.fromJson(dynamic json) {
    int totalPages = 0;
    int total = 0;
    List items = [];
    PageSearch? search;

    if (json is Map) {
      if (json["items"] is List) {
        items = json["items"];
      }

      if (json.containsKey("search") && json["search"] is Map) {
        search = PageSearch.fromJson(json["search"]);
      }

      total = json["total"] ?? 0;
      totalPages = json["totalPages"] ?? (items.isEmpty ? 0 : 0x7fffffff);
    }

    if (json is List) {
      items = json;

      if (items.isNotEmpty) totalPages = 0x7fffffff;
    }

    return PageData(
      totalPages: totalPages,
      total: total,
      items: items,
      search: search,
    );
  }

  bool get cantHaveNext => totalPages == 0;
}

class PageSearch {
  String? id;

  PageSearch({required this.id});

  PageSearch.fromJson(Map<String, dynamic> json) : id = json['id']?.toString();
}
