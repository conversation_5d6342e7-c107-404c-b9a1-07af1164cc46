import 'package:day1/day1.dart';
import 'package:equatable/equatable.dart';

class Identifiable extends Equatable {
  final String uuid;

  const Identifiable({required this.uuid});

  @override
  List<Object?> get props => [uuid];
}

class PaginatedData<T extends Identifiable> extends Equatable {
  final List<T> data;
  final bool isLoading;
  final PageMetaData? meta;
  final NetworkError? pageError;
  final String? query;
  final bool _isPristine;

  const PaginatedData({
    this.meta,
    this.data = const [],
    this.isLoading = false,
    this.pageError,
    this.query,
  }) : _isPristine = false;

  const PaginatedData.pristine({
    this.data = const [],
    this.isLoading = false,
    this.pageError,
  })  : _isPristine = true,
        query = null,
        meta = null;

  bool get isPristine => _isPristine;

  int get page => meta?.currentPage ?? 0;
  int get pages => meta?.totalPages ?? 1;

  bool get hasNext => page < pages;

  int get next {
    if (!hasNext) return page;
    return page + 1;
  }

  bool get hasError {
    return pageError != null;
  }

  bool get hasData {
    return data.isNotEmpty;
  }

  @override
  toString() {
    return "<<meta => $meta, hasNext => $hasNext, isLoading => $isLoading, hasData -> $hasData, data -> ${data.length}>>";
  }

  PaginatedData<T> copyWith({
    List<T>? data,
    bool? isLoading,
    NetworkError? pageError,
    String? query,
    PageMetaData? meta,
  }) {
    return PaginatedData(
      data: data ?? this.data,
      isLoading: isLoading ?? this.isLoading,
      pageError: pageError ?? this.pageError,
      meta: meta ?? this.meta,
      query: query,
    );
  }

  PaginatedData<T> addData(
    PaginatedData<T> pageData, {
    bool ensureUnique = false,
  }) {
    if (this == pageData) return this;
    List<T> items = [...data, ...pageData.data];

    if (ensureUnique) {
      items = [...items.toSet()];
    }

    return PaginatedData(
      data: items,
      isLoading: pageData.isLoading,
      pageError: pageData.pageError,
      meta: pageData.meta,
    );
  }

  PaginatedData<T> addSingleItem(T item, {bool unshift = false}) {
    final items = unshift ? [item, ...data] : [...data, item];
    return PaginatedData(data: items);
  }

  PaginatedData<T> updateSingleItem(T oldItem, T newItem) {
    if (!data.contains(oldItem)) return addSingleItem(newItem);
    final index = data.indexOf(oldItem);
    final items = [...data];
    items[index] = newItem;
    return PaginatedData(data: items);
  }

  PaginatedData<T> removeSingleItem(T item, {bool unshift = false}) {
    data.remove(item);
    return PaginatedData(data: data);
  }

  @override
  List<Object?> get props => [data, isLoading, data.length, meta, query];
}

class PageMetaData extends Equatable {
  final int? hitsPerPage;
  final int? currentPage;
  final int? totalItems;
  final int? totalPages;

  const PageMetaData({
    this.hitsPerPage,
    this.currentPage,
    this.totalItems,
    this.totalPages,
  });

  PageMetaData copyWith({
    int? hitsPerPage,
    int? currentPage,
    int? totalItems,
    int? totalPages,
  }) {
    return PageMetaData(
      hitsPerPage: hitsPerPage ?? this.hitsPerPage,
      currentPage: currentPage ?? this.currentPage,
      totalItems: totalItems ?? this.totalItems,
      totalPages: totalPages ?? this.totalPages,
    );
  }

  factory PageMetaData.fromJson(Map<String, dynamic> json) {
    return PageMetaData(
      hitsPerPage: json["hitsPerPage"],
      currentPage: json["currentPage"],
      totalItems: json["totalItems"],
      totalPages: json["totalPages"],
    );
  }

  @override
  List<Object?> get props => [currentPage, totalPages, totalItems, hitsPerPage];
}
