class UploadUrlResponse {
  String url;
  String uuid;
  String? fileName;
  String? type;
  String? mimeType;

  UploadUrlResponse({
    required this.url,
    required this.uuid,
    this.fileName,
    this.type,
    this.mimeType,
  });

  UploadUrlResponse.fromJson(Map json)
      : url = json["url"] ?? "",
        uuid = json["uuid"] ?? "" {
    fileName = json["fileName"];
    type = json["type"];
    mimeType = json["mimeType"];
  }
}

class UploadResponse {
  String? url;
  String? id;
  String? uuid;
  String? fileName;
  String? uploadedAt;
  String? type;
  String? processedAt;

  UploadResponse({
    this.url,
    this.uploadedAt,
    this.id,
    this.fileName,
    this.uuid,
  });

  UploadResponse.fromJson(Map json) {
    url = json["uri"] ?? json["url"];
    id = json["id"];
    uuid = json["uuid"];
    type = json["type"];
    fileName = json["fileName"];
    uploadedAt = json["uploadedAt"];
    processedAt = json["processedAt"];
  }
}
