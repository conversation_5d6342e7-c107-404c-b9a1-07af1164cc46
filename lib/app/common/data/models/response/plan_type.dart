import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class PlanFeature {
  final String title;
  final String description;
  final IconData icon;

  const PlanFeature({
    required this.title,
    required this.description,
    required this.icon,
  });
}

enum PlanType {
  tier1(1, 1000, LocaleKeys.basic),
  tier2(2, 2500, LocaleKeys.standard),
  tier3(3, 5000, LocaleKeys.premium);

  const PlanType(this.idx, this.price, this.name);

  final int idx;
  final int price;
  final String name;

  static PlanType fromString(String? plan) {
    return switch (plan) {
      "standard" => PlanType.tier2,
      "premium" => PlanType.tier3,
      _ => PlanType.tier1
    };
  }

  List get features {
    return [
      PlanFeature(
        title: LocaleKeys.pickupInstructions.tr(),
        description: LocaleKeys.aRepWillMeetYouAtAirport.tr(),
        icon: AppIcons.mapPin,
      ),
      PlanFeature(
        title: LocaleKeys.contactInfo.tr(),
        description: LocaleKeys.contactUsForAssistance.tr(),
        icon: AppIcons.smartphoneDevice,
      ),
      PlanFeature(
        title: LocaleKeys.emergencyAssistance.tr(),
        description: LocaleKeys.contactUsInCaseOfEmergency.tr(),
        icon: AppIcons.phonePlus,
      ),
      if ([PlanType.tier2, PlanType.tier3].contains(this))
        PlanFeature(
          title: LocaleKeys.securityTips.tr(),
          description: LocaleKeys.securityTipsDetails.tr(),
          icon: AppIcons.securityPass,
        ),
      if (this == PlanType.tier3)
        PlanFeature(
          title: LocaleKeys.usageInformation.tr(),
          description: LocaleKeys.usageInformationDetails.tr(),
          icon: AppIcons.statsReport,
        ),
    ];
  }
}
