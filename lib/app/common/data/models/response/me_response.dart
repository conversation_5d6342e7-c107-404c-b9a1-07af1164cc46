import 'package:day1/day1.dart';
import 'package:equatable/equatable.dart';

class MeResponse implements AvatarSelectable {
  @override
  final String uuid;
  final String? email;
  final String? accountNumber;
  final PlanType? plan;
  final String? firstName;
  final String? lastName;
  final String? photo;
  final Location? location;
  final String? onboardingState;
  final String? id;
  final String? phoneNumber;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final String? pinSetAt;
  final String? biometricsSetAt;
  final String? emailVerifiedAt;
  final String? passwordSetAt;
  final String? phoneVerifiedAt;
  final String? stripeCustomerId;

  MeResponse({
    required this.uuid,
    this.email,
    this.firstName,
    this.plan,
    this.accountNumber,
    this.lastName,
    this.location,
    this.photo,
    this.onboardingState,
    this.id,
    this.phoneNumber,
    this.createdAt,
    this.pinSetAt,
    this.updatedAt,
    this.biometricsSetAt,
    this.emailVerifiedAt,
    this.passwordSetAt,
    this.phoneVerifiedAt,
    this.stripeCustomerId,
  });

  bool hasAttribute(dynamic attribute) => attribute != null;

  MeResponse copyWith({
    String? email,
    String? displayName,
    PlanType? plan,
    String? firstName,
    String? lastName,
    String? accountNumber,
    String? photo,
    String? onboardingState,
    Location? location,
    String? id,
    String? phoneNumber,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? pinSetAt,
    String? biometricsSetAt,
    String? emailVerifiedAt,
    String? passwordSetAt,
    String? phoneVerifiedAt,
    String? stripeCustomerId,
  }) {
    return MeResponse(
      email: email ?? this.email,
      firstName: firstName ?? this.firstName,
      plan: plan ?? this.plan,
      lastName: lastName ?? this.lastName,
      accountNumber: accountNumber ?? this.accountNumber,
      photo: photo ?? this.photo,
      onboardingState: onboardingState ?? this.onboardingState,
      uuid: uuid,
      updatedAt: updatedAt ?? this.updatedAt,
      id: id ?? this.id,
      createdAt: createdAt ?? this.createdAt,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      biometricsSetAt: biometricsSetAt ?? this.biometricsSetAt,
      pinSetAt: pinSetAt ?? this.pinSetAt,
      emailVerifiedAt: emailVerifiedAt ?? this.emailVerifiedAt,
      passwordSetAt: passwordSetAt ?? this.passwordSetAt,
      phoneVerifiedAt: phoneVerifiedAt ?? this.phoneVerifiedAt,
      stripeCustomerId: stripeCustomerId ?? this.stripeCustomerId,
    );
  }

  OnboardingStep get onboardingStep {
    if (!hasSetEmail) return OnboardingStep.email;
    if (!hasVerifiedEmail) return OnboardingStep.emailVerification;
    if (!hasSetPssword) return OnboardingStep.password;
    if (!hasSetPhone) return OnboardingStep.phone;
    if (!hasVerifiedPhone) return OnboardingStep.phoneVerification;
    if (!hasEnabledBioAuth) return OnboardingStep.bioAuth;
    if (!hasSetPin) return OnboardingStep.pin;

    return OnboardingStep.completed;
  }

  bool get hasSetEmail => email.hasValue;
  bool get hasSetPin => pinSetAt.hasValue;
  bool get hasVerifiedEmail => emailVerifiedAt.hasValue;
  bool get hasSetPhone => phoneNumber.hasValue;
  bool get hasVerifiedPhone => phoneVerifiedAt.hasValue;
  bool get hasEnabledBioAuth => biometricsSetAt.hasValue;
  bool get hasSetPssword => passwordSetAt.hasValue;

  factory MeResponse.fromJson(Map<String, dynamic> json) {
    Location? location;
    if (json['location'] is Map) {
      location = Location.fromJson(json["location"]);
    }
    return MeResponse(
      uuid: json['id'] ?? "",
      id: json['id'] ?? "",
      email: json['email'],
      plan: PlanType.fromString(json['plan']),
      firstName: json['firstName'],
      location: location,
      accountNumber: json['accountNumber'],
      lastName: json['lastName'],
      photo: json['photo'],
      onboardingState: json['onboardingState'],
      updatedAt: json["updatedAt"] == null
          ? null
          : DateTime.tryParse(json["updatedAt"]),
      phoneNumber: json["phoneNumber"],
      createdAt: json["createdAt"] == null
          ? null
          : DateTime.tryParse(json["createdAt"]),
      biometricsSetAt: json["biometricsSetAt"],
      emailVerifiedAt: json["emailVerifiedAt"],
      passwordSetAt: json["passwordSetAt"],
      pinSetAt: json["passwordSetAt"],
      phoneVerifiedAt: json["phoneVerifiedAt"],
      stripeCustomerId: json["stripeCustomerId"],
    );
  }

  factory MeResponse.fromCache(UserCache data) {
    return MeResponse.fromJson(data.toMap());
  }

  UserCache toCacheData() {
    return UserCache(
      email: email,
      uuid: uuid,
      firstName: firstName,
      lastName: lastName,
      displayName: name,
      location: LocationCache(
        placeId: location?.placeId,
        displayName: location?.displayName,
      ),
      photo: photo,
      createdAt: createdAt,
      phoneNumber: phoneNumber,
      updatedAt: updatedAt,
      biometricsSetAt: biometricsSetAt,
      pinSetAt: pinSetAt,
      emailVerifiedAt: emailVerifiedAt,
      passwordSetAt: passwordSetAt,
      phoneVerifiedAt: phoneVerifiedAt,
      stripeCustomerId: stripeCustomerId,
    );
  }

  static MeResponse get mock {
    return MeResponse(
      uuid: "0",
      email: "<EMAIL>",
      firstName: "Hailey",
      lastName: "Kavanagh",
      plan: PlanType.tier2,
      accountNumber: "**********",
      onboardingState: LocaleKeys.stepVerified.tr({"step": "1"}),
    );
  }

  @override
  AppImageData? get image {
    if (!photo.hasValue) return null;
    return AppImageData(imageData: photo);
  }

  @override
  String? get initials => AppHelpers.getInitials(name);

  @override
  String? get name => "${firstName.value} ${lastName.value}".value;
}

class TelephoneNumber extends Equatable {
  final String? countryCode;
  final String number;

  const TelephoneNumber({
    this.countryCode,
    required this.number,
  });

  String? get combined {
    if (!countryCode.hasValue && !number.hasValue) return null;
    if (countryCode.hasValue && number.hasValue) return "$countryCode$number";
    return number;
  }

  TelephoneNumber copyWith({String? countryCode, String? number}) {
    return TelephoneNumber(
      countryCode: countryCode ?? this.countryCode,
      number: number ?? this.number,
    );
  }

  TelephoneNumber.fromJson(Map json)
      : countryCode = json['countryCode'],
        number = json['number'];

  @override
  List<Object?> get props => [countryCode, number];
}

class Photo {
  int? id;
  String? uuid;
  String? createdAt;
  String? name;
  String? url;
  String? type;

  Photo({
    this.id,
    this.uuid,
    this.createdAt,
    this.name,
    this.url,
    this.type,
  });

  AppImageData? get image {
    if (!url.hasValue) return null;
    return AppImageData(imageData: url);
  }

  Photo.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    uuid = json['uuid'];
    createdAt = json['createdAt'];
    name = json['name'];
    url = json['url'];
    type = json['type'];
  }

  ImageCache get toCache {
    return ImageCache(
      uuid: uuid,
      createdAt: createdAt,
      name: name,
      url: url,
      type: type,
    );
  }

  factory Photo.fromCache(ImageCache cache) {
    return Photo(
      uuid: cache.uuid,
      createdAt: cache.createdAt,
      name: cache.name,
      type: cache.type,
      url: cache.url,
    );
  }
}

class Location {
  String? placeId;
  String? displayName;

  Location({this.placeId, this.displayName});

  Location.fromJson(Map json) {
    placeId = json["placeId"];
    displayName = json["displayName"];
  }
}
