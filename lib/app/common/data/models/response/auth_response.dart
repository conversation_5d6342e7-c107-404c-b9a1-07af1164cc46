import 'package:day1/day1.dart';
import 'package:equatable/equatable.dart';

class AuthResponse extends Equatable {
  final AuthUser? user;
  final Tokens? tokens;
  final OtpDetails? otpDetails;
  final String? publicKey;

  const AuthResponse({
    this.user,
    this.tokens,
    this.otpDetails,
    this.publicKey,
  });

  AuthResponse copyWith({
    AuthUser? user,
    Tokens? tokens,
    OtpDetails? otpDetails,
    String? publicKey,
  }) {
    return AuthResponse(
      user: user ?? this.user,
      tokens: tokens ?? this.tokens,
      otpDetails: otpDetails ?? this.otpDetails,
      publicKey: publicKey ?? this.publicKey,
    );
  }

  factory AuthResponse.fromJson(Map<String, dynamic> json) {
    return AuthResponse(
      user: json["user"] == null ? null : AuthUser.fromJson(json["user"]),
      publicKey: json["publicKey"],
      tokens: json["tokens"] == null ? null : Tokens.fromJson(json["tokens"]),
      otpDetails: json["OTPDetails"] == null
          ? null
          : OtpDetails.fromJson(json["OTPDetails"]),
    );
  }

  String get accessToken => tokens?.accessToken?.token ?? "";
  String get refreshToken => tokens?.refreshToken?.token ?? "";
  String? get userPublicKey => user?.pubKey;
  String? get email => user?.email;
  String? get phoneNumber => user?.phoneNumber;
  int get expiresIn => tokens?.accessToken?.expiresIn ?? 0;
  int get otpLifeTime => otpDetails?.otpLifetime ?? 0;

  AuthUser get _user => user ?? AuthUser();

  OnboardingStep get onboardingStep {
    if (!_user.hasSetEmail) return OnboardingStep.email;
    if (!_user.hasVerifiedEmail) return OnboardingStep.emailVerification;
    if (!_user.hasSetPssword) return OnboardingStep.password;
    if (!_user.hasSetPhone) return OnboardingStep.phone;
    if (!_user.hasVerifiedPhone) return OnboardingStep.phoneVerification;
    if (!_user.hasEnabledBioAuth) return OnboardingStep.bioAuth;
    if (!_user.hasSetPin) return OnboardingStep.pin;

    return OnboardingStep.completed;
  }

  @override
  List<Object?> get props => [accessToken, refreshToken, expiresIn];
}

class OtpDetails {
  final DateTime? createdAt;
  final DateTime? expiredAt;
  final String? email;
  final String? id;
  final DateTime? updatedAt;

  OtpDetails({
    this.createdAt,
    this.expiredAt,
    this.email,
    this.id,
    this.updatedAt,
  });

  OtpDetails copyWith({
    DateTime? createdAt,
    DateTime? expiredAt,
    String? email,
    String? id,
    DateTime? updatedAt,
  }) {
    return OtpDetails(
      updatedAt: updatedAt ?? this.updatedAt,
      email: email ?? this.email,
      expiredAt: expiredAt ?? this.expiredAt,
      id: id ?? this.id,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  int get otpLifetime {
    if (createdAt == null || expiredAt == null) return 0;
    return expiredAt!.difference(createdAt!).inSeconds;
  }

  factory OtpDetails.fromJson(Map<String, dynamic> json) {
    return OtpDetails(
      createdAt:
          json["createdAt"] == null ? null : DateTime.parse(json["createdAt"]),
      expiredAt:
          json["expiredAt"] == null ? null : DateTime.parse(json["expiredAt"]),
      email: json["email"],
      id: json["id"],
      updatedAt:
          json["updatedAt"] == null ? null : DateTime.parse(json["updatedAt"]),
    );
  }
}

class Tokens {
  final Token? refreshToken;
  final Token? accessToken;

  Tokens({this.refreshToken, this.accessToken});

  Tokens copyWith({Token? refreshToken, Token? accessToken}) {
    return Tokens(
      accessToken: accessToken ?? this.accessToken,
      refreshToken: refreshToken ?? this.refreshToken,
    );
  }

  factory Tokens.fromJson(Map<String, dynamic> json) {
    return Tokens(
      refreshToken: json["refreshToken"] == null
          ? null
          : Token.fromJson(json["refreshToken"]),
      accessToken: json["accessToken"] == null
          ? null
          : Token.fromJson(json["accessToken"]),
    );
  }
}

class Token {
  final String? token;
  final int? iat;
  final int? expiresIn;

  Token copyWith({
    String? token,
    int? iat,
    int? expiresIn,
  }) {
    return Token(
      token: token ?? this.token,
      iat: iat ?? this.iat,
      expiresIn: expiresIn ?? this.expiresIn,
    );
  }

  Token({
    this.token,
    this.iat,
    this.expiresIn,
  });

  factory Token.fromJson(Map<String, dynamic> json) {
    return Token(
      token: json["token"],
      iat: json["iat"],
      expiresIn: json["expiresIn"],
    );
  }
}

class AuthUser {
  final String? id;
  final String? email;
  final String? phoneNumber;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final String? pinSetAt;
  final String? biometricsSetAt;
  final String? emailVerifiedAt;
  final String? passwordSetAt;
  final String? phoneVerifiedAt;
  final String? stripeCustomerId;
  final AuthPubKey? publicKey;

  AuthUser({
    this.id,
    this.email,
    this.phoneNumber,
    this.createdAt,
    this.pinSetAt,
    this.updatedAt,
    this.biometricsSetAt,
    this.emailVerifiedAt,
    this.passwordSetAt,
    this.phoneVerifiedAt,
    this.stripeCustomerId,
    this.publicKey,
  });

  AuthUser copyWith({
    String? id,
    String? email,
    String? phoneNumber,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? pinSetAt,
    String? biometricsSetAt,
    String? emailVerifiedAt,
    String? passwordSetAt,
    String? phoneVerifiedAt,
    String? stripeCustomerId,
    AuthPubKey? publicKey,
  }) {
    return AuthUser(
      updatedAt: updatedAt ?? this.updatedAt,
      publicKey: publicKey ?? this.publicKey,
      id: id ?? this.id,
      createdAt: createdAt ?? this.createdAt,
      email: email ?? this.email,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      biometricsSetAt: biometricsSetAt ?? this.biometricsSetAt,
      pinSetAt: pinSetAt ?? this.pinSetAt,
      emailVerifiedAt: emailVerifiedAt ?? this.emailVerifiedAt,
      passwordSetAt: passwordSetAt ?? this.passwordSetAt,
      phoneVerifiedAt: phoneVerifiedAt ?? this.phoneVerifiedAt,
      stripeCustomerId: stripeCustomerId ?? this.stripeCustomerId,
    );
  }

  bool get hasSetEmail => email.hasValue;
  bool get hasSetPin => pinSetAt.hasValue;
  bool get hasVerifiedEmail => emailVerifiedAt.hasValue;
  bool get hasSetPhone => phoneNumber.hasValue;
  bool get hasVerifiedPhone => phoneVerifiedAt.hasValue;
  bool get hasEnabledBioAuth => biometricsSetAt.hasValue;
  bool get hasPubKey => publicKey?.publicKey.hasValue ?? false;
  bool get hasSetPssword => passwordSetAt.hasValue;

  String? get pubKey => publicKey?.publicKey;

  factory AuthUser.fromJson(Map<String, dynamic> json) {
    return AuthUser(
      id: json["id"],
      email: json["email"],
      phoneNumber: json["phoneNumber"],
      createdAt:
          json["createdAt"] == null ? null : DateTime.parse(json["createdAt"]),
      updatedAt:
          json["updatedAt"] == null ? null : DateTime.parse(json["updatedAt"]),
      biometricsSetAt: json["biometricsSetAt"],
      emailVerifiedAt: json["emailVerifiedAt"],
      passwordSetAt: json["passwordSetAt"],
      pinSetAt: json["passwordSetAt"],
      phoneVerifiedAt: json["phoneVerifiedAt"],
      stripeCustomerId: json["stripeCustomerId"],
    );
  }
}

class AuthPubKey {
  final DateTime? updatedAt;
  final String? publicKeyType;
  final String? publicKey;
  final int? id;
  final DateTime? createdAt;

  AuthPubKey({
    this.updatedAt,
    this.publicKeyType,
    this.publicKey,
    this.id,
    this.createdAt,
  });

  AuthPubKey copyWith({
    DateTime? updatedAt,
    String? publicKeyType,
    String? publicKey,
    int? id,
    DateTime? createdAt,
  }) {
    return AuthPubKey(
      updatedAt: updatedAt ?? this.updatedAt,
      publicKeyType: publicKeyType ?? this.publicKeyType,
      publicKey: publicKey ?? this.publicKey,
      id: id ?? this.id,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  factory AuthPubKey.fromJson(Map<String, dynamic> json) {
    return AuthPubKey(
      updatedAt:
          json["updatedAt"] == null ? null : DateTime.parse(json["updatedAt"]),
      publicKeyType: json["publicKeyType"],
      publicKey: json["publicKey"],
      id: json["id"],
      createdAt:
          json["createdAt"] == null ? null : DateTime.parse(json["createdAt"]),
    );
  }
}
