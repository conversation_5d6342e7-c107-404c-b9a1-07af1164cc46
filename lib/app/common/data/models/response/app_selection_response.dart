import 'package:day1/day1.dart';
import 'package:equatable/equatable.dart';

class AppSelectionResponse extends Equatable {
  final String? id;
  final String? uuid;
  final String name;

  const AppSelectionResponse({
    required this.id,
    required this.name,
    this.uuid,
  });

  factory AppSelectionResponse.fromJson(Map json) {
    return AppSelectionResponse(
      id: json["id"]?.toString(),
      uuid: json["uuid"],
      name: json["name"] ?? "",
    );
  }

  factory AppSelectionResponse.fromLinkJson(Map json) {
    return AppSelectionResponse(id: json["url"], name: json["title"] ?? "");
  }

  AppSelectionResponse copyWith({
    String? id,
    String? uuid,
    String? name,
    String? parentId,
  }) {
    return AppSelectionResponse(
      id: id ?? this.id,
      uuid: uuid ?? this.uuid,
      name: name ?? this.name,
    );
  }

  SelectionData<AppSelectionResponse> selectionData({bool includeId = false}) {
    String name = this.name;

    if (includeId) {
      name += " ($id)";
    }

    return SelectionData(selection: this, label: name);
  }

  @override
  List<Object?> get props => [id, name, uuid];
}

class SuggestionItem extends Equatable {
  final String? id;
  final String? name;
  final bool _userGenerated;

  const SuggestionItem({this.id, this.name}) : _userGenerated = false;
  const SuggestionItem.userGenerated({this.name})
      : id = null,
        _userGenerated = true;

  bool get isUserGenerated => _userGenerated;

  factory SuggestionItem.fromJson(Map json) {
    return SuggestionItem(id: json['id']?.toString(), name: json['name']);
  }

  @override
  List<Object?> get props => [id];
}

class SuggestionGroup {
  List<SuggestionItem>? items;
  List<SuggestionItem>? additionalItems;
  String? key;

  SuggestionGroup({this.items, this.additionalItems, this.key});

  SuggestionGroup.fromJson(Map json) {
    if (json['items'] != null) {
      items = <SuggestionItem>[];
      json['items'].forEach((v) {
        items!.tryAdd(SuggestionItem.fromJson(v));
      });
    }
    if (json['additionalItems'] != null) {
      additionalItems = <SuggestionItem>[];
      json['additionalItems'].forEach((v) {
        additionalItems!.tryAdd(SuggestionItem.fromJson(v));
      });
    }
    key = json['key'];
  }
}
