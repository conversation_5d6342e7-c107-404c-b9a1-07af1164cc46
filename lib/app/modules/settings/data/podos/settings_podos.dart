import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class SettingsTileData {
  final IconData icon;
  final String label;
  final RouteSettings? route;
  final OnPressed? onTap;
  final Widget? trailing;

  const SettingsTileData({
    required this.icon,
    required this.label,
    this.route,
    this.onTap,
    this.trailing,
  })  : assert(!(route == null && onTap == null)),
        assert(!(route != null && onTap != null));
}
