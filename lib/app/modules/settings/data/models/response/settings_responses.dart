import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

enum DeviceOs { ios, android, web }

class UserDevice {
  final String name;
  final String imei;
  final DeviceOs os;
  final DateTime lastActive;

  const UserDevice({
    required this.name,
    required this.imei,
    required this.os,
    required this.lastActive,
  });

  IconData get icon {
    return switch (os) {
      DeviceOs.ios => AppIcons.appleMac,
      _ => AppIcons.smartphoneDevice
    };
  }

  bool get isActive {
    return lastActive.isSameDay(DateTime.now());
  }

  static List<UserDevice> devices = [
    UserDevice(
      name: "Pixel 5",
      imei: "358265019329048", // Valid IMEI
      os: DeviceOs.android,
      lastActive: DateTime.now().subtract(const Duration(days: 1)),
    ),
    UserDevice(
      name: "iPhone 12",
      imei: "356938035643809", // Valid IMEI
      os: DeviceOs.ios,
      lastActive: DateTime.now().subtract(const Duration(hours: 6)),
    ),
    UserDevice(
      name: "Samsung Galaxy S21",
      imei: "353789102938164", // Valid IMEI
      os: DeviceOs.android,
      lastActive: DateTime.now().subtract(const Duration(days: 2)),
    ),
    UserDevice(
      name: "iPad Pro",
      imei: "354638102764953", // Valid IMEI
      os: DeviceOs.ios,
      lastActive: DateTime.now().subtract(const Duration(hours: 10)),
    ),
    UserDevice(
      name: "MacBook Air",
      imei: "356892036427895", // Valid IMEI
      os: DeviceOs.ios,
      lastActive: DateTime.now().subtract(const Duration(days: 3)),
    ),
    UserDevice(
      name: "OnePlus 9",
      imei: "352983102365498", // Valid IMEI
      os: DeviceOs.android,
      lastActive: DateTime.now().subtract(const Duration(hours: 5)),
    ),
    UserDevice(
      name: "Google Pixel 6",
      imei: "355709128365903", // Valid IMEI
      os: DeviceOs.android,
      lastActive: DateTime.now().subtract(const Duration(days: 7)),
    ),
    UserDevice(
      name: "iPhone 13",
      imei: "356985043762910", // Valid IMEI
      os: DeviceOs.ios,
      lastActive: DateTime.now().subtract(const Duration(hours: 12)),
    ),
    UserDevice(
      name: "Surface Pro",
      imei: "353907103295614", // Valid IMEI
      os: DeviceOs.web,
      lastActive: DateTime.now().subtract(const Duration(days: 1)),
    ),
    UserDevice(
      name: "Galaxy Tab S7",
      imei: "354726105639284", // Valid IMEI
      os: DeviceOs.android,
      lastActive: DateTime.now().subtract(const Duration(hours: 8)),
    ),
  ];
}
