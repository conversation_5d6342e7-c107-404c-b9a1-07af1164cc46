import 'package:day1/day1.dart';

class SettingsProfileState extends StateModel with AppTaskMixin {
  final AppMeService _meService;

  SettingsProfileState(this._meService) {
    fetchUserData();
  }

  MeResponse? _userData;
  MeResponse? get userData => _userData;
  
  NetworkError? _error;
  NetworkError? get error => _error;
  bool get hasError => _error != null;

  Future<void> fetchUserData() async {
    setLoadingState(true);
    _error = null;
    
    // First try to get cached data
    _userData = _meService.cachedMeData;
    if (_userData != null) {
      notifyListeners();
    }
    
    // Then fetch fresh data from server
    final response = await _meService.getMeData(useCache: false);
    
    if (response.hasError) {
      _error = response.error;
    } else {
      _userData = response.data;
      _error = null;
    }
    
    setLoadingState(false);
  }

  Future<void> refreshUserData() async {
    await fetchUserData();
  }
}