import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class SettingsState extends StateModel {
  List<SettingsTileData> get topTiles => [
        SettingsTileData(
          icon: AppIcons.userBadgeCheck,
          label: LocaleKeys.accountVerification,
          route: const RouteSettings(name: KycRoutes.status),
          trailing: AppPill(LocaleKeys.stepVerified.tr({"step": "1"})),
        ),
        const SettingsTileData(
          icon: AppIcons.key,
          label: LocaleKeys.changePassword,
          route: RouteSettings(name: AuthRoutes.passwordChange),
        ),
        const SettingsTileData(
          icon: AppIcons.shield,
          label: LocaleKeys.twoFA,
          route: RouteSettings(
            name: AuthRoutes.twoFaSetup,
            arguments: Auth2faScreenArguments(isSetup: true),
          ),
        ),
        const SettingsTileData(
          icon: AppIcons.asterisk,
          label: LocaleKeys.changeYourPin,
          route: RouteSettings(name: AuthRoutes.pinChange),
        ),
      ];

  final medianTiles = const [
    SettingsTileData(
      icon: AppIcons.headsetHelp,
      label: LocaleKeys.support,
      route: RouteSettings(name: SettingsRoutes.support),
    ),
    SettingsTileData(
      icon: AppIcons.smartphoneDevice,
      label: LocaleKeys.yourDevices,
      route: RouteSettings(name: SettingsRoutes.devices),
    ),
  ];

  final terminalTiles = [
    const SettingsTileData(
      icon: AppIcons.privacyPolicy,
      label: LocaleKeys.privacyPolicy,
      route: RouteSettings(name: SettingsRoutes.privacyPolicy),
    ),
    SettingsTileData(
      icon: AppIcons.logOut,
      label: LocaleKeys.logout,
      onTap: () {
        locator<AuthSettingsState>().logout();
      },
    ),
  ];
}
