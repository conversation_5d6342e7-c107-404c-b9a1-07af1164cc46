import 'package:flutter/material.dart';
import 'package:day1/day1.dart';

class SettingsRoutes implements RouteRegistry {
  static const basePath = "/settings";
  static const plans = "$basePath/plans";
  static const referral = "$basePath/referral";
  static const profile = "$basePath/profile";
  static const root = "$basePath/root";
  static const devices = "$basePath/devices";
  static const String support = '$basePath/support';
  static const String privacyPolicy = '$basePath/privacy-policy';

  const SettingsRoutes();

  @override
  Route dynamicRoutes(RouteSettings settings, Route fallbackRoute) {
    return switch (settings.name) {
      root => MaterialPageRoute(
          builder: (context) => AppLocalStateWrapper<SettingsState>(
            builder: (_) => const SettingsScreen(key: Key(root)),
          ),
        ),
      referral => MaterialPageRoute(
          builder: (context) => AppLocalStateWrapper<SettingsState>(
            builder: (_) => const SettingsReferralScreen(key: Key(referral)),
          ),
        ),
      profile => MaterialPageRoute(
          builder: (context) => AppLocalStateWrapper<SettingsState>(
            builder: (_) => const SettingsProfileScreen(key: Key(profile)),
          ),
        ),
      plans => MaterialPageRoute(
          builder: (context) => AppLocalStateWrapper<SettingsPlanState>(
            builder: (_) => const SettingsPlansScreen(key: Key(plans)),
          ),
        ),
      devices => MaterialPageRoute(
          builder: (context) => AppLocalStateWrapper<SettingsPlanState>(
            builder: (_) => const SettingsDevicesScreen(key: Key(devices)),
          ),
        ),
      privacyPolicy => MaterialPageRoute(
          builder: (context) => const SettingsPrivacyScreen(
            key: Key(privacyPolicy),
          ),
        ),
      support => MaterialPageRoute(
          builder: (context) => AppLocalStateWrapper<SettingsPlanState>(
            builder: (_) => const SettingsSupportScreen(key: Key(support)),
          ),
        ),
      _ => fallbackRoute,
    };
  }
}
