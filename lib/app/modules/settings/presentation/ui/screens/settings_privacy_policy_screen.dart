import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class SettingsPrivacyScreen extends AppStatelessWidget {
  const SettingsPrivacyScreen({super.key});

  static const _policyItems = [
    "1. Introduction",
    "Welcome to DayOne! Your privacy is important to us. This Privacy Policy explains how we collect, use, and share your personal information when you use our app.",
    "2. Information We Collect",
    """We may collect the following information when you use the DayOne app:\n\n- Personal information (e.g., name, email address, contact information)\n- Usage data (e.g., how you interact with the app)\n- Device information (e.g., IP address, browser type, operating system)""",
    "3. How We Use Your Information",
    """We use your information to provide and improve the DayOne app. This includes:\n\n- Personalizing your experience"\n- Providing customer support\n- Analyzing usage trends and preferences\n- Communicating updates and promotions (with your consent)""",
    "4. How We Share Your Information",
    """We may share your information in the following circumstances:\n\n- With third-party service providers to help deliver our services\n- When required by law or to protect the security and integrity of DayOne""",
    "5. Security",
    "We take reasonable measures to protect your personal information, but no system is completely secure. Please use the app with caution and report any suspicious activity.",
    "6. Data Retention",
    "We will retain your personal information for as long as necessary to fulfill the purposes outlined in this Privacy Policy, unless a longer retention period is required or permitted by law.",
    "7. Your Rights",
    """You have the following rights regarding your personal information:\n\n- Access: You can request access to your data at any time.\n- Correction: You can update or correct your personal data.\n- Deletion: You can request the deletion of your personal data.""",
    "8. Changes to this Privacy Policy",
    "We may update this Privacy Policy from time to time. We will notify you of any significant changes by posting the new policy in the DayOne app.",
    "9. Contact Us",
    "If you have any questions or concerns about this Privacy Policy, please contact us at [Insert Contact Information].",
    "Thank you for using DayOne!"
  ];

  @override
  Widget build(BuildContext context) {
    final count = _policyItems.length;
    return Scaffold(
      appBar: DoAppBar(title: LocaleKeys.privacyPolicy.tr()),
      body: AppCard(
        margin: context.insets.defaultAllInsets,
        padding: context.insets.zero,
        child: ListView.builder(
          padding: context.insets.defaultCardInsets,
          itemBuilder: (_, index) {
            if (index == 0) {
              return AppFormHeader(title: LocaleKeys.privacyPolicy.tr());
            }

            final item = _policyItems[index - 1];

            return PolicySection(item, key: Key("${index - 1})::policy_item"));
          },
          itemCount: count,
        ),
      ),
    );
  }
}

class PolicySection extends AppStatelessWidget {
  final String text;

  const PolicySection(this.text, {super.key});

  bool get isHeader {
    return AppRegex.startsWithDigit.hasMatch(text);
  }

  @override
  Widget build(BuildContext context) {
    EdgeInsetsGeometry padding =
        context.insets.onlySp(bottom: AppFontSizes.px8);
    final style = isHeader
        ? context.textStyle.h6()
        : context.textStyle.b2(color: context.secondaryTextColor);

    if (!isHeader) {
      padding = context.insets.onlySp(bottom: AppFontSizes.px32);
    }

    return Padding(
      padding: padding,
      child: AppText(text, style: style),
    );
  }
}
