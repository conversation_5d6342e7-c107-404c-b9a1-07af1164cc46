import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class SettingsDevicesScreen extends AppStatelessWidget {
  const SettingsDevicesScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final devices = UserDevice.devices;
    final count = devices.length;
    return Scaffold(
      appBar: DoAppBar(title: LocaleKeys.yourDevices.tr()),
      body: AppCard(
        margin: context.insets.defaultAllInsets,
        padding: context.insets.zero,
        child: ListView.separated(
          padding: context.insets.defaultCardInsets,
          itemBuilder: (_, index) {
            if (index == 0) {
              return AppFormHeader(
                title: LocaleKeys.youHaveCountDevices.tr({"count": "$count"}),
                titleStyle: context.textStyle.b2(
                  color: context.secondaryTextColor,
                ),
                bottomGap: const AppGap.y8(),
              );
            }

            final device = devices[index - 1];

            return SettingsDeviceCard(
              device,
              key: Key("${index - 1}) ${device.imei}"),
            );
          },
          separatorBuilder: (_, __) => const AppGap.y16(),
          itemCount: devices.length,
        ),
      ),
    );
  }
}
