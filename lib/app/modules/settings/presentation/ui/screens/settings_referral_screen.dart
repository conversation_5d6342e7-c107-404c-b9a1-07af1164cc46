import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class SettingsReferralScreen extends AppStatelessWidget {
  const SettingsReferralScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final amount = 10000.formattedCurrency;
    return Scaffold(
      appBar: DoAppBar(title: LocaleKeys.inviteFriends.tr()),
      body: ListView(
        padding: context.insets.defaultAllInsets,
        children: [
          const ReferralInfoCard(),
          const AppGap.y12(),
          const ReferralEarningsCard(20),
          const AppGap.y48(),
          Padding(
            padding: context.insets.defaultCardHInsets,
            child: App<PERSON><PERSON><PERSON>(
              onPressed: () {
                context.shareText("Use my code D0EWHI to join Day1");
              },
              text: LocaleKeys.shareNEarnAmount.tr({"amount": amount}),
            ),
          )
        ],
      ),
    );
  }
}
