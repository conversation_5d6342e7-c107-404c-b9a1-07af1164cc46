import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class SettingsSupportScreen extends AppStatelessWidget {
  const SettingsSupportScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: DoAppBar(title: LocaleKeys.supportChat.tr()),
      body: SingleChildScrollView(
        child: AppCard(
          gradient: context.gradients.supportGradient,
          margin: context.insets.defaultAllInsets,
          child: const Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              SupportHeaderSection(),
              AppGap.y20(),
              SupportHelpSection(),
              AppGap.y8(),
              SupportMessageSection(),
              AppGap.y8(),
              SupportSearchSection(),
              AppGap.y64()
            ],
          ),
        ),
      ),
    );
  }
}
