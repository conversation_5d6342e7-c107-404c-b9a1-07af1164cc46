import 'package:day1/day1.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

class SettingsScreen extends AppStatelessWidget {
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final state = context.watch<SettingsState>();
    final meService = locator<AppMeService>();
    final user = meService.cachedMeData ?? MeResponse.mock;
    
    return Scaffold(
      appBar: DoAppBar(title: LocaleKeys.settings.tr()),
      body: ListView(
        padding: context.insets.defaultAllInsets,
        children: [
          const AppGap.y12(),
          UserProfile<PERSON><PERSON>(user),
          const AppGap.y12(),
          const SettingsTierInvitationRow(),
          const AppGap.y24(),
          SettingsSection(state.topTiles),
          SettingsSection(state.medianTiles),
          SettingsSection(state.terminalTiles)
        ],
      ),
    );
  }
}
