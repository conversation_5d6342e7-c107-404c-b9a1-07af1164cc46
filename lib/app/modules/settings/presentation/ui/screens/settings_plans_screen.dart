import 'package:day1/day1.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

class SettingsPlansScreen extends AppStatelessWidget {
  const SettingsPlansScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final state = context.watch<SettingsPlanState>();
    final tierTabs = Column(
      children: [
        const AppGap.y12(),
        GenericListener(
          valueListenable: state.selectedPlan,
          builder: (selection) {
            return PlanSelectionTabbar(
              selection,
              onSelect: (value) {
                state.selectedPlan.value = value;
              },
              key: const Key("plan_selection_tabbar"),
            );
          },
        )
      ],
    );
    return Scaffold(
      appBar: DoAppBar(
        title: LocaleKeys.selectTier.tr(),
        padding: context.insets.symmetricSp(
          horizontal: AppFontSizes.px24,
        ),
      ),
      body: Padding(
        padding: context.insets.defaultHorizontalInsets,
        child: CustomScrollView(
          slivers: [
            SliverResizingHeader(
              maxExtentPrototype: tierTabs,
              minExtentPrototype: tierTabs,
              child: tierTabs,
            ),
            SliverList.list(
              children: [
                const AppGap.y24(),
                PlanPriceCard(
                  state.selectedPlan,
                  key: const Key("plan_price_card"),
                ),
                const AppGap.y12(),
                PlanFeatureCard(
                  state.selectedPlan,
                  key: const Key("plan_features_card"),
                  onSelect: (_) {
                    context.showNotification(LocaleKeys.success.tr());
                    AppRouter.popView();
                  },
                ),
                const AppGap.y40(),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
