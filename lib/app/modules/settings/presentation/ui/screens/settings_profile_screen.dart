import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class SettingsProfileScreen extends AppStatelessWidget {
  const SettingsProfileScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return AppLocalStateWrapper<SettingsProfileState>(
      builder: (state) {
        return Scaffold(
          appBar: DoAppBar(title: LocaleKeys.myProfile.tr()),
          body: RefreshIndicator(
            onRefresh: state.refreshUserData,
            child: _buildBody(context, state),
          ),
        );
      },
    );
  }

  Widget _buildBody(BuildContext context, SettingsProfileState state) {
    if (state.isLoading && state.userData == null) {
      return const Center(child: CircularProgressIndicator());
    }

    if (state.hasError && state.userData == null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              'Failed to load user data',
              style: context.textStyle.b2(),
            ),
            const AppGap.y16(),
            AppButton(
              onPressed: state.fetchUserData,
              text: 'Retry',
            ),
          ],
        ),
      );
    }

    final user = state.userData ?? MeResponse.mock;
    return ListView(
      padding: context.insets.defaultAllInsets,
      children: [
        UserProfileCardAlt(user),
        const AppGap.y16(),
        SettingsProfileTable(user),
        const AppGap.y16(),
        SettingsProfileAccountDetailsTable(user),
      ],
    );
  }
}
