import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class UserProfileCard extends AppStatelessWidget {
  final MeResponse user;

  const UserProfileCard(this.user, {super.key});

  @override
  Widget build(BuildContext context) {
    return AppCard(
      color: context.raisedBtnBBgColor,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Row(
            children: [
              AppAvatar(
                avatar: user.image,
                initials: user.initials,
                bgColor: context.cardColor,
              ),
              const AppGap.h8(),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    AppText(
                      user.name,
                      style: context.textStyle.b2(
                        color: context.raisedBtnTextColor,
                        weight: FontWeight.w500,
                      ),
                    ),
                    AppText(
                      user.accountNumber,
                      style: context.textStyle.b3(
                        color: context.neutralTextColor.withOpacity(.75),
                      ),
                    ),
                  ],
                ),
              ),
              AppOutlineButton(
                onPressed: () {},
                size: ButtonSize.medium,
                borderColor: context.disabledBtnColor,
                bgColor: context.transparent,
                minSize: Size.fromWidth(context.sp(AppFontSizes.px52)),
                icon: AppIcon(AppIcons.shareIos, color: context.cardColor),
              ),
            ],
          ),
          const AppGap.y24(),
          AppButton(
            onPressed: () {
              AppRouter.pushNamed(SettingsRoutes.profile);
            },
            text: LocaleKeys.viewProfile.tr(),
          )
        ],
      ),
    );
  }
}

class UserProfileCardAlt extends AppStatelessWidget {
  final MeResponse user;

  const UserProfileCardAlt(this.user, {super.key});

  @override
  Widget build(BuildContext context) {
    return AppCard(
      padding: context.insets.symmetricSp(
        vertical: AppFontSizes.px16,
        horizontal: AppFontSizes.px24,
      ),
      cornerStyle: CornerStyle.rounded,
      borderRadius: context.lgBorderRadius,
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                AppText(
                  user.name,
                  style: context.textStyle.b2(weight: FontWeight.w500),
                ),
                const AppGap.y4(),
                AppText(
                  user.accountNumber,
                  style: context.textStyle.labelText(),
                ),
              ],
            ),
          ),
          AppAvatar(
            avatar: user.image,
            initials: user.initials,
            bgColor: context.raisedBtnBgColor,
          ),
        ],
      ),
    );
  }
}
