import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class SupportHeaderSection extends AppStatelessWidget {
  const SupportHeaderSection({super.key});

  @override
  Widget build(BuildContext context) {
    const users = [("TO", null), ("BM", null), ("TG", null)];
    final nameStyle = context.textStyle.b2(weight: FontWeight.w500);
    final helpColor = context.secondaryTextColor;
    final meService = locator<AppMeService>();
    final user = meService.cachedMeData ?? MeResponse.mock;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        Row(
          children: [
            const AppLogo(iconOnly: true, height: AppFontSizes.px40),
            const Spacer(),
            AppAvatarStack(users),
          ],
        ),
        const AppGap.y32(),
        AppText(user.name, style: nameStyle),
        AppText(
          LocaleKeys.howCanWeHelp.tr(),
          style: nameStyle.copyWith(color: helpColor),
        ),
      ],
    );
  }
}
