import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class ReferralInfoCard extends AppStatelessWidget {
  const ReferralInfoCard({super.key});

  @override
  Widget build(BuildContext context) {
    final amount = 10000.formattedCurrency;

    return AppCard(
      image: const DecorationImage(
        image: AssetImage(AppImages.pattern),
        fit: BoxFit.cover,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          AppText(
            LocaleKeys.inviteAndEarn.tr(),
            style: context.textStyle.h4(),
          ),
          const AppGap.y8(),
          AppText(
            "${LocaleKeys.earnAmountByReferral.tr({"amount": amount})}.",
            style: context.textStyle.labelText(),
          ),
          const AppGap.y16(),
          AppText(
            LocaleKeys.shareCode.tr(),
            style: context.textStyle.b4(weight: FontWeight.w500),
          ),
          const AppGap.y4(),
          const Align(
            alignment: Alignment.centerLeft,
            child: App<PERSON><PERSON><PERSON>ill(value: "D0EWHI"),
          ),
          const AppGap.y24(),
          const ReferralInstructionsSection(),
        ],
      ),
    );
  }
}
