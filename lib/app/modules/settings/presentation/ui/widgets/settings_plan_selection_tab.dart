import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class PlanSelectionTab extends AppStatelessWidget {
  final PlanType selection;
  final PlanType plan;
  final OnChanged<PlanType> onSelect;

  const PlanSelectionTab(
    this.plan, {
    required this.onSelect,
    required this.selection,
    super.key,
  });

  selectPlan() => onSelect(plan);

  @override
  Widget build(BuildContext context) {
    final isSelected = selection == plan;
    final text = LocaleKeys.tier.tr({"index": "${plan.idx}"});
    final minSize = Size.fromWidth(context.sp(AppFontSizes.px120));

    return AppAnimatedSlider(
      child: Builder(
        key: ValueKey(isSelected),
        builder: (context) {
          if (!isSelected) {
            return AppOutlineButton(
              onPressed: selectPlan,
              size: ButtonSize.small,
              variant: OutlineBtnVariant.neutral,
              minSize: minSize,
              text: text,
            );
          }

          return AppButton(
            onPressed: selectPlan,
            size: ButtonSize.small,
            variant: RaisedButtonVariant.c,
            minSize: minSize,
            text: text,
          );
        },
      ),
    );
  }
}
