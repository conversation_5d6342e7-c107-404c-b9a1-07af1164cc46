import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class ReferralInstructionsSection extends AppStatefulWidget {
  const ReferralInstructionsSection({super.key});

  @override
  State<StatefulWidget> createState() => _ReferralInstructionsSectionState();
}

class _ReferralInstructionsSectionState
    extends State<ReferralInstructionsSection> {
  final ValueNotifier<bool> _isExpanded = ValueNotifier(false);

  @override
  Widget build(BuildContext context) {
    final style = context.textStyle.b3(weight: FontWeight.w600);

    return InkWell(
      onTap: () {
        _isExpanded.value = !_isExpanded.value;
      },
      child: BoolListener(
        valueListenable: _isExpanded,
        builder: (isExpanded) {
          final icon =
              !isExpanded ? AppIcons.navArrowDown : AppIcons.navArrowUp;
          return Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              AppCard(
                color: context.scaffoldBgColor,
                borderRadius: context.lgBorderRadius,
                cornerStyle: CornerStyle.rounded,
                padding: context.insets.allSp(AppFontSizes.px12),
                child: Row(
                  children: [
                    AppOutlineButton(
                      onPressed: () {},
                      minSize: Size.fromWidth(
                        context.sp(AppFontSizes.px56),
                      ),
                      variant: OutlineBtnVariant.neutral,
                      size: ButtonSize.medium,
                      icon: const AppIcon(
                        AppIcons.infoCircle,
                        size: AppFontSizes.px24,
                      ),
                    ),
                    const AppGap.h16(),
                    Expanded(
                      child: AppText(
                        LocaleKeys.howDoesItWork.tr(),
                        style: style,
                      ),
                    ),
                    AppAnimatedSwitcher(
                      child: AppIcon(
                        icon,
                        key: ValueKey(isExpanded),
                        size: AppFontSizes.px24,
                      ),
                    ),
                  ],
                ),
              ),
              AppAnimatedSlider(
                child: Builder(
                  key: ValueKey(isExpanded),
                  builder: (_) {
                    if (!isExpanded) return const Offstage();
                    return const ReferralInstructionsList();
                  },
                ),
              )
            ],
          );
        },
      ),
    );
  }
}
