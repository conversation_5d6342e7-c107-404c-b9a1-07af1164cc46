import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class PlanFeatureCard extends AppStatelessWidget {
  final ValueNotifier<PlanType> plan;
  final OnChanged<PlanType> onSelect;

  const PlanFeatureCard(
    this.plan, {
    super.key,
    required this.onSelect,
  });

  @override
  Widget build(BuildContext context) {
    return GenericListener(
      valueListenable: plan,
      builder: (selection) {
        return AppCard(
          child: AppAnimatedSlider(
            axis: Axis.horizontal,
            child: Column(
              key: ValueKey(selection),
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                AppText(
                  LocaleKeys.tierFeatures.tr(),
                  style: context.textStyle.h5(),
                ),
                const AppGap.y32(),
                for (final feature in selection.features)
                  PlanFeatureTile(
                    feature,
                    key: Value<PERSON><PERSON>(feature),
                  ),
                AppButton(
                  onPressed: () {
                    onSelect(selection);
                  },
                  text: LocaleKeys.getYourPlan.tr(),
                )
              ],
            ),
          ),
        );
      },
    );
  }
}
