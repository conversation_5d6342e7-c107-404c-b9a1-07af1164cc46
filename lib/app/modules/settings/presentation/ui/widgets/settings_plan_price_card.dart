import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class PlanPriceCard extends AppStatelessWidget {
  final ValueNotifier<PlanType> plan;

  const PlanPriceCard(this.plan, {super.key});

  @override
  Widget build(BuildContext context) {
    return GenericListener(
      valueListenable: plan,
      builder: (selection) {
        return AppCard(
          color: context.raisedBtnBBgColor,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: AppText(
                      selection.name.ctr(),
                      style: context.textStyle.h5(
                        color: context.raisedBtnTextColor,
                      ),
                    ),
                  ),
                  AppButton(
                    onPressed: () {},
                    text: LocaleKeys.active.ctr(),
                    size: ButtonSize.small,
                    borderColor: context.disabledBtnColor,
                    icon: AppIcons.infoCircle,
                  )
                ],
              ),
              const AppGap.y20(),
              AppText(
                LocaleKeys.pricePerMonth.tr(
                  {"price": selection.price.formattedCurrency},
                ),
                style: context.textStyle.b3(color: context.inputBorderColor),
              )
            ],
          ),
        );
      },
    );
  }
}
