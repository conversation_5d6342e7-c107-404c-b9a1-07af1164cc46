import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class SettingsProfileTable extends AppStatelessWidget {
  final MeResponse user;

  const SettingsProfileTable(this.user, {super.key});

  @override
  Widget build(BuildContext context) {
    return AppCard(
      padding: context.insets.symmetricSp(
        horizontal: AppFontSizes.px24,
        vertical: AppFontSizes.px16,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        mainAxisSize: MainAxisSize.min,
        children: [
          SettingsProfileTableRow(
            LocaleKeys.firstName.tr(),
            value: user.firstName ?? "",
          ),
          SettingsProfileTableRow(
            LocaleKeys.lastName.tr(),
            value: user.lastName ?? "",
          ),
          SettingsProfileTableRow(
            LocaleKeys.mobileNumber.tr(),
            value: "****** 564 1243",
          ),
          SettingsProfileTableRow(
            LocaleKeys.emailAddress.tr(),
            value: user.email ?? "",
          ),
        ],
      ),
    );
  }
}

class SettingsProfileAccountDetailsTable extends AppStatelessWidget {
  final MeResponse user;

  const SettingsProfileAccountDetailsTable(this.user, {super.key});

  @override
  Widget build(BuildContext context) {
    return AppCard(
      padding: context.insets.symmetricSp(
        horizontal: AppFontSizes.px24,
        vertical: AppFontSizes.px16,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        mainAxisSize: MainAxisSize.min,
        children: [
          SettingsProfileTableRow(
            LocaleKeys.day1AcctNumber.tr(),
            colSpan: (2, 1),
            value: user.accountNumber ?? "",
            trailing: AppCopyButton(
              value: user.accountNumber.value,
            ),
          ),
          SettingsProfileTableRow(
            LocaleKeys.accountTier.tr(),
            value: (user.plan?.name ?? "").tr(),
            onTap: () {
              AppRouter.pushNamed(SettingsRoutes.plans);
            },
            trailing: AppPill(
              LocaleKeys.upgrade.tr(),
              textColor: context.errorColor,
              bgColor: context.errorNotificationBackgroundColor,
              trailing: AppIcon(
                AppIcons.navArrowRight,
                size: AppFontSizes.px16,
                color: context.errorColor,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
