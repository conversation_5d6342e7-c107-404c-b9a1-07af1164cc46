import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class SupportActionTile extends AppStatelessWidget {
  final String text;
  final IconData? icon;
  final EdgeInsetsGeometry? padding;
  final OnPressed? onTap;

  const SupportActionTile(
    this.text, {
    this.icon,
    this.padding,
    this.onTap,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Padding(
        padding: padding ?? context.insets.allSp(AppFontSizes.px16),
        child: Row(
          children: [
            Expanded(
              child: AppText(
                text,
                style: context.textStyle.b4(weight: FontWeight.w500),
              ),
            ),
            const AppGap.h16(),
            AppIcon(
              icon ?? AppIcons.navArrowRight,
              size: AppFontSizes.px18,
            )
          ],
        ),
      ),
    );
  }
}
