import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class SupportHelpSection extends AppStatelessWidget {
  const SupportHelpSection({super.key});

  @override
  Widget build(BuildContext context) {
    return AppCard(
      padding: EdgeInsets.zero,
      borderRadius: context.xlBorderRadius,
      child: Column(
        children: [
          const AppGap.y4(),
          SupportActionTile(
            LocaleKeys.message.tr(),
            icon: AppIcons.messageText,
          ),
          Padding(
            padding: context.insets.symmetricSp(
              horizontal: AppFontSizes.px16,
            ),
            child: const AppDivider(
              size: AppDividerSize.xSmall,
            ),
          ),
          SupportActionTile(
            LocaleKeys.help.tr(),
            icon: AppIcons.questionMark,
          ),
          const AppGap.y4(),
        ],
      ),
    );
  }
}
