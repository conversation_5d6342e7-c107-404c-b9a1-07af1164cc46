import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class PlanSelectionTabbar extends AppStatelessWidget {
  final PlanType selection;
  final OnChanged<PlanType> onSelect;

  const PlanSelectionTabbar(
    this.selection, {
    required this.onSelect,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    List<Widget> tabs = [
      for (final plan in PlanType.values)
        Expanded(
          child: PlanSelectionTab(
            plan,
            onSelect: onSelect,
            selection: selection,
          ),
        )
    ];
    tabs = tabs.intersperse(const AppGap.h4());
    return AppCard(
      padding: context.insets.allSp(AppFontSizes.px8),
      borderRadius: context.btnBorderRadius,
      cornerStyle: CornerStyle.rounded,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: tabs,
      ),
    );
  }
}
