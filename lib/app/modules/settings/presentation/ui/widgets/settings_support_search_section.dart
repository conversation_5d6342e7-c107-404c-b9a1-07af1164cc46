import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class SupportSearchSection extends AppStatelessWidget {
  const SupportSearchSection({super.key});

  @override
  Widget build(BuildContext context) {
    return AppCard(
      padding: context.insets.allSp(AppFontSizes.px12),
      borderRadius: context.xxlBorderRadius,
      shadows: context.shadows.small,
      child: Column(
        children: [
          AppCard(
            borderRadius: context.xlBorderRadius,
            padding: EdgeInsets.zero,
            color: context.scaffoldBgColor,
            child: SupportActionTile(
              LocaleKeys.searchForHelp.tr(),
              icon: AppIcons.search,
            ),
          ),
          SupportActionTile(LocaleKeys.whatIsServiceFee.tr()),
          SupportActionTile(LocaleKeys.howDoIWithDrawFunds.tr()),
          SupportActionTile(LocaleKeys.refundPolicy.tr()),
        ],
      ),
    );
  }
}
