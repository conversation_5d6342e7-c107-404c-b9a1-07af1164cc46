import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class ReferralEarningsCard extends AppStatelessWidget {
  final num earnings;

  const ReferralEarningsCard(this.earnings, {super.key});

  @override
  Widget build(BuildContext context) {
    final color = context.raisedBtnTextColor;
    final titleStyle = context.textStyle.b2(
      weight: FontWeight.w500,
      color: color,
    );

    return AppCard(
      color: context.raisedBtnBBgColor,
      padding: context.insets.allSp(AppFontSizes.px24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Row(
            children: [
              AppIcon(AppIcons.coins, color: color),
              const AppGap.h8(),
              Expanded(
                child: AppText(
                  LocaleKeys.totalEarnings.tr(),
                  style: titleStyle,
                ),
              ),
              const AppGap.h8(),
              AppText(
                earnings.formattedCurrency,
                style: titleStyle.copyWith(color: context.raisedBtnBgColor),
              )
            ],
          ),
          const AppGap.y8(),
          const ReferralRecordCard(),
        ],
      ),
    );
  }
}
