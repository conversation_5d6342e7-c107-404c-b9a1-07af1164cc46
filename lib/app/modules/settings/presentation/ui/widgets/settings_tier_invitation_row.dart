import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class SettingsTierInvitationRow extends AppStatelessWidget {
  const SettingsTierInvitationRow({super.key});

  @override
  Widget build(BuildContext context) {
    final meService = locator<AppMeService>();
    final user = meService.cachedMeData ?? MeResponse.mock;
    final plan = user.plan ?? PlanType.tier1;
    return IntrinsicHeight(
      child: Row(
        children: [
          Expanded(
            flex: 55,
            child: SettingsTierInvitationCard(
              title: LocaleKeys.tier.tr({"index": "${plan.idx}"}),
              icon: AppIcons.creditCard,
              subTitle: plan.name.tr(),
              onTap: () {
                AppRouter.pushNamed(SettingsRoutes.plans);
              },
            ),
          ),
          const AppGap.h12(),
          Expanded(
            flex: 45,
            child: SettingsTierInvitationCard(
              title: LocaleKeys.inviteFriends.tr(),
              icon: AppIcons.plus,
              color: context.successCardColor,
              subtitleColor: context.highlightedTextColor,
              subTitle: LocaleKeys.getAmount.tr(
                {"amount": 10000.formattedCurrency},
              ),
              onTap: () {
                AppRouter.pushNamed(SettingsRoutes.referral);
              },
            ),
          ),
        ],
      ),
    );
  }
}
