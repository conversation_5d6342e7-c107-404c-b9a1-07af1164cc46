import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class SettingsSection extends AppStatelessWidget {
  final List<SettingsTileData> data;

  const SettingsSection(this.data, {super.key});

  @override
  Widget build(BuildContext context) {
    return AppCard(
      margin: context.insets.onlySp(bottom: AppFontSizes.px16),
      padding: context.insets.symmetricSp(
        horizontal: AppFontSizes.px32,
        vertical: AppFontSizes.px16,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [for (final info in data) SettingsSectionTile(info)],
      ),
    );
  }
}
