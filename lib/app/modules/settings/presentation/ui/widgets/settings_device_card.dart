import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class SettingsDeviceCard extends AppStatelessWidget {
  final UserDevice device;
  const SettingsDeviceCard(this.device, {super.key});

  @override
  Widget build(BuildContext context) {
    final status = device.isActive ? LocaleKeys.active : LocaleKeys.inActive;
    final statusTextcolor =
        device.isActive ? null : context.warningNotificationTextColor;
    final statusBgcolor = device.isActive
        ? context.notificationBackgroundColor
        : context.warningNotificationBackgroundColor;

    return AppCard(
      color: context.scaffoldBgColor,
      padding: context.insets.allSp(AppFontSizes.px16),
      borderRadius: context.xlBorderRadius,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Row(
            children: [
              AppIcon(
                device.icon,
                alignment: Alignment.centerLeft,
              ),
              const AppGap.h4(),
              Expanded(
                child: AppText(
                  device.name,
                  style: context.textStyle.b3(weight: FontWeight.w600),
                ),
              ),
              const AppGap.h16(),
              App<PERSON><PERSON>(
                status.tr(),
                bgColor: statusBgcolor,
                textColor: statusTextcolor,
              )
            ],
          ),
          const AppGap.h16(),
          AppText(
            device.imei,
            style: context.textStyle.b4(
              color: context.disabledBtntextColor.withOpacity(.8),
            ),
          )
        ],
      ),
    );
  }
}
