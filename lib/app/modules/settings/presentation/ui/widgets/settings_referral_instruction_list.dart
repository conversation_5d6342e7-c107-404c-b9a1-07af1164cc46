import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class ReferralInstructionsList extends AppStatelessWidget {
  const ReferralInstructionsList({super.key});

  @override
  Widget build(BuildContext context) {
    final tilePadding = context.insets.onlySp(bottom: AppFontSizes.px28);
    return AppCard(
      margin: context.insets.onlySp(top: AppFontSizes.px12),
      color: context.scaffoldBgColor,
      padding: context.insets.symmetricSp(
        horizontal: AppFontSizes.px24,
        vertical: AppFontSizes.px32,
      ),
      borderRadius: context.xxlBorderRadius,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          IconRowTile(
            icon: AppIcons.securityPass,
            title: LocaleKeys.shareYourCode.tr(),
            description: LocaleKeys.startBySharingYourCode.tr(),
            padding: tilePadding,
          ),
          IconRowTile(
            icon: AppIcons.securityPass,
            title: LocaleKeys.friendsSignUp.tr(),
            description: LocaleKeys.whenFriendsSignupTheySelectTier.tr(),
            padding: tilePadding,
          ),
          IconRowTile(
            icon: AppIcons.securityPass,
            title: LocaleKeys.earnRewards.tr(),
            description: LocaleKeys.youGetAmountAfterTheySelectTier.tr(),
            padding: EdgeInsets.zero,
          ),
        ],
      ),
    );
  }
}
