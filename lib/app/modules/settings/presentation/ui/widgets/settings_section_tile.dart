import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class SettingsSectionTile extends AppStatelessWidget {
  final SettingsTileData data;

  const SettingsSectionTile(this.data, {super.key});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        if (data.route != null) {
          AppRouter.pushNamed(
            data.route?.name ?? "",
            arguments: data.route?.arguments,
          );
          return;
        }
        if (data.onTap != null) data.onTap?.call();
      },
      child: Padding(
        padding: context.insets.symmetricSp(
          horizontal: AppFontSizes.px8,
          vertical: AppFontSizes.px12,
        ),
        child: Row(
          children: [
            Padding(
              padding: context.insets.allSp(AppFontSizes.px7),
              child: AppIcon(
                data.icon,
                size: AppFontSizes.px24,
              ),
            ),
            const AppGap.h16(),
            Expanded(
              child: AppText(
                data.label.tr(),
                style: context.textStyle.b2(weight: FontWeight.w500),
              ),
            ),
            if (data.trailing != null) ...[const AppGap.h16(), data.trailing!]
          ],
        ),
      ),
    );
  }
}
