import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class SettingsProfileTableRow extends AppStatelessWidget {
  final String label;
  final String value;
  final Widget? trailing;
  final OnPressed? onTap;
  final (int, int) colSpan;

  const SettingsProfileTableRow(
    this.label, {
    required this.value,
    this.trailing,
    this.onTap,
    this.colSpan = (1, 2),
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Padding(
        padding: context.insets.symmetricSp(vertical: AppFontSizes.px8),
        child: Row(
          children: [
            Expanded(
              flex: colSpan.$1,
              child: AppText(
                label,
                style: context.textStyle.b3(
                  color: context.textColor.withOpacity(.5),
                ),
              ),
            ),
            Expanded(
              flex: colSpan.$2,
              child: Row(
                children: [
                  Expanded(child: AppText(value, textAlign: TextAlign.end)),
                  if (trailing != null) ...[const AppGap.h8(), trailing!]
                ],
              ),
            )
          ],
        ),
      ),
    );
  }
}
