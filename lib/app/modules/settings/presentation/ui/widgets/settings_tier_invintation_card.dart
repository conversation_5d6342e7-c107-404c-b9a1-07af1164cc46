import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class SettingsTierInvitationCard extends AppStatelessWidget {
  final OnPressed? onTap;
  final String title;
  final String subTitle;
  final IconData icon;
  final Color? color;
  final Color? subtitleColor;

  const SettingsTierInvitationCard({
    super.key,
    required this.title,
    required this.icon,
    required this.subTitle,
    this.color,
    this.onTap,
    this.subtitleColor,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: AppCard(
        color: color,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            AppIcon(
              icon,
              size: AppFontSizes.px24,
            ),
            const AppGap.y24(),
            AppText(
              title,
              style: context.textStyle.b2(weight: FontWeight.w500),
            ),
            AppText(
              subTitle,
              style: context.textStyle.b3(
                color: subtitleColor ?? context.disabledBtntextColor,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
