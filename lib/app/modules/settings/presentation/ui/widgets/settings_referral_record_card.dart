import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class ReferralRecordCard extends AppStatelessWidget {
  const ReferralRecordCard({super.key});

  @override
  Widget build(BuildContext context) {
    final style = context.textStyle.b4(weight: FontWeight.w500);

    return AppCard(
      color: context.scaffoldBgColor,
      borderRadius: context.xxlBorderRadius,
      padding: context.insets.allSp(AppFontSizes.px16),
      child: Row(
        children: [
          Expanded(
            child: AppText(LocaleKeys.referralRecord.tr(), style: style),
          ),
          const AppGap.h8(),
          const AppIcon(AppIcons.navArrowRight, size: AppFontSizes.px24)
        ],
      ),
    );
  }
}
