import 'package:day1/day1.dart';

void registerSettingsDI(DoAppConfig config) {
  ///[Services]
  locator.registerFactory<SettingsService>(() {
    return SettingsHttpService(locator());
  });

  ///[State]
  locator.registerLazySingleton<SettingsState>(() => SettingsState());
  locator.registerLazySingleton<SettingsPlanState>(() => SettingsPlanState());
  locator.registerLazySingleton<SettingsProfileState>(() => SettingsProfileState(locator()));
}

void resetSettingsDI() {
  locator<SettingsState>().reset();
  locator<SettingsPlanState>().reset();
  locator<SettingsProfileState>().reset();
}
