import 'package:day1/day1.dart';

void registerModulesDI(DoAppConfig config) {
  registerLauncherDI(config);
  registerAuthDI(config);
  registerOnboardingDI(config);
  registerDashboardDI(config);
  registerAccountDI(config);
  registerSignupDI(config);
  registerKycDI(config);
  registerPaymentDI(config);
  registerSettingsDI(config);
  registerPhysicalcardDI(config);
  registerTransactionsDI(config);
}

void resetModulesDI() {
  resetLauncherDI();
  resetAuthDI();
  resetOnboardingDI();
  resetDashboardDI();
  resetAccountDI();
  resetSignupDI();
  resetKycDI();
  resetPaymentDI();
  resetSettingsDI();
  resetPhysicalcardDI();
  resetTransactionsDI();
}
