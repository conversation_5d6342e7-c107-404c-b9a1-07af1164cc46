import 'package:day1/day1.dart';

class LauncherState extends StateModel with AppTaskMixin {
  bool _hasFetchedAppData = false;

  final AppSessionService _sessionService;
  final AppMeService _meService;
  final AppDataService _appService;

  LauncherState(this._sessionService, this._meService, this._appService);

  Future<bool> _getAppData() async {
    final data = await Future.wait(
      [_meService.getMeData(useCache: true), _appService.getAllBaseData()],
    );
    final hasError = data.any((it) => it.hasError);

    _hasFetchedAppData = !hasError;

    return _hasFetchedAppData;
  }

  Future<String> get _loggedInRoute async {
    if (!_hasFetchedAppData) await _getAppData();
    if (!_sessionService.hasCompletedOnboarding && _hasFetchedAppData) {
      return _routeForPartialOnboarding;
    }
    return _hasFetchedAppData ? DashboardRoutes.home : _loggedOutRoute;
  }

  String get _loggedOutRoute {
    final canBioLogin = _sessionService.allowsBiometricAuth;
    final canPinLogin = _sessionService.allowsPinAuth;

    if (canBioLogin && canPinLogin) return AuthRoutes.pin;
    return AuthRoutes.login;
  }

  Future<String> getLicitRoute() async {
    return await runThrowableTask(
      () async {
        if (_sessionService.isLoggedIn) return await _loggedInRoute;
        if (!_sessionService.hasSession) return OnboardingRoutes.slides;
        return _loggedOutRoute;
      },
      onError: () => _loggedOutRoute,
    );
  }

  String get _routeForPartialOnboarding {
    final state = _sessionService.onboardingState.value;
    final step = OnboardingStep.fromString(state);

    return step.nextRoutePath;
  }

  gotoRoute(String? path) async {
    if (AppRouter.currentRoute.hasValue) return;
    if (!path.hasValue) {
      AppRouter.pushAndRemoveUntil(_loggedOutRoute);
      return;
    }
    AppRouter.pushAndRemoveUntil(path!);
  }
}
