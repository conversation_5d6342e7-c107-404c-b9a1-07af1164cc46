import 'package:day1/day1.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

class LaunchScreen extends AppStatefulWidget {
  final String? initialLink;
  const LaunchScreen({super.key, this.initialLink});

  @override
  State<StatefulWidget> createState() => _LaunchScreenState();
}

class _LaunchScreenState extends State<LaunchScreen> {
  late final LauncherState _state;
  late Future<String> _routeTask;

  @override
  void initState() {
    super.initState();
    _state = context.read<LauncherState>();
    _routeTask = _state.getLicitRoute();
    // WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
    //   locator<AppFcmService>().initialiseMessaging();
    // });
  }

  @override
  Widget build(BuildContext context) {
    final textColor = context.appleBtnTextColor;

    return Scaffold(
      body: Padding(
        padding: context.insets.defaultAllInsets,
        child: <PERSON><PERSON><PERSON>(
          bottom: false,
          child: FutureBuilder(
            future: _routeTask,
            builder: (_, task) {
              if (task.hasData) {
                WidgetsBinding.instance.addPostFrameCallback((_) {
                  _state.gotoRoute(task.data);
                });
              }
              return Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  const AppGap.y24(),
                  Expanded(
                    child: AppCard(
                      color: context.raisedBtnBgColor,
                      child: const Center(
                        child: AppSvg(AppVectors.logoIcon, height: 124),
                      ),
                    ),
                  ),
                  const AppGap.y12(),
                  AppCard(
                    color: context.raisedBtnCBgColor,
                    child: Padding(
                      padding: context.insets.symmetricSp(
                        vertical: AppFontSizes.px16,
                      ),
                      child: Row(
                        children: [
                          Expanded(
                            child: AppText(
                              LocaleKeys.standWhenLanded.tr(),
                              style: context.textStyle.h4(color: textColor),
                            ),
                          ),
                          AppIcon(AppIcons.star, color: textColor)
                        ],
                      ),
                    ),
                  )
                ],
              );
            },
          ),
        ),
      ),
    );
  }
}
