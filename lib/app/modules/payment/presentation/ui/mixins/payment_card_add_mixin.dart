// ignore_for_file: use_build_context_synchronously

import 'package:flutter/material.dart';
import 'package:day1/day1.dart';

mixin PaymentCardAddMixin<T extends PaymentCardAddScreen> on State<T> {
  late final ValueNotifier<bool> formStateEmitter;

  ///Key
  final GlobalKey<FormState> formKey = GlobalKey<FormState>();

  //Controller
  late final TextEditingController numCtrl;
  late final TextEditingController expiryCtrl;
  late final TextEditingController cvvCtrl;
  late final ValueNotifier<bool> doSave;

  @override
  void initState() {
    super.initState();

    numCtrl = TextEditingController();
    expiryCtrl = TextEditingController();
    cvvCtrl = TextEditingController();
    doSave = ValueNotifier(true);

    _trackValidity();

    formStateEmitter = ValueNotifier(_formValidityStatus());
  }

  @override
  void dispose() {
    numCtrl.dispose();
    cvvCtrl.dispose();
    expiryCtrl.dispose();
    formStateEmitter.dispose();
    super.dispose();
  }

  _trackValidity() {
    numCtrl.addListener(() {
      formStateEmitter.value = _formValidityStatus();
    });
    expiryCtrl.addListener(() {
      formStateEmitter.value = _formValidityStatus();
    });
    cvvCtrl.addListener(() {
      formStateEmitter.value = _formValidityStatus();
    });
  }

  bool _formValidityStatus() {
    final numIsValid =
        AppValidators.minLength(numCtrl.textValue, length: 9) == null;
    final expiryIsValid =
        AppValidators.length(expiryCtrl.textValue, length: 5) == null;
    final cvvIsValid = AppValidators.length(cvvCtrl.textValue) == null;

    return numIsValid && expiryIsValid && cvvIsValid;
  }

  submit() async {
    if (!context.validateForm(formKey)) return;
     AppRouter.pushNamed(PaymentRoutes.cardRedirect);
  }
}
