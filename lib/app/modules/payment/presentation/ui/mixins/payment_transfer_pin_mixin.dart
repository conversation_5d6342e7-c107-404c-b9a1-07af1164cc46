import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

mixin TransferPinMixin<T extends PaymentTransferPinScreen> on State<T> {
  late final ValueNotifier<bool> formStateEmitter;

  ///Key
  final GlobalKey<FormState> formKey = GlobalKey<FormState>();

  //Controller
  late final TextEditingController otpCtrl;

  final otpFocus = FocusNode();

  String get title => LocaleKeys.enterPin.tr();

  String get header => "PIN";

  String get rider => LocaleKeys.completeTransferByPinEntry.tr();

  PaymentsTransferArgument get argument => widget.argument;

  @override
  void initState() {
    super.initState();

    otpCtrl = TextEditingController();

    _trackValidity();

    formStateEmitter = ValueNotifier(_formValidityStatus());
  }

  @override
  void dispose() {
    formStateEmitter.dispose();
    otpCtrl.dispose();
    super.dispose();
  }

  _trackValidity() {
    otpCtrl.addListener(() {
      formStateEmitter.value = _formValidityStatus();
    });
  }

  bool _formValidityStatus() {
    return AppValidators.minLength(otpCtrl.text, length: 4) == null;
  }

  _goToSuccess([Transaction? transaction]) {
    AppRouter.pushAndRemoveUntil(
      PaymentRoutes.transferSuccess,
      arguments: argument.copyWith(transaction: transaction),
    );
  }

  submit([TransferAuthType auth = TransferAuthType.pin]) async {
    if (auth != TransferAuthType.bio && !context.validateForm(formKey)) return;

    _makeTransfer(auth);
  }

  _makeTransfer(TransferAuthType auth) {
    locator<PaymentTransferState>().makeTransfer(
      widget.argument,
      auth: auth,
      onSuccess:  _goToSuccess,
      onError: context.showErrorNotification,
      pin: otpCtrl.text,
    );
  }
}
