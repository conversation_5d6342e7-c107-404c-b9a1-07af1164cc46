import 'package:flutter/material.dart';
import 'package:day1/day1.dart';

mixin PaymentFundTransferMixin<T extends PaymentFundTransferTab> on State<T> {
  String lastTerm = "";
  late final ValueNotifier<bool> formStateEmitter;
  late final ValueNotifier<num> fxEstimate = ValueNotifier(0);
  late final ValueNotifier<FutureData<TransferRecipient>> recipient =
      ValueNotifier(const FutureData.pristine());

  ///Key
  final GlobalKey<FormState> formKey = GlobalKey<FormState>();

  final ValueNotifier<Bank?> selectedBank = ValueNotifier(null);
  final ValueNotifier<FutureListData<PredictedBank>> predictedBanks =
      ValueNotifier(const FutureListData.pristine());

  //Controller
  final TextEditingController acctNumField = TextEditingController();
  final TextEditingController amountField = TextEditingController();

  final amountFocus = FocusNode();
  final acctNumFocus = FocusNode();

  BankState get _state => locator<BankState>();

  @override
  void initState() {
    super.initState();
    amountField.addListener(_amountListener);
    acctNumField.addListener(_accountListener);
    predictedBanks.addListener(_predictionListener);
    recipient.addListener(_recipientListener);
    selectedBank.addListener(_bankListener);
    formStateEmitter = ValueNotifier(_formValidityStatus());
  }

  _predictionListener() {
    formStateEmitter.value = _formValidityStatus();
    if (predictedBanks.value.hasData) {
      context.resetFocus();
    }
  }

  _recipientListener() {
    formStateEmitter.value = _formValidityStatus();
  }

  _bankListener() {
    formStateEmitter.value = _formValidityStatus();
    if (!acctNumField.hasValue || selectedBank.value == null) return;
    _determineRecipient(selectedBank.value?.code ?? "");
  }

  _amountListener() {
    formStateEmitter.value = _formValidityStatus();
    final amount = amountField.text.asAmount ?? 0;
    final convertedAmount = amount / 1600;
    WidgetsBinding.instance.addPostFrameCallback((_) {
      fxEstimate.value = convertedAmount;
    });
  }

  _accountListener() {
    formStateEmitter.value = _formValidityStatus();
  }

  bool get _termIsUnchanged => lastTerm == acctNumField.text;
  bool get _hasPrediction => predictedBanks.value.hasData;

  getPredictions() {
    if (_termIsUnchanged && _hasPrediction) return;
    WidgetsBinding.instance.addPostFrameCallback((_) {
      lastTerm = acctNumField.text.trim();
      _state.getBankPredictions(predictedBanks, lastTerm);
    });
  }

  clearPrediction() {
    if (!predictedBanks.value.hasData) return;
    predictedBanks.value = const FutureListData.pristine();
  }

  clearRecipient() {
    if (!recipient.value.hasData) return;
    recipient.value = const FutureData.pristine();
  }

  selectPrediction(PredictedBank selection) {
    selectedBank.value = selection.bank;
    clearPrediction();
    _determineRecipient(selection.bank.code);
  }

  _determineRecipient(String bankCode) async {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final state = locator<PaymentRecipientState>();
      state.getRecipient(
        recipient,
        accountNumber: acctNumField.text,
        bankCode: bankCode,
        onError: context.showErrorNotification,
      );
    });
  }

  @override
  void dispose() {
    acctNumField.removeListener(_accountListener);
    amountField.removeListener(_amountListener);
    predictedBanks.removeListener(_predictionListener);

    super.dispose();
  }

  bool _formValidityStatus() {
    final acctIsValid =
        AppValidators.length(acctNumField.text, length: 10) == null;
    final ngnIsValid =
        AppValidators.amountValidator(amountField.text, minAmount: 0.1) == null;
    final hasBank = selectedBank.value != null;
    final hasRecipient = recipient.value.hasData;

    return ngnIsValid && acctIsValid && hasBank && hasRecipient;
  }

  submit() async {
    if (!context.validateForm(formKey)) return;
    AppRouter.pushNamed(
      PaymentRoutes.transferReview,
      arguments: PaymentsTransferArgument(
        fxValue: fxEstimate.value,
        ngValue: amountField.text.asAmount ?? 0,
        recipient: recipient.value.data!,
      ),
    );
  }
}
