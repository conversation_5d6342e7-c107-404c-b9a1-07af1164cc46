import 'package:flutter/material.dart';
import 'package:day1/day1.dart';

mixin PaymentAccountFundingMixin<T extends PaymentAddFundTab> on State<T> {
  late final ValueNotifier<bool> formStateEmitter;

  ///Key
  final GlobalKey<FormState> formKey = GlobalKey<FormState>();

  late final ValueNotifier<CardResponse?> _selectedCard;

  //Controller
  final TextEditingController usdField = TextEditingController();
  final TextEditingController ngnField = TextEditingController();

  final usdFocus = FocusNode();
  final ngnFocus = FocusNode();

  @override
  void initState() {
    super.initState();
    _selectedCard = _cardState.selectedCard;
    usdField.addListener(_usdListener);
    ngnField.addListener(_ngnListener);
    _selectedCard.addListener(_cardListener);

    formStateEmitter = ValueNotifier(_formValidityStatus());
  }

  _usdListener() {
    formStateEmitter.value = _formValidityStatus();
    if (ngnFocus.hasFocus) return;
    final amount = usdField.text.asAmount ?? 0;
    final convertedAmount = amount * 1600;
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ngnField.text = amount == 0 ? '' : convertedAmount.formattedNumberLong;
    });
  }

  _cardListener() {
    formStateEmitter.value = _formValidityStatus();
  }

  _ngnListener() {
    formStateEmitter.value = _formValidityStatus();
    if (usdFocus.hasFocus) return;
    final amount = ngnField.text.asAmount ?? 0;
    final convertedAmount = amount / 1600;
    WidgetsBinding.instance.addPostFrameCallback((_) {
      usdField.text = amount == 0 ? '' : convertedAmount.formattedNumberLong;
    });
  }

  @override
  void dispose() {
    usdField.removeListener(_usdListener);
    ngnField.removeListener(_ngnListener);
    _selectedCard.removeListener(_cardListener);

    super.dispose();
  }

  bool _formValidityStatus() {
    final usdIsValid =
        AppValidators.amountValidator(usdField.text, minAmount: 0.1) == null;
    final ngnIsValid =
        AppValidators.amountValidator(ngnField.text, minAmount: 0.1) == null;
    // final hasCard = _selectedCard.value != null;

    return ngnIsValid && usdIsValid;
  }

  submit() async {
    if (!context.validateForm(formKey)) return;
    AppRouter.pushNamed(
      PaymentRoutes.fundingConfirm,
      arguments: PaymentsFundingArgument(
        fxValue: usdField.text.asAmount ?? 0,
        ngValue: ngnField.text.asAmount ?? 0,
        fxRate: 1600,
        nextRoute: const RouteSettings(name: DashboardRoutes.home),
      ),
    );
  }

  PaymentMethodsState get _cardState => locator();
}
