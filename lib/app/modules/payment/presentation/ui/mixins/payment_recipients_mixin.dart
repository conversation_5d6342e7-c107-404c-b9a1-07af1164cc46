// ignore_for_file: use_build_context_synchronously

import 'package:flutter/material.dart';
import 'package:day1/day1.dart';

enum RecipientTab { all, favourites }

mixin PaymentRecipientsMixin<T extends PaymentRecipientsScreen> on State<T> {
  final ValueNotifier<RecipientTab> activeTab = ValueNotifier(RecipientTab.all);
  final GlobalKey<FormState> formKey = GlobalKey();
  //Controller
  late final TextEditingController queryCtrl;

  @override
  void initState() {
    super.initState();
    queryCtrl = TextEditingController();
  }

  @override
  void dispose() {
    queryCtrl.dispose();
    super.dispose();
  }

  PaymentRecipientsArguments get argument => widget.arguments;

  switchTab(RecipientTab tab) => activeTab.value = tab;

  selectRecipient(TransferRecipient recipient) {
    argument.onSelect(recipient);
    AppRouter.popView();
  }
}
