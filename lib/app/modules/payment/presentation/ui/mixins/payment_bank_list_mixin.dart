// ignore_for_file: use_build_context_synchronously

import 'package:flutter/material.dart';
import 'package:day1/day1.dart';
import 'package:provider/provider.dart';

mixin PaymentBankListMixin<T extends PaymentBankListScreen> on State<T> {
  final GlobalKey<FormState> formKey = GlobalKey();
  //Controller
  late final TextEditingController queryCtrl;

  @override
  void initState() {
    super.initState();
    context.read<BankState>().searchBank("");
    queryCtrl = TextEditingController();
  }

  @override
  void dispose() {
    queryCtrl.dispose();
    super.dispose();
  }

  PaymentBanklListArguments get argument => widget.arguments;
  Bank? get matchedBanks => argument.matchedBank;

  selectBank(Bank bank) {
    argument.onSelect(bank);
    AppRouter.popView();
  }
}
