import 'package:day1/day1.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

class PaymentMethodsScreen extends AppStatelessWidget {
  const PaymentMethodsScreen({super.key});

  _goHome() {
    if (AppRouter.canPop()) {
      AppRouter.popView();
    } else {
      AppRouter.pushAndRemoveUntil(DashboardRoutes.home);
    }
  }

  @override
  Widget build(BuildContext context) {
    final state = context.watch<PaymentMethodsState>();

    return Scaffold(
      appBar: DoAppBar(
        title: LocaleKeys.paymentMethod.tr(),
        leading: AppBackButton(routeStackSensitive: false, action: _goHome),
      ),
      body: ListenableBuilder(
        listenable: Listenable.merge([state.cards, state.selectedCard]),
        builder: (_, __) {
          final cards = state.cards.value.data;
          final selection = state.selectedCard.value;
          const trailer = PaymentMethodActionCardTrailer();

          return ListView(
            padding: context.insets.defaultAllInsets,
            children: [
              AppCard(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    AppFormHeader(
                      title: LocaleKeys.walletFundingMethod.tr(),
                      subTitle: LocaleKeys.availablePaymentMethods.tr(),
                    ),
                    ...[
                      for (final card in cards)
                        PaymentCardCard(
                          data: card,
                          selection: selection,
                          onSelect: (card) {
                            state.selectCard(card);
                            _goHome();
                          },
                        ),
                      PaymentMethodActionCard(
                        title: LocaleKeys.addCard.tr(),
                        subTitle: PaymentMethodActionCardSubtitle(
                          label: LocaleKeys.fundWithCard.tr(),
                        ),
                        prefixIcon: AppIcons.mastercardCard,
                        onPressed: () {
                          AppRouter.pushNamed(PaymentRoutes.addCard);
                        },
                        trailing: trailer,
                      ),
                      PaymentMethodActionCard(
                        title: LocaleKeys.addCard.tr(),
                        subTitle: PaymentMethodActionCardSubtitle(
                          label: LocaleKeys.fundWithPaypal.tr(),
                        ),
                        prefixIcon: AppIcons.paypal,
                        trailing: trailer,
                      )
                    ].intersperse(const AppGap.y24()),
                  ],
                ),
              ),
            ],
          );
        },
      ),
    );
  }
}
