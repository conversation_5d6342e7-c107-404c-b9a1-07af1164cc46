import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class PaymentSuccessScreen extends AppStatelessWidget {
  final PaymentsFundingArgument? argument;
  const PaymentSuccessScreen({
    super.key,
    this.argument,
  });

  @override
  Widget build(BuildContext context) {
    final description = argument == null
        ? LocaleKeys.welldoneUser.tr({"name": "N6 Team"})
        : null;

    String title = LocaleKeys.cardAddedSuccessfully.tr();

    if (argument?.ngValue != null) {
      title = LocaleKeys.youAddedAmountToWallet.tr(
        {"amount": argument?.ngValue.formattedCurrency ?? ""},
      );
    }

    return Scaffold(
      appBar: DoAppBar(title: LocaleKeys.fund_wallet.tr()),
      body: SingleChildScrollView(
        child: PaymentStatusCard(
          title: title,
          decription: description,
          btnText: argument != null ? LocaleKeys.done.tr() : null,
          action: () {
            locator<PaymentWalletState>().fetchBalance();
            AppRouter.pushAndRemoveUntil(
              argument?.nextRoute?.name ?? PaymentRoutes.methods,
              arguments: argument?.nextRoute?.arguments,
            );
          },
        ),
      ),
    );
  }
}
