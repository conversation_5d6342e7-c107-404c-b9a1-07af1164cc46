import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class PaymentScreen extends AppStatefulWidget {
  const PaymentScreen({super.key});

  @override
  State<PaymentScreen> createState() => _PaymentScreenState();
}

class _PaymentScreenState extends State<PaymentScreen> with AppModalMixin {
  final ValueNotifier<int> tab = ValueNotifier(0);

  @override
  Widget build(BuildContext context) {
    final title = LocaleKeys.money.tr();
    final appbar = DoAppBar(
      leading: AppAvatar(
        initials: "HK",
        defaultAvatarColor: context.cardColor,
        alignment: Alignment.centerLeft,
      ),
      title: title,
      trailing: AppOutlineButton(
        onPressed: () {},
        icon: const AppIcon(AppIcons.bellNotification),
        size: ButtonSize.medium,
        variant: OutlineBtnVariant.neutral,
      ),
    );
    return Scaffold(
      body: CustomScrollView(
        slivers: [
          SliverResizingHeader(
            child: appbar,
          ),
          SliverPadding(
            padding: context.insets.defaultAllInsets.add(
              context.insets.onlySp(bottom: AppFontSizes.px100),
            ),
            sliver: SliverToBoxAdapter(
              child: PaymentBody(
                headerChild: const PaymentUserBalanceTable(),
                contentChild: Padding(
                  padding: context.insets.symmetricSp(
                    horizontal: AppFontSizes.px12,
                    vertical: AppFontSizes.px24,
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      Padding(
                        padding: context.insets.symmetricSp(
                          horizontal: AppFontSizes.px22,
                        ),
                        child: NumberListener(
                          valueListenable: tab,
                          builder: (activeTab) {
                            return PaymentButtonTabbar(
                              onTabSelect: (value) {
                                tab.value = value;
                              },
                              tabs: [
                                PaymentPrimaryButtonTab(
                                  index: 0,
                                  activeIndex: activeTab,
                                  label: LocaleKeys.fund_wallet.tr(),
                                  icon: AppIcons.arrowDownCircle,
                                ),
                                PaymentPrimaryButtonTab(
                                  index: 1,
                                  activeIndex: activeTab,
                                  label: LocaleKeys.send_money.tr(),
                                  icon: AppIcons.arrowUpRightCircle,
                                ),
                              ],
                            );
                          },
                        ),
                      ),
                      NumberListener(
                        valueListenable: tab,
                        builder: (index) {
                          if (index != 1) {
                            return const PaymentAddFundTab(
                              key: Key("payment_add_fund_tab"),
                            );
                          }
                          return const PaymentFundTransferTab(
                            key: Key("payment_fund_transfer_tab"),
                          );
                        },
                      )
                    ],
                  ),
                ),
              ),
            ),
          )
        ],
      ),
    );
  }
}
