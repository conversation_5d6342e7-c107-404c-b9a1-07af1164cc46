import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class PaymentTransferSuccessScreen extends AppStatefulWidget {
  final PaymentsTransferArgument argument;
  const PaymentTransferSuccessScreen(this.argument, {super.key});

  @override
  State<StatefulWidget> createState() => _PaymentTransferSuccessScreenState();
}

class _PaymentTransferSuccessScreenState
    extends State<PaymentTransferSuccessScreen> {
  late final PaymentsTransferArgument argument;
  late final ValueNotifier<bool> saveRecipient;

  @override
  void initState() {
    super.initState();
    argument = widget.argument;
    saveRecipient = ValueNotifier(false);
  }

  String get amount => argument.ngValue.formattedCurrency;
  String get name => argument.recipient.accountName ?? "";

  @override
  Widget build(BuildContext context) {
    final title = LocaleKeys.paymentSuccessful.tr();
    final subTitle = LocaleKeys.youSentFundToUserSuccesfully.tr({
      "amount": amount,
      "user": name,
    });

    return Scaffold(
      appBar: DoAppBar(
        title: title,
        leading: AppCloseButton(onTap: () {
          AppRouter.pushAndRemoveUntil(DashboardRoutes.home);
        }),
      ),
      body: SingleChildScrollView(
        child: PaymentStatusCard(
          title: "$title!",
          decription: subTitle,
          btnText: LocaleKeys.shareReceipt.tr(),
          action: () {
            AppRouter.pushNamed(
              PaymentRoutes.transferReceipt,
              arguments: argument,
            );
          },
        ),
      ),
    );
  }
}
