import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class PaymentTransferReceiptScreen extends AppStatelessWidget {
  final PaymentsTransferArgument argument;
  const PaymentTransferReceiptScreen(this.argument, {super.key});

  String get amount => argument.amount.formattedCurrency;

  _takeScreenShot([bool asPdf = true]) {
    AppRouter.popAndPushNamed(
      PaymentRoutes.transferReceiptScreenshot,
      arguments: PaymentsScreenShotArgument(
        data: argument,
        asPdf: asPdf,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: DoAppBar(
        title: LocaleKeys.receipt.tr(),
      ),
      body: ListView(
        padding: context.insets.defaultAllInsets,
        children: [
          TransactionHeaderCard(
            LocaleKeys.transactionReceipt.tr(),
            amount: amount,
          ),
          const AppGap.y12(),
          AppCard(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                FormLabel(label: LocaleKeys.transactionDetails.tr()),
                PaymentTransferSummaryTable(data: argument),
                const AppGap.y32(),
                AppOutlineButton(
                  onPressed: _takeScreenShot,
                  text: LocaleKeys.downloadAsPdf.tr(),
                ),
                const AppGap.y16(),
                AppOutlineButton(
                  text: LocaleKeys.downloadAsImage.tr(),
                  onPressed: () {
                    _takeScreenShot(false);
                  },
                )
              ],
            ),
          ),
        ],
      ),
    );
  }
}
