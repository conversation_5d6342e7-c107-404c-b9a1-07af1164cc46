import 'dart:typed_data';

import 'package:day1/day1.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';

class ReceiptScreenShotScreen extends AppStatelessWidget {
  final PaymentsScreenShotArgument argument;

  const ReceiptScreenShotScreen(this.argument, {super.key});

  PaymentsTransferArgument get data => argument.data;

  String get amount => data.amount.formattedCurrency;

  String get fileName {
    final tag = LocaleKeys.receipt.tr().split(" ");
    final recipient = data.recipient.accountName?.split(" ") ?? [];
    final date = data.transaction?.createdAt?.format("yyyy_MM_dd_hh_mm");

    return "${tag.join("_")}_${recipient.join("_")}_$date".upper;
  }

  _captureScreen(BuildContext context, ScreenShotService service) async {
    final color = context.scaffoldBgColor;
    final image = await service.captureScreen(
      context,
      delay: 300.milliDuration,
    );
    await _saveImage(image, color);
  }

  Future _saveImage(Uint8List? image, Color bgColor) async {
    try {
      if (image == null) return;
      final bytes = argument.asPdf
          ? await PdfService.pdfFromImageBytes(image, color: bgColor)
          : image;
      await AppFilePlugin.saveFile(
        bytes,
        filename: "DAY_ONE_$fileName",
        fileType: argument.asPdf ? FileType.custom : FileType.image,
        ext: argument.asPdf ? "pdf" : "png",
      );
      AppRouter.popView();
    } catch (e) {
      AppRouter.popView();
    }
  }

  @override
  Widget build(BuildContext context) {
    final service = ScreenShotService();
    final year = DateTime.now().year;

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _captureScreen(context, service);
    });

    return SafeArea(
      child: ScreenShotWidget(
        service: service,
        child: Scaffold(
          body: Builder(
            builder: (context) {
              return Padding(
                padding: context.insets.defaultAllInsets,
                child: Column(
                  children: [
                    TransactionHeaderCard(
                      LocaleKeys.transactionReceipt.tr(),
                      amount: amount,
                    ),
                    const AppGap.y12(),
                    Expanded(
                      child: AppCard(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.stretch,
                          children: [
                            FormLabel(
                                label: LocaleKeys.transactionDetails.tr()),
                            PaymentTransferSummaryTable(data: data),
                            const Spacer(),
                            const AppLogo(),
                            const AppGap.y12(),
                            AppText(
                              LocaleKeys.copyrightText.tr({"year": "$year"}),
                              textAlign: TextAlign.center,
                            )
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
        ),
      ),
    );
  }
}
