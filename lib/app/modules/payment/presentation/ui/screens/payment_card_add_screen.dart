import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class PaymentCardAddScreen extends AppStatefulWidget {
  const PaymentCardAddScreen({super.key});

  @override
  State<StatefulWidget> createState() => _PaymentCardAddScreenState();
}

class _PaymentCardAddScreenState extends State<PaymentCardAddScreen>
    with PaymentCardAddMixin {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: DoAppBar(title: LocaleKeys.payWithCard.tr()),
      body: ListView(
        padding: context.insets.defaultAllInsets,
        children: [
          AppForm(
            formKey: formKey,
            child: AppCard(
              child: AppLocalStateWrapper<PaymentMethodsState>(
                builder: (state) {
                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      AppFormHeader(title: LocaleKeys.addCardDetails.tr()),
                      FormLabel(label: LocaleKeys.cardNum.tr()),
                      AppCardNumberField(
                        controller: numCtrl,
                        isEnabled: !state.isLoading,
                      ),
                      const AppGap.y24(),
                      FormLabel(label: LocaleKeys.expiryDate.tr()),
                      AppDateField.manual(
                        controller: expiryCtrl,
                        firstDate: DateTime.now(),
                        selectedDate: DateTime.now(),
                        validator: AppValidators.cardExpiryValidator,
                        dateFormat: "MM/yy",
                        hintText: "MM/YY",
                        formatter: CardDateFormatter(),
                        isEnabled: !state.isLoading,
                        hint: AutofillHints.creditCardExpirationDate,
                      ),
                      const AppGap.y24(),
                      FormLabel(label: LocaleKeys.securityCode.tr()),
                      AppCvvField(
                        controller: cvvCtrl,
                        isEnabled: !state.isLoading,
                      ),
                      const AppGap.y24(),
                      BoolListener(
                        valueListenable: doSave,
                        builder: (save) {
                          return AppSwitchTile(
                            value: save,
                            onChanged: (value) {
                              doSave.value = value;
                            },
                            leading: Expanded(
                              child: AppText(
                                LocaleKeys.saveCardForFuture.tr(),
                                textAlign: TextAlign.end,
                                style: context.textStyle.b2(
                                  weight: FontWeight.w500,
                                ),
                              ),
                            ),
                          );
                        },
                      ),
                      const AppGap.y64(),
                      BoolListener(
                        valueListenable: formStateEmitter,
                        builder: (isValid) {
                          return AppButton(
                            onPressed: submit,
                            isLoading: state.isLoading,
                            text: LocaleKeys.addDebitCard.tr(),
                            isDisabled: !isValid,
                          );
                        },
                      ),
                    ],
                  );
                },
              ),
            ),
          ),
        ],
      ),
    );
  }
}
