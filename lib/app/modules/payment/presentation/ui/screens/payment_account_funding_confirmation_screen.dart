import 'package:day1/day1.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

class PaymentFundingConfirmScreen extends AppStatelessWidget {
  final PaymentsFundingArgument argument;
  const PaymentFundingConfirmScreen(this.argument, {super.key});

  String get amount => argument.ngValue.formattedCurrency;

  @override
  Widget build(BuildContext context) {
    final state = context.watch<PaymentWalletState>();

    return Scaffold(
      appBar: const DoAppBar(),
      body: ListView(
        padding: context.insets.defaultAllInsets,
        children: [
          AppCard(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                AppFormHeader(
                  title: LocaleKeys.confirmAmount.tr(),
                  subTitle: amount,
                  bottomGap: const AppGap.y32(),
                  subTitleStyle: context.textStyle.b1(
                    color: context.secondaryTextColor,
                    weight: FontWeight.w500,
                  ),
                ),
                PaymentConfirmationTable(
                  fxValue: argument.fxValue,
                  ngValue: argument.ngValue,
                  fxRate: argument.fxRate,
                ),
                const AppGap.y64(),
                AppButton(
                  onPressed: () {
                    state.fundWallet(
                      FundingParam(amount: argument.fxValue),
                      onSuccess: () {
                        AppRouter.pushAndRemoveUntil(
                          PaymentRoutes.paymentSuccess,
                          arguments: argument,
                        );
                      },
                      onError: context.showErrorNotification,
                      theme: context.theme,
                    );
                  },
                  isDisabled: state.isLoading,
                  isLoading: state.isLoading,
                  text: LocaleKeys.addAmount.tr({"amount": amount}),
                )
              ],
            ),
          ),
        ],
      ),
    );
  }
}
