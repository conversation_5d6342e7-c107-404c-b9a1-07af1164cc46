import 'package:day1/day1.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

class PaymentRecipientsScreen extends AppStatefulWidget {
  final PaymentRecipientsArguments arguments;
  const PaymentRecipientsScreen(this.arguments, {super.key});

  @override
  State<StatefulWidget> createState() => _PaymentRecipientsScreenState();
}

class _PaymentRecipientsScreenState extends State<PaymentRecipientsScreen>
    with PaymentRecipientsMixin {
  final ScrollController controller = ScrollController();

  @override
  Widget build(BuildContext context) {
    final state = context.watch<PaymentRecipientState>();
    final hPadding = context.insets.symmetricSp(horizontal: AppFontSizes.px32);
    final listPadding =
        hPadding.add(context.insets.onlySp(bottom: AppFontSizes.px20));

    return Scaffold(
      resizeToAvoidBottomInset: false,
      appBar: DoAppBar(title: LocaleKeys.transfer.tr()),
      body: AppCard(
        margin: context.insets.defaultAllInsets,
        padding: EdgeInsets.zero,
        child: ListenableBuilder(
          key: const Key("bene_list_emitter"),
          listenable: Listenable.merge([
            state.recipients,
            activeTab,
          ]),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              const AppGap.y32(),
              AppFormHeader(
                title: LocaleKeys.selectAcctToSendMoney.tr(),
                subTitle: LocaleKeys.sendMoneyToFavs.tr(),
              ),
              AppForm(
                formKey: formKey,
                child: AppSearchField(
                  key: const Key("bene_search_input"),
                  fillColor: context.cardColor,
                  onChanged: state.searchRecipient,
                  controller: queryCtrl,
                  isEnabled: !state.recipients.value.isLoading,
                  hintText: LocaleKeys.searchByNameOrAcct.tr(),
                ),
              ),
              const AppGap.y12(),
            ],
          ),
          builder: (_, child) {
            final task = state.recipients.value;
            final isLoading = task.isLoading && !task.hasData;

            List<Beneficiary> data = state.recipients.value.data;
            final tab = activeTab.value;

            if (data.hasValue && tab == RecipientTab.favourites) {
              data = data.whereList((it) => it.isFavorite);
            }

            return AppInfiniteScrollView(
              controller: controller,
              data: task,
              indicatorOffset: context.fractionalHeight(10),
              onScrollEnd: (onSuccess) {
                return state.getRecipients(onSuccess: onSuccess);
              },
              onRefresh: () => state.getRecipients(force: true),
              child: CustomScrollView(
                key: const PageStorageKey("bank_list_view"),
                controller: controller,
                keyboardDismissBehavior:
                    ScrollViewKeyboardDismissBehavior.onDrag,
                slivers: [
                  SliverPadding(
                    padding: hPadding,
                    sliver: SliverList.list(
                      children: [
                        child!,
                        AppSegmentedTabBar(tab, tabs: [
                          Expanded(
                            child: AppSegmentedTab(
                              value: RecipientTab.all,
                              activeValue: tab,
                              label: LocaleKeys.all.ctr(),
                              onSelect: switchTab,
                            ),
                          ),
                          Expanded(
                            child: AppSegmentedTab(
                              value: RecipientTab.favourites,
                              activeValue: tab,
                              label: LocaleKeys.favourites.ctr(),
                              onSelect: switchTab,
                            ),
                          ),
                        ]),
                        const AppGap.y24(),
                      ],
                    ),
                  ),
                  if (isLoading)
                    const SliverFillRemaining(
                      child: Center(
                        child: AppSpinner(),
                      ),
                    ),
                  if (!data.hasValue && !isLoading)
                    SliverPadding(
                      padding: hPadding,
                      sliver: SliverToBoxAdapter(
                        child: AppEmptyStateWidget(
                          title: LocaleKeys.noBeneficiaries.tr(),
                        ),
                      ),
                    ),
                  if (data.hasValue && !isLoading)
                    SliverPadding(
                      padding: listPadding,
                      sliver: SliverList.builder(
                        itemBuilder: (_, index) {
                          final recipient = data[index];
                          return PaymentRecipientTile(
                            recipient.asRecipient,
                            onSelect: selectRecipient,
                            onFaving: (_) {
                              state.toggleFavState(recipient);
                            },
                            onDelete: (_) {
                              state.removeRecipient(recipient);
                            },
                            key: PageStorageKey(
                              "$index-${recipient.props}",
                            ),
                          );
                        },
                        itemCount: data.length,
                      ),
                    )
                ],
              ),
            );
          },
        ),
      ),
    );
  }
}
