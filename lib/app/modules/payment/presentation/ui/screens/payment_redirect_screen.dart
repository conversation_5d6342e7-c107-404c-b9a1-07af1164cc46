import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class PaymentRedirectScreen extends AppStatelessWidget {
  final PaymentRedirectArgument argument;
  const PaymentRedirectScreen(this.argument, {super.key});

  @override
  Widget build(BuildContext context) {
    final amount = argument.payData?.ngValue.formattedCurrency;
    final title =
        argument.payData == null ? LocaleKeys.paymentMethod.tr() : null;
    return Scaffold(
      appBar: DoAppBar(title: title),
      body: SingleChildScrollView(
        child: PaymentStatusCard(
          title: amount ?? LocaleKeys.redirectionMessage.tr(),
          decription: LocaleKeys.youWillBeRedirectedToCardIssuer.tr(),
          header: Padding(
            padding: context.insets.symmetricSp(
              vertical: AppFontSizes.px4,
              horizontal: AppFontSizes.px10,
            ),
            child: const AppLogo(
              iconOnly: true,
              alignment: Alignment.centerLeft,
              height: AppFontSizes.px40,
            ),
          ),
          action: () {
            AppRouter.pushNamed(
              PaymentRoutes.paymentSuccess,
              arguments: argument.payData,
            );
          },
        ),
      ),
    );
  }
}
