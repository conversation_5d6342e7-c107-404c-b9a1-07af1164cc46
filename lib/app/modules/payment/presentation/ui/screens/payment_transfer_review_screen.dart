import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class PaymentTransferReviewScreen extends AppStatefulWidget {
  final PaymentsTransferArgument argument;
  const PaymentTransferReviewScreen(this.argument, {super.key});

  @override
  State<StatefulWidget> createState() => _PaymentTransferReviewScreenState();
}

class _PaymentTransferReviewScreenState
    extends State<PaymentTransferReviewScreen> {
  final controller = TextEditingController();

  String get amount => widget.argument.ngValue.formattedCurrency;
  String get name => widget.argument.recipient.accountName ?? "";

  @override
  Widget build(BuildContext context) {
    final title = LocaleKeys.reviewDetails.tr();
    final subTitle = LocaleKeys.exactAmoountWillReachRecipient.tr({
      "amount": amount,
      "user": name,
    });

    return Scaffold(
      appBar: DoAppBar(title: title),
      body: ListView(
        padding: context.insets.defaultAllInsets,
        children: [
          AppCard(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                AppFormHeader(
                  title: title,
                  subTitle: subTitle,
                  bottomGap: const AppGap.y32(),
                ),
                PaymentTransferReviewTable(data: widget.argument),
                const AppGap.y32(),
                FormLabel(label: LocaleKeys.purpose.tr()),
                AppTextField(
                  controller: controller,
                  hintText: LocaleKeys.purposeOptions.tr(),
                  textCapitalization: TextCapitalization.sentences,
                ),
                const AppGap.y32(),
                AppButton(
                  onPressed: () {
                    AppRouter.pushNamed(
                      PaymentRoutes.transferPin,
                      arguments: widget.argument.copyWith(
                        narration: controller.textValue,
                      ),
                    );
                  },
                  text: LocaleKeys.sendAmount.tr({"amount": amount}),
                )
              ],
            ),
          ),
        ],
      ),
    );
  }
}
