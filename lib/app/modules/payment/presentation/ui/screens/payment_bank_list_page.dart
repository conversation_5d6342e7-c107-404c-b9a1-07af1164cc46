import 'package:day1/day1.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

class PaymentBankListScreen extends AppStatefulWidget {
  final PaymentBanklListArguments arguments;
  const PaymentBankListScreen(this.arguments, {super.key});

  @override
  State<StatefulWidget> createState() => _PaymentBankListScreenState();
}

class _PaymentBankListScreenState extends State<PaymentBankListScreen>
    with PaymentBankListMixin {
  @override
  Widget build(BuildContext context) {
    final state = context.watch<BankState>();
    final hPadding = context.insets.symmetricSp(horizontal: AppFontSizes.px32);
    final matchPadding = context.insets.symmetricSp(
      horizontal: AppFontSizes.px16,
      vertical: AppFontSizes.px14,
    );
    final listPadding = context.insets.symmetricSp(
      horizontal: AppFontSizes.px16,
      vertical: AppFontSizes.px12,
    );

    return Scaffold(
      appBar: DoAppBar(title: LocaleKeys.selectBank.tr()),
      body: AppCard(
        margin: context.insets.defaultAllInsets,
        padding: EdgeInsets.zero,
        child: ListenableBuilder(
          key: const Key("bank_list_emitter"),
          listenable: Listenable.merge([
            state.banksNotifier,
            state.groupedBanks,
          ]),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              const AppGap.y32(),
              AppForm(
                formKey: formKey,
                child: AppSearchField(
                  key: const Key("bank_search_input"),
                  fillColor: context.cardColor,
                  onChanged: state.searchBank,
                  controller: queryCtrl,
                  hintText: LocaleKeys.searchByName.tr(),
                ),
              )
            ],
          ),
          builder: (_, child) {
            final task  = state.banksNotifier.value;
            final isLoading = task.isLoading && !task.hasData;
            final groupData = state.groupedBanks.value;
            final keys = [...groupData.keys];
            keys.sort();

            return CustomScrollView(
              key: const Key("bank_list_view"),
              slivers: [
                SliverPadding(
                  padding: hPadding,
                  sliver: SliverList.list(
                    children: [
                      child!,
                      const AppGap.y24(),
                      if (argument.matchedBank != null) ...[
                        FormLabel(label: LocaleKeys.matchedBank.tr()),
                        AppCard(
                          borderRadius: context.xxlBorderRadius,
                          color: context.scaffoldBgColor,
                          padding: EdgeInsets.zero,
                          child: PaymentBankSelectionTile(
                            argument.matchedBank!,
                            onSelect: selectBank,
                            padding: matchPadding,
                          ),
                        ),
                        const AppGap.y24(),
                      ]
                    ],
                  ),
                ),
                if (isLoading)
                  const SliverFillRemaining(
                    child: AppGenericShimmer(),
                  ),
                if (!isLoading && !keys.hasValue)
                  const SliverFillRemaining(
                    child: AppEmptyStateWidget(),
                  ),
                if (!isLoading && keys.hasValue)
                  SliverPadding(
                    padding: hPadding.add(context.insets.only(bottom: 3)),
                    sliver: SliverList.separated(
                      itemBuilder: (_, index) {
                        final key = keys[index];
                        final banks = groupData[key] ?? [];

                        return PaymentBankListSection(
                          key,
                          banks: banks,
                          padding: listPadding,
                          onSelect: selectBank,
                        );
                      },
                      separatorBuilder: (_, __) => const AppGap.y24(),
                      itemCount: keys.length,
                    ),
                  ),
              ],
            );
          },
        ),
      ),
    );
  }
}
