import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class PaymentTransferPinScreen extends AppStatefulWidget {
  final PaymentsTransferArgument argument;

  const PaymentTransferPinScreen(this.argument, {super.key});
  @override
  State<PaymentTransferPinScreen> createState() => _TransferPinScreenState();
}

class _TransferPinScreenState extends State<PaymentTransferPinScreen>
    with TransferPinMixin {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: DoAppBar(title: header),
      body: AppLocalStateWrapper<PaymentTransferState>(
        builder: (state) {
          return AppPinForm(
            formKey,
            deferBioLoginToParent: false,
            isLoading: state.isLoading,
            onBioAuth: (proceed) {
              if (!proceed) return;
              submit(TransferAuthType.bio);
            },
            onSubmit: (_) => submit(),
            otpCtrl: otpCtrl,
            title: title.tr(),
            rider: rider.tr(),
            formStateEmitter: formStateEmitter,
          );
        },
      ),
    );
  }
}
