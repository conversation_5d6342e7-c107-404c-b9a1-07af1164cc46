import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class PaymentRecipientTile extends AppStatefulWidget {
  final TransferRecipient recipient;
  final OnChanged<TransferRecipient> onSelect;
  final OnChanged<TransferRecipient> onFaving;
  final OnChanged<TransferRecipient> onDelete;

  const PaymentRecipientTile(
    this.recipient, {
    super.key,
    required this.onSelect,
    required this.onFaving,
    required this.onDelete,
  });

  @override
  State<StatefulWidget> createState() => _PaymentRecipientTileState();
}

class _PaymentRecipientTileState extends State<PaymentRecipientTile>
    with SingleTickerProviderStateMixin {
  late final ValueNotifier<Offset> offsetRef;

  @override
  void initState() {
    super.initState();
    offsetRef = ValueNotifier(Offset.zero);
  }

  onSelect() => widget.onSelect(recipient);

  onFaving() => widget.onFaving(recipient);

  _handleDrag(DragUpdateDetails details) {
    if (details.delta.dx > 0) {
      offsetRef.value = Offset.zero;
      return;
    }
    final width = (context.size?.width ?? context.sizer.width);
    final target = (width * .18);
    offsetRef.value = -Offset(target, 0);
  }

  TransferRecipient get recipient => widget.recipient;

  @override
  Widget build(BuildContext context) {
    final titleStyle = context.textStyle.b2(weight: FontWeight.w600);
    return AspectRatio(
      aspectRatio: 290 / 58,
      child: ClipRect(
        child: Stack(
          children: [
            Positioned.fill(
              child: RecipientTileBackground(
                recipient,
                onDelete: widget.onDelete,
              ),
            ),
            Positioned(
              child: GenericListener(
                valueListenable: offsetRef,
                builder: (offset) {
                  final margin = offset.dx == 0
                      ? EdgeInsets.zero
                      : context.insets.symmetricSp(vertical: AppFontSizes.px1);

                  return Transform.translate(
                    offset: offset,
                    child: GestureDetector(
                      onTap: onSelect,
                      onHorizontalDragUpdate: _handleDrag,
                      child: AppCard(
                        cornerStyle: CornerStyle.rounded,
                        borderRadius: context.mdBorderRadius,
                        margin: margin,
                        padding: context.insets.symmetricSp(
                          vertical: AppFontSizes.px8,
                          horizontal: AppFontSizes.px16,
                        ),
                        child: Row(
                          children: [
                            IgnorePointer(
                              child: AppAvatar(
                                bgColor: context.cardColor,
                                avatar: AppImageData(
                                  imageData: recipient.bank.logo,
                                ),
                                initials: recipient.bank.initials,
                                size: context.sp(AppFontSizes.px40),
                                borderColor: context.inputBorderColor,
                              ),
                            ),
                            const AppGap.h16(),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  AppText(
                                    recipient.accountName?.capitalise(),
                                    style: titleStyle,
                                  ),
                                  AppText(
                                    recipient.bank.name,
                                    style: context.textStyle.b4(
                                      color: context.textColor.withOpacity(.75),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            const AppGap.h16(),
                            AppAnimatedSwitcher(
                              child: AppTextButton(
                                key: ValueKey(recipient.isFavourite),
                                size: ButtonSize.small,
                                textAlign: TextAlign.end,
                                onPressed: onFaving,
                                icon: AppIcon(
                                  AppIcons.star,
                                  color: recipient.isFavourite
                                      ? context.textColor
                                      : context.disabledBtnColor,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class RecipientTileBackground extends AppStatelessWidget with AppModalMixin {
  final TransferRecipient recipient;
  final OnChanged<TransferRecipient> onDelete;

  const RecipientTileBackground(
    this.recipient, {
    required this.onDelete,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return AppCard(
      cornerStyle: CornerStyle.rounded,
      borderRadius: context.mdBorderRadius,
      color: context.scaffoldBgColor,
      padding: context.insets.symmetricSp(
        vertical: AppFontSizes.px8,
        horizontal: AppFontSizes.px6,
      ),
      child: Align(
        alignment: Alignment.centerRight,
        child: InkWell(
          onTap: () {
            confirmAction(
              context,
              allowText: LocaleKeys.delete.tr(),
              canceltext: LocaleKeys.cancel.tr(),
              onContinue: () => onDelete(recipient),
              title: LocaleKeys.sureYouWannaDeleteBeneficiary.tr(),
            );
          },
          child: Container(
            width: context.sp(AppFontSizes.px40),
            height: context.sp(AppFontSizes.px40),
            padding: context.insets.allSp(AppFontSizes.px8),
            decoration: BoxDecoration(
              borderRadius: context.mdBorderRadius,
              color: context.errorColor,
            ),
            child: const Center(
              child: AppIcon(
                AppIcons.binMinusIn,
                color: AppColors.white,
              ),
            ),
          ),
        ),
      ),
    );
  }
}
