import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class PaymentAddFundTab extends AppStatefulWidget {
  const PaymentAddFundTab({super.key});

  @override
  State createState() => _PaymentAddFundTabState();
}

class _PaymentAddFundTabState extends State<PaymentAddFundTab>
    with PaymentAccountFundingMixin {
  @override
  Widget build(BuildContext context) {
    final btnPadding = context.insets.symmetricSp(
      horizontal: AppFontSizes.px20,
    );

    return AppForm(
      formKey: formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          const AppGap.y16(),
          PaymentConversionCard(
            usdField: usdField,
            ngnField: ngnField,
            ngnFocus: ngnFocus,
            usdFocus: usdFocus,
            key: const Key("pay_conversion_card"),
          ),
          const AppGap.y16(),
          // const PaymentMethodsCard(),
          // const AppGap.y16(),
          BoolListener(
            valueListenable: formStateEmitter,
            builder: (isValid) {
              return Padding(
                padding: btnPadding,
                child: AppButton(
                  isDisabled: !isValid,
                  text: LocaleKeys.continueText.tr(),
                  onPressed: submit,
                ),
              );
            },
          )
        ],
      ),
    );
  }
}
