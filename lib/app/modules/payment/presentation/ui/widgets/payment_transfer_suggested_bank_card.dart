import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class PaymentPredictedBanksCard extends AppStatelessWidget {
  final OnChanged<PredictedBank> onSelect;
  final List<PredictedBank> banks;

  const PaymentPredictedBanksCard(
    this.banks, {
    super.key,
    required this.onSelect,
  });

  @override
  Widget build(BuildContext context) {
    List<Widget> children = [
      for (final bank in banks) PaymentBankTile(bank, onSelect: onSelect),
    ];
    final color = context.disabledBtnColor;
    return Stack(
      children: [
        Positioned(
          top: context.sp(AppFontSizes.px3),
          left: context.sp(AppFontSizes.px18),
          child: RotatedBox(
            quarterTurns: 3,
            child: SizedBox(
              width: 30,
              height: 30,
              child: DecoratedBox(
                decoration: ShapeDecoration(
                  color: color,
                  shape: BeveledRectangleBorder(
                    borderRadius: context.btnBorderRadius,
                  ),
                ),
              ),
            ),
          ),
        ),
        AppCard(
          margin: context.insets.onlySp(top: AppFontSizes.px16),
          color: color,
          borderRadius: context.xxlBorderRadius,
          padding: context.insets.symmetricSp(
            vertical: AppFontSizes.px12,
            horizontal: AppFontSizes.px16,
          ),
          child: Column(
            children: [
              for (final tile in children.intersperse(
                AppDivider(color: context.inputBorderColor),
              ))
                tile
            ],
          ),
        )
      ],
    );
  }
}

class PaymentBankTile extends AppStatelessWidget {
  final PredictedBank bank;
  final OnChanged<PredictedBank> onSelect;

  const PaymentBankTile(this.bank, {super.key, required this.onSelect});

  @override
  Widget build(BuildContext context) {
    final style = context.textStyle.b4(weight: FontWeight.w500);
    return InkWell(
      onTap: () => onSelect(bank),
      child: Padding(
        padding: context.insets.symmetricSp(vertical: AppFontSizes.px4),
        child: Row(
          children: [
            IgnorePointer(
              child: AppOutlineButton(
                onPressed: () {},
                icon: const AppIcon(AppIcons.bank),
                variant: OutlineBtnVariant.neutral,
                size: ButtonSize.small,
                minSize: Size.fromWidth(context.sp(AppFontSizes.px34)),
              ),
            ),
            const AppGap.h16(),
            Expanded(child: AppText(bank.name, style: style)),
          ],
        ),
      ),
    );
  }
}
