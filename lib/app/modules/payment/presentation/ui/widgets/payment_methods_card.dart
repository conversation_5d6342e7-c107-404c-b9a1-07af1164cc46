import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class PaymentMethodsCard extends AppStatelessWidget {
  final OnChanged<CardResponse>? onSelect;
  const PaymentMethodsCard({super.key, this.onSelect});

  _addCard() {
    AppRouter.pushNamed(PaymentRoutes.methods);
  }

  @override
  Widget build(BuildContext context) {
    final padding = context.insets.symmetricSp(horizontal: AppFontSizes.px20);
    return AppLocalStateWrapper<PaymentMethodsState>(builder: (state) {
      return ListenableBuilder(
        listenable: state.selectedCard,
        builder: (_, __) {
          final card = state.selectedCard.value;

          if (card == null) {
            return PaymentMethodActionCard(
              color: context.raisedBtnBgColor,
              title: LocaleKeys.add_payment_method.tr(),
              subTitle: PaymentMethodActionCardSubtitle(
                label: LocaleKeys.credit_cards_debit_cards_are_supported.tr(),
              ),
              prefixIcon: AppIcons.plus,
              onPressed: _addCard,
              margin: padding,
            );
          }

          return Padding(
            padding: padding,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: AppText(
                        LocaleKeys.paymentMethod.tr(),
                        style: context.textStyle.labelText(),
                      ),
                    ),
                    AppTextButton(
                      onPressed: _addCard,
                      size: ButtonSize.small,
                      textAlign: TextAlign.end,
                      icon: AppIcon(
                        AppIcons.plus,
                        color: context.cardBorderColor,
                      ),
                      text: LocaleKeys.addNewCard.tr(),
                      color: context.disabledBtntextColor,
                    )
                  ],
                ),
                const AppGap.y8(),
                PaymentCardCard(data: card, selection: card),
              ],
            ),
          );
        },
      );
    });
  }
}

class PaymentCardCard extends AppStatelessWidget {
  final OnChanged<CardResponse>? onSelect;
  final CardResponse? selection;
  final CardResponse data;

  const PaymentCardCard({
    super.key,
    this.onSelect,
    this.selection,
    required this.data,
  });

  _onSelect() {
    onSelect?.call(data);
  }

  @override
  Widget build(BuildContext context) {
    final isSelected = selection == data;

    return InkWell(
      onTap: _onSelect,
      child: AppCard(
        color: context.scaffoldBgColor,
        borderRadius: context.lgBorderRadius,
        cornerStyle: CornerStyle.rounded,
        padding: context.insets.allSp(AppFontSizes.px12),
        child: Row(
          children: [
            AppOutlineButton(
              onPressed: _onSelect,
              variant: OutlineBtnVariant.neutral,
              size: ButtonSize.medium,
              minSize: Size.fromWidth(
                context.sp(AppFontSizes.px56),
              ),
              contentPadding: EdgeInsets.zero,
              icon: AppSvg(
                data.cardPrefix,
                width: context.sp(AppFontSizes.px24),
              ),
            ),
            const AppGap.h16(),
            Expanded(
              child: AppText(
                data.trucatedNumber,
                style: context.textStyle.b2(weight: FontWeight.w600),
              ),
            ),
            const AppGap.h16(),
            AppAnimatedSwitcher(
              child: Builder(
                key: ValueKey(isSelected),
                builder: (context) {
                  if (!isSelected) return const Offstage();

                  return AppButton(
                    onPressed: _onSelect,
                    variant: RaisedButtonVariant.b,
                    size: ButtonSize.medium,
                    minSize: Size.fromWidth(context.sp(AppFontSizes.px56)),
                    contentPadding: EdgeInsets.zero,
                    icon: AppIcons.check,
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class PaymentMethodActionCard extends AppStatelessWidget {
  final OnPressed? onPressed;
  final Widget? trailing;
  final String title;
  final EdgeInsetsGeometry? margin;
  final Widget subTitle;
  final Color? color;
  final IconData prefixIcon;

  const PaymentMethodActionCard({
    super.key,
    this.onPressed,
    this.trailing,
    this.margin,
    this.color,
    required this.title,
    required this.subTitle,
    required this.prefixIcon,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onPressed,
      child: AppCard(
        color: color ?? context.scaffoldBgColor,
        borderRadius: context.lgBorderRadius,
        cornerStyle: CornerStyle.rounded,
        margin: margin,
        padding: context.insets.allSp(AppFontSizes.px12),
        child: Row(
          children: [
            AppOutlineButton(
              onPressed: () {
                onPressed?.call();
              },
              variant: OutlineBtnVariant.neutral,
              size: ButtonSize.medium,
              minSize: Size.fromWidth(
                context.sp(AppFontSizes.px56),
              ),
              contentPadding: EdgeInsets.zero,
              icon: AppIcon(prefixIcon, size: AppFontSizes.px24),
            ),
            const AppGap.h16(),
            Expanded(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  AppText(
                    title,
                    style: context.textStyle.b3(weight: FontWeight.w600),
                  ),
                  subTitle,
                ],
              ),
            ),
            if (trailing != null) ...[
              const AppGap.h16(),
              trailing!,
            ]
          ],
        ),
      ),
    );
  }
}

class PaymentMethodActionCardSubtitle extends AppStatelessWidget {
  final String label;

  const PaymentMethodActionCardSubtitle({super.key, required this.label});

  @override
  Widget build(BuildContext context) {
    return AppText(
      label,
      style: context.textStyle.b5(color: context.secondaryTextColor),
    );
  }
}

class PaymentMethodActionCardTrailer extends AppStatelessWidget {
  const PaymentMethodActionCardTrailer({super.key});
  @override
  Widget build(BuildContext context) {
    return const AppIcon(
      AppIcons.navArrowRight,
      alignment: Alignment.centerRight,
    );
  }
}
