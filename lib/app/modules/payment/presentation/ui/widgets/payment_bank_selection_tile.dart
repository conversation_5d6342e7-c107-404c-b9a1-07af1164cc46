import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class PaymentBankSelectionTile extends AppStatelessWidget {
  final Bank bank;
  final OnChanged<Bank> onSelect;
  final EdgeInsetsGeometry? padding;

  const PaymentBankSelectionTile(
    this.bank, {
    super.key,
    required this.onSelect,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    final defaultPadding = context.insets.symmetricSp(
      vertical: AppFontSizes.px4,
    );
    return InkWell(
      onTap: () => onSelect(bank),
      child: Padding(
        padding: padding ?? defaultPadding,
        child: Row(
          children: [
            AppAvatar(
              bgColor: context.cardColor,
              avatar: AppImageData(imageData: bank.logo),
              initials: bank.initials,
              size: context.sp(AppFontSizes.px34),
            ),
            const AppGap.h16(),
            Expanded(
              child: AppText(
                bank.name,
                style: context.textStyle.b3(
                  weight: FontWeight.w500,
                ),
              ),
            )
          ],
        ),
      ),
    );
  }
}
