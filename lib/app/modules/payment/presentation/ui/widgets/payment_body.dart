import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class PaymentBody extends AppStatelessWidget {
  final Widget headerChild;
  final Widget contentChild;

  const PaymentBody({
    super.key,
    required this.headerChild,
    required this.contentChild,
  });

  @override
  Widget build(BuildContext context) {
    final borderColor = context.raisedBtnBBgColor.withOpacity(.2);

    return Container(
      decoration: ShapeDecoration(
        color: context.raisedBtnBgColor,
        shape: ContinuousRectangleBorder(
          borderRadius: context.xxxlBorderRadius,
          side: BorderSide(color: borderColor, width: .5),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        mainAxisSize: MainAxisSize.min,
        children: [
          PaymentHeader(child: headerChild),
          Transform.scale(
            scaleY: 1.003,
            scaleX: 1.004,
            child: PaymentContent(child: contentChild),
          )
        ],
      ),
    );
  }
}
