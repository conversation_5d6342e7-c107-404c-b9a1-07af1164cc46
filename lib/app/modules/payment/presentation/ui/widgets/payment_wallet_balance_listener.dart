import 'package:day1/day1.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

class WalletListener extends AppStatelessWidget {
  final ValueBuilder<FutureData<WalletResponse>> builder;

  const WalletListener({
    super.key,
    required this.builder,
  });

  @override
  Widget build(BuildContext context) {
    final state = context.watch<PaymentWalletState>();

    return GenericListener(
      valueListenable: state.wallet,
      builder: (task) {
        return builder(task);
      },
    );
  }
}

class WalletBalanceVisiblityListener extends AppStatelessWidget {
  final ValueBuilder<bool> builder;

  const WalletBalanceVisiblityListener({
    super.key,
    required this.builder,
  });

  @override
  Widget build(BuildContext context) {
    final state = context.watch<PaymentWalletState>();

    return BoolListener(
      valueListenable: state.showBalance,
      builder: builder,
    );
  }
}

class WalletBalanceVisiblityButton extends AppStatelessWidget {
  const WalletBalanceVisiblityButton({super.key});

  @override
  Widget build(BuildContext context) {
    final state = context.watch<PaymentWalletState>();

    return WalletBalanceVisiblityListener(
      builder: (show) {
        final icon = !show ? AppIcons.eyeClosed : AppIcons.eye;
        return AppAnimatedSwitcher(
          child: AppTextButton(
            key: ValueKey(show),
            onPressed: state.toggleBalance,
            size: ButtonSize.small,
            icon: AppIcon(icon, color: context.raisedBtnCBgColor),
          ),
        );
      },
    );
  }
}
