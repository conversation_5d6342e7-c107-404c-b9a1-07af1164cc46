import 'dart:math';

import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class PaymentConversionTable extends AppStatelessWidget {
  final num fxValue;
  final num ngValue;
  final num fxRate;

  const PaymentConversionTable({
    super.key,
    required this.fxValue,
    required this.ngValue,
    required this.fxRate,
  });

  num get convertFee => fxValue * .005;
  num get sendFee => min((ngValue * .1), 3200);
  num get convertedValue => fxValue - (convertFee + (sendFee / fxRate));

  @override
  Widget build(BuildContext context) {
    return AppCard(
      color: context.transparent,
      borderRadius: context.lgBorderRadius,
      border: BorderSide(
        color: context.inputBorderColor,
        width: 2,
      ),
      padding: context.insets.symmetricSp(
        horizontal: AppFontSizes.px16,
        vertical: AppFontSizes.px8,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        mainAxisSize: MainAxisSize.min,
        children: [
          PaymentConversionTableRow(
            LocaleKeys.conversion_fee.tr(),
            value: "-${convertFee.asCurrency("\$")}",
          ),
          PaymentConversionTableRow(
            LocaleKeys.send_fee.tr(),
            value: "-${sendFee.formattedCurrency}",
          ),
          PaymentConversionTableRow(
            LocaleKeys.amount_we_will_convert.tr(),
            value: "=${convertedValue.asCurrency("\$")}",
          ),
          const AppDivider(),
          PaymentConversionTableRow(
            LocaleKeys.todays_rate.tr(),
            value: "${1.asCurrency("\$")}=${fxRate.formattedCurrency}",
          ),
        ],
      ),
    );
  }
}

class PaymentConfirmationTable extends AppStatelessWidget {
  final num fxValue;
  final num ngValue;
  final num fxRate;

  const PaymentConfirmationTable({
    super.key,
    required this.fxValue,
    required this.ngValue,
    required this.fxRate,
  });

  num get convertFee => fxValue * .005;
  num get sendFee => min((ngValue * .1), 3200);
  num get convertedValue => fxValue - (convertFee + (sendFee / fxRate));

  @override
  Widget build(BuildContext context) {
    return AppCard(
      color: context.scaffoldBgColor,
      borderRadius: context.lgBorderRadius,
      border: BorderSide(
        color: context.inputBorderColor,
        width: 2,
      ),
      padding: context.insets.symmetricSp(
        horizontal: AppFontSizes.px16,
        vertical: AppFontSizes.px8,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        mainAxisSize: MainAxisSize.min,
        children: [
          PaymentConversionTableRow(
            LocaleKeys.conversion_fee.tr(),
            value: "-${convertFee.asCurrency("\$")}",
          ),
          PaymentConversionTableRow(
            LocaleKeys.send_fee.tr(),
            value: "-${sendFee.formattedCurrency}",
          ),
          PaymentConversionTableRow(
            LocaleKeys.amountAddedToWallet.tr(),
            value: convertedValue.asCurrency("\$"),
          ),
          const AppDivider(),
          PaymentConversionTableRow(
            LocaleKeys.totalAmunt.tr(),
            value: ngValue.formattedCurrency,
          ),
        ],
      ),
    );
  }
}

class PaymentTransferReviewTable extends AppStatelessWidget {
  final PaymentsTransferArgument data;

  const PaymentTransferReviewTable({super.key, required this.data});

  @override
  Widget build(BuildContext context) {
    return AppCard(
      color: context.scaffoldBgColor,
      borderRadius: context.lgBorderRadius,
      border: BorderSide(
        color: context.inputBorderColor,
        width: 2,
      ),
      padding: context.insets.symmetricSp(
        horizontal: AppFontSizes.px16,
        vertical: AppFontSizes.px8,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        mainAxisSize: MainAxisSize.min,
        children: [
          PaymentConversionTableRow(
            LocaleKeys.accountName.tr(),
            value: data.recipient.accountName ?? "",
          ),
          PaymentConversionTableRow(
            LocaleKeys.bankName.tr(),
            value: data.recipient.bankName ?? "",
          ),
          PaymentConversionTableRow(
            LocaleKeys.accountNumber.tr(),
            value: data.recipient.accountNumber ?? "",
          ),
          const AppDivider(),
          PaymentConversionTableRow(
            LocaleKeys.totalAmunt.tr(),
            value: data.ngValue.formattedCurrency,
          ),
        ],
      ),
    );
  }
}

class PaymentTransferSummaryTable extends AppStatelessWidget {
  final PaymentsTransferArgument data;

  const PaymentTransferSummaryTable({super.key, required this.data});

  @override
  Widget build(BuildContext context) {
    return AppCard(
      color: context.scaffoldBgColor,
      borderRadius: context.lgBorderRadius,
      border: BorderSide(
        color: context.inputBorderColor,
        width: 2,
      ),
      padding: context.insets.symmetricSp(
        horizontal: AppFontSizes.px16,
        vertical: AppFontSizes.px8,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        mainAxisSize: MainAxisSize.min,
        children: [
          PaymentConversionTableRow(
            LocaleKeys.status.tr(),
            value: data.status ?? LocaleKeys.success.tr(),
            valueColor: context.highlightedTextColor,
          ),
          PaymentConversionTableRow(
            LocaleKeys.date.tr(),
            value: data.prettyCreatedAt,
          ),
          PaymentConversionTableRow(
            LocaleKeys.transactionType.tr(),
            value: data.type ?? "",
          ),
          // PaymentConversionTableRow(
          //   LocaleKeys.senderName.tr(),
          //   value: "HK",
          // ),
          PaymentConversionTableRow(
            LocaleKeys.receiverName.tr(),
            value: (data.receiver?.accountName ?? "").capitalise(),
          ),
          PaymentConversionTableRow(
            LocaleKeys.receiverAcctNum.tr(),
            value: data.receiver?.accountNumber ?? "",
          ),
          PaymentConversionTableRow(
            LocaleKeys.refNum.tr(),
            value: data.referenceNumber ?? "",
          ),
          PaymentConversionTableRow(
            LocaleKeys.narration.tr(),
            value: data.narration ?? "None",
          ),
        ],
      ),
    );
  }
}
