import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class PaymentConversionCard extends AppStatefulWidget {
  final TextEditingController usdField;
  final TextEditingController ngnField;
  final FocusNode usdFocus;
  final FocusNode ngnFocus;

  const PaymentConversionCard({
    super.key,
    required this.ngnField,
    required this.usdField,
    required this.usdFocus,
    required this.ngnFocus,
  });

  @override
  State<PaymentConversionCard> createState() => _PaymentConversionCardState();
}

class _PaymentConversionCardState extends State<PaymentConversionCard> {
  late final TextEditingController usdField;
  late final TextEditingController ngnField;
  late final FocusNode usdFocus;
  late final FocusNode ngnFocus;

  @override
  void initState() {
    super.initState();
    usdField = widget.usdField;
    ngnField = widget.ngnField;
    usdFocus = widget.usdFocus;
    ngnFocus = widget.ngnFocus;
  }

  @override
  Widget build(BuildContext context) {
    return AppCard(
      color: context.scaffoldBgColor,
      padding: context.insets.symmetricSp(
        horizontal: AppFontSizes.px16,
        vertical: AppFontSizes.px24,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          FormLabel(
            label: LocaleKeys.amount_to_add_to_wallet.tr(),
          ),
          AppAmountField(
            controller: usdField,
            fillColor: context.cardColor,
            hint: "\$0.00",
            focusNode: usdFocus,
            currency: "USD",
          ),
          const AppGap.y12(),
          ListenableBuilder(
              listenable: Listenable.merge([usdField, ngnField]),
              builder: (_, __) {
                return PaymentConversionTable(
                  fxValue: usdField.text.asAmount ?? 0,
                  ngValue: ngnField.text.asAmount ?? 0,
                  fxRate: 1600,
                );
              }),
          const AppGap.y12(),
          FormLabel(
            label: LocaleKeys.amount_in_denom.tr({"denom": "NGN"}),
          ),
          AppAmountField(
            controller: ngnField,
            fillColor: context.cardColor,
            hint: "N0.00",
            focusNode: ngnFocus,
          )
        ],
      ),
    );
  }
}
