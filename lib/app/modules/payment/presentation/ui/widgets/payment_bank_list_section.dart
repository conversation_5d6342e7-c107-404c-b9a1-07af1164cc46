import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class PaymentBankListSection extends AppStatelessWidget {
  final List<Bank> banks;
  final String tag;
  final OnChanged<Bank> onSelect;
  final EdgeInsetsGeometry? padding;

  const PaymentBankListSection(
    this.tag, {
    super.key,
    required this.onSelect,
    required this.banks,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    final key = tag;
    List<Widget> children = [
      for (final bank in banks)
        PaymentBankSelectionTile(bank, onSelect: onSelect)
    ];
    children = children.intersperse(const AppDivider());

    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        FormLabel(label: key),
        AppCard(
          borderRadius: context.xxlBorderRadius,
          color: context.scaffoldBgColor,
          padding: padding,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [for (final child in children) child],
          ),
        )
      ],
    );
  }
}
