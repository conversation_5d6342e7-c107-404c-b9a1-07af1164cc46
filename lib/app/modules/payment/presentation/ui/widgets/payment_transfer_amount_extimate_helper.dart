import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class PaymentAmountDollarEstimateHelper extends AppStatelessWidget {
  final ValueNotifier<num> estimate;

  const PaymentAmountDollarEstimateHelper(this.estimate, {super.key});

  @override
  Widget build(BuildContext context) {
    return NumberListener(
      valueListenable: estimate,
      builder: (estimate) {
        if (estimate == 0) return const Offstage();
        final style = context.textStyle.b3(
          color: context.textColor.withOpacity(.5),
        );
        final label = LocaleKeys.estimatedDollarValue.tr();
        return Row(
          children: [
            Expanded(
              child: AppText(label, style: style),
            ),
            AppText(
              estimate.asCurrency("\$"),
              style: style.copyWith(fontWeight: FontWeight.w500),
            )
          ],
        );
      },
    );
  }
}
