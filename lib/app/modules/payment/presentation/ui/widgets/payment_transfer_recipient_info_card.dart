import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class PaymentRecipientInfoCard extends AppStatelessWidget {
  final FutureData<TransferRecipient> recipient;

  const PaymentRecipientInfoCard(this.recipient, {super.key});

  @override
  Widget build(BuildContext context) {
    if (recipient.isPristine) return const Offstage();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.makeVisible();
    });

    Color bgColor = context.successCardColor;
    Color borderColor = context.activeInputBorderColor;
    Color textColor = context.notificationTextColor;

    TextStyle textStyle = context.textStyle.b2(
      color: textColor,
      weight: FontWeight.w500,
    );

    final isLoading = recipient.isLoading;
    String message = LocaleKeys.verifyingAccountDetails.tr();

    if (recipient.hasData && !isLoading) {
      message = recipient.data?.accountName ?? "";
    }

    return Container(
      padding: context.insets.symmetricSp(
        horizontal: AppFontSizes.px16,
        vertical: AppFontSizes.px12,
      ),
      margin: context.insets.onlySp(top: AppFontSizes.px12),
      decoration: ShapeDecoration(
        shape: ContinuousRectangleBorder(
          borderRadius: context.xlBorderRadius,
          side: BorderSide(color: borderColor, width: 2),
        ),
        color: bgColor,
      ),
      child: Row(
        children: [
          if (isLoading)
            const AppSpinner(forceCentering: false)
          else
            AppIcon(AppIcons.checkCircle, color: textColor),
          const AppGap.h12(),
          Expanded(child: AppRichText(message, textStyle: textStyle)),
        ],
      ),
    );
  }
}
