import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class PaymentButtonTabbar extends AppStatelessWidget {
  final List<PaymentButtonTab> tabs;
  final OnChanged<int> onTabSelect;

  const PaymentButtonTabbar({
    required this.tabs,
    required this.onTabSelect,
    super.key,
  });

  GapSpace get gap {
    final first = tabs.tryFirst;
    return first?.gap ?? GapSpace.spacing12;
  }

  GapType get type => GapType.horizontal;

  @override
  Widget build(BuildContext context) {
    final List<Widget> children = tabs;
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        for (final tab in children.intersperse(AppGap(space: gap, type: type)))
          if (tab is AppGap)
            tab
          else
            Expanded(
              child: InkWell(
                onTap: () {
                  onTabSelect((tab as PaymentButtonTab).index);
                },
                child: tab,
              ),
            )
      ],
    );
  }
}

abstract class PaymentButtonTab extends AppStatelessWidget {
  final int index;
  final String label;
  final int activeIndex;

  const PaymentButtonTab({
    super.key,
    required this.index,
    required this.label,
    required this.activeIndex,
  });

  GapSpace get gap;
}

class PaymentPrimaryButtonTab extends PaymentButtonTab {
  final IconData? icon;

  const PaymentPrimaryButtonTab({
    super.key,
    required super.index,
    required super.activeIndex,
    required super.label,
    this.icon,
  });

  @override
  GapSpace get gap => GapSpace.spacing12;

  bool get isActive => index == activeIndex;

  @override
  Widget build(BuildContext context) {
    final tab = switch (isActive) {
      true => AppButton(
          text: label,
          onPressed: () {},
          icon: icon,
          size: ButtonSize.medium,
          variant: RaisedButtonVariant.c,
        ),
      _ => AppOutlineButton(
          text: label,
          onPressed: () {},
          icon: icon != null ? AppIcon(icon!) : null,
          size: ButtonSize.medium,
          variant: OutlineBtnVariant.neutral,
        ),
    };

    return IgnorePointer(child: tab);
  }
}
