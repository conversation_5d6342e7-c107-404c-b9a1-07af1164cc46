import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class PaymentHeader extends AppStatelessWidget {
  final Widget? child;

  const PaymentHeader({
    super.key,
    this.child,
  });

  @override
  Widget build(BuildContext context) {
    final radius = context.xxlRadius;
    final borderRadius = BorderRadius.vertical(top: Radius.circular(radius));

    return IntrinsicHeight(
      child: ClipRRect(
        borderRadius: borderRadius,
        child: Stack(
          fit: StackFit.expand,
          children: [
            Positioned.fill(
              child: AppSvg(
                AppVectors.cardPattern,
                color: context.transparent,
                fit: BoxFit.cover,
              ),
            ),
            if (child != null) child!
          ],
        ),
      ),
    );
  }
}
