import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class PaymentFundTransferTab extends AppStatefulWidget {
  const PaymentFundTransferTab({super.key});

  @override
  State createState() => _PaymentFundTransferTabState();
}

class _PaymentFundTransferTabState extends State<PaymentFundTransferTab>
    with PaymentFundTransferMixin {
  @override
  Widget build(BuildContext context) {
    final btnPadding = context.insets.symmetricSp(
      horizontal: AppFontSizes.px20,
    );

    return AppForm(
      formKey: formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          const AppGap.y16(),
          AppCard(
            padding: context.insets.symmetricSp(
              horizontal: AppFontSizes.px16,
              vertical: AppFontSizes.px24,
            ),
            color: context.scaffoldBgColor,
            child: ListenableBuilder(
              listenable: Listenable.merge([
                predictedBanks,
                recipient,
                selectedBank,
              ]),
              builder: (_, __) {
                final predictions = predictedBanks.value;
                final transferRecipient = recipient.value;

                final isBusy = predictions.isLoading || transferRecipient.isLoading;

                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    FormLabel(label: LocaleKeys.amountToSend.tr()),
                    AppAmountField(
                      controller: amountField,
                      fillColor: context.cardColor,
                      hint: "N0.00",
                      focusNode: amountFocus,
                      helper: PaymentAmountDollarEstimateHelper(fxEstimate),
                      isEnabled: !isBusy,
                      max: 2000000,
                    ),
                    const AppGap.y12(),
                    FormLabel(label: LocaleKeys.accountDetails.tr()),
                    PaymentRecipientSelectionRow(
                      AppNumberField(
                        hintText: LocaleKeys.enterAccountNumber.tr(),
                        fillColor: context.cardColor,
                        controller: acctNumField,
                        maxLength: 10,
                        keyboardType: const TextInputType.numberWithOptions(),
                        isEnabled: !isBusy,
                        isLoading: predictions.isLoading,
                        onChanged: (text) {
                          final length = text?.length ?? 0;
                          if (length == 10) {
                            getPredictions();
                            return;
                          }
                          clearPrediction();
                          clearRecipient();
                        },
                      ),
                      onSelect: (value) {
                        recipient.value = FutureData(data: value);
                        acctNumField.text = value.accountNumber.value;
                        selectedBank.value = value.bank;
                      },
                    ),
                    const AppGap.y12(),
                    PaymentSelectBankTile(
                      selectedBank: selectedBank.value,
                      isEnabled: !isBusy,
                      onSelect: (value) => selectedBank.value = value,
                    ),
                    if (predictions.hasData)
                      PaymentPredictedBanksCard(
                        predictions.data,
                        onSelect: selectPrediction,
                      ),
                    if (transferRecipient.hasData || transferRecipient.isLoading)
                      PaymentRecipientInfoCard(transferRecipient)
                  ],
                );
              },
            ),
          ),
          const AppGap.y24(),
          AppText(
            LocaleKeys.paymentWillArriveImmediately.tr(),
            textAlign: TextAlign.center,
            style: context.textStyle.b4(),
          ),
          const AppGap.y16(),
          BoolListener(
            valueListenable: formStateEmitter,
            builder: (isValid) {
              return Padding(
                padding: btnPadding,
                child: AppButton(
                  isDisabled: !isValid,
                  text: LocaleKeys.continueText.tr(),
                  onPressed: submit,
                ),
              );
            },
          ),
        ],
      ),
    );
  }
}
