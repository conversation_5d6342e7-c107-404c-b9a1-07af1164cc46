import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class PaymentStatusCard extends AppStatelessWidget with AppModalMixin {
  final String title;
  final String? decription;
  final IconData? icon;
  final Widget? header;
  final Widget? subButton;
  final String? btnText;
  final TextStyle? subtitleStyle;
  final OnPressed action;
  final EdgeInsetsGeometry? margin;
  final EdgeInsetsGeometry? padding;

  const PaymentStatusCard({
    super.key,
    required this.title,
    required this.decription,
    required this.action,
    this.subButton,
    this.subtitleStyle,
    this.padding,
    this.header,
    this.icon,
    this.btnText,
    this.margin,
  });

  @override
  Widget build(BuildContext context) {
    final subStyle = subtitleStyle ??
        context.textStyle.b3(color: context.secondaryTextColor);
    final iconWidget = AppIcon(
      icon ?? AppIcons.checkCircle,
      size: AppFontSizes.px75,
      color: AppColors.lemon140,
      alignment: Alignment.centerLeft,
    );
    return AppCard(
      margin: margin ?? context.insets.defaultAllInsets,
      padding: padding ??
          context.insets.symmetricSp(
            vertical: AppFontSizes.px82,
            horizontal: AppFontSizes.px32,
          ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          header ?? iconWidget,
          const AppGap.y32(),
          AppText(title, style: context.textStyle.h5()),
          if (decription.hasValue) ...[
            const AppGap.y8(),
            AppRichText(decription, textStyle: subStyle),
          ],
          const AppGap.y48(),
          AppButton(
            text: btnText ?? LocaleKeys.proceed.tr(),
            onPressed: action,
          ),
          if (subButton != null) ...[
            const AppGap.y16(),
            subButton!,
          ]
        ],
      ),
    );
  }
}
