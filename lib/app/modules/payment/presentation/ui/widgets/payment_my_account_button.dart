import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class PaymentAccountButton extends AppStatelessWidget {
  const PaymentAccountButton({super.key});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        AppRouter.pushNamed(AccountRoutes.details);
      },
      child: Container(
        decoration: BoxDecoration(
          borderRadius: context.btnBorderRadius,
          border: Border.all(color: context.raisedBtnCBgColor),
        ),
        padding: context.insets.symmetricSp(
          horizontal: AppFontSizes.px12,
          vertical: AppFontSizes.px2,
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            const AppIcon(AppIcons.bank),
            const AppGap.h8(),
            AppText(
              LocaleKeys.my_account.tr(),
              style: context.textStyle.b4(),
            )
          ],
        ),
      ),
    );
  }
}
