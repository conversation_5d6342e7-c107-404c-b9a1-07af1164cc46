import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class PaymentSelectBankTile extends AppStatelessWidget {
  final Bank? selectedBank;
  final bool isEnabled;
  final OnChanged<Bank> onSelect;

  const PaymentSelectBankTile({
    super.key,
    this.selectedBank,
    this.isEnabled = true,
    required this.onSelect,
  });

  @override
  Widget build(BuildContext context) {
    final hasBank = selectedBank != null;
    final style = context.textStyle.b3(
      weight: FontWeight.w500,
      color: hasBank ? null : context.hintTextColor,
    );
    Widget child = AnimatedOpacity(
      opacity: isEnabled ? 1 : .7,
      duration: 300.milliDuration,
      child: InkWell(
        onTap: () {
          AppRouter.pushNamed(
            PaymentRoutes.bankList,
            arguments: PaymentBanklListArguments(
              onSelect: onSelect,
              matchedBank: selectedBank,
            ),
          );
        },
        child: AppCard(
          border: BorderSide(
            width: 2,
            color: context.inputBorderColor,
          ),
          padding: context.insets.allSp(AppFontSizes.px16),
          borderRadius: context.xlBorderRadius,
          child: Row(
            children: [
              if (hasBank) ...[
                const AppIcon(AppIcons.bank),
                const AppGap.h12(),
              ],
              Expanded(
                child: AppText(
                  selectedBank?.name ?? LocaleKeys.selectBank.tr(),
                  style: style,
                ),
              ),
              const AppGap.h12(),
              const PaymentMethodActionCardTrailer(),
            ],
          ),
        ),
      ),
    );

    if (!isEnabled) {
      child = IgnorePointer(child: child);
    }

    return child;
  }
}
