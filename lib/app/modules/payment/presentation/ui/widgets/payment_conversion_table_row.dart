import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class PaymentConversionTableRow extends AppStatelessWidget {
  final String label;
  final String value;
  final Color? valueColor;

  const PaymentConversionTableRow(
    this.label, {
    required this.value,
    this.valueColor,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: context.insets.symmetricSp(vertical: AppFontSizes.px8),
      child: Row(
        children: [
          Expanded(
            flex: 1,
            child: AppText(
              label,
              style: context.textStyle.b4(color: context.secondaryTextColor),
            ),
          ),
          Expanded(
            flex: 2,
            child: AppText(
              value,
              style: context.textStyle.b3(
                weight: FontWeight.w500,
                color: valueColor,
              ),
              textAlign: TextAlign.end,
            ),
          )
        ],
      ),
    );
  }
}
