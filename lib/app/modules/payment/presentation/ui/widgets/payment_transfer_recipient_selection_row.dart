import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class PaymentRecipientSelectionRow extends AppStatefulWidget {
  final Widget leading;
  final OnChanged<TransferRecipient> onSelect;
  const PaymentRecipientSelectionRow(
    this.leading, {
    super.key,
    required this.onSelect,
  });

  @override
  State createState() => _PaymentRecipientSelectionRowState();
}

class _PaymentRecipientSelectionRowState
    extends State<PaymentRecipientSelectionRow> {
  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: widget.leading,
        ),
        const AppGap.h4(),
        SizedBox(
          height: context.sp(AppFontSizes.px48),
          child: AppIconButton(
            icon: AppIcons.userStar,
            onPressed: () {
              AppRouter.pushNamed(
                PaymentRoutes.recipients,
                arguments: PaymentRecipientsArguments(
                  onSelect: widget.onSelect,
                ),
              );
            },
            minSize: Size.fromWidth(context.sp(AppFontSizes.px20)),
          ),
        ),
      ],
    );
  }
}
