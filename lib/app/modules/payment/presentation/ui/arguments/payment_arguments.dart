import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class PaymentsFundingArgument {
  final num fxValue;
  final num ngValue;
  final num fxRate;
  final RouteSettings? nextRoute;

  const PaymentsFundingArgument({
    required this.fxValue,
    required this.ngValue,
    required this.fxRate,
    this.nextRoute,
  });
}

class PaymentsTransferArgument {
  final num fxValue;
  final num ngValue;
  final String? narration;
  final TransferRecipient recipient;
  final DateTime? createdAt;
  final Transaction? transaction;

  const PaymentsTransferArgument({
    required this.fxValue,
    required this.ngValue,
    required this.recipient,
    this.transaction,
    this.narration,
    this.createdAt,
  });

  double get amount {
    return transaction?.amount ?? ngValue.toDouble();
  }

  String? get status => transaction?.status;
  String? get type => transaction?.type;
  TransferRecipient? get receiver =>
      transaction?.beneficiary?.asRecipient ?? recipient;
  String? get referenceNumber => transaction?.paystackTransferCode;

  PaymentsTransferArgument copyWith({
    num? fxValue,
    num? ngValue,
    String? narration,
    DateTime? createdAt,
    TransferRecipient? recipient,
    Transaction? transaction,
  }) {
    return PaymentsTransferArgument(
      fxValue: fxValue ?? this.fxValue,
      ngValue: ngValue ?? this.ngValue,
      recipient: recipient ?? this.recipient,
      narration: narration ?? this.narration,
      createdAt: createdAt ?? this.createdAt,
      transaction: transaction ?? this.transaction,
    );
  }

  String get prettyCreatedAt {
    final time = transaction?.createdAt ?? createdAt ?? DateTime.now();
    return time.format("d MMM yyyy - hh:mm");
  }
}

class PaymentsScreenShotArgument {
  final PaymentsTransferArgument data;
  final bool asPdf;

  const PaymentsScreenShotArgument({required this.data, this.asPdf = true});
}

class PaymentRedirectArgument {
  final PaymentsFundingArgument? payData;

  const PaymentRedirectArgument({this.payData});
}

class PaymentBanklListArguments {
  final OnChanged<Bank> onSelect;
  final Bank? matchedBank;

  const PaymentBanklListArguments({
    required this.onSelect,
    this.matchedBank,
  });
}

class PaymentRecipientsArguments {
  final OnChanged<TransferRecipient> onSelect;

  const PaymentRecipientsArguments({required this.onSelect});
}
