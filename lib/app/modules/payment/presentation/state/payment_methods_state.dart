import 'package:day1/day1.dart';
import 'package:flutter/widgets.dart';

class PaymentMethodsState extends StateModel {
  final PaymentService _service;

  ValueNotifier<FutureListData<CardResponse>> _cards =
      ValueNotifier(const FutureListData.pristine());
  ValueNotifier<CardResponse?> _selectedCard = ValueNotifier(null);

  ValueNotifier<FutureListData<CardResponse>> get cards => _cards;
  ValueNotifier<CardResponse?> get selectedCard => _selectedCard;

  PaymentMethodsState(this._service);

  selectCard(CardResponse? card) {
    final value = _selectedCard.value;
    if (card != null && value == card) {
      _selectedCard.value = null;
      return;
    }
    _selectedCard.value = card;
  }

  fetchCards() => _getCards();

  _getCards() async {
    final currentState = _cards.value;

    if (currentState.isLoading) return;

    _cards.value = currentState.copyWith(isLoading: true);

    final response = await _service.getCards();

    _cards.value = currentState.copyWith(
      data: response.data,
      error: response.error,
      isLoading: false,
    );
  }

  @override
  reset() {
    _cards = ValueNotifier(const FutureListData.pristine());
    _selectedCard = ValueNotifier(null);
    return super.reset();
  }
}
