import 'package:day1/day1.dart';

class PaymentTransferState extends StateModel {
  final PaymentService _service;
  final AppSessionService _sessionService;
  final PaymentWalletState _walletState;
  final PaymentRecipientState _recipientState;

  PaymentTransferState(
    this._service,
    this._sessionService,
    this._walletState,
    this._recipientState,
  );

  makeTransfer(
    PaymentsTransferArgument args, {
    required TransferAuthType auth,
    required OnChanged<Transaction?> onSuccess,
    required OnChanged<String> onError,
    required String pin,
  }) async {
    if (isLoading) return;

    String? pinCode = pin;

    setLoadingState(true);

    if (auth == TransferAuthType.bio) {
      pinCode = _sessionService.authPublicKey;
    }

    final response = await _service.transferFund(
      TransferParam(
        pin: pinCode.value,
        auth: auth,
        amount: args.amount,
        bankCode: args.recipient.bank.code,
        accountNumber: args.recipient.accountNumber ?? "",
        name: args.recipient.accountName ?? "",
        reason: args.narration,
      ),
    );

    if (response.hasError) {
      onError(response.errorMessage!);
      return setLoadingState(false);
    }

    await Future.wait([
      _walletState.fetchBalance(),
      _recipientState.getRecipients(force: true)
    ]);
    onSuccess(response.data);
    setLoadingState(false);
  }
}
