import 'dart:math';

import 'package:day1/day1.dart';
import 'package:flutter/widgets.dart';

class PaymentWalletState extends StateModel {
  final PaymentService _service;
  final StripePaymentService _stripeService;

  ValueNotifier<bool> _showBalance = ValueNotifier(true);
  ValueNotifier<FutureData<WalletResponse>> _wallet =
      ValueNotifier(const FutureData.pristine());

  ValueNotifier<bool> get showBalance => _showBalance;

  ValueNotifier<FutureData<WalletResponse>> get wallet {
    if (_wallet.value.isPristine) _getBalance();
    return _wallet;
  }

  PaymentWalletState(this._service, this._stripeService);

  toggleBalance() {
    _showBalance.value = !_showBalance.value;
  }

  Future<void> fetchBalance() async => _getBalance();

  updateBalance(num amount, {bool increase = false}) {
    final currentData = _wallet.value.data;

    if (currentData == null) return;

    final value = increase ? amount : -amount;

    final updatedData = currentData.copyWith(
      balance: max(currentData.balance + value, 0),
      baseBalance: max(currentData.baseBalance + value, 0),
    );

    wallet.value = wallet.value.copyWith(data: updatedData);
  }

  _getBalance() async {
    final currentState = _wallet.value;

    if (currentState.isLoading) return;

    _wallet.value = currentState.copyWith(isLoading: true);

    final response = await _service.getWallet();

    _wallet.value = currentState.copyWith(
      data: response.data,
      error: response.error,
      isLoading: false,
    );
  }

  fundWallet(
    FundingParam param, {
    required OnPressed onSuccess,
    required OnChanged<String> onError,
    required AppTheme theme,
  }) async {
    if (isLoading) return;
    setLoadingState(true);

    final response = await _service.fundWallet(param);

    if (response.hasError) {
      setLoadingState(false);
      onError(response.errorMessage!);
      return;
    }

    _makeStripePayment(
      response.data!,
      onSuccess: onSuccess,
      onError: onError,
      theme: theme,
    );
  }

  _makeStripePayment(
    PaymentIntentResponse intent, {
    required OnPressed onSuccess,
    required OnChanged<String> onError,
    required AppTheme theme,
  }) async {
    setLoadingState(true);

    final data = await _stripeService.makePayment(intent: intent, theme: theme);

    if (data.errorMessage != null) {
      setLoadingState(false);
      onError(data.errorMessage!);
      return;
    }

    updateBalance(intent.transactionAmount ?? 0);
    _confirmPayment(intent.id.value, onSuccess: onSuccess, onError: onError);
  }

  _confirmPayment(
    String paymentId, {
    required OnPressed onSuccess,
    required OnChanged<String> onError,
  }) async {
    setLoadingState(true);

    final response = await _service.confirmWalletFunding(paymentId);

    if (response.hasError) {
      setLoadingState(false);
      onError(response.errorMessage!);
      return;
    }

    onSuccess();
    setLoadingState(false);

    _getBalance();
  }

  @override
  reset() {
    _wallet = ValueNotifier(const FutureData.pristine());
    _showBalance = ValueNotifier(true);
    return super.reset();
  }
}
