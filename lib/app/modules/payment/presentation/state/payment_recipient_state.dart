import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class PaymentRecipientState extends StateModel {
  final PaymentService _service;

  PaymentRecipientState(this._service);

  final Debouncer _debouncer = Debouncer(500.milliDuration);

  List<Beneficiary> _recipientCache = [];
  late ValueNotifier<PaginatedData<Beneficiary>> _recipients =
      ValueNotifier(const PaginatedData.pristine());

  ValueNotifier<PaginatedData<Beneficiary>> get recipients {
    if (_recipients.value.isPristine && !_recipients.value.hasData) {
      getRecipients();
    }

    return _recipients;
  }

  Future<void> getRecipients({
    String? query,
    bool force = false,
    OnPressed? onSuccess,
  }) async {
    final currentState = _recipients.value;

    if (currentState.isLoading) return;

    final hasData = currentState.hasData;
    final hasNext = currentState.hasNext;

    if (!force && (hasData && !hasNext)) return;

    _recipients.value = currentState.copyWith(
      isLoading: true,
      query: query,
    );

    final page = force ? 0 : currentState.next;
    final term = _recipients.value.query;

    final response = await _service.getBeneficiaries(
      PageParam(page: page, query: term.hasValue ? term : null),
    );

    final result = response.data;

    PaginatedData<Beneficiary> paginatedData = PaginatedData(
      isLoading: false,
      pageError: response.error,
      data: result?.data ?? [],
      meta: result?.meta,
    );

    if (force || !currentState.hasData) {
      _recipients.value = paginatedData;
    } else {
      _recipients.value = currentState.addData(paginatedData);
    }

    _recipientCache = _recipients.value.data;

    if (!response.hasError && response.hasData && onSuccess != null) {
      onSuccess();
    }
  }

  getRecipient(
    ValueNotifier<FutureData<TransferRecipient>> notifier, {
    required String accountNumber,
    required String bankCode,
    required OnChanged<String> onError,
  }) async {
    FutureData<TransferRecipient> currentState = notifier.value;
    if (currentState.isLoading) return;

    notifier.value = currentState.copyWith(isLoading: true);

    final response = await _service.getTransferRecipient(
      AccountParam(
        accountNumber: accountNumber,
        bankCode: bankCode,
      ),
    );

    if (response.hasError) {
      onError(response.errorMessage!);
    }

    currentState = currentState.reset(isLoading: false);

    notifier.value = currentState.copyWith(
      data: response.data,
      error: response.error,
    );
  }

  List<Beneficiary> _filterRecipients(String query) {
    final data = [..._recipientCache];

    if (data.isEmpty || !query.hasValue) return data;

    return data.whereList((it) {
      final recipient = it.asRecipient;
      final nameIsMatch = recipient.accountName?.includes(query) ?? false;
      final accIsMatch = recipient.accountNumber?.includes(query) ?? false;

      return accIsMatch || nameIsMatch;
    });
  }

  _updateRecipients(List<Beneficiary> newData) {
    final currentState = _recipients.value;

    _recipients.value = currentState.copyWith(data: [...newData]);
  }

  searchRecipient(String? query) {
    if (query == null) return;

    final currentState = _recipients.value;

    if (currentState.isLoading) return;

    if (!query.hasValue) {
      _debouncer.abort();
      return _updateRecipients(_filterRecipients(query));
    }

    _debouncer.abort();
    _debouncer.run(
      () {
        _updateRecipients(_filterRecipients(query));
      },
    );
  }

  removeRecipient(Beneficiary recipient) async {
    final currentData = _recipients.value;

    if (currentData.isLoading) return;

    final data = [...currentData.data];
    final itemIndex = data.indexOf(recipient);

    if (itemIndex == -1) return;

    data.removeAt(itemIndex);

    _recipients.value = currentData.copyWith(data: [...data]);

    final response = await _service.removeBeneficiary(recipient.id.value);

    if (!response.hasError) return;

    data.insert(itemIndex, recipient);

    _recipients.value = currentData.copyWith(data: [...data]);
  }

  _toggleFavState(Beneficiary recipient, bool isFave) {
    final currentData = _recipients.value;
    _recipients.value = currentData.updateSingleItem(
      recipient,
      recipient.copyWith(isFavorite: isFave),
    );
  }

  toggleFavState(Beneficiary recipient) async {
    final currentData = _recipients.value;

    if (currentData.isLoading) return;

    final isFavourite = recipient.isFavorite;

    _toggleFavState(recipient, !isFavourite);

    final response = await _service.toggleBeneficiaryFavState(
      FavParam(isFavourite: !isFavourite, id: recipient.id.value),
    );

    if (!response.hasError) return;

    _toggleFavState(recipient, !isFavourite);
  }

  @override
  void reset() {
    super.reset();
    _recipients = ValueNotifier(const PaginatedData.pristine());
    _recipientCache = [];
  }
}
