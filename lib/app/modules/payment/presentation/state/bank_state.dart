import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class BankState extends StateModel {
  final PaymentService _service;
  final AppDataService _appService;
  final Debouncer _debouncer = Debouncer(500.milliDuration);

  ValueNotifier<Map<String, List<Bank>>> _groupedBanks = ValueNotifier({});
  late ValueNotifier<FutureListData<Bank>> _banksNotifier;

  BankState(this._appService, this._service) {
    _banksNotifier = ValueNotifier(const FutureListData.pristine());
  }

  ValueNotifier<Map<String, List<Bank>>> get groupedBanks => _groupedBanks;

  ValueNotifier<FutureListData<Bank>> get banksNotifier {
    if (_banksNotifier.value.isPristine && !_banksNotifier.value.hasData) {
      _getBanks();
    }

    return _banksNotifier;
  }

  _groupBanks(List<Bank> banks) {
    if (!banks.hasValue) {
      _groupedBanks.value = {};
    }
    Map<String, List<Bank>> hashTable = {};

    for (final bank in banks) {
      final name = bank.name.trim();
      final firstChar = (name.hasValue ? name[0] : "-").upper;

      if (hashTable.containsKey(firstChar)) {
        hashTable[firstChar]?.add(bank);
        continue;
      }
      hashTable[firstChar] = [bank];
    }

    _groupedBanks.value = {...hashTable};
  }

  _getBanks([String? query]) async {
    final currentData = _banksNotifier.value;

    _banksNotifier.value = currentData.copyWith(isLoading: true);

    final response = await _appService.getBanks(query);

    _groupBanks(response.data.value);

    _banksNotifier.value = currentData.copyWith(
      data: response.data,
      isLoading: false,
      error: response.error,
    );
  }

  searchBank(String? query) {
    if (query == null) return;
    if (!query.hasValue) {
      _debouncer.abort();
      return _getBanks();
    }
    _debouncer.abort();
    _debouncer.run(() => _getBanks(query));
  }

  getBankPredictions(
    ValueNotifier<FutureListData<PredictedBank>> notifier,
    String accountNumber,
  ) async {
    final currentState = notifier.value;
    if (currentState.isLoading) return;

    notifier.value = currentState.copyWith(isLoading: true);

    final predictions = await _service.getBankPredictions(
      AccountParam(accountNumber: accountNumber),
    );

    notifier.value = currentState.copyWith(
      isLoading: false,
      data: predictions.data ?? [],
      error: predictions.error,
    );
  }

  @override
  void reset() {
    super.reset();
    _banksNotifier = ValueNotifier(const FutureListData.pristine());
    _groupedBanks = ValueNotifier({});
  }
}
