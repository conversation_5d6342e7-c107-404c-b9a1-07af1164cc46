import 'package:flutter/material.dart';
import 'package:day1/day1.dart';

class PaymentRoutes implements RouteRegistry {
  static const basePath = "/payment";
  static const methods = "$basePath/methods";
  static const addCard = "$basePath/card-add";
  static const cardRedirect = "$basePath/card-redirect";
  static const paymentRedirect = "$basePath/pay-redirect";
  static const fundingConfirm = "$basePath/funding-confirm";
  static const paymentSuccess = "$basePath/payment-success";
  static const transferReview = "$basePath/transfer-review";
  static const transferSuccess = "$basePath/transfer-success";
  static const transferPin = "$basePath/transfer-pin";
  static const transferReceipt = "$basePath/transfer-receipt";
  static const transferReceiptScreenshot = "$basePath/transfer-receipt/screen-shot";
  static const bankList = "$basePath/bank-list";
  static const recipients = "$basePath/recipients";

  const PaymentRoutes();

  @override
  Route dynamicRoutes(RouteSettings settings, Route fallbackRoute) {
    return switch (settings.name) {
      methods => MaterialPageRoute(builder: (context) {
          return AppLocalStateWrapper<PaymentMethodsState>(builder: (_) {
            return const PaymentMethodsScreen(
              key: Key(methods),
            );
          });
        }),
      addCard => MaterialPageRoute(builder: (context) {
          return AppLocalStateWrapper<PaymentMethodsState>(builder: (_) {
            return const PaymentCardAddScreen(
              key: Key(addCard),
            );
          });
        }),
      cardRedirect => MaterialPageRoute(builder: (context) {
          return AppLocalStateWrapper<PaymentMethodsState>(builder: (_) {
            return const PaymentRedirectScreen(
              PaymentRedirectArgument(),
              key: Key(cardRedirect),
            );
          });
        }),
      paymentRedirect => MaterialPageRoute(builder: (context) {
          return AppLocalStateWrapper<PaymentMethodsState>(builder: (_) {
            return PaymentRedirectScreen(
              settings.arguments as PaymentRedirectArgument,
              key: const Key(cardRedirect),
            );
          });
        }),
      fundingConfirm => MaterialPageRoute(builder: (context) {
          return AppLocalStateWrapper<PaymentMethodsState>(builder: (_) {
            return PaymentFundingConfirmScreen(
              settings.arguments as PaymentsFundingArgument,
              key: const Key(fundingConfirm),
            );
          });
        }),
      paymentSuccess => MaterialPageRoute(builder: (context) {
          return AppLocalStateWrapper<PaymentMethodsState>(builder: (_) {
            return PaymentSuccessScreen(
              key: const Key(paymentSuccess),
              argument: settings.arguments as PaymentsFundingArgument?,
            );
          });
        }),
      transferReview => MaterialPageRoute(builder: (context) {
          return PaymentTransferReviewScreen(
            settings.arguments as PaymentsTransferArgument,
            key: const Key(transferReview),
          );
        }),
      transferSuccess => MaterialPageRoute(
          builder: (context) {
            return PaymentTransferSuccessScreen(
              settings.arguments as PaymentsTransferArgument,
              key: const Key(transferSuccess),
            );
          },
          fullscreenDialog: true,
        ),
      transferPin => MaterialPageRoute(
          builder: (context) {
            return PaymentTransferPinScreen(
              settings.arguments as PaymentsTransferArgument,
              key: const Key(transferSuccess),
            );
          },
        ),
      transferReceipt => MaterialPageRoute(builder: (context) {
          return PaymentTransferReceiptScreen(
            settings.arguments as PaymentsTransferArgument,
            key: const Key(transferReceipt),
          );
        }),
      transferReceiptScreenshot => MaterialPageRoute(builder: (context) {
          return ReceiptScreenShotScreen(
            settings.arguments as PaymentsScreenShotArgument,
            key: const Key(transferReceipt),
          );
        }),
      bankList => MaterialPageRoute(builder: (context) {
          return PaymentBankListScreen(
            settings.arguments as PaymentBanklListArguments,
            key: const Key(bankList),
          );
        }),
      recipients => MaterialPageRoute(builder: (context) {
          return AppLocalStateWrapper<PaymentRecipientState>(builder: (_) {
            return PaymentRecipientsScreen(
              settings.arguments as PaymentRecipientsArguments,
              key: const Key(fundingConfirm),
            );
          });
        }),
      _ => fallbackRoute,
    };
  }
}
