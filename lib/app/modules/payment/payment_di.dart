import 'package:day1/day1.dart';

void registerPaymentDI(DoAppConfig config) {
  ///[Services]
  locator.registerFactory<PaymentService>(() {
    return PaymentHttpService(locator());
  });

  ///[State]
  locator.registerLazySingleton<PaymentWalletState>(
    () => PaymentWalletState(locator(), locator()),
  );
  locator.registerLazySingleton<PaymentMethodsState>(
    () => PaymentMethodsState(locator()),
  );
  locator.registerLazySingleton<PaymentRecipientState>(
    () => PaymentRecipientState(locator()),
  );
  locator.registerLazySingleton<BankState>(
    () => BankState(locator(), locator()),
  );
  locator.registerLazySingleton<PaymentTransferState>(
    () => PaymentTransferState(
      locator(),
      locator(),
      locator(),
      locator(),
    ),
  );
}

void resetPaymentDI() {
  locator<PaymentMethodsState>().reset();
  locator<PaymentWalletState>().reset();
  locator<PaymentRecipientState>().reset();
  locator<PaymentTransferState>().reset();
  locator<BankState>().reset();
}
