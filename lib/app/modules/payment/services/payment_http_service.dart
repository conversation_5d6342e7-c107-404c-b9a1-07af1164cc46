import 'package:day1/day1.dart';

class PaymentHttpService
    with AppHttpMixin, AppTaskMixin
    implements PaymentService {
  final AppHttpService _service;

  PaymentHttpService(this._service);

  @override
  NetworkCallResponse<NoResponse> addCard(CardParam param) {
    return requestHandler(() async {
      await _service.post("/cards/add", body: param);
      return const NoResponse();
    });
  }

  @override
  NetworkCallResponse<PaymentIntentResponse> fundWallet(FundingParam param) {
    return requestHandler(() async {
      final response =
          await _service.post("/payments/create/credit", body: param);
      return PaymentIntentResponse.fromJson(response.parsedData);
    });
  }

  @override
  NetworkCallResponse<CardResponse> getCard(String cardId) {
    return requestHandler(() async {
      final response = await _service.get("/cards/$cardId");
      return CardResponse.fromJson(response.parsedData["card"]);
    });
  }

  @override
  NetworkCallResponse<List<CardResponse>> getCards() {
    return requestHandler(() async {
      final response = await _service.get("/cards/all");
      final rawCards = response.parsedData["cards"];

      if (rawCards is! Iterable) return [];

      return rawCards.mapList((it) => CardResponse.fromJson(it));
    });
  }

  @override
  NetworkCallResponse<WalletResponse> getWallet() {
    return requestHandler(() async {
      final response = await _service.get("/payments/wallet");
      return WalletResponse.fromJson(response.parsedData["wallet"]);
    });
  }

  @override
  NetworkCallResponse<PaymentIntentResponse> confirmWalletFunding(
    String paymentId,
  ) {
    return requestHandler(() async {
      final response = await _service.get("/payments/$paymentId");
      return PaymentIntentResponse.fromJson(response.parsedData);
    });
  }

  @override
  NetworkCallResponse<List<PredictedBank>> getBankPredictions(
      AccountParam param) {
    return requestHandler(() async {
      final response = await _service.get(
        "/banks/predict",
        query: param,
        cacheOption: CacheOption.oneDay,
      );
      final rawBanks = response.parsedData["possibleBanks"];

      if (rawBanks is! Iterable) return [];

      return rawBanks.mapList((it) => PredictedBank.fromJson(it));
    });
  }

  @override
  NetworkCallResponse<TransferRecipient> getTransferRecipient(
    AccountParam param,
  ) {
    return requestHandler(() async {
      final response = await _service.get(
        "/banks/verify",
        query: param,
        cacheOption: CacheOption.oneDay,
      );
      final rawData = response.parsedData["data"];

      dynamic data = rawData;

      if (rawData is Iterable) data = rawData.first;

      return TransferRecipient.fromJson(data);
    });
  }

  @override
  NetworkCallResponse<Transaction> transferFund(TransferParam param) {
    return requestHandler(() async {
      final response = await _service.post("/transfers/create", body: param);
      return Transaction.fromJson(response.parsedData);
    });
  }

  @override
  NetworkCallResponse<PaginatedData<Beneficiary>> getBeneficiaries(
      PageParam param) {
    return requestHandler(() async {
      final response = await _service.get(
        "/transfers/beneficiary/all",
        query: param,
      );
      final data = response.parsedData;
      final pageData = PageMetaData.fromJson(data["paginationDetails"]);
      final rawbeneficiaries = data["data"];
      List<Beneficiary> beneficiaries = [];

      if (rawbeneficiaries is Iterable) {
        beneficiaries = rawbeneficiaries.mapList(
          (it) => Beneficiary.fromJson(it),
        );
      }

      return PaginatedData(meta: pageData, data: beneficiaries);
    });
  }

  @override
  NetworkCallResponse<NoResponse> toggleBeneficiaryFavState(FavParam param) {
    return requestHandler(() async {
      await _service.patch("/transfers/beneficiary/${param.id}", body: param);
      return const NoResponse();
    });
  }

  @override
  NetworkCallResponse<NoResponse> removeBeneficiary(String id) {
    return requestHandler(() async {
      await _service.delete("/transfers/beneficiary/$id");
      return const NoResponse();
    });
  }
}
