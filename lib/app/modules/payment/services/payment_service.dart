import 'package:day1/day1.dart';

abstract class PaymentService {
  NetworkCallResponse<NoResponse> addCard(CardParam param);
  NetworkCallResponse<List<CardResponse>> getCards();
  NetworkCallResponse<PaginatedData<Beneficiary>> getBeneficiaries(PageParam param);
  NetworkCallResponse<NoResponse> toggleBeneficiaryFavState(FavParam param);
  NetworkCallResponse<NoResponse> removeBeneficiary(String id);
  NetworkCallResponse<List<PredictedBank>> getBankPredictions(AccountParam param);
  NetworkCallResponse<TransferRecipient> getTransferRecipient(AccountParam param);
  NetworkCallResponse<CardResponse> getCard(String cardId);
  NetworkCallResponse<PaymentIntentResponse> fundWallet(FundingParam param);
  NetworkCallResponse<Transaction> transferFund(TransferParam param);
  NetworkCallResponse<PaymentIntentResponse> confirmWalletFunding(String paymentId);
  NetworkCallResponse<WalletResponse> getWallet();
}
