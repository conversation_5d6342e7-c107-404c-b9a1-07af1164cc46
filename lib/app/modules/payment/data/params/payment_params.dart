import 'package:day1/core/core.dart';

class FundingParam extends MapCodable {
  final num amount;

  const FundingParam({required this.amount});

  @override
  toJson() {
    return {"amount": amount.asCents};
  }
}

class CardParam extends MapCodable {
  final String id;

  const CardParam({required this.id});

  @override
  toJson() => {"paymentMethodId": id.trim()};
}

class FavParam extends MapCodable {
  final String id;
  final bool isFavourite;

  const FavParam({
    required this.isFavourite,
    required this.id,
  });

  @override
  toJson() => {"isFavorite": isFavourite};
}

class AccountParam extends MapCodable {
  final String accountNumber;
  final String? bankCode;

  const AccountParam({required this.accountNumber, this.bankCode});

  @override
  toJson() {
    return {
      "accountNumber": accountNumber.trim(),
      if (bankCode.hasValue) "bankCode": bankCode?.trim()
    };
  }
}

enum TransferAuthType {
  pin("pin", "pin"),
  bio("biometrics", "publicKey");

  const TransferAuthType(this.name, this.key);

  final String name;
  final String key;
}

class TransferParam extends MapCodable {
  final String pin;
  final TransferAuthType auth;
  final double amount;
  final String? reason;
  final String bankCode;
  final String accountNumber;
  final String name;

  TransferParam({
    required this.pin,
    this.auth = TransferAuthType.pin,
    required this.amount,
    this.reason,
    required this.bankCode,
    required this.accountNumber,
    required this.name,
  });

  TransferParam copyWith({
    String? pin,
    TransferAuthType? auth,
    double? amount,
    String? reason,
    String? bankCode,
    String? accountNumber,
    String? name,
  }) {
    return TransferParam(
      pin: pin ?? this.pin,
      auth: auth ?? this.auth,
      amount: amount ?? this.amount,
      reason: reason ?? this.reason,
      bankCode: bankCode ?? this.bankCode,
      accountNumber: accountNumber ?? this.accountNumber,
      name: name ?? this.name,
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      auth.key: pin,
      "auth": auth.name,
      "amount": amount.asCents,
      if (reason.hasValue) "reason": reason,
      "bankCode": bankCode,
      "accountNumber": accountNumber,
      "name": name,
    };
  }
}
