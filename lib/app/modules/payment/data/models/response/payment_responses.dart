import 'package:day1/day1.dart';
import 'package:equatable/equatable.dart';

class CardResponse extends Equatable {
  final String? id;
  final String? object;
  final String? addressCity;
  final String? addressCountry;
  final String? addressLine1;
  final String? addressLine1Check;
  final String? addressLine2;
  final String? addressState;
  final String? addressZip;
  final String? addressZipCheck;
  final String? brand;
  final String? country;
  final String? customer;
  final String? cvcCheck;
  final String? dynamicLast4;
  final int? expMonth;
  final int? expYear;
  final String? fingerprint;
  final String? funding;
  final String? last4;
  final String? name;
  final String? tokenizationMethod;

  const CardResponse({
    this.id,
    this.object,
    this.addressCity,
    this.addressCountry,
    this.addressLine1,
    this.addressLine1Check,
    this.addressLine2,
    this.addressState,
    this.addressZip,
    this.addressZipCheck,
    this.brand,
    this.country,
    this.customer,
    this.cvcCheck,
    this.dynamicLast4,
    this.expMonth,
    this.expYear,
    this.fingerprint,
    this.funding,
    this.last4,
    this.name,
    this.tokenizationMethod,
  });

  String get trucatedNumber {
    return ".... .... $last4";
  }

  String get cardPrefix {
    return switch (brand) {
      "American Express" => AppVectors.amex,
      "JCB" => AppVectors.jcb,
      "MasterCard" => AppVectors.mastercard,
      "UnionPay" => AppVectors.unionpay,
      "Visa" => AppVectors.visa,
      _ => AppVectors.paymentCard,
    };
  }

  factory CardResponse.fromJson(Map<String, dynamic> json) {
    return CardResponse(
      id: json["id"],
      object: json["object"],
      addressCity: json["address_city"],
      addressCountry: json["address_country"],
      addressLine1: json["address_line1"],
      addressLine1Check: json["address_line1_check"],
      addressLine2: json["address_line2"],
      addressState: json["address_state"],
      addressZip: json["address_zip"],
      addressZipCheck: json["address_zip_check"],
      brand: json["brand"],
      country: json["country"],
      customer: json["customer"],
      cvcCheck: json["cvc_check"],
      dynamicLast4: json["dynamic_last4"],
      expMonth: json["exp_month"],
      expYear: json["exp_year"],
      fingerprint: json["fingerprint"],
      funding: json["funding"],
      last4: json["last4"],
      name: json["name"],
      tokenizationMethod: json["tokenization_method"],
    );
  }

  @override
  List<Object?> get props => [id, last4, name, customer, fingerprint];
}

class WalletResponse extends Equatable {
  final String? userId;
  final String? id;
  final String? updatedAt;
  final String? createdAt;
  final double balance;
  final double baseBalance;

  const WalletResponse({
    this.userId,
    this.id,
    this.updatedAt,
    this.createdAt,
    this.balance = 0,
    this.baseBalance = 0,
  });

  WalletResponse copyWith({
    String? userId,
    String? id,
    String? updatedAt,
    String? createdAt,
    double? balance,
    double? baseBalance,
  }) {
    return WalletResponse(
      userId: userId ?? this.userId,
      id: (id ?? this.id)?.toString(),
      updatedAt: updatedAt ?? this.updatedAt,
      createdAt: createdAt ?? this.createdAt,
      balance: balance ?? this.balance,
      baseBalance: baseBalance ?? this.baseBalance,
    );
  }

  double get ledgerBalance => baseBalance.asDollars;

  factory WalletResponse.fromJson(Map<String, dynamic> json) {
    return WalletResponse(
      userId: json["userId"],
      id: json["id"]?.toString(),
      updatedAt: json["updatedAt"],
      createdAt: json["createdAt"],
      balance: double.tryParse(json["balance"]) ?? 0,
      baseBalance: double.tryParse(json["baseBalance"]) ?? 0,
    );
  }

  @override
  List<Object?> get props => [userId, balance, baseBalance, updatedAt];
}

class PredictedBank extends Equatable {
  final String? name;
  final String? bankCode;
  final String? destbankcode;
  final String? recipientaccount;

  const PredictedBank({
    this.name,
    this.bankCode,
    this.destbankcode,
    this.recipientaccount,
  });

  factory PredictedBank.fromJson(Map<String, dynamic> json) {
    return PredictedBank(
      name: json["name"],
      bankCode: json["bank_code"],
      destbankcode: json["destbankcode"],
      recipientaccount: json["recipientaccount"],
    );
  }

  Bank get bank {
    return Bank(name: name?.capitalise() ?? "", code: destbankcode.value);
  }

  @override
  List<Object?> get props => [bank, bankCode];
}

class TransferRecipient extends Equatable {
  final String? bankName;
  final String? accountName;
  final String? accountNumber;
  final String? bankCode;
  final String? id;
  final bool isFavourite;

  const TransferRecipient({
    this.bankName,
    this.accountName,
    this.accountNumber,
    this.bankCode,
    this.isFavourite = false,
    this.id,
  });

  TransferRecipient copyWith({
    String? bankName,
    String? accountName,
    String? accountNumber,
    String? bankCode,
    bool? isFavourite,
  }) {
    return TransferRecipient(
      bankName: bankName ?? this.bankName,
      accountName: accountName ?? this.accountName,
      accountNumber: accountNumber ?? this.accountNumber,
      bankCode: bankCode ?? this.bankCode,
      isFavourite: isFavourite ?? this.isFavourite,
    );
  }

  factory TransferRecipient.fromJson(Map<String, dynamic> json) =>
      TransferRecipient(
        id: json["id"]?.toString(),
        bankName: json["bank_name"],
        accountName: json["account_name"],
        accountNumber: json["account_number"],
        bankCode: json["bank_code"],
      );

  Bank get bank {
    return Bank(name: bankName?.capitalise() ?? "", code: bankCode.value);
  }

  @override
  List<Object?> get props => [accountName, accountNumber, bankCode];
}

class Transaction {
  final String? id;
  final String? transactionTypeId;
  final String? transactionStatusId;
  final String? stripeTransactionStatus;
  final String? paystackTransactionStatus;
  final String? stripeClientSecret;
  final String? paystackTransferCode;
  final double? transactionAmount;
  final double? baseTransactionAmount;
  final String? transactionId;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final String? userId;
  final String? beneficiaryId;
  final TransStatus? transactionStatus;
  final TransType? transactionType;
  final Beneficiary? beneficiary;

  Transaction({
    this.id,
    this.transactionTypeId,
    this.transactionStatusId,
    this.stripeTransactionStatus,
    this.paystackTransactionStatus,
    this.stripeClientSecret,
    this.paystackTransferCode,
    this.transactionAmount,
    this.baseTransactionAmount,
    this.transactionId,
    this.createdAt,
    this.updatedAt,
    this.userId,
    this.beneficiaryId,
    this.transactionStatus,
    this.transactionType,
    this.beneficiary,
  });

  String? get status => transactionStatus?.statusName?.capitalise();
  String? get type => transactionType?.typeName?.capitalise();
  double get amount => baseTransactionAmount?.asDollars ?? 0;

  Transaction copyWith({
    String? id,
    String? transactionTypeId,
    String? transactionStatusId,
    String? stripeTransactionStatus,
    String? paystackTransactionStatus,
    String? stripeClientSecret,
    String? paystackTransferCode,
    double? transactionAmount,
    double? baseTransactionAmount,
    String? transactionId,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? userId,
    String? beneficiaryId,
    TransStatus? transactionStatus,
    TransType? transactionType,
    Beneficiary? beneficiary,
  }) =>
      Transaction(
        id: id ?? this.id,
        transactionTypeId: transactionTypeId ?? this.transactionTypeId,
        transactionStatusId: transactionStatusId ?? this.transactionStatusId,
        stripeTransactionStatus:
            stripeTransactionStatus ?? this.stripeTransactionStatus,
        paystackTransactionStatus:
            paystackTransactionStatus ?? this.paystackTransactionStatus,
        stripeClientSecret: stripeClientSecret ?? this.stripeClientSecret,
        paystackTransferCode: paystackTransferCode ?? this.paystackTransferCode,
        transactionAmount: transactionAmount ?? this.transactionAmount,
        baseTransactionAmount:
            baseTransactionAmount ?? this.baseTransactionAmount,
        transactionId: transactionId ?? this.transactionId,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        userId: userId ?? this.userId,
        beneficiaryId: beneficiaryId ?? this.beneficiaryId,
        transactionStatus: transactionStatus ?? this.transactionStatus,
        transactionType: transactionType ?? this.transactionType,
        beneficiary: beneficiary ?? this.beneficiary,
      );

  factory Transaction.fromJson(Map<String, dynamic> json) {
    return Transaction(
      id: json["id"]?.toString(),
      transactionTypeId: json["transactionTypeId"]?.toString(),
      transactionStatusId: json["transactionStatusId"]?.toString(),
      stripeTransactionStatus: json["stripeTransactionStatus"],
      paystackTransactionStatus: json["paystackTransactionStatus"],
      stripeClientSecret: json["stripeClientSecret"],
      paystackTransferCode: json["paystackTransferCode"],
      transactionAmount: double.tryParse(json["transactionAmount"]),
      baseTransactionAmount: double.tryParse(json["baseTransactionAmount"]),
      transactionId: json["transactionId"]?.toString(),
      createdAt:
          json["createdAt"] == null ? null : DateTime.parse(json["createdAt"]),
      updatedAt:
          json["updatedAt"] == null ? null : DateTime.parse(json["updatedAt"]),
      userId: json["userId"],
      beneficiaryId: json["beneficiaryId"]?.toString(),
      transactionStatus: json["transactionStatus"] == null
          ? null
          : TransStatus.fromJson(json["transactionStatus"]),
      transactionType: json["transactionType"] == null
          ? null
          : TransType.fromJson(json["transactionType"]),
      beneficiary: json["beneficiary"] == null
          ? null
          : Beneficiary.fromJson(json["beneficiary"]),
    );
  }
}

class TransStatus {
  final String? id;
  final String? statusName;

  TransStatus({
    this.id,
    this.statusName,
  });

  factory TransStatus.fromJson(Map<String, dynamic> json) {
    return TransStatus(
      id: json["id"]?.toString(),
      statusName: json["statusName"],
    );
  }
}

class TransType {
  final String? id;
  final String? typeName;

  TransType({
    this.id,
    this.typeName,
  });

  factory TransType.fromJson(Map<String, dynamic> json) {
    return TransType(
      id: json["id"]?.toString(),
      typeName: json["typeName"],
    );
  }
}

class Beneficiary extends Equatable implements Identifiable {
  final bool isFavorite;
  final int? noOfTransactions;
  final DateTime? lastUsedAt;
  final String? beneficiaryDetailId;
  final String? userId;
  final DateTime? createdAt;
  final String? id;
  final BeneficiaryDetails? beneficiaryDetails;

  const Beneficiary({
    this.isFavorite = false,
    this.noOfTransactions,
    this.lastUsedAt,
    this.beneficiaryDetailId,
    this.userId,
    this.createdAt,
    this.id,
    this.beneficiaryDetails,
  });

  TransferRecipient get asRecipient {
    return TransferRecipient(
      bankCode: beneficiaryDetails?.bankCode,
      bankName: beneficiaryDetails?.bankName,
      accountNumber: beneficiaryDetails?.accountNumber,
      accountName: beneficiaryDetails?.accountName,
      isFavourite: isFavorite,
      id: "${beneficiaryDetails?.id ?? beneficiaryDetails?.paystackRecipientId ?? beneficiaryDetailId}",
    );
  }

  Beneficiary copyWith({
    bool? isFavorite,
    int? noOfTransactions,
    DateTime? lastUsedAt,
    String? beneficiaryDetailId,
    String? userId,
    DateTime? createdAt,
    String? id,
    BeneficiaryDetails? beneficiaryDetails,
  }) {
    return Beneficiary(
      isFavorite: isFavorite ?? this.isFavorite,
      noOfTransactions: noOfTransactions ?? this.noOfTransactions,
      lastUsedAt: lastUsedAt ?? this.lastUsedAt,
      beneficiaryDetailId: beneficiaryDetailId ?? this.beneficiaryDetailId,
      userId: userId ?? this.userId,
      createdAt: createdAt ?? this.createdAt,
      id: id ?? this.id,
      beneficiaryDetails: beneficiaryDetails ?? this.beneficiaryDetails,
    );
  }

  factory Beneficiary.fromJson(Map<String, dynamic> json) {
    return Beneficiary(
      isFavorite: json["isFavorite"] ?? false,
      noOfTransactions: json["NoOfTransactions"],
      lastUsedAt: json["lastUsedAt"] == null
          ? null
          : DateTime.parse(json["lastUsedAt"]),
      beneficiaryDetailId: json["beneficiaryDetailId"]?.toString(),
      userId: json["userId"],
      createdAt:
          json["createdAt"] == null ? null : DateTime.parse(json["createdAt"]),
      id: json["id"]?.toString(),
      beneficiaryDetails: json["BeneficiaryDetails"] == null
          ? null
          : BeneficiaryDetails.fromJson(json["BeneficiaryDetails"]),
    );
  }

  @override
  List<Object?> get props => [id, userId, beneficiaryDetailId, isFavorite];

  @override
  String get uuid => "$props";
}

class BeneficiaryDetails {
  final String? accountNumber;
  final String? accountName;
  final String? paystackRecipientId;
  final String? bankCode;
  final String? bankName;
  final DateTime? createdAt;
  final String? id;

  BeneficiaryDetails({
    this.accountNumber,
    this.accountName,
    this.paystackRecipientId,
    this.bankCode,
    this.bankName,
    this.createdAt,
    this.id,
  });

  BeneficiaryDetails copyWith({
    String? accountNumber,
    String? accountName,
    String? paystackRecipientId,
    String? bankCode,
    String? bankName,
    DateTime? createdAt,
    String? id,
  }) {
    return BeneficiaryDetails(
      accountNumber: accountNumber ?? this.accountNumber,
      accountName: accountName ?? this.accountName,
      paystackRecipientId: paystackRecipientId ?? this.paystackRecipientId,
      bankCode: bankCode ?? this.bankCode,
      bankName: bankName ?? this.bankName,
      createdAt: createdAt ?? this.createdAt,
      id: id ?? this.id,
    );
  }

  factory BeneficiaryDetails.fromJson(Map<String, dynamic> json) {
    return BeneficiaryDetails(
      accountNumber: json["accountNumber"],
      accountName: json["accountName"],
      paystackRecipientId: json["paystackRecipientId"],
      bankCode: json["bankCode"],
      bankName: json["bankName"],
      createdAt:
          json["createdAt"] == null ? null : DateTime.parse(json["createdAt"]),
      id: json["id"]?.toString(),
    );
  }
}
