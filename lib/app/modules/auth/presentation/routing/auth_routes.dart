import 'package:flutter/material.dart';
import 'package:day1/day1.dart';

class AuthRoutes implements RouteRegistry {
  static const basePath = "/auth";
  static const String login = '$basePath/login';
  static const String pin = '$basePath/pin';
  static const String pinChange = '$basePath/pin-change';
  static const String pinChangeNewPin = '$basePath/pin-change-new';
  static const String pinChangeConfirm = '$basePath/pin-change-confirm';
  static const String passwordChange = '$basePath/password-change';
  static const String verifyCode = '$basePath/otp';
  static const String twoFaSetup = '$basePath/2fa-setup';
  static const String twoFaPhoneNumber = '$basePath/2fa-phone';
  static const String twoFaCode = '$basePath/2fa-code';
  static const String twoFaSuccess = '$basePath/2fa-success';
  static const String twoFaActive = '$basePath/2fa-active';

  const AuthRoutes();

  @override
  Route dynamicRoutes(RouteSettings settings, Route fallbackRoute) {
    return switch (settings.name) {
      login => MaterialPageRoute(
          builder: (context) => AuthLoginScreen(
            key: const Key(login),
            arguments: settings.arguments as AuthLoginScreenArguments? ??
                const AuthLoginScreenArguments(),
          ),
        ),
      twoFaSetup => MaterialPageRoute(
          builder: (context) => Auth2faSetupScreen(
            key: const Key(twoFaCode),
            arguments: settings.arguments as Auth2faScreenArguments,
          ),
          fullscreenDialog: true,
        ),
      twoFaPhoneNumber => MaterialPageRoute(
          builder: (context) => Auth2faPhoneScreen(
            key: const Key(twoFaPhoneNumber),
            arguments: settings.arguments as Auth2faScreenArguments,
          ),
        ),
      twoFaCode => MaterialPageRoute(
          builder: (context) => Auth2faScreen(
            key: const Key(twoFaCode),
            arguments: (settings.arguments as Auth2faScreenArguments).copyWith(
              isSetup: true,
            ),
          ),
        ),
      twoFaSuccess => MaterialPageRoute(
          builder: (context) => Auth2faSuccessScreen(
            key: const Key(twoFaSuccess),
            arguments: settings.arguments as Auth2faScreenArguments,
          ),
        ),
      twoFaActive => MaterialPageRoute(
          builder: (context) => Auth2faActiveScreen(
            key: const Key(twoFaActive),
            arguments: settings.arguments as Auth2faScreenArguments,
          ),
        ),
      verifyCode => MaterialPageRoute(
          builder: (context) => Auth2faScreen(
            key: const Key(verifyCode),
            arguments: settings.arguments as Auth2faScreenArguments,
          ),
        ),
      pin => MaterialPageRoute(
          builder: (context) => AuthPinScreen(
            settings.arguments as AuthPinScreenArguments?,
            key: const Key(pin),
          ),
        ),
      pinChange => MaterialPageRoute(
          builder: (context) => const AuthPinUpdateScreen(
            null,
            key: Key(pinChange),
          ),
        ),
      pinChangeNewPin => MaterialPageRoute(
          builder: (context) => AuthPinUpdateScreen(
            settings.arguments as AuthPinScreenArguments,
            key: const Key(pinChangeNewPin),
          ),
        ),
      pinChangeConfirm => MaterialPageRoute(
          builder: (context) => AuthPinUpdateScreen(
            settings.arguments as AuthPinScreenArguments,
            key: const Key(pinChangeConfirm),
          ),
        ),
      passwordChange => MaterialPageRoute(
          builder: (context) => const AuthPasswordChangeScreen(
            key: Key(verifyCode),
          ),
        ),
      _ => fallbackRoute,
    };
  }
}
