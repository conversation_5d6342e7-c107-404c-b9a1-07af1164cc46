import 'package:day1/day1.dart';

class AuthSettingsState extends StateModel
    with AppAnalyticsMixin, AppModalMixin {
  // ignore: unused_field
  final AuthService _authService;
  final AppMeService _meService;
  final AppSessionService _sessionService;

  AuthSettingsState(this._authService, this._meService, this._sessionService);

  MeResponse? get me => _meService.cachedMeData;

  deactivateAccount({
    required OnChanged<NetworkError> onError,
    required OnPressed onSuccess,
  }) async {
    // setLoadingState(true);
    // final response = await _authService.deleteAccount();
    // if (response.hasError) {
    //   setLoadingState(false);
    //   onError(response.error!);
    //   return;
    // }
    // onSuccess();
    // setLoadingState(false);
  }

  logout() async {
    trackEvent(AppEvent.loggedOut);
    await _sessionService.closeSession();
    AppRouter.pushAndRemoveUntil(AuthRoutes.login);
    await Future.delayed(1.secondDuration, resetAppDI);
  }
}
