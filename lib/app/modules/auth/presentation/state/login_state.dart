import 'package:day1/day1.dart';

class LoginState extends StateModel {
  final AuthService _authService;
  final AppSessionService _sessionService;
  final BiometricAuthService _biometricAuthService;
  final AppMeService _meService;
  final AppDataService _appService;

  LoginState(
    this._authService,
    this._meService,
    this._appService,
    this._sessionService,
    this._biometricAuthService,
  );

  String? get lastEmail => _authService.lastEmail;

  Future _createSession(AuthResponse? data, [bool ephemeral = false]) async {
    if (data == null) return;
    await _sessionService.createSession(
      authData: data,
      ephemeral: ephemeral,
    );
  }

  NetworkCallResponse<AuthResponse> login(LoginParam param) async {
    setLoadingState(true);
    final response = await _authService.login(param);
    await _createSession(response.data, true);
    setLoadingState(false);
    return response;
  }

  Future<NetworkResponse<MeResponse>?> loginWithBio() async {
    setLoadingState(true);
    final canProceed = await _biometricAuthService.authenticate(
      title: LocaleKeys.login.tr(),
    );
    if (!canProceed) {
      setLoadingState(false);
      return null;
    }
    final pubKey = _sessionService.authPublicKey;
    final response = await _authService.loginWithBio(
      AuthParam(
        data: pubKey.value,
        email: _sessionService.lastEmail,
        type: PubKeyParamType(),
      ),
    );
    return await _handleAuthData(response, response.data);
  }

  NetworkCallResponse<MeResponse> loginWithPin(String pin) async {
    setLoadingState(true);
    final response = await _authService.loginWithPin(
      AuthParam(
        data: pin,
        email: _sessionService.lastEmail,
        type: PinAuthParamType(),
      ),
    );
    return await _handleAuthData(response, response.data);
  }

  NetworkCallResponse<MeResponse> verifyCode(
    String otp,
    AuthResponse authData,
  ) async {
    setLoadingState(true);
    final response = await _authService.verify2faCode(
      AuthParam(
        data: otp,
        type: TwoFaAuthParamType(),
      ),
    );
    return await _handleAuthData(response, authData);
  }

  NetworkCallResponse<MeResponse> _handleAuthData(
    NetworkResponse response,
    AuthResponse? authData,
  ) async {
    if (response.hasError) {
      setLoadingState(false);
      return NetworkResponse(error: response.error);
    }
    await _createSession(authData);
    return await _fetchMeData();
  }

  NetworkCallResponse<MeResponse> _fetchMeData() async {
    final responses = await Future.wait([
      _meService.getMeData(),
      _appService.getAllBaseData(),
    ]);
    NetworkError? error;
    final hasError = responses.every((it) => it.hasError);
    if (hasError) {
      responses.shuffle();
      error = responses.tryFirstWhere((it) => it.hasError)?.error;
    }
    final me = responses.first.data as MeResponse?;
    setLoadingState(false);
    return NetworkResponse(error: error, data: me);
  }
}
