import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class AuthStatusCard extends AppStatelessWidget with AppModalMixin {
  final String title;
  final String decription;
  final IconData? icon;
  final String? btnText;
  final OnPressed action;
  final EdgeInsetsGeometry? margin;

  const AuthStatusCard({
    super.key,
    required this.title,
    required this.decription,
    required this.action,
    this.icon,
    this.btnText,
    this.margin,
  });

  @override
  Widget build(BuildContext context) {
    return AppCard(
      margin: margin ?? context.insets.defaultAllInsets,
      padding: context.insets.defaultCardHInsets.add(
        context.insets.symmetricSp(vertical: AppFontSizes.px86),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          AppIcon(
            icon ?? AppIcons.shieldCheck,
            size: AppFontSizes.px82,
            color: AppColors.lemon140,
            alignment: Alignment.centerLeft,
          ),
          const AppGap.y32(),
          AppText(title, style: context.textStyle.h5()),
          const AppGap.y8(),
          AppText(decription, style: context.textStyle.labelText()),
          const AppGap.y48(),
          AppButton(
            text: btnText ?? LocaleKeys.continueText.tr(),
            onPressed: action,
          ),
        ],
      ),
    );
  }
}
