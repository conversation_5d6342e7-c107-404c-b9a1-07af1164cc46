import 'package:day1/day1.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

mixin AuthPinMixin<T extends AuthPinScreen> on State<T> {
  late final ValueNotifier<bool> formStateEmitter;

  ///Key
  final GlobalKey<FormState> formKey = GlobalKey<FormState>();

  //Controller
  late final TextEditingController pinCtrl;

  final otpFocus = FocusNode();

  AuthPinScreenArguments? get arguments => widget.arguments;

  String? get user => arguments?.user;

  bool get hasUser => user.hasValue;

  String get title {
    return hasUser
        ? LocaleKeys.welcomeBackUser.tr({"user": user!})
        : LocaleKeys.enterD1Pin;
  }

  String? get header {
    return hasUser ? null : "PIN";
  }

  String? get rider => LocaleKeys.enter4DigitCode;

  @override
  void initState() {
    super.initState();

    pinCtrl = TextEditingController();

    _trackValidity();

    formStateEmitter = ValueNotifier(_formValidityStatus());
  }

  @override
  void dispose() {
    formStateEmitter.dispose();
    pinCtrl.dispose();
    super.dispose();
  }

  _trackValidity() {
    pinCtrl.addListener(() {
      formStateEmitter.value = _formValidityStatus();
    });
  }

  bool _formValidityStatus() {
    return AppValidators.minLength(pinCtrl.text, length: 4) == null;
  }

  _handleAuthResponse(NetworkResponse<MeResponse>? response) {
    if (response == null) return;
    if (response.hasError) {
      // ignore: use_build_context_synchronously
      context.showErrorNotification(LocaleKeys.loginError.tr());
      return;
    }
    final onboardingStep = response.data?.onboardingStep;
    final nextRoute = onboardingStep?.nextRoutePath ?? DashboardRoutes.home;
    AppRouter.pushAndRemoveUntil(nextRoute);
  }

  _loginWithPin(String pin) async {
    final state = context.read<LoginState>();
    final response = await state.loginWithPin(pin);
    _handleAuthResponse(response);
  }

  _loginWithBio() async {
    final state = context.read<LoginState>();
    final response = await state.loginWithBio();
    _handleAuthResponse(response);
  }

  submit([bool withBio = false]) async {
    if (withBio) return _loginWithBio();
    if (!context.validateForm(formKey)) return;
    _loginWithPin(pinCtrl.text);
  }
}

mixin AuthPinUpdateMixin<T extends AuthPinUpdateScreen> on State<T> {
  late final ValueNotifier<bool> formStateEmitter;

  ///Key
  final GlobalKey<FormState> formKey = GlobalKey<FormState>();

  //Controller
  late final TextEditingController pinCtrl;

  final otpFocus = FocusNode();

  AuthPinScreenArguments? get arguments => widget.arguments;

  bool get hasPin => pin.hasValue;

  bool get hasOldPin => oldPin.hasValue;

  String? get pin => arguments?.pin;

  String? get oldPin => arguments?.oldPin;

  String get title {
    if (arguments == null) return LocaleKeys.enterCurrentPin;
    return hasPin ? LocaleKeys.confirmPin : LocaleKeys.changeYourPin;
  }

  String? get rider {
    if (arguments == null) return LocaleKeys.enterCurrent4DigitPin;
    return hasPin ? null : LocaleKeys.enter4DigitPin4Transfer;
  }

  @override
  void initState() {
    super.initState();

    pinCtrl = TextEditingController();

    _trackValidity();

    formStateEmitter = ValueNotifier(_formValidityStatus());
  }

  @override
  void dispose() {
    formStateEmitter.dispose();
    pinCtrl.dispose();
    super.dispose();
  }

  _trackValidity() {
    pinCtrl.addListener(() {
      formStateEmitter.value = _formValidityStatus();
    });
  }

  bool _formValidityStatus() {
    return AppValidators.minLength(pinCtrl.text, length: 4) == null;
  }

  _handleRouting() {
    if (!hasOldPin && !hasPin) {
      AppRouter.pushNamed(
        AuthRoutes.pinChangeNewPin,
        arguments: AuthPinScreenArguments(oldPin: pinCtrl.text),
      );
      return;
    }
    if (hasOldPin && !hasPin) {
      AppRouter.pushNamed(
        AuthRoutes.pinChangeConfirm,
        arguments: AuthPinScreenArguments(
          oldPin: pinCtrl.text,
          pin: pinCtrl.text,
        ),
      );
      return;
    }
    AppRouter.pushNamed(
      AuthRoutes.pin,
      arguments: AuthPinScreenArguments(
        successRouteSettings: arguments?.successRouteSettings,
        user: "Hailey",
      ),
    );
  }

  submit() async {
    if (context.validateForm(formKey)) return _handleRouting();
  }
}
