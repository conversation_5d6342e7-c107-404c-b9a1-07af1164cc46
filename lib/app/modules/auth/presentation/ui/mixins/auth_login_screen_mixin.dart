// ignore_for_file: use_build_context_synchronously

import 'package:flutter/material.dart';
import 'package:day1/day1.dart';
import 'package:provider/provider.dart';

mixin AuthLoginMixin<T extends AuthLoginScreen> on State<T> {
  late final ValueNotifier<bool> formStateEmitter;

  ///Key
  final GlobalKey<FormState> formKey = GlobalKey<FormState>();

  //Controller
  late final TextEditingController emailCtrl;
  late final TextEditingController passwordCtrl;

  @override
  void initState() {
    super.initState();

    final state = context.read<LoginState>();

    emailCtrl = TextEditingController(text: state.lastEmail);
    passwordCtrl = TextEditingController();

    _trackValidity();

    formStateEmitter = ValueNotifier(_formValidityStatus());
  }

  AuthLoginScreenArguments get arguments => widget.arguments;

  @override
  void dispose() {
    emailCtrl.dispose();
    formStateEmitter.dispose();
    super.dispose();
  }

  _trackValidity() {
    emailCtrl.addListener(() {
      formStateEmitter.value = _formValidityStatus();
    });
    passwordCtrl.addListener(() {
      formStateEmitter.value = _formValidityStatus();
    });
  }

  bool _formValidityStatus() {
    return AppValidators.emailValidator(emailCtrl.text) == null &&
        AppValidators.required(passwordCtrl.text) == null;
  }

  _verify2faCode(
    String otp,
    AuthResponse authData,
  ) async {
    final state = context.read<LoginState>();
    final response = await state.verifyCode(otp, authData);
    if (response.hasError) {
      context.showErrorNotification(LocaleKeys.loginError.tr());
      return;
    }
    final onboardingStep = response.data?.onboardingStep;
    final nextRoute = onboardingStep?.nextRoutePath ?? DashboardRoutes.home;
    AppRouter.pushAndRemoveUntil(nextRoute);
  }

  Future<NetworkResponse<AuthResponse>> _login(LoginParam param) async {
    final state = context.read<LoginState>();
    return state.login(param);
  }

  submit() async {
    if (!context.validateForm(formKey)) return;
    final param = LoginParam(
      email: emailCtrl.text,
      password: passwordCtrl.text,
    );
    final response = await _login(param);
    if (response.hasError) {
      context.showErrorNotification(LocaleKeys.loginError.tr());
      return;
    }
    AppRouter.pushNamed(
      AuthRoutes.verifyCode,
      arguments: Auth2faScreenArguments(
        successRouteSettings: arguments.successRouteSettings,
        param: param,
        phoneNumber: response.data?.phoneNumber,
        onResend: (param) {
          if (param == null) return;
          _login(param);
        },
        onCodeSubmitted: (otp) {
          _verify2faCode(otp, response.data!);
        },
      ),
    );
  }
}

mixin Auth2faMixin<T extends Auth2faScreen> on State<T> {
  late final ValueNotifier<bool> formStateEmitter;
  late final ValueNotifier<Stream<int>> countDownStream;

  ///Key
  final GlobalKey<FormState> formKey = GlobalKey<FormState>();

  //Controller
  late final TextEditingController otpCtrl;

  final otpFocus = FocusNode();

  @override
  void initState() {
    super.initState();

    otpCtrl = TextEditingController();

    countDownStream = ValueNotifier(_countDownDuration);

    _trackValidity();

    formStateEmitter = ValueNotifier(_formValidityStatus());
  }

  @override
  void dispose() {
    formStateEmitter.dispose();
    otpCtrl.dispose();
    super.dispose();
  }

  Stream<int> get _countDownDuration {
    return AppHelpers.countDown(60).asBroadcastStream();
  }

  resendOtp() {
    final state = context.read<LoginState>();
    if (state.isLoading) return;
    if (arguments.onResend == null) return false;

    otpCtrl.clear();

    context.showInfoNotification(LocaleKeys.resendingOtp.tr());
    arguments.onResend?.call(arguments.param);

    countDownStream.value = _countDownDuration;
  }

  _trackValidity() {
    otpCtrl.addListener(() {
      formStateEmitter.value = _formValidityStatus();
    });
  }

  bool get canResend => arguments.onResend != null;

  Auth2faScreenArguments get arguments => widget.arguments;

  String? get phoneNumber => arguments.phoneNumber?.lastChars;

  bool _formValidityStatus() {
    return AppValidators.minLength(otpCtrl.text) == null;
  }

  submit() async {
    if (!context.validateForm(formKey)) return;
    if (arguments.isSetup) {
      AppRouter.pushNamed(
        AuthRoutes.twoFaSuccess,
        arguments: widget.arguments,
      );
      return;
    }
    arguments.onCodeSubmitted?.call(otpCtrl.text);
  }
}
