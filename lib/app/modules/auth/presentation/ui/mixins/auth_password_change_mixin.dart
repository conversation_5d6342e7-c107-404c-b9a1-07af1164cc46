import 'package:flutter/material.dart';
import 'package:day1/day1.dart';

mixin AuthPasswordChangeMixin<T extends AuthPasswordChangeScreen> on State<T> {
  late final ValueNotifier<bool> formStateEmitter;

  ///Key
  final GlobalKey<FormState> formKey = GlobalKey<FormState>();

  //Controller
  late final TextEditingController oldPasswordCtrl;
  late final TextEditingController passwordCtrl;
  late final TextEditingController passwordConfirmCtrl;

  final FocusNode oldPassFocus = FocusNode();
  final FocusNode passFocus = FocusNode();
  final FocusNode confirmFocus = FocusNode();

  @override
  void initState() {
    super.initState();
    oldPasswordCtrl = TextEditingController();
    passwordCtrl = TextEditingController();
    passwordConfirmCtrl = TextEditingController();

    _trackValidity();

    formStateEmitter = ValueNotifier(_formValidityStatus());
  }

  @override
  void dispose() {
    oldPasswordCtrl.dispose();
    passwordCtrl.dispose();
    passwordConfirmCtrl.dispose();
    formStateEmitter.dispose();
    super.dispose();
  }

  _trackValidity() {
    oldPasswordCtrl.addListener(() {
      formStateEmitter.value = _formValidityStatus();
    });
    passwordCtrl.addListener(() {
      formStateEmitter.value = _formValidityStatus();
    });
    passwordConfirmCtrl.addListener(() {
      formStateEmitter.value = _formValidityStatus();
    });
  }

  bool _formValidityStatus() {
    final oldPasswordIsValid =
        AppValidators.required(oldPasswordCtrl.text) == null;
    final passwordIsValid =
        AppValidators.passwordValidator(passwordCtrl.text) == null;
    final passwordConfirmIsValid =
        AppValidators.passwordValidator(passwordConfirmCtrl.text) == null;
    return passwordIsValid && passwordConfirmIsValid && oldPasswordIsValid;
  }

  submit() async {
    if (!context.validateForm(formKey)) return;
    context.showNotification(LocaleKeys.passwordUpdated.tr());
    AppRouter.popView();
  }
}
