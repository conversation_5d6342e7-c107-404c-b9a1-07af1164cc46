import 'package:flutter/material.dart';
import 'package:day1/day1.dart';

class Auth2faSetupScreen extends AppStatelessWidget {
  final Auth2faScreenArguments arguments;
  const Auth2faSetupScreen({super.key, required this.arguments});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      appBar: DoAppBar(
        title: LocaleKeys.twoFA.tr(),
        leading: const App<PERSON>lose<PERSON>utton(),
      ),
      body: AuthStatusCard(
        title: LocaleKeys.enhancedLoginSecurity.tr(),
        decription: LocaleKeys.addExtraAuthStep.tr(),
        btnText: LocaleKeys.setUpNow.tr(),
        action: () {
          AppRouter.pushNamed(
            AuthRoutes.twoFaPhoneNumber,
            arguments: arguments,
          );
        },
      ),
    );
  }
}
