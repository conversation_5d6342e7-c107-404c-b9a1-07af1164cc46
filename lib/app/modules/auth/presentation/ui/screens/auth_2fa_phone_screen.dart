import 'package:flutter/material.dart';
import 'package:day1/day1.dart';

class Auth2faPhoneScreen extends AppStatefulWidget {
  final Auth2faScreenArguments arguments;
  const Auth2faPhoneScreen({super.key, required this.arguments});

  @override
  State<StatefulWidget> createState() => _Auth2faPhoneScreenState();
}

class _Auth2faPhoneScreenState extends State<Auth2faPhoneScreen> {
  late final TextEditingController _controller;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: "****** 564 1243");
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      appBar: DoAppBar(title: LocaleKeys.twoFA.tr()),
      body: AppCard(
        margin: context.insets.defaultAllInsets,
        padding: context.insets.zero,
        child: ListView(
          padding: context.insets.defaultCardInsets,
          children: [
            AppText(
              LocaleKeys.aVerificationCodeWillBeSentToYourNumber.tr(),
              style: context.textStyle.b2(color: context.secondaryTextColor),
            ),
            const AppGap.y12(),
            AppTextField(
              controller: _controller,
              isEnabled: false,
              decoration: context.inputStyle.outlined(false),
            ),
            ...const AppGap.y20() * 15,
            AppButton(
              onPressed: () {
                AppRouter.pushNamed(
                  AuthRoutes.twoFaCode,
                  arguments: widget.arguments,
                );
              },
              text: LocaleKeys.continueText.tr(),
            )
          ],
        ),
      ),
    );
  }
}
