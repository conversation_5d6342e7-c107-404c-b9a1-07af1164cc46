import 'package:flutter/material.dart';
import 'package:day1/day1.dart';

class AuthPasswordChangeScreen extends AppStatefulWidget {
  const AuthPasswordChangeScreen({super.key});

  @override
  State<AuthPasswordChangeScreen> createState() =>
      _AuthPasswordChangeScreenState();
}

class _AuthPasswordChangeScreenState extends State<AuthPasswordChangeScreen>
    with AuthPasswordChangeMixin {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      appBar: const DoAppBar(),
      body: AppForm(
        formKey: formKey,
        child: AppCard(
          margin: context.insets.defaultAllInsets.add(
            context.insets.onlySp(bottom: AppFontSizes.px32),
          ),
          padding: context.insets.zero,
          child: ListView(
            padding: context.insets.defaultCardInsets,
            children: [
              AppFormHeader(title: LocaleKeys.changePassword.tr()),
              FormLabel(label: LocaleKeys.currentPassword.tr()),
              AppPasswordField(
                controller: oldPasswordCtrl,
                hintText: LocaleKeys.enterPassword.tr(),
                focusNode: oldPassFocus,
                validator: AppValidators.required,
                autoFocus: true,
              ),
              const AppGap.y24(),
              FormLabel(label: LocaleKeys.newPassword.tr()),
              AppPasswordField(
                controller: passwordCtrl,
                hintText: LocaleKeys.enterPassword.tr(),
                focusNode: passFocus,
                action: TextInputAction.done,
              ),
              const AppGap.y24(),
              FormLabel(label: LocaleKeys.confirmPassword.tr()),
              AppPasswordField(
                controller: passwordConfirmCtrl,
                hintText: LocaleKeys.reEnterPassword.tr(),
                focusNode: confirmFocus,
                validator: (value) => AppValidators.matchValidator(
                  value,
                  passwordCtrl.text,
                  message: LocaleKeys.passwordsDoNotMatch.tr(),
                ),
                action: TextInputAction.done,
              ),
              const AppGap.y24(),
              PasswordRequirementsList(passwordCtrl),
              const AppGap.y80(),
              BoolListener(
                valueListenable: formStateEmitter,
                builder: (isValid) {
                  return AppButton(
                    text: LocaleKeys.changePassword.tr(),
                    isDisabled: !isValid,
                    onPressed: submit,
                  );
                },
              ),
            ],
          ),
        ),
      ),
    );
  }
}
