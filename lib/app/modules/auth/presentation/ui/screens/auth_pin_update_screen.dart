import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class AuthPinUpdateScreen extends AppStatefulWidget {
  final AuthPinScreenArguments? arguments;

  const AuthPinUpdateScreen(this.arguments, {super.key});

  @override
  State<AuthPinUpdateScreen> createState() => _AuthPinUpdateScreenState();
}

class _AuthPinUpdateScreenState extends State<AuthPinUpdateScreen>
    with AuthPinUpdateMixin {
  @override
  Widget build(BuildContext context) {
    const p = AppFontSizes.px32;

    return Scaffold(
      appBar: DoAppBar(title: LocaleKeys.changeYourPin.tr()),
      body: AppForm(
        formKey: formKey,
        child: ListView(
          padding: context.insets.defaultAllInsets,
          children: [
            AppCard(
              padding: context.insets.fromLTRBSp(p, p, p, p / 2),
              child: <PERSON>umn(
                mainAxisAlignment: MainAxisAlignment.center,
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  AppFormHeader(
                    title: title.tr(),
                    subTitle: rider?.tr(),
                  ),
                  IgnorePointer(
                    child: AppPinInput(
                      length: 4,
                      obscureText: false,
                      controller: pinCtrl,
                      fieldSize: const Size(70, 88),
                      validator: hasPin
                          ? (text) => AppValidators.matchValidator(
                                text,
                                pin,
                                message: LocaleKeys.pinDontMatch.tr(),
                              )
                          : null,
                    ),
                  )
                ],
              ),
            ),
            const AppGap.y12(),
            AppCard(child: AppKeyPad(controller: pinCtrl, limit: 4)),
            const AppGap.y12(),
            BoolListener(
              valueListenable: formStateEmitter,
              builder: (isValid) {
                return Padding(
                  padding: context.insets.symmetricSp(horizontal: p),
                  child: AppButton(
                    text: LocaleKeys.done.tr(),
                    onPressed: submit,
                    isDisabled: !isValid,
                  ),
                );
              },
            )
          ],
        ),
      ),
    );
  }
}
