import 'package:flutter/material.dart';
import 'package:day1/day1.dart';
import 'package:provider/provider.dart';

class Auth2faScreen extends AppStatefulWidget {
  final Auth2faScreenArguments arguments;
  const Auth2faScreen({super.key, required this.arguments});

  @override
  State<StatefulWidget> createState() => _OTPTabState();
}

class _OTPTabState extends State<Auth2faScreen> with Auth2faMixin {
  @override
  Widget build(BuildContext context) {
    final state = context.watch<LoginState>();

    return Scaffold(
      resizeToAvoidBottomInset: false,
      appBar: DoAppBar(title: LocaleKeys.twoFA.tr()),
      body: SingleChildScrollView(
        padding: context.insets.defaultAllInsets.add(
          context.insets.onlySp(
            bottom: AppFontSizes.px16,
          ),
        ),
        child: AppCard(
          child: AppForm(
            key: const Key("auth_login_form"),
            formKey: form<PERSON><PERSON>,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              mainAxisSize: MainAxisSize.min,
              children: [
                AppRichText(
                  LocaleKeys.enter2FACode.tr({"num": phoneNumber.value}),
                  textStyle: context.textStyle.b2(
                    color: context.secondaryTextColor,
                  ),
                ),
                const AppGap.y32(),
                AppPinInput(
                  key: const Key("otp_input"),
                  autoFocus: true,
                  focusNode: otpFocus,
                  length: 6,
                  controller: otpCtrl,
                  obscureText: true,
                  keyboardType: TextInputType.number,
                  onFieldSubmitted: (_) => submit(),
                ),
                CountDownListener(
                  countDownStream,
                  onResend: resendOtp,
                  padding: context.insets.onlySp(
                    top: AppFontSizes.px24,
                    bottom: AppFontSizes.px16,
                  ),
                ),
                BoolListener(
                  builder: (valid) {
                    return AppButton(
                      key: const Key("auth_login_submit_btn"),
                      text: LocaleKeys.continueText.tr(),
                      onPressed: submit,
                      isDisabled: !valid || state.isLoading,
                      isLoading: state.isLoading,
                    );
                  },
                  valueListenable: formStateEmitter,
                ),
                if (!widget.arguments.isSetup)
                  CountDownListener(
                    countDownStream,
                    onResend: resendOtp,
                    padding: context.insets.zero,
                    builder: (value) {
                      return Column(
                        crossAxisAlignment: CrossAxisAlignment.stretch,
                        children: [
                          const AppGap.y160(),
                          AppOutlineButton(
                            text: LocaleKeys.resendVia.tr({"source": "SMS"}),
                            icon: const AppIcon(AppIcons.messageText),
                            onPressed: resendOtp,
                            isDisabled: state.isLoading || value != 0,
                          ),
                          const AppGap.y16(),
                          AppOutlineButton(
                            text:
                                LocaleKeys.resendVia.tr({"source": "WhatsApp"}),
                            icon: const AppIcon(AppIcons.chatBubbleCheck),
                            onPressed: resendOtp,
                            isDisabled: state.isLoading || value != 0,
                          ),
                        ],
                      );
                    },
                  )
              ],
            ),
          ),
        ),
      ),
    );
  }
}
