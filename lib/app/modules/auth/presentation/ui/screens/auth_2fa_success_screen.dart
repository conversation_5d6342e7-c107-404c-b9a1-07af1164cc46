import 'package:flutter/material.dart';
import 'package:day1/day1.dart';

class Auth2faSuccessScreen extends AppStatelessWidget {
  final Auth2faScreenArguments arguments;
  const Auth2faSuccessScreen({super.key, required this.arguments});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      appBar: DoAppBar(title: LocaleKeys.twoFA.tr()),
      body: AuthStatusCard(
        title: LocaleKeys.success.tr(),
        decription: LocaleKeys.your2faSetupIsDone.tr(),
        btnText: LocaleKeys.done.tr(),
        action: () {
          AppRouter.pushNamed(AuthRoutes.twoFaActive, arguments: arguments);
        },
      ),
    );
  }
}
