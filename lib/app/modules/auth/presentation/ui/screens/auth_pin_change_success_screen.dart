import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class PinUpdateSuccessScreen extends AppStatelessWidget {
  const PinUpdateSuccessScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: DoAppBar(
        title: LocaleKeys.confirmAmount.ctr(),
        leading: const AppCloseButton(),
      ),
      resizeToAvoidBottomInset: false,
      body: SingleChildScrollView(
        child: AppCard(
          margin: context.insets.defaultAllInsets,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              const AppGap.y48(),
              const AppIcon(
                AppIcons.checkCircle,
                size: AppFontSizes.px75,
                color: AppColors.lemon140,
                alignment: Alignment.centerLeft,
              ),
              const AppGap.y32(),
              AppForm<PERSON>eader(
                title: LocaleKeys.allDone.tr(),
                titleStyle: context.textStyle.h5(),
                subTitle: LocaleKeys.congratsCardIsActivated.tr(),
                bottomGap: const AppGap.y48(),
              ),
              AppOutlineButton(
                onPressed: () {
                  AppRouter.pushAndRemoveUntil(DashboardRoutes.home);
                },
                text: LocaleKeys.goBackHome.tr(),
              ),
              const AppGap.y16(),
              AppButton(
                onPressed: () {
                  AppRouter.pushAndRemoveUntil(DashboardRoutes.cards);
                },
                text: LocaleKeys.goToMyCard.tr(),
              ),
              const AppGap.y48(),
            ],
          ),
        ),
      ),
    );
  }
}
