import 'package:flutter/material.dart';
import 'package:day1/day1.dart';

class Auth2faActiveScreen extends AppStatelessWidget {
  final Auth2faScreenArguments arguments;
  const Auth2faActiveScreen({super.key, required this.arguments});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      appBar: DoAppBar(title: LocaleKeys.twoFA.tr()),
      body: AuthStatusCard(
        title: LocaleKeys.twoFaIsOn.tr(),
        decription: LocaleKeys.yourWillReceiveCodesByText.tr(),
        btnText: LocaleKeys.turnOff.tr(),
        action: () {
          AppRouter.pushNamed(
            AuthRoutes.verifyCode,
            arguments: Auth2faScreenArguments(
              successRouteSettings: arguments.successRouteSettings,
            ),
          );
        },
      ),
    );
  }
}
