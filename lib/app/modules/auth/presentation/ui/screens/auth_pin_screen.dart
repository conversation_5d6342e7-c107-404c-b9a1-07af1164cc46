import 'package:day1/day1.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

class AuthPinScreen extends AppStatefulWidget {
  final AuthPinScreenArguments? arguments;

  const AuthPinScreen(this.arguments, {super.key});

  @override
  State<AuthPinScreen> createState() => _AuthPinScreenState();
}

class _AuthPinScreenState extends State<AuthPinScreen> with AuthPinMixin {
  @override
  Widget build(BuildContext context) {
    final state = context.watch<LoginState>();

    return Scaffold(
      appBar: DoAppBar(title: header),
      bottomNavigationBar: Padding(
        padding: context.insets.defaultAllInsets,
        child: Builder(builder: (_) {
          return Padding(
            padding: context.insets.onlySp(
              left: AppFontSizes.px32,
              right: AppFontSizes.px32,
              bottom: AppFontSizes.px16,
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                AppText(
                  LocaleKeys.notYourAccount.tr(),
                  style: context.textStyle.b2(),
                ),
                const Spacer(),
                AppTextButton(
                  text: "Log out",
                  onPressed: locator<AuthSettingsState>().logout,
                  size: ButtonSize.medium,
                )
              ],
            ),
          );
        }),
      ),
      body: AppPinForm(
        formKey,
        onBioAuth: submit,
        onSubmit: (_) => submit(),
        otpCtrl: pinCtrl,
        title: title.tr(),
        rider: rider?.tr(),
        isLoading: state.isLoading,
        formStateEmitter: formStateEmitter,
      ),
    );
  }
}
