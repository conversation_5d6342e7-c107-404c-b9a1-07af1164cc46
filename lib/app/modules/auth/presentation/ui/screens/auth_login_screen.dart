import 'package:flutter/material.dart';
import 'package:day1/day1.dart';
import 'package:provider/provider.dart';

class AuthLoginScreen extends AppStatefulWidget {
  final AuthLoginScreenArguments arguments;
  const AuthLoginScreen({super.key, required this.arguments});

  @override
  State<StatefulWidget> createState() => _LoginTabState();
}

class _LoginTabState extends State<AuthLoginScreen> with AuthLoginMixin {
  @override
  Widget build(BuildContext context) {
    final state = context.watch<LoginState>();

    return Scaffold(
      appBar: const DoAppBar(),
      resizeToAvoidBottomInset: false,
      body: AppForm(
        key: const Key("auth_login_form"),
        formKey: formKey,
        child: AppCard(
          margin: context.insets.defaultAllInsets.add(
            context.insets.onlySp(
              bottom: AppFontSizes.px16,
            ),
          ),
          padding: context.insets.zero,
          child: ListView(
            padding: context.insets.defaultCardInsets,
            children: [
              AppFormHeader(title: LocaleKeys.welcomeBack.tr()),
              FormLabel(label: LocaleKeys.enterYourEmail.tr()),
              AppEmailField(
                key: const Key("auth_login_email_input"),
                controller: emailCtrl,
                isEnabled: !state.isLoading,
                action: TextInputAction.next,
                autoFocus: true,
              ),
              const AppGap.y24(),
              FormLabel(label: LocaleKeys.password.tr()),
              AppPasswordField(
                key: const Key("auth_login_password_input"),
                controller: passwordCtrl,
                hintText: LocaleKeys.enterPassword.tr(),
                isEnabled: !state.isLoading,
                validator: AppValidators.required,
                action: TextInputAction.done,
                autoFocus: true,
              ),
              AppTextButton(
                text: LocaleKeys.forgotPassword.tr(),
                onPressed: () {},
                textAlign: TextAlign.end,
                size: ButtonSize.medium,
              ),
              const AppGap.y24(),
              BoolListener(
                builder: (valid) {
                  return AppButton(
                    key: const Key("auth_login_submit_btn"),
                    text: LocaleKeys.continueText.tr(),
                    onPressed: submit,
                    isDisabled: !valid || state.isLoading,
                    isLoading: state.isLoading,
                  );
                },
                valueListenable: formStateEmitter,
              ),
              const AppGap.y16(),
              InkWell(
                onTap: () {
                  AppRouter.pushNamed(SignupRoutes.email);
                },
                child: AppRichText(
                  LocaleKeys.noAccountRegister.tr(),
                  textAlign: TextAlign.center,
                ),
              )
            ],
          ),
        ),
      ),
    );
  }
}
