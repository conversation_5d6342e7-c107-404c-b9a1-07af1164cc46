import 'package:flutter/material.dart';
import 'package:day1/day1.dart';

class AuthLoginScreenArguments {
  final RouteSettings? successRouteSettings;
  final String? user;

  const AuthLoginScreenArguments({this.successRouteSettings, this.user});
}

class AuthPinScreenArguments {
  final RouteSettings? successRouteSettings;
  final String? user;
  final String? oldPin;
  final String? pin;

  const AuthPinScreenArguments({
    this.successRouteSettings,
    this.user,
    this.oldPin,
    this.pin,
  });
}

class Auth2faScreenArguments {
  final RouteSettings? successRouteSettings;
  final String? phoneNumber;
  final LoginParam? param;
  final OnChanged<String>? onCodeSubmitted;
  final OnChanged<LoginParam?>? onResend;
  final bool isSetup;

  const Auth2faScreenArguments({
    this.successRouteSettings,
    this.onCodeSubmitted,
    this.phoneNumber,
    this.onResend,
    this.param,
    this.isSetup = false,
  });

  Auth2faScreenArguments copyWith({
    RouteSettings? successRouteSettings,
    LoginParam? param,
    OnChanged<String>? onCodeSubmitted,
    bool? isSetup,
  }) {
    return Auth2faScreenArguments(
      successRouteSettings: successRouteSettings ?? this.successRouteSettings,
      param: param ?? this.param,
      onCodeSubmitted: onCodeSubmitted ?? this.onCodeSubmitted,
      isSetup: isSetup ?? this.isSetup,
    );
  }
}
