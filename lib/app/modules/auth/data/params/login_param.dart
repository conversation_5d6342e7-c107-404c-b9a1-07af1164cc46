import 'package:day1/day1.dart';

class <PERSON>ginParam implements Codable {
  final String email;
  final String password;

  const LoginParam({required this.email, required this.password});

  LoginParam copyWith({
    String? email,
    String? password,
  }) {
    return LoginParam(
      email: email ?? this.email,
      password: password ?? this.password,
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {"email": email.trim(), "password": password.trim()};
  }
}

class PasswordResetParam implements MapCodable {
  final String email;
  final String confirmPassword;
  final String password;
  final String resetToken;

  PasswordResetParam({
    required this.email,
    required this.confirmPassword,
    required this.password,
    required this.resetToken,
  });

  PasswordResetParam copyWith({
    String? email,
    String? confirmPassword,
    String? password,
    String? resetToken,
  }) {
    return PasswordResetParam(
      email: email ?? this.email,
      confirmPassword: confirmPassword ?? this.confirmPassword,
      password: password ?? this.password,
      resetToken: resetToken ?? this.resetToken,
    );
  }

  @override
  Map<String, dynamic> to<PERSON><PERSON>() {
    return {
      "email": email,
      "confirmPassword": confirmPassword,
      "password": password,
      "resetToken": resetToken,
    };
  }
}

sealed class AuthParamType {
  String get key;
}

class TwoFaAuthParamType extends AuthParamType {
  @override
  final key = "otp";
}

class PinAuthParamType extends AuthParamType {
  @override
  final key = "pin";
}

class PubKeyParamType extends AuthParamType {
  @override
  final key = "publicKey";
}

class AuthParam<T extends AuthParamType> implements MapCodable {
  final AuthParamType type;
  final String data;
  final String? email;

  const AuthParam({required this.data, required this.type, this.email});

  AuthParam copyWith({String? data}) {
    return AuthParam(data: data ?? this.data, type: type);
  }

  @override
  Map<String, dynamic> toJson() {
    return {type.key: data.trim(), if (email.hasValue) "email": email?.trim()};
  }
}
