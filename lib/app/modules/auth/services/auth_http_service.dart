import 'package:day1/day1.dart';

class AuthHttpService with AppHttpMixin, AppTaskMixin implements AuthService {
  final AppHttpService _service;
  final AppSessionService _sessionService;

  AuthHttpService(
    this._service,
    this._sessionService,
    AuthMockInterceptor? interceptor,
  ) {
    if (interceptor != null) {
      _service.attachInterceptor(interceptor);
    }
  }

  @override
  String get lastEmail => _sessionService.lastEmail ?? "";

  @override
  NetworkCallResponse<NoResponse> completePasswordReset(
    PasswordResetParam param,
  ) {
    return requestHandler(() async {
      await _service.patch("/auth/reset-password", body: param);
      return const NoResponse();
    });
  }

  @override
  NetworkCallResponse<AuthResponse> login(LoginParam param) {
    return requestHandler(() async {
      final request = await _service.post("/auth/login", body: param);
      return AuthResponse.fromJson(request.parsedData);
    });
  }

  @override
  NetworkCallResponse<AuthResponse> loginWithBio(
    AuthParam<PubKeyParamType> param,
  ) {
    return requestHandler(() async {
      final request = await _service.post(
        "/auth/public-key-login",
        body: param,
      );
      return AuthResponse.fromJson(request.parsedData);
    });
  }

  @override
  NetworkCallResponse<AuthResponse> loginWithPin(
    AuthParam<PinAuthParamType> param,
  ) {
    return requestHandler(() async {
      final request = await _service.post("/auth/pin-login", body: param);
      return AuthResponse.fromJson(request.parsedData);
    });
  }

  @override
  NetworkCallResponse<NoResponse> startPasswordReset(EmailParam param) {
    return requestHandler(() async {
      await _service.patch("/auth/forgot-password", body: param);
      return const NoResponse();
    });
  }

  @override
  NetworkCallResponse<NoResponse> verify2faCode(
    AuthParam<TwoFaAuthParamType> param,
  ) {
    return requestHandler(() async {
      await _service.post("/auth/two-fa", body: param);
      return const NoResponse();
    });
  }
}
