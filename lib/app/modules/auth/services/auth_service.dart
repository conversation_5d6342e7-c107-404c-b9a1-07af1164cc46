import 'package:day1/day1.dart';

abstract class AuthService {
  NetworkCallResponse<NoResponse> startPasswordReset(EmailParam param);
  NetworkCallResponse<NoResponse> completePasswordReset(
    PasswordResetParam param,
  );
  NetworkCallResponse<AuthResponse> login(LoginParam param);
  NetworkCallResponse<AuthResponse> loginWithBio(
    AuthParam<PubKeyParamType> param,
  );
  NetworkCallResponse<AuthResponse> loginWithPin(
    AuthParam<PinAuthParamType> param,
  );
  NetworkCallResponse<NoResponse> verify2faCode(
    AuthParam<TwoFaAuthParamType> param,
  );
  String get lastEmail;
}
