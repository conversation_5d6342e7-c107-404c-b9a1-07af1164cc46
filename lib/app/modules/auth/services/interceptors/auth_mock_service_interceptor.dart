import 'package:dio/dio.dart';
import 'package:day1/day1.dart';

class AuthMockInterceptor extends Interceptor {
  final AuthMockResponses _mock;
  // ignore: unused_field
  final MeMockResponses _userMock;

  AuthMockInterceptor(this._mock, this._userMock);

  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    Response? response = switch (options.path) {
      "/auth/google/redirect" ||
      "/auth/facebook/redirect" ||
      "/auth/apple/redirect" ||
      "/auth/test/redirect" ||
      "/auth/login" ||
      "/me/token" =>
        Response(
          requestOptions: options,
          statusCode: 200,
          data: _mock.loginResponse,
        ),
      "/me/deactivate" => Response(
          requestOptions: options,
          statusCode: 204,
        ),
      _ => null,
    };

    if (response != null) {
      return handler.resolve(response);
    }

    super.onRequest(options, handler);
  }
}
