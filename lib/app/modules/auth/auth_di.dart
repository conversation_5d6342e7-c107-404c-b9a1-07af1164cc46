import 'package:day1/day1.dart';

void registerAuthDI(DoAppConfig config) {
  if (config.isMock) {
    ///[Mocks]
    locator.registerLazySingleton<AuthMockResponses>(
      () => AuthMockResponses(),
    );

    ///[Mock Interceptors]
    locator.registerLazySingleton<AuthMockInterceptor>(
      () => AuthMockInterceptor(locator(), locator()),
    );
  }

  ///[Services]
  locator.registerFactory<AuthService>(() {
    return AuthHttpService(
      locator(),
      locator(),
      config.isMock ? locator() : null,
    );
  });

  ///[States]
  locator.registerLazySingleton<LoginState>(() {
    return LoginState(
      locator(),
      locator(),
      locator(),
      locator(),
      locator(),
    );
  });
  locator.registerLazySingleton<AuthSettingsState>(() {
    return AuthSettingsState(locator(), locator(), locator());
  });
}

void resetAuthDI() {
  locator<LoginState>().reset();
  locator<AuthSettingsState>().reset();
}
