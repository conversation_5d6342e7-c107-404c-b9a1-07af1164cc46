import 'package:equatable/equatable.dart';

enum PhysicalCardStatus { active, inactive, frozen }

class PhysicalCardData extends Equatable {
  final String name;
  final String number;
  final DateTime exipresAt;
  final String cvv;
  final PhysicalCardStatus status;

  const PhysicalCardData({
    required this.name,
    required this.number,
    required this.status,
    required this.cvv,
    required this.exipresAt,
  });

  PhysicalCardData activate() {
    return PhysicalCardData(
      name: name,
      number: number,
      status: PhysicalCardStatus.active,
      cvv: cvv,
      exipresAt: exipresAt,
    );
  }

  PhysicalCardData freeze() {
    final isFrozen = status == PhysicalCardStatus.frozen;
    return PhysicalCardData(
      name: name,
      number: number,
      status: isFrozen ? PhysicalCardStatus.active : PhysicalCardStatus.frozen,
      cvv: cvv,
      exipresAt: exipresAt,
    );
  }

  bool get isActive => status == PhysicalCardStatus.active;
  bool get isFrozen => status == PhysicalCardStatus.frozen;

  @override
  List<Object?> get props => [number, cvv, status];
}
