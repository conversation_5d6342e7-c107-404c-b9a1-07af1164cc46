import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class CardPickupFormScreen extends AppStatefulWidget {
  const CardPickupFormScreen({super.key});

  @override
  State<StatefulWidget> createState() => _CardPickupFormScreenState();
}

class _CardPickupFormScreenState extends State<CardPickupFormScreen> {
  final GlobalKey<FormState> formKey = GlobalKey();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: DoAppBar(title: LocaleKeys.airportPickup.tr()),
      body: AppForm(
        formKey: formKey,
        child: AppCard(
          margin: context.insets.defaultAllInsets,
          padding: EdgeInsets.zero,
          child: SingleChildScrollView(
            padding: context.insets.defaultCardInsets,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                AppFormHeader(
                  title: LocaleKeys.travelNPickupDetails.tr(),
                  subTitle: LocaleKeys.travleNPickupDetailsRider.tr(),
                ),
                FormLabel(label: LocaleKeys.stateOfArrival.tr()),
                AppDropDownField(
                  controller: TextEditingController(),
                  hintText: LocaleKeys.selectArrivalState.tr(),
                  overlayMargin: context.insets.symmetricSp(
                    horizontal: AppFontSizes.px46,
                  ),
                  options: [
                    SelectionData.fromLabel(
                      "Lagos",
                    ),
                    SelectionData.fromLabel(
                      "Abuja",
                    ),
                    SelectionData.fromLabel(
                      "Rivers (Port Harcourt)",
                    ),
                    SelectionData.fromLabel(
                      "Akwa Ibom (Uyo)",
                    ),
                  ],
                ),
                const AppGap.y24(),
                FormLabel(label: LocaleKeys.pickupAirport.tr()),
                AppDropDownField(
                  controller: TextEditingController(),
                  hintText: LocaleKeys.selectYourPickupAirport.tr(),
                  overlayMargin: context.insets.symmetricSp(
                    horizontal: AppFontSizes.px46,
                  ),
                  options: [
                    SelectionData.fromLabel(
                      "Murtala Muhammed International Airport (LOS)",
                    ),
                    SelectionData.fromLabel(
                      "Nnamdi Azikiwe International Airport (ABV)",
                    ),
                    SelectionData.fromLabel(
                      "Port Harcourt International Airport (PHC)",
                    ),
                    SelectionData.fromLabel(
                      "Akwa Ibom International Airport (QUO)",
                    ),
                  ],
                ),
                const AppGap.y24(),
                FormLabel(label: LocaleKeys.expectedTimeOnNigeriaArrival.tr()),
                AppDateField(controller: TextEditingController()),
                const AppGap.y24(),
                FormLabel(label: LocaleKeys.expectedArrivalTimeInNigeria.tr()),
                AppTimeField(controller: TextEditingController()),
                const AppGap.y24(),
                FormLabel(label: LocaleKeys.flightNumber.tr()),
                AppNumberField(
                  controller: TextEditingController(),
                  hintText: LocaleKeys.enterYourFlightNumber.tr(),
                ),
                const AppGap.y24(),
                FormLabel(label: LocaleKeys.alternativeContactNumber.tr()),
                AppPhoneField(
                  controller: TextEditingController(),
                  countryCode: ValueNotifier(null),
                ),
                const AppGap.y24(),
                const CardDeliveryReminderCard(),
                const AppGap.y24(),
                AppButton(
                  onPressed: () {
                    AppRouter.pushNamed(
                      PhysicalcardRoutes.orderStatus,
                      arguments: CardDeliveryType.pickup,
                    );
                    locator<CardState>().createCard();
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
