import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class CardDeliveryTypeScreen extends AppStatefulWidget {
  const CardDeliveryTypeScreen({super.key});

  @override
  State<StatefulWidget> createState() => _CardDeliveryTypeScreenState();
}

class _CardDeliveryTypeScreenState extends State<CardDeliveryTypeScreen> {
  final ValueNotifier<CardDeliveryType> deliveryType =
      ValueNotifier(CardDeliveryType.pickup);

  @override
  Widget build(BuildContext context) {
    final padding = context.insets.allSp(AppFontSizes.px24);

    return Scaffold(
      appBar: DoAppBar(title: LocaleKeys.deliveryMethod.tr()),
      body: SingleChildScrollView(
        padding: context.insets.defaultAllInsets,
        child: GenericListener(
          valueListenable: deliveryType,
          builder: (type) {
            return AppCard(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  AppFormHeader(
                    title: LocaleKeys.selectDeliveryMethod.tr(),
                    subTitle:
                        LocaleKeys.selectYourPreferredCardDeliveryMethod.tr(),
                    bottomGap: const AppGap.y32(),
                  ),
                  AppRadioCard(
                    value: CardDeliveryType.pickup,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    padding: padding,
                    groupValue: type,
                    icon: const AppIndicatorIconPrefix(
                      icon: AppIcons.airplane,
                    ),
                    title: LocaleKeys.airportPickup.tr(),
                    rider: LocaleKeys.collectCardAtAirport.tr(),
                    onChanged: (value) => deliveryType.value = value,
                  ),
                  const AppGap.y12(),
                  AppRadioCard(
                    value: CardDeliveryType.delivery,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    padding: padding,
                    groupValue: type,
                    icon: const AppIndicatorIconPrefix(
                      icon: AppIcons.homeAlt,
                    ),
                    title: LocaleKeys.placeDelivery.tr(),
                    rider: LocaleKeys.receiveCardAtYourPlace.tr(),
                    onChanged: (value) => deliveryType.value = value,
                  ),
                  const AppGap.y48(),
                  AppButton(
                    onPressed: () {
                      AppRouter.pushNamed(
                        PhysicalcardRoutes.order,
                        arguments: CardOrderArguments(deliveryType.value),
                      );
                    },
                    text: LocaleKeys.continueText.tr(),
                  )
                ],
              ),
            );
          },
        ),
      ),
    );
  }
}
