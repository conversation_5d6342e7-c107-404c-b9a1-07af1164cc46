import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class CardOrderScreen extends AppStatelessWidget {
  final CardOrderArguments arguments;
  const CardOrderScreen(this.arguments, {super.key});

  String get _title {
    return isPickup
        ? LocaleKeys.airportPickup.tr()
        : LocaleKeys.placeOfStayDelivery.tr();
  }

  String get _instruction2 {
    return isPickup
        ? LocaleKeys.selectPickupAirport.tr()
        : LocaleKeys.providePlaceOfStayInfo.tr();
  }

  String get _header {
    return isPickup
        ? LocaleKeys.getYourCardAtAirport.tr()
        : LocaleKeys.getUrCardAtPlaceOfStay.tr();
  }

  bool get isPickup => arguments.deliveryType == CardDeliveryType.pickup;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: DoAppBar(title: _title),
      body: SingleChildScrollView(
        padding: context.insets.defaultAllInsets,
        child: AppCard(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              AppFormHeader(
                title: _header,
                subTitle: LocaleKeys.heresWhatToDoNext.tr(),
                subTitleStyle: context.textStyle.b2(
                  color: context.secondaryTextColor,
                ),
              ),
              AppCard(
                color: context.scaffoldBgColor,
                padding: context.insets.allSp(AppFontSizes.px24),
                child: Column(
                  children: [
                    CardOrderInstructionTile(
                      LocaleKeys.selectArrivalState.tr(),
                    ),
                    CardOrderInstructionTile(_instruction2),
                    CardOrderInstructionTile(
                      LocaleKeys.provideTravelInformation.tr(),
                    ),
                  ],
                ),
              ),
              const AppGap.y24(),
              AppButton(
                onPressed: () {
                  AppRouter.pushNamed(arguments.formRoute);
                },
                text: LocaleKeys.continueText.tr(),
              )
            ],
          ),
        ),
      ),
    );
  }
}
