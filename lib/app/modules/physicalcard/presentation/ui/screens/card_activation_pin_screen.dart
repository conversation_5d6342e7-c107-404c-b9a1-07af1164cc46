import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class CardActivationPinScreen extends AppStatefulWidget {
  const CardActivationPinScreen({super.key});

  @override
  State<StatefulWidget> createState() => _CardActivationPinScreenState();
}

class _CardActivationPinScreenState extends State<CardActivationPinScreen>
    with CardActivationPinMixin {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: DoAppBar(title: header),
      body: AppPinForm(
        formKey,
        onBioAuth: submit,
        btnText: btnText,
        onSubmit: (_) => submit(),
        otpCtrl: otpCtrl,
        title: title.tr(),
        formStateEmitter: formStateEmitter,
      ),
    );
  }
}
