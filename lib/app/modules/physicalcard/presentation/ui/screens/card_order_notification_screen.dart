import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class CardOrderNotificationScreen extends AppStatelessWidget {
  final CardDeliveryType argument;

  const CardOrderNotificationScreen(this.argument, {super.key});

  String get _para1 {
    return isPickup
        ? LocaleKeys.yourCardWillBeReadyOnArrival.tr()
        : LocaleKeys.cardWillBDeliveredAtPlaceOfStay.tr();
  }

  String get _para2 {
    return isPickup
        ? LocaleKeys.otpWillBeSentForPickup.tr()
        : LocaleKeys.otpWillBeSentForDelivery.tr();
  }

  bool get isPickup => argument == CardDeliveryType.pickup;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: DoAppBar(
        title: LocaleKeys.notification.tr(),
        leading: const AppCloseButton(),
      ),
      body: AppCard(
        margin: context.insets.defaultAllInsets.add(
          context.insets.onlySp(bottom: AppFontSizes.px178),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const AppIcon(
              AppIcons.checkCircle,
              size: AppFontSizes.px75,
              color: AppColors.lemon140,
              alignment: Alignment.centerLeft,
            ),
            const AppGap.y32(),
            AppFormHeader(
              title: LocaleKeys.thanksForYourSubmission.tr(),
              titleStyle: context.textStyle.h5(),
              subTitle: _para1,
            ),
            AppRichText(_para2, textStyle: context.textStyle.labelText()),
            const AppGap.y24(),
            AppText(
              LocaleKeys.safeJourney.tr(),
              style: context.textStyle.b3(
                weight: FontWeight.w500,
                color: context.disabledBtntextColor,
              ),
            ),
            const AppGap.y48(),
            AppButton(
              onPressed: () {
                AppRouter.pushAndRemoveUntil(DashboardRoutes.cards);
              },
              text: LocaleKeys.done.tr(),
            )
          ],
        ),
      ),
    );
  }
}
