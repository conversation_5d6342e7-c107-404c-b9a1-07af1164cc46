import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class CardDeliveryFormScreen extends AppStatefulWidget {
  const CardDeliveryFormScreen({super.key});

  @override
  State<StatefulWidget> createState() => _CardDeliveryFormScreenState();
}

class _CardDeliveryFormScreenState extends State<CardDeliveryFormScreen> {
  final GlobalKey<FormState> formKey = GlobalKey();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: DoAppBar(title: LocaleKeys.placeOfStayDelivery.tr()),
      body: AppForm(
        formKey: formKey,
        child: AppCard(
          margin: context.insets.defaultAllInsets,
          padding: EdgeInsets.zero,
          child: SingleChildScrollView(
            padding: context.insets.defaultCardInsets,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                AppFormHeader(
                  title: LocaleKeys.travelNDeliveryDetails.tr(),
                  subTitle: LocaleKeys.travelNDeliveryDetailsRider.tr(),
                ),
                FormLabel(label: LocaleKeys.stateOfArrival.tr()),
                AppDropDownField(
                  controller: TextEditingController(),
                  hintText: LocaleKeys.selectArrivalState.tr(),
                  overlayMargin: context.insets.symmetricSp(
                    horizontal: AppFontSizes.px46,
                  ),
                  options: [
                    SelectionData.fromLabel(
                      "Lagos",
                    ),
                    SelectionData.fromLabel(
                      "Abuja",
                    ),
                    SelectionData.fromLabel(
                      "Rivers (Port Harcourt)",
                    ),
                    SelectionData.fromLabel(
                      "Akwa Ibom (Uyo)",
                    ),
                  ],
                ),
                const AppGap.y24(),
                FormLabel(label: LocaleKeys.placeOfStay.tr()),
                AppTextField(
                  controller: TextEditingController(),
                  hintText: LocaleKeys.enterPlaceOfStay.tr(),
                ),
                const AppGap.y24(),
                FormLabel(label: LocaleKeys.placeOfStayAddress.tr()),
                AppTextField(
                  controller: TextEditingController(),
                  hintText: LocaleKeys.enterPlaceOfStayAddress.tr(),
                ),
                const AppGap.y24(),
                FormLabel(label: LocaleKeys.expectedTimeOnNigeriaArrival.tr()),
                AppDateField(controller: TextEditingController()),
                const AppGap.y24(),
                FormLabel(label: LocaleKeys.expectedArrivalTimeInNigeria.tr()),
                AppTimeField(controller: TextEditingController()),
                const AppGap.y24(),
                FormLabel(label: LocaleKeys.flightNumber.tr()),
                AppNumberField(
                  controller: TextEditingController(),
                  hintText: LocaleKeys.enterYourFlightNumber.tr(),
                ),
                const AppGap.y24(),
                FormLabel(label: LocaleKeys.alternativeContactNumber.tr()),
                AppPhoneField(
                  controller: TextEditingController(),
                  countryCode: ValueNotifier(null),
                ),
                const AppGap.y24(),
                const CardDeliveryReminderCard(isPickup: false),
                const AppGap.y24(),
                AppButton(
                  onPressed: () {
                    AppRouter.pushNamed(
                      PhysicalcardRoutes.orderStatus,
                      arguments: CardDeliveryType.delivery,
                    );
                    locator<CardState>().createCard();
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
