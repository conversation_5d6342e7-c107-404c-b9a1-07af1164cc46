import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class CardActivationPaymentConfirmScreen extends AppStatelessWidget {
  const CardActivationPaymentConfirmScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: DoAppBar(
        title: LocaleKeys.confirmAmount.ctr(),
      ),
      resizeToAvoidBottomInset: false,
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Expanded(
            child: AppCard(
              margin: context.insets.defaultAllInsets,
              padding: context.insets.zero,
              child: ListView(
                padding: context.insets.defaultCardInsets,
                children: [
                  AppFormHeader(title: LocaleKeys.confirmAmount.tr()),
                  const CardActivationConfirmationTable(),
                ],
              ),
            ),
          ),
          const AppGap.y24(),
          SafeArea(
            minimum: context.insets.defaultHorizontalInsets,
            child: A<PERSON><PERSON><PERSON>on(
              text: LocaleKeys.payAmount.tr({"amount": 1400.formattedCurrency}),
              onPressed: () {
                AppRouter.pushNamed(PhysicalcardRoutes.activationSuccess);
                locator<CardState>().activateCard();
              },
            ),
          ),
        ],
      ),
    );
  }
}
