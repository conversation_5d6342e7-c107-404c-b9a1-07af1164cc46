import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class CardInfoScreen extends AppStatelessWidget {
  const CardInfoScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: DoAppBar(
        title: LocaleKeys.moreInfo.tr(),
      ),
      body: AppCard(
        margin: context.insets.defaultAllInsets,
        padding: context.insets.zero,
        child: ListView(
          padding: context.insets.defaultCardInsets,
          children: [
            AppFormHeader(
              title: LocaleKeys.cardOnItsWay.tr(),
              subTitle: LocaleKeys.yourCardWillBeAvailableON.tr(),
              titleStyle: context.textStyle.h3(),
              subTitleStyle: context.textStyle.b2(
                color: context.secondaryTextColor,
              ),
              bottomGap: const AppGap.y40(),
            ),
            for (final feature in PlanType.tier3.features)
              PlanFeatureTile(
                feature,
                key: <PERSON><PERSON><PERSON>(feature),
              ),
          ],
        ),
      ),
    );
  }
}
