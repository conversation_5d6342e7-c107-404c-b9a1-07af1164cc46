import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class CardSettingsScreen extends AppStatelessWidget {
  const CardSettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: DoAppBar(
        title: LocaleKeys.cardSettings.tr(),
      ),
      body: AppCard(
        margin: context.insets.defaultAllInsets,
        padding: context.insets.zero,
        child: ListView(
          padding: context.insets.defaultCardInsets,
          children: [
            AppFormHeader(title: LocaleKeys.manageCard.tr()),
            CardSettingsTile(
              LocaleKeys.changePin.tr(),
              icon: AppIcons.dialpad,
              onTap: () {
                AppRouter.pushNamed(PhysicalcardRoutes.activation);
              },
            ),
            const AppGap.y16(),
            CardSettingsTile(
              LocaleKeys.cardReplacement.tr(),
              icon: AppIcons.cardReader,
            ),
            const AppGap.y16(),
            CardSettingsTile(
              LocaleKeys.terminateCard.tr(),
              icon: AppIcons.trash,
              iconColor: context.errorColor,
            ),
          ],
        ),
      ),
    );
  }
}
