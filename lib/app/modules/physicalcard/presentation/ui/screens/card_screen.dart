import 'package:day1/day1.dart';
import 'package:flutter/widgets.dart';

class CardScreen extends AppStatefulWidget {
  const CardScreen({super.key});

  @override
  State<StatefulWidget> createState() => _CardScreenState();
}

class _CardScreenState extends State<CardScreen> {
  @override
  Widget build(BuildContext context) {
    return AppLocalStateWrapper<CardState>(
      builder: (state) {
        return GenericListener(
          valueListenable: state.userCard,
          builder: (card) {
            if (card == null) return const CardSlides();
            return const CardDetailsBody();
          },
        );
      },
    );
  }
}
