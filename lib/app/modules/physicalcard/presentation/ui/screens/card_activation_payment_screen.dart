import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class CardActivationPaymentScreen extends AppStatefulWidget {
  const CardActivationPaymentScreen({super.key});

  @override
  State<StatefulWidget> createState() => _CardActivationPaymentScreenState();
}

class _CardActivationPaymentScreenState
    extends State<CardActivationPaymentScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: DoAppBar(
        title: LocaleKeys.cardRequest.tr(),
      ),
      resizeToAvoidBottomInset: false,
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Expanded(
            child: AppCard(
              margin: context.insets.defaultAllInsets,
              padding: context.insets.zero,
              child: ListView(
                padding: context.insets.defaultCardInsets,
                children: [
                  AppFormHeader(title: LocaleKeys.payment.tr()),
                  FormLabel(label: LocaleKeys.cardCostBreakdown.tr()),
                  const CardActivationBreakdownTable(),
                  const AppGap.y24(),
                  FormLabel(label: LocaleKeys.addMoneyToSpendLater.tr()),
                  AppTextField(
                    controller: TextEditingController(),
                    hintText: 0.formattedCurrency,
                  )
                ],
              ),
            ),
          ),
          const AppGap.y24(),
          SafeArea(
            minimum: context.insets.defaultHorizontalInsets,
            child: AppButton(
              text: LocaleKeys.payAmount.tr({"amount": 1400.formattedCurrency}),
              onPressed: () {
                AppRouter.pushNamed(
                  PhysicalcardRoutes.activationFeeConfirmation,
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}
