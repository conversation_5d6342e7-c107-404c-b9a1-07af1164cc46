import 'package:day1/day1.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

class CardDetailsBody extends AppStatefulWidget {
  const CardDetailsBody({super.key});

  @override
  State<StatefulWidget> createState() => _CardDetailsBodyState();
}

class _CardDetailsBodyState extends State<CardDetailsBody> {
  final ValueNotifier<bool> visibility = ValueNotifier(false);
  final ScrollController _controller = ScrollController();

  @override
  void initState() {
    super.initState();

    visibility.addListener(() {
      if (!visibility.value) {
        _handleScroll();
        return;
      }

      WidgetsBinding.instance.addPostFrameCallback((_) => _handleScroll());
    });
  }

  _handleScroll() {
    if (visibility.value == false) return;
    final position = _controller.position;
    final offset = visibility.value ? position.maxScrollExtent : 0.0;
    _controller.animateTo(
      offset,
      duration: 300.milliDuration,
      curve: Curves.decelerate,
    );
  }

  @override
  Widget build(BuildContext context) {
    final state = context.watch<CardState>();
    final spacePair = [const AppGap.y16(), const AppGap.y12()];

    return Scaffold(
      appBar: DoAppBar(
        title: LocaleKeys.card.tr(),
      ),
      body: GenericListener(
        valueListenable: state.userCard,
        builder: (card) {
          if (card == null) return const Offstage();
          final bottom = card.isActive ? null : CardActivationCard(card);
          return ListView(
            controller: _controller,
            padding: context.insets.defaultAllInsets.add(
              context.insets.onlySp(bottom: AppFontSizes.px90),
            ),
            children: [
              if (card.isFrozen)
                AppNotificationOverlayWidget(
                  LocaleKeys.cardIsFrozen.tr(),
                  type: NotificationType.success,
                  margin: context.insets.onlySp(bottom: AppFontSizes.px12),
                ),
              CardDetailCard(card, bottom: bottom),
              ...spacePair,
              CardDetailNavRow(card, visibility: visibility),
              ...spacePair,
              CardInfoCard(card, visibility: visibility)
            ],
          );
        },
      ),
    );
  }
}
