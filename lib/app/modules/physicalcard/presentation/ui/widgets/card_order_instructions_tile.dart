import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class CardOrderInstructionTile extends AppStatelessWidget {
  final String instruction;
  final IconData icon;

  const CardOrderInstructionTile(
    this.instruction, {
    this.icon = AppIcons.securityPass,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: context.insets.symmetricSp(vertical: AppFontSizes.px8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          AppIcon(icon, size: AppFontSizes.px24),
          const AppGap.h12(),
          Expanded(
            child: AppText(
              instruction,
              style: context.textStyle.b3(
                color: context.disabledBtntextColor,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
