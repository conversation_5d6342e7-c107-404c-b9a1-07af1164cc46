import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class CardInfoTile extends AppStatelessWidget {
  final String label;
  final String? value;
  final bool copyable;
  final Widget? subTitle;

  const CardInfoTile(
    this.label, {
    super.key,
    this.value,
    this.copyable = true,
    this.subTitle,
  })  : assert(!(copyable && value == null)),
        assert(!(value == null && subTitle == null)),
        assert(!(value != null && subTitle != null));

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        if (!copyable) return;
        context.copyText(value);
      },
      child: Padding(
        padding: context.insets.symmetricSp(vertical: AppFontSizes.px8),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Row(
              children: [
                Expanded(
                  child: AppText(label, style: context.textStyle.labelText()),
                ),
                if (copyable)
                  const AppIcon(AppIcons.copy, size: AppFontSizes.px24)
              ],
            ),
            const AppGap.y2(),
            if (subTitle != null)
              subTitle!
            else
              AppText(value, style: context.textStyle.b2())
          ],
        ),
      ),
    );
  }
}
