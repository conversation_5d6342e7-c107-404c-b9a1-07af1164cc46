import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class CardActivationCard extends AppStatelessWidget {
  final PhysicalCardData card;

  const CardActivationCard(this.card, {super.key});

  @override
  Widget build(BuildContext context) {
    if (card.isActive) return const Offstage();

    return AppCard(
      padding: context.insets.symmetricSp(
        vertical: AppFontSizes.px24,
        horizontal: AppFontSizes.px32,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          CardArrivalPill(card),
          const AppGap.y16(),
          AppText(
            LocaleKeys.yourCardAwaitsYou.tr(),
            style: context.textStyle.h6(),
          ),
          const AppGap.y8(),
          Row(
            children: [
              AppOutlineButton(
                onPressed: () {
                  AppRouter.pushNamed(PhysicalcardRoutes.info);
                },
                text: LocaleKeys.moreInfo.tr(),
                size: ButtonSize.small,
                alignment: Alignment.centerLeft,
                trailingIcon: const AppIcon(AppIcons.navArrowRight),
              ),
              const Spacer(),
            ],
          ),
          const AppGap.y16(),
          AppButton(
            onPressed: () {
              AppRouter.pushNamed(PhysicalcardRoutes.activation);
            },
            isDisabled: card.isFrozen,
            text: LocaleKeys.activate.tr(),
          )
        ],
      ),
    );
  }
}
