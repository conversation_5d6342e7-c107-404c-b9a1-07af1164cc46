import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class CardDetailCard extends AppStatelessWidget {
  final PhysicalCardData card;
  final Widget? bottom;

  const CardDetailCard(this.card, {this.bottom, super.key});

  @override
  Widget build(BuildContext context) {
    final cardText = LocaleKeys.card.tr();
    final cardNumber = card.number.asCardNumber;

    final cardContent = Padding(
      padding: context.insets.defaultCardHInsets,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          const AppGap.y48(),
          const AppLogo(iconOnly: true, alignment: Alignment.topLeft),
          const AppGap.y32(),
          Row(
            children: [
              Expanded(
                child: AppText(
                  "$cardText $cardNumber",
                  style: context.textStyle.b3(
                    color: context.textColor.withOpacity(.7),
                  ),
                ),
              ),
              AppSvg(
                AppVectors.paymentLogo,
                height: context.sp(AppFontSizes.px16),
              )
            ],
          )
        ],
      ),
    );

    return AppCard(
      color: context.raisedBtnBgColor,
      border: BorderSide(color: context.raisedBtnBorderColor),
      padding: EdgeInsets.zero,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          cardContent,
          if (bottom != null) ...[
            const AppGap.y16(),
            Transform.scale(scaleY: 1.02, scaleX: 1.008, child: bottom)
          ] else
            const AppGap.y48(),
        ],
      ),
    );
  }
}
