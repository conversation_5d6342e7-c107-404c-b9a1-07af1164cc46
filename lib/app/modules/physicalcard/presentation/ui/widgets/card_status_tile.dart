import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class CardStatusTile extends AppStatelessWidget {
  final PhysicalCardStatus status;

  const CardStatusTile(this.status, {super.key});

  String get tag {
    return switch (status) {
      PhysicalCardStatus.active => LocaleKeys.active,
      PhysicalCardStatus.frozen => LocaleKeys.frozen,
      _ => LocaleKeys.inActive,
    };
  }

  @override
  Widget build(BuildContext context) {
    final color = switch (status) {
      PhysicalCardStatus.active => context.successCardColor,
      PhysicalCardStatus.frozen => context.errorColor,
      _ => context.disabledBtnColor,
    };
    final size = context.sp(AppFontSizes.px16);

    return Row(
      children: [
        Container(
          height: size,
          width: size,
          decoration: BoxDecoration(shape: BoxShape.circle, color: color),
        ),
        const AppGap.h10(),
        Expanded(child: AppText(tag.ctr(), style: context.textStyle.b2())),
      ],
    );
  }
}
