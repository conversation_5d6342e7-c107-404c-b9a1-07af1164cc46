import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class CardActivationConfirmationTable extends AppStatelessWidget {
  const CardActivationConfirmationTable({super.key});

  @override
  Widget build(BuildContext context) {
    return AppCard(
      color: context.scaffoldBgColor,
      borderRadius: context.lgBorderRadius,
      padding: context.insets.zero,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        mainAxisSize: MainAxisSize.min,
        children: [
          CardActivationConversionTableRow(
            LocaleKeys.cardCost.tr(),
            value: "-${1000.formattedCurrency}",
          ),
          CardActivationConversionTableRow(
            LocaleKeys.processingFee.tr(),
            value: "-${400.formattedCurrency}",
          ),
          CardActivationConversionTableRow(
            LocaleKeys.amountAddedToWallet.tr(),
            value: 0.formattedCurrency,
          ),
          CardActivationConversionTableRow(
            LocaleKeys.totalAmunt.tr(),
            value: 1400.formattedCurrency,
          ),
        ],
      ),
    );
  }
}

class CardActivationBreakdownTable extends AppStatelessWidget {
  const CardActivationBreakdownTable({super.key});

  @override
  Widget build(BuildContext context) {
    return AppCard(
      color: context.scaffoldBgColor,
      borderRadius: context.lgBorderRadius,
      padding: context.insets.zero,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        mainAxisSize: MainAxisSize.min,
        children: [
          CardActivationConversionTableRow(
            LocaleKeys.cardCost.tr(),
            value: "-${1000.formattedCurrency}",
          ),
          CardActivationConversionTableRow(
            LocaleKeys.cardDelivery.tr(),
            value: LocaleKeys.freeDelivery.tr(),
          ),
          CardActivationConversionTableRow(
            LocaleKeys.processingFee.tr(),
            value: 400.formattedCurrency,
          ),
        ],
      ),
    );
  }
}

class CardActivationConversionTableRow extends AppStatelessWidget {
  final String label;
  final String value;

  const CardActivationConversionTableRow(
    this.label, {
    required this.value,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: context.insets.allSp(
        AppFontSizes.px16,
      ),
      child: Row(
        children: [
          Expanded(
            flex: 1,
            child: AppText(
              label,
              style: context.textStyle.b3(color: context.secondaryTextColor),
            ),
          ),
          Expanded(
            flex: 2,
            child: AppText(
              value,
              style: context.textStyle.b3(weight: FontWeight.w500),
              textAlign: TextAlign.end,
            ),
          )
        ],
      ),
    );
  }
}
