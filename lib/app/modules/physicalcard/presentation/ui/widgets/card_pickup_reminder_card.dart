import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class CardDeliveryReminderCard extends AppStatelessWidget {
  final bool isPickup;

  const CardDeliveryReminderCard({super.key, this.isPickup = true});

  String get title {
    return isPickup
        ? LocaleKeys.cardPickupReminder
        : LocaleKeys.cardDeliveryReminer;
  }

  String get body {
    return isPickup
        ? LocaleKeys.pleaseHaveYourPassportAtPickup
        : LocaleKeys.pleaseHavePassportAtDelivery;
  }

  @override
  Widget build(BuildContext context) {
    return AppCard(
      padding: context.insets.allSp(AppFontSizes.px24),
      color: context.dividerColor,
      borderRadius: context.xxlBorderRadius,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          AppInfoHeader(title: title.tr()),
          const AppGap.y8(),
          AppText(
            body.tr(),
            style: context.textStyle.b3(color: context.disabledBtntextColor),
          )
        ],
      ),
    );
  }
}
