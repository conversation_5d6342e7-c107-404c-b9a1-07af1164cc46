import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class CardDetailNavButton extends AppStatelessWidget {
  final OnPressed onTap;
  final IconData icon;
  final String label;
  final bool isActive;
  final Color? activeColor;

  const CardDetailNavButton(
    this.label, {
    super.key,
    this.activeColor,
    required this.icon,
    required this.onTap,
    this.isActive = false,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        BottomNavIcon(
          icon: icon,
          activeColor: activeColor ?? context.raisedBtnBBgColor,
          isActive: isActive,
          onPressed: onTap,
          buttonSize: ButtonSize.large,
        ),
        const AppGap.y12(),
        AppText(
          label,
          style: context.textStyle.b2(weight: FontWeight.w500),
        )
      ],
    );
  }
}
