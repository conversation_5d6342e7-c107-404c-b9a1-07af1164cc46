import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class CardSlides extends AppStatelessWidget {
  const CardSlides({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Padding(
        padding: context.insets.onlySp(bottom: AppFontSizes.px86),
        child: AppSlides(
          slides: context.cardSlides,
          bottom: Padding(
            padding: context.insets.onlySp(top: AppFontSizes.px20),
            child: AppButton(
              onPressed: () {
                AppRouter.pushNamed(PhysicalcardRoutes.deliveryType);
              },
              alignment: Alignment.centerLeft,
              text: LocaleKeys.orderYourCard.tr(),
            ),
          ),
        ),
      ),
    );
  }
}
