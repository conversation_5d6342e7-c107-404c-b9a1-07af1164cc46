import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class CardDetailNavRow extends AppStatelessWidget with AppModalMixin {
  final PhysicalCardData card;
  final ValueNotifier<bool> visibility;

  const CardDetailNavRow(
    this.card, {
    super.key,
    required this.visibility,
  });

  @override
  Widget build(BuildContext context) {
    final freezeText = card.isFrozen ? LocaleKeys.unFreeze : LocaleKeys.freeze;
    final freezeColor = !card.isFrozen
        ? AppColors.success
        : context.warningNotificationBackgroundColor;

    return Padding(
      padding: context.insets.symmetricSp(horizontal: AppFontSizes.px32),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          BoolListener(
            valueListenable: visibility,
            builder: (isVisible) {
              final eye = isVisible ? AppIcons.eye : AppIcons.eyeClosed;
              final viewText =
                  isVisible ? LocaleKeys.hideDetails : LocaleKeys.viewDetails;
              return CardDetailNavButton(
                viewText.tr(),
                icon: eye,
                onTap: () {
                  visibility.value = !visibility.value;
                },
                isActive: isVisible,
              );
            },
          ),
          CardDetailNavButton(
            freezeText.tr(),
            icon: AppIcons.snowFlake,
            isActive: true,
            activeColor: freezeColor,
            onTap: () {
              final state = locator<CardState>();
              if (!card.isFrozen) {
                confirmAction(
                  context,
                  onContinue: state.freezeCard,
                  allowText: LocaleKeys.freeze.tr(),
                  title: LocaleKeys.freezeCard.tr(),
                  description: LocaleKeys.transactionsWillBeDisables.tr(),
                );
                return;
              }
              state.freezeCard();
            },
          ),
          CardDetailNavButton(
            LocaleKeys.settings.tr(),
            icon: AppIcons.settings,
            onTap: () {
              AppRouter.pushNamed(PhysicalcardRoutes.cardSettings);
            },
          )
        ],
      ),
    );
  }
}
