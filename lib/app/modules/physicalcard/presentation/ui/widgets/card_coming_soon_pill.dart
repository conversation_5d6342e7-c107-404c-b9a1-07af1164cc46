import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class CardComingSoonPill extends AppStatelessWidget {
  const CardComingSoonPill({super.key});

  @override
  Widget build(BuildContext context) {
    return AppPill(
      LocaleKeys.comingSoon.tr(),
      bgColor: context.cardColor.withOpacity(.65),
      padding: context.insets.symmetricSp(
        horizontal: AppFontSizes.px16,
        vertical: AppFontSizes.px8,
      ),
      style: context.textStyle.b3(
        color: context.disabledBtntextColor.withOpacity(.6),
        weight: FontWeight.w600,
      ),
    );
  }
}
