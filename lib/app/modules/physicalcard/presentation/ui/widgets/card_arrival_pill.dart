import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class CardArrivalPill extends AppStatelessWidget {
  final PhysicalCardData card;

  const CardArrivalPill(this.card, {super.key});

  @override
  Widget build(BuildContext context) {
    if (card.isActive) return const Offstage();
    final daysText = LocaleKeys.numDays.tr({"count": "12"});
    final timeText = card.exipresAt.format("hh:mm:ss");
    final arrivalText = "$daysText  |  $timeText";
    final btnWidth = context.sp(AppFontSizes.px44);

    return Align(
      alignment: Alignment.centerLeft,
      child: Container(
        decoration: BoxDecoration(
          border: Border.all(color: context.inputBorderColor),
          borderRadius: context.btnBorderRadius,
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Transform.scale(
              scale: 1.05,
              child: AppButton(
                onPressed: () {},
                icon: AppIcons.airplane,
                size: ButtonSize.small,
                minSize: Size.fromWidth(btnWidth),
              ),
            ),
            const AppGap.h16(),
            AppText(arrivalText),
            const AppGap.h12(),
          ],
        ),
      ),
    );
  }
}
