import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class CardInfoCard extends AppStatelessWidget {
  final PhysicalCardData card;
  final ValueNotifier<bool> visibility;

  const CardInfoCard(this.card, {super.key, required this.visibility});

  @override
  Widget build(BuildContext context) {
    return BoolListener(
      valueListenable: visibility,
      builder: (isVisible) {
        return AppAnimatedSwitcher(
          child: Builder(
            key: ValueKey(isVisible),
            builder: (_) {
              if (!isVisible) return const Offstage();
              return AppCard(
                padding: context.insets.symmetricSp(
                  vertical: AppFontSizes.px24,
                  horizontal: AppFontSizes.px32,
                ),
                child: Column(
                  children: [
                    CardInfoTile(
                      LocaleKeys.cardName.tr(),
                      value: card.name,
                    ),
                    const AppGap.y8(),
                    CardInfoTile(
                      LocaleKeys.cardNum.tr(),
                      value: card.number.asCardNumber,
                    ),
                    const AppGap.y8(),
                    CardInfoTile(
                      LocaleKeys.expiryDate.tr(),
                      value: card.exipresAt.format("MM/yy"),
                    ),
                    const AppGap.y8(),
                    CardInfoTile(
                      "CVV",
                      value: card.cvv,
                    ),
                    const AppGap.y8(),
                    CardInfoTile(
                      LocaleKeys.cardStatus.tr(),
                      subTitle: CardStatusTile(card.status),
                      copyable: false,
                    ),
                    const AppGap.y16(),
                    AppShareButton(
                      title: LocaleKeys.card.tr(),
                      value: "0192837465",
                      text: LocaleKeys.shareDetails.tr(),
                      hasTrailingIcon: true,
                      size: ButtonSize.medium,
                    )
                  ],
                ),
              );
            },
          ),
        );
      },
    );
  }
}
