import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class CardSettingsTile extends AppStatelessWidget {
  final String text;
  final IconData icon;
  final Widget? trailing;
  final OnPressed? onTap;
  final Color? iconColor;

  const CardSettingsTile(
    this.text, {
    required this.icon,
    this.iconColor,
    this.trailing,
    this.onTap,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    const size = AppFontSizes.px24;
    return InkWell(
      onTap: onTap,
      child: AppCard(
        cornerStyle: CornerStyle.rounded,
        color: context.scaffoldBgColor,
        padding: context.insets.symmetricSp(
          vertical: AppFontSizes.px16,
          horizontal: AppFontSizes.px12,
        ),
        borderRadius: context.lgBorderRadius,
        child: Row(
          children: [
            AppOutlineButton(
              onPressed: () {
                onTap?.call();
              },
              variant: OutlineBtnVariant.neutral,
              icon: AppIcon(
                icon,
                size: size,
                color: iconColor,
              ),
              size: ButtonSize.medium,
              minSize: Size.fromWidth(context.sp(AppFontSizes.px16)),
            ),
            const AppGap.h16(),
            Expanded(
              child: AppText(
                text,
                style: context.textStyle.b3(weight: FontWeight.w600),
              ),
            ),
            const AppGap.h8(),
            trailing ?? const AppIcon(AppIcons.navArrowRight, size: size)
          ],
        ),
      ),
    );
  }
}
