import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

mixin CardActivationPinMixin<T extends CardActivationPinScreen> on State<T> {
  late final ValueNotifier<bool> formStateEmitter;

  ///Key
  final GlobalKey<FormState> formKey = GlobalKey<FormState>();

  //Controller
  late final TextEditingController otpCtrl;

  final otpFocus = FocusNode();

  String get title => LocaleKeys.setA4DigitCardPin.tr();

  String get header => LocaleKeys.setupYourCardPin.tr();

  String get btnText => LocaleKeys.chooseYourPin.tr();

  @override
  void initState() {
    super.initState();

    otpCtrl = TextEditingController();

    _trackValidity();

    formStateEmitter = ValueNotifier(_formValidityStatus());
  }

  @override
  void dispose() {
    formStateEmitter.dispose();
    otpCtrl.dispose();
    super.dispose();
  }

  _trackValidity() {
    otpCtrl.addListener(() {
      formStateEmitter.value = _formValidityStatus();
    });
  }

  bool _formValidityStatus() {
    return AppValidators.minLength(otpCtrl.text, length: 4) == null;
  }

  submit([bool isBioAuth = false]) async {
    if (isBioAuth || context.validateForm(formKey)) {
      AppRouter.pushNamed(PhysicalcardRoutes.activationFee);
    }
  }
}
