import 'package:flutter/material.dart';
import 'package:day1/day1.dart';

class PhysicalcardRoutes implements RouteRegistry {
  static const basePath = "/physicalcard";
  static const root = "$basePath/root";
  static const deliveryType = "$basePath/delivery-type";
  static const order = "$basePath/order";
  static const pickupForm = "$basePath/pickup-form";
  static const deliveryForm = "$basePath/delivery-form";
  static const orderStatus = "$basePath/order-status";
  static const cardSettings = "$basePath/settings";
  static const info = "$basePath/info";
  static const activation = "$basePath/activation";
  static const activationFee = "$basePath/activation-fee";
  static const activationFeeConfirmation = "$basePath/activation-fee-confirm";
  static const activationSuccess = "$basePath/activation-success";

  const PhysicalcardRoutes();

  @override
  Route dynamicRoutes(RouteSettings settings, Route fallbackRoute) {
    return switch (settings.name) {
      root => MaterialPageRoute(builder: (_) {
          return const CardScreen(key: Key(root));
        }),
      deliveryType => MaterialPageRoute(builder: (_) {
          return const CardDeliveryTypeScreen(key: Key(order));
        }),
      order => MaterialPageRoute(builder: (_) {
          return CardOrderScreen(
            settings.arguments as CardOrderArguments,
            key: const Key(order),
          );
        }),
      pickupForm => MaterialPageRoute(builder: (_) {
          return const CardPickupFormScreen(key: Key(pickupForm));
        }),
      deliveryForm => MaterialPageRoute(builder: (_) {
          return const CardDeliveryFormScreen(key: Key(pickupForm));
        }),
      orderStatus => MaterialPageRoute(
          builder: (_) {
            return CardOrderNotificationScreen(
              settings.arguments as CardDeliveryType,
              key: const Key(orderStatus),
            );
          },
          fullscreenDialog: true,
        ),
      cardSettings => MaterialPageRoute(builder: (_) {
          return const CardSettingsScreen(key: Key(cardSettings));
        }),
      info => MaterialPageRoute(builder: (_) {
          return const CardInfoScreen(key: Key(info));
        }),
      activation => MaterialPageRoute(builder: (_) {
          return const CardActivationPinScreen(key: Key(activation));
        }),
      activationFee => MaterialPageRoute(builder: (_) {
          return const CardActivationPaymentScreen(key: Key(activationFee));
        }),
      activationFeeConfirmation => MaterialPageRoute(builder: (_) {
          return const CardActivationPaymentConfirmScreen(
            key: Key(activationFeeConfirmation),
          );
        }),
      activationSuccess => MaterialPageRoute(
          builder: (_) {
            return const CardActivationSuccessScreen(
              key: Key(activationSuccess),
            );
          },
          fullscreenDialog: true,
        ),
      _ => fallbackRoute,
    };
  }
}
