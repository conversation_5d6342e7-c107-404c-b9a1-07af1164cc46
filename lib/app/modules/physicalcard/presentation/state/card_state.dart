import 'package:day1/day1.dart';
import 'package:flutter/foundation.dart';

class CardState extends StateModel {
  ValueNotifier<PhysicalCardData?> _userCard = ValueNotifier(null);
  ValueNotifier<PhysicalCardData?> get userCard => _userCard;

  createCard() {
    final card = PhysicalCardData(
      name: "<PERSON>",
      number: "823456782345",
      status: PhysicalCardStatus.inactive,
      cvv: "867",
      exipresAt: DateTime.now().add(1098.dayDuration),
    );

    _userCard.value = card;
  }

  activateCard() {
    _userCard.value = _userCard.value?.activate();
  }

  freezeCard() {
    _userCard.value = _userCard.value?.freeze();
  }

  @override
  reset() {
    super.reset();
    _userCard = ValueNotifier(null);
  }
}
