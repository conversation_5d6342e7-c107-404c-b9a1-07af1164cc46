// ignore_for_file: use_build_context_synchronously

import 'package:flutter/material.dart';
import 'package:day1/day1.dart';

mixin AccountStatementMixin<T extends AccountStatementScreen> on State<T> {
  late final ValueNotifier<bool> formStateEmitter;

  ///Key
  final GlobalKey<FormState> formKey = GlobalKey<FormState>();

  //Controller
  late final TextEditingController startCtrl;
  late final TextEditingController endCtrl;
  late final ValueNotifier<SelectionData<String>?> formatCtrl;

  @override
  void initState() {
    super.initState();

    startCtrl = TextEditingController();
    endCtrl = TextEditingController();
    formatCtrl = ValueNotifier(null);

    _trackValidity();

    formStateEmitter = ValueNotifier(_formValidityStatus());
  }

  @override
  void dispose() {
    endCtrl.dispose();
    startCtrl.dispose();
    formatCtrl.dispose();
    formStateEmitter.dispose();
    super.dispose();
  }

  _trackValidity() {
    startCtrl.addListener(() {
      formStateEmitter.value = _formValidityStatus();
    });
    endCtrl.addListener(() {
      formStateEmitter.value = _formValidityStatus();
    });
    formatCtrl.addListener(() {
      formStateEmitter.value = _formValidityStatus();
    });
  }

  bool _formValidityStatus() {
    final startIsValid = AppValidators.required(startCtrl.textValue) == null;
    final endIsValid = AppValidators.required(endCtrl.textValue) == null;
    final formatIsValid = formatCtrl.value != null;

    return startIsValid && endIsValid && formatIsValid;
  }

  List<SelectionData<String>> get options {
    return [
      SelectionData.fromLabel("CSV"),
      SelectionData.fromLabel("PDF"),
      SelectionData.fromLabel("XML"),
    ];
  }

  submit() async {
    if (context.validateForm(formKey)) AppRouter.popView();
  }
}
