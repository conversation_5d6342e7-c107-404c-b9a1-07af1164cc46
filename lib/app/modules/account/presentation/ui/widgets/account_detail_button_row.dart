import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class AccountDetailButtonRow extends AppStatelessWidget {
  const AccountDetailButtonRow({super.key});

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: AppButton(
            variant: RaisedButtonVariant.b,
            size: ButtonSize.medium,
            text: LocaleKeys.acctStatement.tr(),
            icon: AppIcons.page,
            onPressed: () {
              AppRouter.pushNamed(AccountRoutes.statement);
            },
          ),
        ),
        const AppGap.h16(),
        Expanded(
          child: AppButton(
            variant: RaisedButtonVariant.b,
            size: ButtonSize.medium,
            text: LocaleKeys.proofOfAcct.tr(),
            icon: AppIcons.bank,
            onPressed: () {
              AppRouter.pushNamed(AccountRoutes.proof);
            },
          ),
        ),
      ],
    );
  }
}
