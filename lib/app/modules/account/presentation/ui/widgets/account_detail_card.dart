import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class AccountDetailCard extends AppStatelessWidget {
  const AccountDetailCard({super.key});

  @override
  Widget build(BuildContext context) {
    return AppCard(
      color: context.scaffoldBgColor,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          AccountDetailCardRow(
            label: LocaleKeys.bankName.tr(),
            value: "Paystack Titan",
          ),
          const AppGap.y24(),
          AccountDetailCardRow(
            label: LocaleKeys.acctName.tr(),
            value: "Hailey Kavanagh Harris",
          ),
          const AppGap.y24(),
          AccountDetailCardRow(
            label: LocaleKeys.acctNumber.tr(),
            value: "**********",
            canCopy: true,
          ),
        ],
      ),
    );
  }
}

class AccountDetailCardRow extends AppStatelessWidget {
  final String label;
  final String value;
  final bool canCopy;

  const AccountDetailCardRow({
    super.key,
    this.canCopy = false,
    required this.label,
    required this.value,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        Row(
          children: [
            Expanded(
              child: AppText(
                label,
                style: context.textStyle.labelText(),
              ),
            ),
            if (canCopy) AppCopyButton(value: value)
          ],
        ),
        const AppGap.y2(),
        AppText(
          value,
          style: context.textStyle.b2(),
        )
      ],
    );
  }
}
