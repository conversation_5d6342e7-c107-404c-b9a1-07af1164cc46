import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class AccountBalanceTable extends AppStatelessWidget {
  final EdgeInsetsGeometry? padding;
  final Widget? accountButton;

  const AccountBalanceTable({
    super.key,
    this.accountButton,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    final paddingValue = padding ??
        context.insets.symmetricSp(
          horizontal: AppFontSizes.px32,
          vertical: AppFontSizes.px24,
        );
    return Padding(
      padding: paddingValue,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              AppText(
                LocaleKeys.your_balance.tr(),
                style: context.textStyle.b2(),
              ),
              const Spacer(),
              if (accountButton != null) accountButton!
            ],
          ),
          const AppGap.y8(),
          WalletListener(builder: (task) {
            final wallet = task.data;
            final balance = wallet?.ledgerBalance ?? 0;
            return BalanceRow(balance);
          })
        ],
      ),
    );
  }
}

class BalanceRow extends AppStatelessWidget {
  final num balance;

  const BalanceRow(this.balance, {super.key});

  @override
  Widget build(BuildContext context) {
    return WalletBalanceVisiblityListener(
      builder: (show) {
        final value =
            !show ? balance.maskedCurrency : balance.formattedCurrency;
        return Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              child: AppText(
                value,
                style: context.textStyle.h1(),
              ),
            ),
            const WalletBalanceVisiblityButton(),
          ],
        );
      },
    );
  }
}
