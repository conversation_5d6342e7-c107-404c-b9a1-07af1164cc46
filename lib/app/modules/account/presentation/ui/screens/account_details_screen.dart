import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class AccountDetailsScreen extends AppStatelessWidget {
  const AccountDetailsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: DoAppBar(title: LocaleKeys.account.tr()),
      body: ListView(
        padding: context.insets.defaultAllInsets,
        children: [
          AppCard(
            padding: context.insets.symmetricSp(
              vertical: AppFontSizes.px32,
              horizontal: AppFontSizes.px12,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                AccountBalanceTable(
                  padding: context.insets.symmetricSp(
                    horizontal: AppFontSizes.px16,
                  ),
                ),
                const AppGap.y16(),
                const AccountDetailButtonRow(),
                const AppGap.y16(),
                const AccountDetailCard(),
              ],
            ),
          ),
          const AppGap.y12(),
          const AccountDetailShareButton(),
        ],
      ),
    );
  }
}
