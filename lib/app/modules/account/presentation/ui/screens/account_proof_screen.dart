import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class AccountProofScreen extends AppStatelessWidget {
  const AccountProofScreen({super.key});

  DateTime get now => DateTime.now();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: DoAppBar(title: LocaleKeys.proofOfAcct.tr()),
      body: ListView(
        padding: context.insets.defaultAllInsets,
        children: [
          AppCard(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                AppFormHeader(
                  title: LocaleKeys.getUrProofOfAcct.tr(),
                  subTitle: LocaleKeys.downloadPoaSecurely.tr(),
                ),
                AppText(
                  LocaleKeys.yourPoaData.tr({"date": now.format("MM-dd-yyyy")}),
                  style: context.textStyle.b3(
                    color: context.disabledBtntextColor,
                  ),
                ),
                const AppGap.y160(),
                AppButton(
                  onPressed: AppRouter.popView,
                  text: LocaleKeys.downloadProofOfAcct.tr(),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
