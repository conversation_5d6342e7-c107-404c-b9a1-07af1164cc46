import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class AccountStatementScreen extends AppStatefulWidget {
  const AccountStatementScreen({super.key});

  @override
  State<StatefulWidget> createState() => _AccountStatementScreenState();
}

class _AccountStatementScreenState extends State<AccountStatementScreen>
    with AccountStatementMixin {
  @override
  Widget build(BuildContext context) {
    final fillColor = context.cardColor;
    final startError = LocaleKeys.startDateMustBeBeforeEnd.tr();
    final endError = LocaleKeys.endMustBeAfterStart.tr();
    return Scaffold(
      appBar: DoAppBar(title: LocaleKeys.acctStatement.tr()),
      body: ListView(
        padding: context.insets.defaultAllInsets,
        children: [
          AppCard(
            padding: context.insets.symmetricSp(
              horizontal: AppFontSizes.px14,
              vertical: AppFontSizes.px32,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                AppCard(
                  color: context.scaffoldBgColor,
                  padding: context.insets.allSp(AppFontSizes.px24),
                  child: AppForm(
                    formKey: formKey,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        FormLabel(label: LocaleKeys.startDate.tr()),
                        AppDateField(
                          fillColor: fillColor,
                          controller: startCtrl,
                          lastDate: DateTime.now(),
                          validator: (date) {
                            return AppValidators.dateValidator(
                              date,
                              shouldBeGreaterThan: false,
                              otherDate: endCtrl.textValue,
                              comparisonErrorMessage: startError,
                            );
                          },
                        ),
                        const AppGap.y24(),
                        FormLabel(label: LocaleKeys.endDate.tr()),
                        AppDateField(
                          fillColor: fillColor,
                          controller: endCtrl,
                          lastDate: DateTime.now(),
                          validator: (date) {
                            return AppValidators.dateValidator(
                              date,
                              shouldBeGreaterThan: true,
                              otherDate: startCtrl.textValue,
                              comparisonErrorMessage: endError,
                            );
                          },
                        ),
                        const AppGap.y24(),
                        FormLabel(label: LocaleKeys.formatType.tr()),
                        AppDropDownField.withSelection(
                          hintText: LocaleKeys.selectAnOption.tr(),
                          options: options,
                          selection: formatCtrl,
                          fillColor: fillColor,
                          overlayMargin: context.insets.symmetricSp(
                            horizontal: AppFontSizes.px48,
                          ),
                          validator: AppValidators.required,
                        ),
                      ],
                    ),
                  ),
                ),
                const AppGap.y160(),
                const AppGap.y20(),
                Padding(
                  padding: context.insets.symmetricSp(
                    horizontal: AppFontSizes.px18,
                  ),
                  child: BoolListener(
                    valueListenable: formStateEmitter,
                    builder: (isValid) {
                      return AppButton(
                        onPressed: submit,
                        text: LocaleKeys.downloadAcctStatement.tr(),
                        isDisabled: !isValid,
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
