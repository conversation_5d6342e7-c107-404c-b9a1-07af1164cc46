import 'package:flutter/material.dart';
import 'package:day1/day1.dart';

class AccountRoutes implements RouteRegistry {
  static const basePath = "/account";
  static const details = "$basePath/details";
  static const statement = "$basePath/statement";
  static const proof = "$basePath/proof";

  const AccountRoutes();

  @override
  Route dynamicRoutes(RouteSettings settings, Route fallbackRoute) {
    return switch (settings.name) {
      details => MaterialPageRoute(
          builder: (context) => const AccountDetailsScreen(key: Key(details)),
        ),
      statement => MaterialPageRoute(
          builder: (context) => const AccountStatementScreen(
            key: Key(statement),
          ),
        ),
      proof => MaterialPageRoute(
          builder: (context) => const AccountProofScreen(key: Key(proof)),
        ),
      _ => fallbackRoute,
    };
  }
}
