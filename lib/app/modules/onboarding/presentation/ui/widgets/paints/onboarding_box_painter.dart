import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class OnboardBoxPainter extends CustomPainter {
  final bool inDarkMode;

  OnboardBoxPainter(this.inDarkMode);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = AppColors.surfaceTertiary2.forTheme(inDarkMode);
    final paint2 = Paint()
      ..color = AppColors.secondaryLightBlue.forTheme(inDarkMode);
    final paint3 = Paint()..color = AppColors.secondary7.forTheme(inDarkMode);

    const radius = Radius.circular(29);

    canvas.drawRRect(
      RRect.fromRectAndRadius(
        Rect.fromLTWH(size.width * .64, size.height * .3, 148, 148),
        radius,
      ),
      paint,
    );

    canvas.drawRRect(
      RRect.fromRectAndRadius(
        Rect.fromLTWH(size.width * .32, size.height * .43, 120, 120),
        radius,
      ),
      paint2,
    );

    canvas.drawRRect(
      RRect.fromRectAndRadius(
        Rect.fromLTWH(size.width * .64, size.height * .495, 179, 179),
        radius,
      ),
      paint3,
    );
  }

  @override
  bool shouldRepaint(OnboardBoxPainter oldDelegate) {
    return oldDelegate.inDarkMode != inDarkMode;
  }
}

class OnboardSlideBottomPainter extends CustomPainter {
  final bool inDarkMode;

  OnboardSlideBottomPainter(this.inDarkMode);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = AppColors.surfaceSecondary120.forTheme(inDarkMode);

    canvas.drawRect(
      Rect.fromLTWH(0, size.height * .65, size.width, size.height * .22),
      paint,
    );
  }

  @override
  bool shouldRepaint(OnboardSlideBottomPainter oldDelegate) {
    return oldDelegate.inDarkMode != inDarkMode;
  }
}
