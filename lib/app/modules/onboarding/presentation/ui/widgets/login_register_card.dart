import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class LoginRegisterCard extends AppStatelessWidget {
  final Color? color;
  final Color? buttonColor;
  final Color? textColor;

  const LoginRegisterCard({
    super.key,
    this.color,
    this.buttonColor,
    this.textColor,
  });

  @override
  Widget build(BuildContext context) {
    return AppCard(
      color: color ?? context.raisedBtnCBgColor,
      child: Row(
        children: [
          Expanded(
            child: AppTextButton(
              text: LocaleKeys.login.tr(),
              color: textColor ?? context.raisedBtnTextColor,
              onPressed: () {
                AppRouter.pushNamed(AuthRoutes.login);
              },
              textAlign: TextAlign.start,
              size: ButtonSize.medium,
            ),
          ),
          AppButton(
            alignment: Alignment.centerRight,
            text: LocaleKeys.register.tr(),
            variant: RaisedButtonVariant.b,
            bgColor: buttonColor,
            onPressed: () {
              AppRouter.pushNamed(SignupRoutes.email);
            },
            size: ButtonSize.medium,
          )
        ],
      ),
    );
  }
}
