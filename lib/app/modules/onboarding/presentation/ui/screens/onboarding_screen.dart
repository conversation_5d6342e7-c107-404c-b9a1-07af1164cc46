import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class OnboardingScreen extends AppStatefulWidget {
  const OnboardingScreen({super.key});

  @override
  State<StatefulWidget> createState() => _OnboardingScreenState();
}

class _OnboardingScreenState extends State<OnboardingScreen> {
  late final ValueNotifier<int> activeSlide;

  @override
  void initState() {
    super.initState();
    activeSlide = ValueNotifier(0);
  }

  @override
  Widget build(BuildContext context) {
    final slides = context.onboardingSlides;
    return Scaffold(
      body: AppSlides(
        activeSlide: activeSlide,
        slides: slides,
        bottom: NumberListener(
          valueListenable: activeSlide,
          builder: (index) {
            final slide = slides[index];
            return LoginRegisterCard(
              buttonColor: slide.bottomCardButtonColor,
              textColor: slide.bottomCardTextColor,
              color: slide.bottomCardColor,
            );
          },
        ),
      ),
    );
  }
}
