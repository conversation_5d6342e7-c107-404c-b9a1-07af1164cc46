import 'package:flutter/material.dart';
import 'package:day1/day1.dart';

class OnboardingRoutes implements RouteRegistry {
  static const basePath = "/onboarding";
  static const String slides = '$basePath/slides';

  const OnboardingRoutes();

  @override
  Route dynamicRoutes(RouteSettings settings, Route fallbackRoute) {
    return switch (settings.name) {
      slides => MaterialPageRoute(
          builder: (context) => const OnboardingScreen(key: Key(slides)),
        ),
      _ => fallbackRoute,
    };
  }
}
