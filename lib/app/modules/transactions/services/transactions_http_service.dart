import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class TransactionsHttpService
    with AppHttpMixin, AppTaskMixin
    implements TransactionsService {
  // ignore: unused_field
  final AppHttpService _service;

  TransactionsHttpService(this._service);

  @override
  NetworkCallResponse<List<TransactionResponse>> getTransactionsForPeriod(
    DateTimeRange period,
    TransactionStatus? status,
  ) async {
    return requestHandler(() async {
      final start = period.start;
      final end = period.end;

      final matches = _transactions.whereList((it) {
        final dateIsMatch = it.date >= start && it.date <= end;
        bool typeIsMatch = true;

        if (status != null) {
          typeIsMatch = it.status == status;
        }

        return dateIsMatch && typeIsMatch;
      });

      if (matches.hasValue) {
        matches.sort((p, n) => -p.date.compareTo(n.date));
      }

      return matches;
    });
  }
}

List<TransactionResponse> _transactions = [
  TransactionResponse(
    status: TransactionStatus.successful,
    type: TransactionType.inflow,
    date: DateTime.parse('2024-09-01T10:15:08.882728'),
    sender: '<PERSON><PERSON><PERSON> Okeke',
    receiver: 'Amaka Nwosu',
    receiverAccountNumber: 'ACC00001',
    refNumber: 'REF00001',
    amount: 29700.77,
  ),
  TransactionResponse(
    status: TransactionStatus.failed,
    type: TransactionType.expenses,
    date: DateTime.parse('2024-09-03T14:45:00.652080'),
    sender: 'Ngozi Umeh',
    receiver: 'Chukwuma Eze',
    receiverAccountNumber: 'ACC00002',
    refNumber: 'REF00002',
    amount: 29002.64,
    narration: 'Payment for services',
  ),
  TransactionResponse(
    status: TransactionStatus.successful,
    type: TransactionType.inflow,
    date: DateTime.parse('2024-09-05T09:30:07.221135'),
    sender: 'Funmi Johnson',
    receiver: 'Tunde Adeoye',
    receiverAccountNumber: 'ACC00003',
    refNumber: 'REF00003',
    amount: 92007.52,
  ),
  TransactionResponse(
    status: TransactionStatus.reversed,
    type: TransactionType.expenses,
    date: DateTime.parse('2024-09-07T17:54:11.866851'),
    sender: 'Ifeanyi Okafor',
    receiver: 'Bola Ige',
    receiverAccountNumber: 'ACC00004',
    refNumber: 'REF00004',
    amount: 60800.34,
    narration: 'Refund',
  ),
  TransactionResponse(
    status: TransactionStatus.successful,
    type: TransactionType.inflow,
    date: DateTime.parse('2024-09-09T20:15:05.623941'),
    sender: 'Adeola Adebayo',
    receiver: 'Suleiman Musa',
    receiverAccountNumber: 'ACC00005',
    refNumber: 'REF00005',
    amount: 91300.08,
  ),
  TransactionResponse(
    status: TransactionStatus.failed,
    type: TransactionType.expenses,
    date: DateTime.parse('2024-09-10T07:19:53.445541'),
    sender: 'Chisom Onwudiwe',
    receiver: 'Akinyemi Ajayi',
    receiverAccountNumber: 'ACC00006',
    refNumber: 'REF00006',
    amount: 20400.39,
  ),
  TransactionResponse(
    status: TransactionStatus.pending,
    type: TransactionType.inflow,
    date: DateTime.parse('2024-09-11T15:32:44.197223'),
    sender: 'Yetunde Olawale',
    receiver: 'Femi Adesina',
    receiverAccountNumber: 'ACC00007',
    refNumber: 'REF00007',
    amount: 49009.19,
  ),
  TransactionResponse(
    status: TransactionStatus.successful,
    type: TransactionType.inflow,
    date: DateTime.parse('2024-09-13T12:41:13.231364'),
    sender: 'Ijeoma Anozie',
    receiver: 'Obinna Nwankwo',
    receiverAccountNumber: 'ACC00008',
    refNumber: 'REF00008',
    amount: 13700.34,
  ),
  TransactionResponse(
    status: TransactionStatus.successful,
    type: TransactionType.expenses,
    date: DateTime.parse('2024-09-14T14:14:24.022466'),
    sender: 'Abiodun Oyebanji',
    receiver: 'Mariam Sanni',
    receiverAccountNumber: 'ACC00009',
    refNumber: 'REF00009',
    amount: 50098.85,
  ),
  TransactionResponse(
    status: TransactionStatus.pending,
    type: TransactionType.inflow,
    date: DateTime.parse('2024-09-15T18:42:38.505734'),
    sender: 'Gbenga Alabi',
    receiver: 'Halima Yakubu',
    receiverAccountNumber: 'ACC00010',
    refNumber: 'REF00010',
    amount: 51800.49,
  ),
  TransactionResponse(
    status: TransactionStatus.successful,
    type: TransactionType.inflow,
    date: DateTime.parse('2024-09-16T09:13:21.837623'),
    sender: 'Uchechi Eze',
    receiver: 'Kemi Oladipo',
    receiverAccountNumber: 'ACC00011',
    refNumber: 'REF00011',
    amount: 40027.45,
  ),
  TransactionResponse(
    status: TransactionStatus.successful,
    type: TransactionType.inflow,
    date: DateTime.parse('2024-09-17T12:14:10.492223'),
    sender: 'Ibrahim Abdullahi',
    receiver: 'Ngozi Nnamdi',
    receiverAccountNumber: 'ACC00012',
    refNumber: 'REF00012',
    amount: 30012.76,
  ),
  TransactionResponse(
    status: TransactionStatus.reversed,
    type: TransactionType.expenses,
    date: DateTime.parse('2024-09-18T10:34:32.002184'),
    sender: 'Yemi Alade',
    receiver: 'Chinwe Obiora',
    receiverAccountNumber: 'ACC00013',
    refNumber: 'REF00013',
    amount: 22005.64,
    narration: 'Refund for service',
  ),
  TransactionResponse(
    status: TransactionStatus.successful,
    type: TransactionType.expenses,
    date: DateTime.parse('2024-09-20T14:11:05.881226'),
    sender: 'Damilola Afolabi',
    receiver: 'Sade Olorunfemi',
    receiverAccountNumber: 'ACC00014',
    refNumber: 'REF00014',
    amount: 67400.89,
  ),
  TransactionResponse(
    status: TransactionStatus.failed,
    type: TransactionType.expenses,
    date: DateTime.parse('2024-09-21T18:05:18.442039'),
    sender: 'Blessing Chukwu',
    receiver: 'Ifeanyi Obi',
    receiverAccountNumber: 'ACC00015',
    refNumber: 'REF00015',
    amount: 52900.12,
  ),
  TransactionResponse(
    status: TransactionStatus.successful,
    type: TransactionType.expenses,
    date: DateTime.parse('2024-09-22T23:19:30.619492'),
    sender: 'Bode Fashola',
    receiver: 'Adamu Danjuma',
    receiverAccountNumber: 'ACC00016',
    refNumber: 'REF00016',
    amount: 36900.24,
  ),
  TransactionResponse(
    status: TransactionStatus.successful,
    type: TransactionType.inflow,
    date: DateTime.parse('2024-09-23T08:31:59.492211'),
    sender: 'Zainab Aliyu',
    receiver: 'Fatima Abdullahi',
    receiverAccountNumber: 'ACC00017',
    refNumber: 'REF00017',
    amount: 89200.76,
  ),
  TransactionResponse(
    status: TransactionStatus.reversed,
    type: TransactionType.expenses,
    date: DateTime.parse('2024-09-24T17:43:21.003148'),
    sender: 'Emeka Onwu',
    receiver: 'Tope Adeyemi',
    receiverAccountNumber: 'ACC00018',
    refNumber: 'REF00018',
    amount: 18200.30,
    narration: 'Refund for product',
  ),
  TransactionResponse(
    status: TransactionStatus.pending,
    type: TransactionType.expenses,
    date: DateTime.parse('2024-09-25T14:15:10.232194'),
    sender: 'Chioma Okocha',
    receiver: 'Bola Tinubu',
    receiverAccountNumber: 'ACC00019',
    refNumber: 'REF00019',
    amount: 41003.99,
  ),
  TransactionResponse(
    status: TransactionStatus.successful,
    type: TransactionType.inflow,
    date: DateTime.parse('2024-09-26T19:52:12.591122'),
    sender: 'Tochukwu Nwafor',
    receiver: 'Khadijat Mohammed',
    receiverAccountNumber: 'ACC00020',
    refNumber: 'REF00020',
    amount: 60003.89,
  ),
  TransactionResponse(
    status: TransactionStatus.successful,
    type: TransactionType.expenses,
    date: DateTime.parse('2024-09-28T12:44:21.482098'),
    sender: 'Olufunke Oyeniyi',
    receiver: 'Ibrahim Yusuf',
    receiverAccountNumber: 'ACC00021',
    refNumber: 'REF00021',
    amount: 82300.77,
  ),
  TransactionResponse(
    status: TransactionStatus.successful,
    type: TransactionType.expenses,
    date: DateTime.parse('2024-10-01T10:17:05.214110'),
    sender: 'Yetunde Fakorede',
    receiver: 'Ayo Balogun',
    receiverAccountNumber: 'ACC00022',
    refNumber: 'REF00022',
    amount: 31012.94,
  ),
  TransactionResponse(
    status: TransactionStatus.successful,
    type: TransactionType.inflow,
    date: DateTime.parse('2024-10-03T00:15:38.554141'),
    sender: 'Bashir Bello',
    receiver: 'Ebele Okoye',
    receiverAccountNumber: 'ACC00023',
    refNumber: 'REF00023',
    amount: 13000.35,
  ),
  TransactionResponse(
    status: TransactionStatus.successful,
    type: TransactionType.expenses,
    date: DateTime.parse('2024-10-05T03:17:29.405000'),
    sender: 'Ikenna Nwosu',
    receiver: 'Paul Akinbode',
    receiverAccountNumber: 'ACC00024',
    refNumber: 'REF00024',
    amount: 47004.45,
  ),
  TransactionResponse(
    status: TransactionStatus.successful,
    type: TransactionType.expenses,
    date: DateTime.parse('2024-10-07T15:22:29.405000'),
    sender: 'Bola Ogun',
    receiver: 'Chisom Agwu',
    receiverAccountNumber: 'ACC00025',
    refNumber: 'REF00025',
    amount: 62001.34,
  ),
  TransactionResponse(
    status: TransactionStatus.pending,
    type: TransactionType.inflow,
    date: DateTime.parse('2024-10-10T09:27:19.312078'),
    sender: 'Musa Sani',
    receiver: 'Victoria Okeke',
    receiverAccountNumber: 'ACC00026',
    refNumber: 'REF00026',
    amount: 88005.23,
  ),
  TransactionResponse(
    status: TransactionStatus.successful,
    type: TransactionType.inflow,
    date: DateTime.parse('2024-10-12T13:19:43.202901'),
    sender: 'Bukola Adeyemi',
    receiver: 'Ahmed Lawal',
    receiverAccountNumber: 'ACC00027',
    refNumber: 'REF00027',
    amount: 94000.87,
  ),
  TransactionResponse(
    status: TransactionStatus.reversed,
    type: TransactionType.expenses,
    date: DateTime.parse('2024-10-15T21:12:48.456123'),
    sender: 'Adedayo Olaniyi',
    receiver: 'Aisha Bello',
    receiverAccountNumber: 'ACC00028',
    refNumber: 'REF00028',
    amount: 75002.15,
    narration: 'Refund for services',
  ),
  TransactionResponse(
    status: TransactionStatus.successful,
    type: TransactionType.expenses,
    date: DateTime.parse('2024-10-17T19:31:02.871200'),
    sender: 'Femi Babalola',
    receiver: 'Patricia Eze',
    receiverAccountNumber: 'ACC00029',
    refNumber: 'REF00029',
    amount: 46003.45,
  ),
  TransactionResponse(
    status: TransactionStatus.pending,
    type: TransactionType.expenses,
    date: DateTime.parse('2024-10-20T16:08:36.789034'),
    sender: 'Umaru Ali',
    receiver: 'Mojisola Alade',
    receiverAccountNumber: 'ACC00030',
    refNumber: 'REF00030',
    amount: 99001.12,
  ),
];
