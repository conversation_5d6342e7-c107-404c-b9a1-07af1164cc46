import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

enum TransactionStatus {
  pending(LocaleKeys.panding),
  successful(LocaleKeys.successful),
  failed(LocaleKeys.failed),
  reversed(LocaleKeys.reversed);

  const TransactionStatus(this.name);

  final String name;
}

enum TransactionPeriod {
  sevenDays(Duration(days: 7)),
  twoWeeks(Duration(days: 14)),
  thirtyDays(Duration(days: 30)),
  quarter(Duration(hours: 2190)),
  halfYear(Duration(hours: 4380)),
  custom(null);

  const TransactionPeriod(this.duration);

  final Duration? duration;

  bool get isCustom => this == TransactionPeriod.custom;

  DateTimeRange? get range {
    if (isCustom) return null;
    final end = DateTime.now().endOfDay;
    final start = end.subtract(duration!).startOfDay;

    return DateTimeRange(start: start, end: end);
  }

  String get name {
    final days = duration?.inDays ?? 0;

    return switch (this) {
      TransactionPeriod.thirtyDays ||
      TransactionPeriod.sevenDays =>
        LocaleKeys.lastNumDays.tr({"num": "$days"}),
      TransactionPeriod.twoWeeks => LocaleKeys.lastNumWeeks.tr({"num": "2"}),
      TransactionPeriod.quarter => LocaleKeys.lastQuarter.tr(),
      TransactionPeriod.halfYear => LocaleKeys.lastNumMonths.tr({"num": "6"}),
      _ => LocaleKeys.customRange.tr(),
    };
  }

  String? get rangeText => range?.formattedDateRange;
}

enum TransactionType {
  expenses(LocaleKeys.expenses),
  inflow(LocaleKeys.inflow);

  const TransactionType(this.name);

  final String name;
}
