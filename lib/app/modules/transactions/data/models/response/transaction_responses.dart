import 'package:day1/day1.dart';
import 'package:equatable/equatable.dart';

class TransactionResponse extends Equatable {
  final TransactionStatus status;
  final TransactionType type;
  final DateTime date;
  final String? narration;
  final String sender;
  final String receiver;
  final String receiverAccountNumber;
  final String refNumber;
  final double amount;

  const TransactionResponse({
    required this.status,
    required this.type,
    required this.date,
    this.narration,
    required this.sender,
    required this.receiver,
    required this.receiverAccountNumber,
    required this.refNumber,
    required this.amount,
  });

  bool get isDebit => type == TransactionType.expenses;

  @override
  List<Object?> get props =>
      [refNumber, date, amount, type, narration, receiverAccountNumber];
}
