import 'package:day1/day1.dart';
import 'package:flutter/widgets.dart';

class TransactionsState extends StateModel {
  ValueNotifier<int> pageIndex = ValueNotifier(0);
  List<int> pageStack = [0];
  List<NavPageData> pages = [
    NavPageData(
      page: const TransactionsHistoryScreen(key: Key("trans_tab")),
      icon: AppIcons.list,
      label: LocaleKeys.transactions.tr(),
    ),
    NavPageData(
      page: const TransactionsStatsScreen(key: Key("trans_tab")),
      icon: AppIcons.reports,
      label: LocaleKeys.statistics.tr(),
    ),
  ];

  @override
  reset() {
    pageIndex.value = 0;
    pageStack = [pageStack.last];
  }

  updateIndex(int page) {
    if (pageStack.last != page) pageStack.add(page);

    pageIndex.value = page;
  }
}
