import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class TransactionsHistoryState extends StateModel {
  final TransactionsService _service;

  ValueNotifier<FutureListData<TransactionResponse>> _transactions =
      ValueNotifier(const FutureListData.pristine());
  ValueNotifier<SupportedCurrency> _currency =
      ValueNotifier(SupportedCurrency.ngn);
  late ValueNotifier<DateTimeRange?> _range;
  ValueNotifier<TransactionStatus?> _status = ValueNotifier(null);
  late ValueNotifier<TransactionPeriod> _period;

  TransactionsHistoryState(this._service) {
    _period = ValueNotifier(TransactionPeriod.sevenDays);
    _range = ValueNotifier(_period.value.range);
  }

  ValueNotifier<SupportedCurrency> get currency => _currency;
  ValueNotifier<TransactionStatus?> get status => _status;
  ValueNotifier<TransactionPeriod> get period => _period;
  ValueNotifier<DateTimeRange?> get range => _range;
  ValueNotifier<FutureListData<TransactionResponse>> get transactions {
    if (_transactions.value.isPristine) {
      fetchTransactions();
    }
    return _transactions;
  }

  setCurrency(SupportedCurrency value) => _currency.value = value;

  setStatus(TransactionStatus? value) {
    _status.value = value;
    _getData();
  }

  setPeriod(TransactionPeriod value) {
    _period.value = value;
    _getData();
  }

  setRange(DateTimeRange? value) {
    _range.value = value;
    _getData();
  }

  _getData() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      fetchTransactions();
    });
  }

  fetchTransactions() async {
    final range = _period.value.range ?? _range.value;

    if (range == null) return;

    final currentState = _transactions.value;

    if (currentState.isLoading) return;

    _transactions.value = currentState.copyWith(isLoading: true);

    final response = await _service.getTransactionsForPeriod(
      range,
      status.value,
    );

    _transactions.value = currentState.copyWith(
      data: [...response.data ?? []],
      error: response.error,
      isLoading: false,
    );
  }

  @override
  reset() {
    _transactions = ValueNotifier(const FutureListData.pristine());
    _currency = ValueNotifier(SupportedCurrency.ngn);
    _period = ValueNotifier(TransactionPeriod.sevenDays);
    _range = ValueNotifier(_period.value.range);
    _status = ValueNotifier(null);
  }
}
