import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class TransactionsStatState extends StateModel {
  // ignore: unused_field
  final TransactionsService _service;
  ValueNotifier<SupportedCurrency> _currency =
      ValueNotifier(SupportedCurrency.ngn);
  ValueNotifier<DateTimeRange?> _range = ValueNotifier(null);
  ValueNotifier<TransactionType> _type =
      ValueNotifier(TransactionType.expenses);
  ValueNotifier<TransactionPeriod> _period =
      ValueNotifier(TransactionPeriod.sevenDays);

  TransactionsStatState(this._service);

  ValueNotifier<SupportedCurrency> get currency => _currency;
  ValueNotifier<TransactionType> get type => _type;
  ValueNotifier<TransactionPeriod> get period => _period;

  setCurrency(SupportedCurrency value) => _currency.value = value;
  setType(TransactionType value) => _type.value = value;
  setPeriod(TransactionPeriod value) => _period.value = value;
  setRange(DateTimeRange value) => _range.value = value;

  final data = const PieData(items: [
    PieItem(value: 12000, label: "Online Transactions"),
    PieItem(value: 5000, label: "Card Maintenance"),
    PieItem(value: 7500, label: "Travel Charges"),
    PieItem(value: 8890, label: "Food"),
    PieItem(value: 10000, label: "Gaming"),
  ]);

  final barData = const BarChartData(
    series: [
      BarChartBarData(value: 20000, label: "Feb 12"),
      BarChartBarData(value: 40000, label: "Feb 13"),
      BarChartBarData(value: 30000, label: "Feb 13"),
      BarChartBarData(value: 8000, label: "Feb 13"),
      BarChartBarData(value: 8500, label: "Feb 16"),
      BarChartBarData(value: 19000, label: "Feb 17"),
      BarChartBarData(value: 15000, label: "Feb 18"),
      BarChartBarData(value: 20000, label: "M"),
      BarChartBarData(value: 40000, label: "T"),
      BarChartBarData(value: 30000, label: "W"),
      BarChartBarData(value: 8000, label: "TH"),
      BarChartBarData(value: 8500, label: "F"),
      BarChartBarData(value: 1900, label: "S"),
      BarChartBarData(value: 1500, label: "S"),
    ],
    legend: "Total Spend",
    period: "Apr 15-21",
  );

  @override
  reset() {
    _currency = ValueNotifier(
      SupportedCurrency.ngn,
    );
    _range = ValueNotifier(null);
    _type = ValueNotifier(TransactionType.expenses);
    _period = ValueNotifier(TransactionPeriod.sevenDays);
  }
}
