import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class TransactionDetailScreen extends AppStatelessWidget {
  final TransactionResponse argument;
  const TransactionDetailScreen(this.argument, {super.key});

  String get amount => argument.amount.formattedCurrency;

  _goBack() => AppRouter.popView();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const DoAppBar(),
      body: ListView(
        padding: context.insets.defaultAllInsets,
        children: [
          TransactionHeaderCard(
            LocaleKeys.transferToUser.tr({"user": argument.receiver}),
            amount: amount,
            style: context.textStyle.h6(),
          ),
          const AppGap.y12(),
          AppCard(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                FormLabel(label: LocaleKeys.transactionDetails.tr()),
                TransactionSummaryTable(data: argument),
                const AppGap.y32(),
                AppButton(
                  onPressed: _goBack,
                  text: LocaleKeys.shareTransactionReceipt.tr(),
                ),
                const AppGap.y16(),
                AppOutlineButton(
                  onPressed: _goBack,
                  text: LocaleKeys.reportAProblem.tr(),
                )
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class TransactionSummaryTable extends AppStatelessWidget {
  final TransactionResponse data;

  const TransactionSummaryTable({super.key, required this.data});

  @override
  Widget build(BuildContext context) {
    final statusColor = switch (data.status) {
      TransactionStatus.failed => context.errorColor,
      TransactionStatus.pending => AppColors.secondary6,
      TransactionStatus.successful => context.notificationTextColor,
      TransactionStatus.reversed => context.disabledBtntextColor,
    };

    return AppCard(
      color: context.scaffoldBgColor,
      borderRadius: context.lgBorderRadius,
      border: BorderSide(
        color: context.inputBorderColor,
        width: 2,
      ),
      padding: context.insets.symmetricSp(
        horizontal: AppFontSizes.px16,
        vertical: AppFontSizes.px8,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        mainAxisSize: MainAxisSize.min,
        children: [
          PaymentConversionTableRow(
            LocaleKeys.status.tr(),
            value: data.status.name.tr(),
            valueColor: statusColor,
          ),
          PaymentConversionTableRow(
            LocaleKeys.dateTime.tr(),
            value: data.date.format("d MMM yyyy - hh:mm"),
          ),
          PaymentConversionTableRow(
            LocaleKeys.transactionType.tr(),
            value: data.type.name.tr(),
          ),
          PaymentConversionTableRow(
            LocaleKeys.senderName.tr(),
            value: data.sender,
          ),
          PaymentConversionTableRow(
            LocaleKeys.receiverName.tr(),
            value: data.receiver,
          ),
          PaymentConversionTableRow(
            LocaleKeys.receiverAcctNum.tr(),
            value: data.receiverAccountNumber,
          ),
          PaymentConversionTableRow(
            LocaleKeys.refNum.tr(),
            value: data.refNumber,
          ),
          PaymentConversionTableRow(
            LocaleKeys.narration.tr(),
            value: data.narration ?? "None",
          ),
        ],
      ),
    );
  }
}
