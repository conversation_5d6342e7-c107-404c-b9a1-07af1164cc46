import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class TransactionsStatsScreen extends AppStatefulWidget {
  const TransactionsStatsScreen({super.key});

  @override
  State createState() => _TransactionsStatsScreenState();
}

class _TransactionsStatsScreenState extends State<TransactionsStatsScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: DoAppBar(
        title: LocaleKeys.statistics.tr(),
      ),
      body: AppLocalStateWrapper<TransactionsStatState>(
        builder: (state) {
          return ListenableBuilder(
            listenable: Listenable.merge([
              state.currency,
              state.type,
              state.period,
            ]),
            builder: (_, __) {
              final currency = state.currency;
              final type = state.type;
              final period = state.period;
              final data = state.data;
              final barData = state.barData;

              return ListView(
                padding: context.insets.defaultAllInsets.add(
                  context.insets.onlySp(bottom: AppFontSizes.px90),
                ),
                children: [
                  StatInflowExpenseTabbar(
                    type,
                    leading: StatPeriodPicker(
                      period.value,
                      onChange: (value) {
                        period.value = value;
                      },
                      isOutlined: false,
                    ),
                  ),
                  const AppGap.y16(),
                  StatSectionHeader(
                    LocaleKeys.dailyExpenses.tr(),
                    currencyWidget: StatCurrencyPicker(currency),
                  ),
                  const AppGap.y12(),
                  StatContainer(
                    color: context.raisedBtnBgColor,
                    padding: context.insets.symmetricSp(
                      vertical: AppFontSizes.px32,
                    ),
                    chart: BarChartGraph(
                      data: barData,
                      currency: currency.value,
                    ),
                  ),
                  const AppGap.y24(),
                  StatSectionHeader(
                    LocaleKeys.distribution.tr(),
                    currencyWidget: StatCurrencyPicker(currency),
                  ),
                  const AppGap.y12(),
                  StatContainer(
                    chart: StatPieChart(data, currency: currency.value),
                    legend: StatLegend(data, currency: currency.value),
                  ),
                ],
              );
            },
          );
        },
      ),
    );
  }
}
