import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class TransactionsScreen extends AppStatefulWidget {
  final int tab;

  const TransactionsScreen({this.tab = 0, super.key});

  @override
  State<StatefulWidget> createState() => _TransactionsScreenState();
}

class _TransactionsScreenState extends State<TransactionsScreen>
    with AppModalMixin {
  late PageController ctrl;
  late TransactionsState state;

  @override
  void initState() {
    super.initState();
    ctrl = PageController(initialPage: widget.tab);
    state = locator<TransactionsState>();
    if (widget.tab != state.pageIndex.value) {
      state.updateIndex(widget.tab);
    }
  }

  @override
  Widget build(BuildContext context) {
    final pages = state.pages;

    return Scaffold(
      bottomSheet: AppBottomNavBar(
        state.pages,
        controller: ctrl,
        pageIndex: state.pageIndex,
      ),
      resizeToAvoidBottomInset: false,
      body: PageView.builder(
        restorationId: "dashboard_slides",
        physics: const NeverScrollableScrollPhysics(),
        controller: ctrl,
        onPageChanged: state.updateIndex,
        itemBuilder: (context, index) {
          return pages[index].page;
        },
        itemCount: pages.length,
      ),
    );
  }
}
