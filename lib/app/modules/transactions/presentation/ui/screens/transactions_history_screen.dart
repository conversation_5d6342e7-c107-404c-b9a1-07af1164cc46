import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class TransactionsHistoryScreen extends AppStatefulWidget {
  const TransactionsHistoryScreen({super.key});

  @override
  State createState() => _TransactionsHistoryScreenState();
}

class _TransactionsHistoryScreenState extends State<TransactionsHistoryScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: DoAppBar(
        title: LocaleKeys.transactionHistory.tr(),
      ),
      body: Padding(
        padding: context.insets.defaultHorizontalInsets.add(
          context.insets.onlySp(bottom: AppFontSizes.px96),
        ),
        child: AppLocalStateWrapper<TransactionsHistoryState>(
          builder: (state) {
            return ListenableBuilder(
              listenable: Listenable.merge([
                state.currency,
                state.status,
                state.period,
                state.range,
                state.transactions
              ]),
              builder: (_, __) {
                final task = state.transactions.value;
                final isLoading = task.isLoading;
                final hasError = task.hasError;
                final transactions = task.data;

                return Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    const AppGap.y12(),
                    TransactionStatusPickerContainer(
                      state.status.value,
                      onChanged: state.setStatus,
                      leading: StatPeriodPicker(
                        state.period.value,
                        range: state.range.value,
                        onChange: state.setPeriod,
                        onDateRange: state.setRange,
                      ),
                    ),
                    TransactionCurrencyHeader(
                      transactions,
                      currency: state.currency.value,
                      currencyPicker: StatCurrencyPicker(state.currency),
                    ),
                    Expanded(
                      child: Builder(
                        builder: (_) {
                          if (hasError || isLoading) {
                            return AppCard(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.stretch,
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  if (hasError)
                                    AppErrorStateWidget(
                                      description: task.error?.message,
                                      onRetry: state.fetchTransactions,
                                    ),
                                  if (isLoading) const AppGenericShimmer(),
                                ],
                              ),
                            );
                          }
                          return TransactionsList(
                            transactions,
                            currency: state.currency.value,
                          );
                        },
                      ),
                    )
                  ],
                );
              },
            );
          },
        ),
      ),
    );
  }
}
