import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class StatLegend extends StatelessWidget {
  final SupportedCurrency? currency;
  final PieData data;

  const StatLegend(this.data, {this.currency, super.key});

  @override
  Widget build(BuildContext context) {
    if (!data.items.hasValue) return const Offstage();
    final sum = data.sum;

    List<Widget> children = data.items.mapList(
      (it) => StatLegendTile(
        data: it,
        color: data.itemColor(it),
        sum: sum,
        currency: currency,
      ),
    );

    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        const AppGap.y32(),
        for (final child in children.intersperse(const AppGap.y8())) child,
      ],
    );
  }
}
