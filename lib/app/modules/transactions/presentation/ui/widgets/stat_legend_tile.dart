import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class StatLegendTile extends StatelessWidget {
  final ColorSet color;
  final double sum;
  final SupportedCurrency? currency;
  final PieItem data;

  String get symbol => currency?.symbol ?? AppStrings.naira;
  String get value {
    final fx = currency?.fxRate ?? 1;
    return (data.value * fx).asCurrencyShort(symbol);
  }

  String get fraction {
    final value = data.fraction(sum) * 100;
    return "${value.toStringAsFixed(0)} %";
  }

  const StatLegendTile({
    required this.data,
    super.key,
    this.currency,
    required this.color,
    required this.sum,
  });

  @override
  Widget build(BuildContext context) {
    final valueText = "$value - $fraction";
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        Expanded(
          child: AppText(
            data.label,
            style: context.textStyle.b3(weight: FontWeight.w500),
          ),
        ),
        App<PERSON>ill(
          valueText,
          bgColor: color,
          textColor: color.dark,
          style: context.textStyle.b4(
            weight: FontWeight.w500,
            color: color.dark,
          ),
        )
      ],
    );
  }
}
