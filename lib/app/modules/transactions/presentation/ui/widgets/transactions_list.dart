import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class TransactionsList extends AppStatelessWidget {
  final List<TransactionResponse> transactions;
  final SupportedCurrency? currency;
  final OnPressed? onRetry;

  const TransactionsList(
    this.transactions, {
    super.key,
    this.currency,
    this.onRetry,
  });

  @override
  Widget build(BuildContext context) {
    if (transactions.isEmpty) {
      return AppCard(
        child: Center(
          child: AppEmptyStateWidget(
            padding: EdgeInsets.zero,
            icon: AppIcon(
              AppIcons.searchEngine,
              color: context.errorColor,
              size: AppFontSizes.px100,
              alignment: Alignment.centerLeft,
            ),
            title: LocaleKeys.noTransactionRecord.tr(),
            description: LocaleKeys.noTransactionsFromPeriod.tr(),
            onRetry: onRetry,
          ),
        ),
      );
    }
    return AppCard(
      padding: context.insets.zero,
      child: ListView.separated(
        padding: context.insets.defaultAllInsets,
        itemBuilder: (_, index) => TransactionTile(
          transactions[index],
          currency: currency,
        ),
        separatorBuilder: (_, __) {
          return Padding(
            padding: context.insets.symmetricSp(
              horizontal: AppFontSizes.px16,
            ),
            child: AppDivider(
              size: AppDividerSize.none,
              color: context.outlineBtnAltBorderColor,
            ),
          );
        },
        itemCount: transactions.length,
      ),
    );
  }
}
