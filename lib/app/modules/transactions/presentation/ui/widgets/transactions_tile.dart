import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class TransactionTile extends AppStatelessWidget {
  final TransactionResponse transaction;
  final SupportedCurrency? currency;

  String get symbol => currency?.symbol ?? AppStrings.naira;
  double get value {
    final fx = currency?.fxRate ?? 1;
    return transaction.amount * fx;
  }

  String get formattedValue {
    final text = value.asCurrency(symbol);
    final prefix = transaction.isDebit ? "-" : "+";

    return "$prefix$text";
  }

  const TransactionTile(
    this.transaction, {
    super.key,
    this.currency,
  });

  @override
  Widget build(BuildContext context) {
    final amountColor = switch (transaction.type) {
      TransactionType.expenses => AppColors.secondary6,
      _ => context.raisedBtnCBgColor,
    };

    return InkWell(
      onTap: () {
        AppRouter.pushNamed(
          TransactionsRoutes.detail,
          arguments: transaction,
        );
      },
      child: Padding(
        padding: context.insets.allSp(
          AppFontSizes.px16,
        ),
        child: Row(
          children: [
            TransactionIcon(transaction.type),
            const AppGap.h16(),
            Expanded(
              flex: 3,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  AppText(
                    transaction.receiver,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    style: context.textStyle.b3(weight: FontWeight.w600),
                  ),
                  const AppGap.y4(),
                  AppText(
                    transaction.date.format("MMMM dd, yyyy"),
                    style: context.textStyle.helperText(),
                  )
                ],
              ),
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                AppText(
                  formattedValue,
                  maxLines: 1,
                  textAlign: TextAlign.end,
                  style: context.textStyle.b4(
                    weight: FontWeight.w600,
                    color: amountColor,
                  ),
                ),
                const AppGap.y4(),
                TransactionPill(transaction.status)
              ],
            ),
          ],
        ),
      ),
    );
  }
}
