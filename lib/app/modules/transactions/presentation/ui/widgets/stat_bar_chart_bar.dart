import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class Bar<PERSON>hartBar extends AppStatelessWidget {
  final double fraction;
  final double height;
  final String label;

  const BarChartBar({
    super.key,
    this.fraction = .001,
    required this.label,
    required this.height,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Expanded(
          child: BarChartBarAnimation(
            extent: fraction,
            height: height,
          ),
        ),
        BarLabelText(label),
      ],
    );
  }
}

class BarChartBarAnimation extends StatefulWidget {
  final double? extent;
  final double height;
  final Color valueColor;
  final Color inActiveColor;

  const BarChartBarAnimation({
    this.extent,
    this.valueColor = AppColors.black,
    this.height = 100,
    this.inActiveColor = AppColors.transparent,
    super.key,
  });

  @override
  State<StatefulWidget> createState() {
    return _BarChartBarAnimationState();
  }
}

class _BarChartBarAnimationState extends State<BarChartBarAnimation>
    with SingleTickerProviderStateMixin {
  late AnimationController _ctrl;
  @override
  void initState() {
    super.initState();
    _ctrl = AnimationController(
      duration: 500.milliDuration,
      lowerBound: 0,
      upperBound: widget.extent ?? .0001,
      vsync: this,
    )..forward();
  }

  @override
  void dispose() {
    _ctrl.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _ctrl,
      builder: (_, __) {
        return SizedBox(
          height: widget.height,
          width: context.sp(AppFontSizes.px30),
          child: CustomPaint(
            foregroundPainter: BarChartPainter(
              color: widget.valueColor,
              extent: _ctrl.value,
            ),
          ),
        );
      },
    );
  }
}
