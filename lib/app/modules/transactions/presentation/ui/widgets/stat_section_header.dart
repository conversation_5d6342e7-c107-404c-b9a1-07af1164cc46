import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class StatSectionHeader extends AppStatelessWidget {
  final String label;
  final StatCurrencyPicker currencyWidget;

  const StatSectionHeader(
    this.label, {
    super.key,
    required this.currencyWidget,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: context.insets.symmetricSp(horizontal: AppFontSizes.px16),
      child: Row(
        children: [
          Expanded(
            child: AppText(
              label,
              style: context.textStyle.b3(weight: FontWeight.w500),
            ),
          ),
          currencyWidget,
        ],
      ),
    );
  }
}
