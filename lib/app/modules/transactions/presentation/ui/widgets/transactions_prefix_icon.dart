import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class TransactionIcon extends AppStatelessWidget {
  final TransactionType type;

  const TransactionIcon(this.type, {super.key});

  @override
  Widget build(BuildContext context) {
    final iconColor = switch (type) {
      TransactionType.expenses => AppColors.secondary6,
      _ => context.notificationTextColor,
    };
    final bgColor = switch (type) {
      TransactionType.expenses => AppColors.secondary7,
      _ => context.notificationBackgroundColor,
    };
    final icon = switch (type) {
      TransactionType.expenses => AppIcons.arrowUpRightCircle,
      _ => AppIcons.arrowDownCircle,
    };

    const size = AppFontSizes.px40;

    return Container(
      constraints: const BoxConstraints(maxHeight: size, maxWidth: size),
      padding: context.insets.allSp(AppFontSizes.px8),
      decoration: BoxDecoration(color: bgColor, shape: BoxShape.circle),
      child: AppIcon(
        icon,
        size: AppFontSizes.px24,
        color: iconColor,
      ),
    );
  }
}
