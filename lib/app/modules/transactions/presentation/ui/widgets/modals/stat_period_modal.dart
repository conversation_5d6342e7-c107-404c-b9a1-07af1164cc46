import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class StatPeriodModal extends AppStatefulWidget {
  final ScrollController controller;
  final TransactionPeriod period;
  final DateTimeRange? range;
  final OnChanged<(TransactionPeriod, DateTimeRange?)> onSelect;

  const StatPeriodModal(
    this.period, {
    super.key,
    required this.onSelect,
    required this.controller,
    this.range,
  });

  @override
  State<StatefulWidget> createState() => _StatPeriodModalState();
}

class _StatPeriodModalState extends State<StatPeriodModal> {
  ///Key
  final GlobalKey<FormState> formKey = GlobalKey<FormState>();

  late final TextEditingController _startCtrl;
  late final TextEditingController _endCtrl;
  late final ValueNotifier<TransactionPeriod> _period;
  late final ValueNotifier<bool> _formValidityStatus;

  @override
  void initState() {
    super.initState();
    final range = widget.range;
    final start = range?.start.toIso8601String();
    final end = range?.end.toIso8601String();

    _period = ValueNotifier(widget.period);
    _startCtrl = TextEditingController(text: _start ?? start);
    _endCtrl = TextEditingController(text: _end ?? end);

    _period.addListener(_periodListener);
    _startCtrl.addListener(_rangeListener);
    _endCtrl.addListener(_rangeListener);

    _formValidityStatus = ValueNotifier(_formIsValid);
  }

  _periodListener() {
    _startCtrl.text = _start.value;
    _endCtrl.text = _end.value;
  }

  _rangeListener() {
    _formValidityStatus.value = _formIsValid;
  }

  bool get _formIsValid {
    final startIsValid = AppValidators.dateValidator(_startCtrl.text) == null;
    final endIsValid = AppValidators.dateValidator(_endCtrl.text) == null;
    final hasValidRange = _ctrlRange != null;
    bool isDifferent = true;

    if (hasValidRange) {
      isDifferent = _ctrlRange! != widget.range;
    }

    return startIsValid && endIsValid && hasValidRange && isDifferent;
  }

  DateTimeRange? get range => _period.value.range;

  String? get _start {
    return range?.start.toIso8601String();
  }

  String? get _end {
    return range?.end.toIso8601String();
  }

  DateTime? get _ctrlStart {
    return _startCtrl.text.asDate;
  }

  DateTime? get _ctrlEnd {
    return _endCtrl.text.asDate;
  }

  DateTimeRange? get _ctrlRange {
    if (_ctrlEnd == null || _ctrlStart == null) return null;
    if (_ctrlStart! > _ctrlEnd!) return null;
    return DateTimeRange(start: _ctrlStart!, end: _ctrlEnd!);
  }

  _onSelect(TransactionPeriod value) {
    _period.value = value;
  }

  submit() {
    final selection = _period.value;
    if (!selection.isCustom || context.validateForm(formKey)) {
      final now = DateTime.now();
      final start = _ctrlStart ?? now;
      final end = _ctrlEnd ?? now;
      final span = selection.range ?? DateTimeRange(start: start, end: end);

      if (!span.isLessThanAYear) {
        return context.showErrorNotification(
          LocaleKeys.periodShouldBelessThanYear.tr(),
        );
      }

      widget.onSelect((selection, span));
      AppRouter.popView();
    }
  }

  @override
  Widget build(BuildContext context) {
    final startError = LocaleKeys.startDateMustBeBeforeEnd.tr();
    final endError = LocaleKeys.endMustBeAfterStart.tr();
    final options = TransactionPeriod.values.mapList(
      (it) => NamedOptionData(it, name: it.name),
    );
    final padding = context.insets.symmetricSp(horizontal: AppFontSizes.px32);
    final fillColor = context.scaffoldBgColor;
    final iconColor = context.textColor;
    const icon = AppIcons.navArrowRight;

    return SingleChildScrollView(
      controller: widget.controller,
      child: GenericListener(
        valueListenable: _period,
        builder: (period) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            mainAxisSize: MainAxisSize.min,
            children: [
              const AppGap.y24(),
              Padding(
                padding: padding,
                child: Row(
                  children: [
                    Expanded(
                      child: AppText(
                        LocaleKeys.chooseDateRange.tr(),
                        style: context.textStyle.h6(),
                      ),
                    ),
                    const AppCancelButton(),
                  ],
                ),
              ),
              const AppGap.y12(),
              AppSelectionPills(
                options,
                onSelect: _onSelect,
                selections: [period],
              ),
              const AppGap.y12(),
              AppForm(
                formKey: formKey,
                child: Padding(
                  padding: padding,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      FormLabel(label: LocaleKeys.startDate.tr()),
                      AppDateField(
                        controller: _startCtrl,
                        isEnabled: period.isCustom,
                        fillColor: fillColor,
                        iconColor: iconColor,
                        trailingIcon: icon,
                        validator: (date) {
                          return AppValidators.dateValidator(
                            date,
                            shouldBeGreaterThan: false,
                            otherDate: _endCtrl.textValue,
                            comparisonErrorMessage: startError,
                          );
                        },
                      ),
                      const AppGap.y16(),
                      FormLabel(label: LocaleKeys.endDate.tr()),
                      AppDateField(
                        controller: _endCtrl,
                        isEnabled: period.isCustom,
                        fillColor: fillColor,
                        iconColor: iconColor,
                        trailingIcon: icon,
                        validator: (date) {
                          return AppValidators.dateValidator(
                            date,
                            shouldBeGreaterThan: true,
                            otherDate: _startCtrl.textValue,
                            comparisonErrorMessage: endError,
                          );
                        },
                      ),
                      const AppGap.y24(),
                      BoolListener(
                        valueListenable: _formValidityStatus,
                        builder: (isValid) {
                          return AppButton(
                            onPressed: submit,
                            isDisabled: !isValid,
                            text: LocaleKeys.confirm.tr(),
                          );
                        },
                      ),
                      const AppGap.y24(),
                    ],
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }
}
