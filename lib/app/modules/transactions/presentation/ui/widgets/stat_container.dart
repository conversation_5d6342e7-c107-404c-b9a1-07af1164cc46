import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class StatContainer extends StatelessWidget {
  final Widget chart;
  final Widget? legend;
  final Color? color;
  final EdgeInsetsGeometry? padding;

  const StatContainer({
    required this.chart,
    this.legend,
    this.padding,
    this.color,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return AppCard(
      color: color,
      padding: padding ??
          context.insets.symmetricSp(
            vertical: AppFontSizes.px40,
            horizontal: AppFontSizes.px32,
          ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        mainAxisSize: MainAxisSize.min,
        children: [chart, if (legend != null) legend!],
      ),
    );
  }
}
