import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class BarLabelText extends AppStatelessWidget {
  final String label;

  const BarLabelText(this.label, {super.key});

  @override
  Widget build(BuildContext context) {
    return AppText(
      label,
      textAlign: TextAlign.center,
      style: context.textStyle.b4(
        color: context.disabledBtntextColor,
      ),
    );
  }
}

class BarYLabels extends AppStatelessWidget {
  final String max;
  final String mid;
  final double height;
  final String min;

  const BarYLabels({
    required this.max,
    required this.mid,
    required this.min,
    required this.height,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final leftPadding = context.sp(AppFontSizes.px32);

    return Stack(
      children: [
        Positioned(
          left: leftPadding,
          top: 0,
          child: BarLabelText(max),
        ),
        Positioned(
          left: leftPadding,
          bottom: (height * .44),
          child: Bar<PERSON><PERSON>lText(mid),
        ),
        Positioned(
          left: leftPadding,
          bottom: (height * .09),
          child: BarLabelText(min),
        ),
      ],
    );
  }
}
