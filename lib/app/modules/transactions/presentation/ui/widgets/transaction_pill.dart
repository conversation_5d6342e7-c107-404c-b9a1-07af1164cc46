import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class TransactionPill extends AppStatelessWidget {
  final TransactionStatus status;

  const TransactionPill(this.status, {super.key});

  @override
  Widget build(BuildContext context) {
    final textColor = switch (status) {
      TransactionStatus.pending => AppColors.secondary6,
      TransactionStatus.failed => context.errorNotificationTextColor,
      TransactionStatus.successful => context.notificationTextColor,
      _ => context.disabledBtntextColor,
    };
    final bgColor = switch (status) {
      TransactionStatus.pending => AppColors.secondary7,
      TransactionStatus.failed => context.errorNotificationBackgroundColor,
      TransactionStatus.successful => context.notificationBackgroundColor,
      _ => context.disabledBtnColor,
    };

    return AppPill(
      status.name.tr(),
      padding: context.insets.symmetricSp(horizontal: AppFontSizes.px8),
      bgColor: bgColor,
      textColor: textColor,
    );
  }
}
