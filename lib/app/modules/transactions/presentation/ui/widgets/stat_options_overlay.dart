import 'package:day1/day1.dart';
import 'package:flutter/widgets.dart';

class NamedOptionDatasOverlay<T> extends AppStatelessWidget {
  final OnChanged<T> onSelect;
  final List<NamedOptionData<T>> options;
  final Decoration? decoration;
  final EdgeInsets? padding;
  final EdgeInsets? tilePadding;
  final CrossAxisAlignment crossAxisAlignment;
  final T? selection;

  const NamedOptionDatasOverlay({
    required this.onSelect,
    required this.options,
    required this.selection,
    this.padding,
    this.tilePadding,
    this.crossAxisAlignment = CrossAxisAlignment.center,
    this.decoration,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return AppCard(
      shadows: context.shadows.xSmall,
      margin: context.insets.defaultHorizontalInsets,
      padding: padding ?? context.insets.allSp(AppFontSizes.px16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: crossAxisAlignment,
        children: [
          for (final (index, option) in options.indexed)
            SelectionTile(
              title: option.name,
              key: ValueKey([index, option.name]),
              isSelected: selection == option.option,
              selectionRadius: context.xsBorderRadius,
              onTap: () => onSelect(option.option),
              tilePadding: tilePadding ??
                  context.insets.symmetricSp(
                    vertical: AppFontSizes.px8,
                    horizontal: AppFontSizes.px4,
                  ),
            )
        ],
      ),
    );
  }
}
