import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class StatPieChart extends AppStatelessWidget {
  final SupportedCurrency? currency;
  final PieData data;

  const StatPieChart(this.data, {this.currency, super.key});

  String get symbol => currency?.symbol ?? AppStrings.naira;
  double get value {
    final fx = currency?.fxRate ?? 1;
    return data.sum * fx;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      alignment: Alignment.center,
      child: CustomPaint(
        painter: <PERSON><PERSON><PERSON><PERSON>(data: data),
        child: Padding(
          padding: context.insets.symmetricSp(vertical: AppFontSizes.px96),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              AppText(
                value.asCurrencyShort(symbol),
                textAlign: TextAlign.center,
                style: context.textStyle.d0(),
              )
            ],
          ),
        ),
      ),
    );
  }
}
