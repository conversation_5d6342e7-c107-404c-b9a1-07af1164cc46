import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class TransactionStatusPickerContainer extends AppStatelessWidget {
  final TransactionStatus? status;
  final OnChanged<TransactionStatus?> onChanged;
  final Widget leading;

  const TransactionStatusPickerContainer(
    this.status, {
    super.key,
    required this.leading,
    required this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    return AppCard(
      padding: context.insets.symmetricSp(
        horizontal: AppFontSizes.px16,
        vertical: AppFontSizes.px12,
      ),
      border: BorderSide(color: context.inputBorderColor, width: 2),
      cornerStyle: CornerStyle.rounded,
      borderRadius: context.lgBorderRadius,
      child: Row(
        children: [
          leading,
          const Spacer(),
          TransactionStatusPicker(status, onChanged: onChanged),
        ],
      ),
    );
  }
}
