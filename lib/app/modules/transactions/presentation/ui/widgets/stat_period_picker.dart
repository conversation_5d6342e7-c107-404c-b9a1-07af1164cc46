import 'package:day1/day1.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class StatPeriodPicker extends AppStatefulWidget {
  final TransactionPeriod period;
  final DateTimeRange? range;
  final OnChanged<DateTimeRange?>? onDateRange;
  final OnChanged<TransactionPeriod> onChange;
  final bool isOutlined;

  const StatPeriodPicker(
    this.period, {
    super.key,
    this.onDateRange,
    this.range,
    required this.onChange,
    this.isOutlined = true,
  });

  @override
  State<StatefulWidget> createState() => _StatPeriodPickerState();
}

class _StatPeriodPickerState extends State<StatPeriodPicker>
    with TransactionsModalMixin {
  late final ValueNotifier<DateTimeRange?> range;

  @override
  void initState() {
    super.initState();
    range = ValueNotifier(widget.range);
    range.addListener(_rangeListener);
  }

  _rangeListener() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      widget.onDateRange?.call(range.value);
    });
  }

  @override
  Widget build(BuildContext context) {
    final outlined = widget.isOutlined;

    return ListenableBuilder(
      listenable: Listenable.merge([range]),
      builder: (_, __) {
        final period = widget.period;
        final hasSpan = period.isCustom && range.hasValue;
        final text = hasSpan ? range.value?.formattedDateRange : period.name;

        return AppOutlineButton(
          height: context.sp(AppFontSizes.px36),
          onPressed: () {
            HapticFeedback.mediumImpact();
            showPeriodModal(
              context,
              period: period,
              range: widget.range,
              onSelect: (value) {
                widget.onChange(value.$1);
                range.value = value.$2;
              },
            );
          },
          contentPadding: outlined ? null : EdgeInsets.zero,
          trailingIcon: const AppIcon(AppIcons.navArrowDown),
          alignment: Alignment.centerLeft,
          borderColor: outlined ? null : AppColors.transparent,
          bgColor: outlined ? null : AppColors.transparent,
          variant: OutlineBtnVariant.neutral,
          icon: outlined ? const AppIcon(AppIcons.calendar) : null,
          text: text,
          size: hasSpan ? ButtonSize.small : ButtonSize.medium,
        );
      },
    );
  }
}
