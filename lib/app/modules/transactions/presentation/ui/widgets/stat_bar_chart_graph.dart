import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class BarChartGraph extends StatelessWidget {
  final BarChartData data;
  final SupportedCurrency? currency;

  String get symbol => currency?.symbol ?? AppStrings.naira;
  String get value {
    final fx = currency?.fxRate ?? 1;
    return (data.sum * fx).asCurrencyShort(symbol);
  }

  BarChartData get currencyData {
    if (currency == null) return data;
    return BarChartData(
      series: data.series.mapList((it) {
        return BarChartBarData(
          value: it.value * (currency?.fxRate ?? 1),
          label: it.label,
        );
      }),
      legend: data.legend,
      period: data.period,
    );
  }

  String get min {
    final fx = currency?.fxRate ?? 1;
    return (data.min * fx).ceil().formattedNumber;
  }

  String get median {
    final fx = currency?.fxRate ?? 1;
    return (data.median * fx).ceil().formattedNumber;
  }

  String get max {
    final fx = currency?.fxRate ?? 1;
    return (data.max * fx).ceil().formattedNumber;
  }

  const BarChartGraph({super.key, required this.data, this.currency});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        Padding(
          padding: context.insets.onlySp(left: AppFontSizes.px32),
          child: AppText(
            value,
            style: context.textStyle.h3(),
          ),
        ),
        const AppGap.y24(),
        AspectRatio(
          aspectRatio: 20 / 9,
          child: LayoutBuilder(
            builder: (context, constraints) {
              final height = constraints.maxHeight;
              return Stack(
                children: [
                  Positioned.fill(
                    child: Row(
                      children: [
                        Flexible(
                          flex: 1,
                          child: BarYLabels(
                            max: max,
                            mid: median,
                            min: min,
                            height: height,
                          ),
                        ),
                        Expanded(
                          flex: 5,
                          child: BarChartBarList(data, height: height),
                        )
                      ],
                    ),
                  ),
                ],
              );
            },
          ),
        ),
      ],
    );
  }
}
