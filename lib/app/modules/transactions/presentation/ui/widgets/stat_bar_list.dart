import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class BarChartBarList extends AppStatelessWidget {
  final BarChartData data;
  final double height;

  const BarChartBarList(
    this.data, {
    super.key,
    required this.height,
  });

  @override
  Widget build(BuildContext context) {
    if (!data.series.hasValue) return const Offstage();
    return Scrollbar(
      child: ListView.separated(
        padding: context.insets.onlySp(
          left: AppFontSizes.px10,
          right: AppFontSizes.px32,
        ),
        scrollDirection: Axis.horizontal,
        separatorBuilder: (context, index) {
          return const AppGap.h20();
        },
        itemBuilder: (context, index) {
          final item = data.series[index];
          return BarChartBar(
            fraction: item.fraction(data.max),
            label: item.label,
            height: height,
          );
        },
        itemCount: data.series.length,
      ),
    );
  }
}
