import 'package:day1/day1.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class StatCurrencyPicker extends AppStatefulWidget {
  final ValueNotifier<SupportedCurrency> currency;

  const StatCurrencyPicker(this.currency, {super.key});

  @override
  State<StatefulWidget> createState() => _StatCurrencyPickerState();
}

class _StatCurrencyPickerState extends State<StatCurrencyPicker> {
  late OverlayPortalController _overlayController;

  @override
  void initState() {
    super.initState();
    _overlayController = OverlayPortalController();
  }

  @override
  Widget build(BuildContext context) {
    final xOffset = context.sizer.width * .925;
    final topMargin = context.sp(AppFontSizes.px5);

    return AppOverlayPortal(
      controller: _overlayController,
      overlayOffSet: Offset(-xOffset, topMargin),
      childAnchor: Alignment.bottomRight,
      overlayChild: GenericListener(
        valueListenable: widget.currency,
        builder: (currency) {
          final options = SupportedCurrency.values.mapList(
            (it) => NamedOptionData(it, name: it.name),
          );
          return Align(
            alignment: Alignment.topCenter,
            child: NamedOptionDatasOverlay(
              onSelect: (value) {
                widget.currency.value = value;
                _overlayController.hide();
              },
              options: options,
              selection: currency,
            ),
          );
        },
      ),
      child: GenericListener(
        valueListenable: widget.currency,
        builder: (currency) {
          return AppOutlineButton(
            onPressed: () {
              HapticFeedback.mediumImpact();
              _overlayController.toggle();
            },
            text: currency.name,
            size: ButtonSize.small,
            trailingIcon: const AppIcon(AppIcons.navArrowDown),
            alignment: Alignment.centerRight,
          );
        },
      ),
    );
  }
}
