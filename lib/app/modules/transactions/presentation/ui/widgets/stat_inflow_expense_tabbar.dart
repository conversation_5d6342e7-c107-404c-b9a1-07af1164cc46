import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class StatInflowExpenseTabbar extends AppStatelessWidget {
  final ValueNotifier<TransactionType> type;
  final Widget leading;
  const StatInflowExpenseTabbar(this.type, {super.key, required this.leading});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: context.insets.symmetricSp(horizontal: AppFontSizes.px16),
      child: Row(
        children: [
          Expanded(child: leading),
          GenericListener(
            valueListenable: type,
            builder: (selection) {
              return AppSegmentedTabBar(
                selection,
                bgColor: context.cardColor,
                tabs: [
                  for (final item in TransactionType.values)
                    AppSegmentedTab(
                      constraints: BoxConstraints(
                        minWidth: context.sp(AppFontSizes.px70),
                      ),
                      value: item,
                      inActiveBorder: context.outlineBtnAltBorderColor,
                      padding: context.insets.symmetricSp(
                        horizontal: AppFontSizes.px14,
                        vertical: AppFontSizes.px4,
                      ),
                      activeValue: selection,
                      label: item.name.tr(),
                      onSelect: (value) {
                        type.value = value;
                      },
                    )
                ],
              );
            },
          ),
        ],
      ),
    );
  }
}
