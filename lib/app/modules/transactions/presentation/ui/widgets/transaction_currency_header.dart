import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class TransactionCurrencyHeader extends AppStatelessWidget {
  final List<TransactionResponse> transactions;
  final StatCurrencyPicker currencyPicker;
  final SupportedCurrency? currency;

  const TransactionCurrencyHeader(
    this.transactions, {
    super.key,
    this.currency,
    required this.currencyPicker,
  });

  String get symbol => currency?.symbol ?? AppStrings.naira;

  double getValue(double sum) {
    final fx = currency?.fxRate ?? 1;
    return sum * fx;
  }

  double get inflow {
    final credits = transactions.whereList((it) => !it.isDebit);
    if (credits.isEmpty) return 0;
    final amounts = credits.mapList((it) => it.amount);
    final value = amounts.reduce((p, n) => p + n);

    return getValue(value);
  }

  double get outflow {
    final dedits = transactions.whereList((it) => it.isDebit);
    if (dedits.isEmpty) return 0;
    final amounts = dedits.mapList((it) => it.amount);
    final value = amounts.reduce((p, n) => p + n);

    return getValue(value);
  }

  @override
  Widget build(BuildContext context) {
    if (transactions.isEmpty) return const AppGap.y24();

    final inflowColor = context.raisedBtnCBgColor;
    const outFlowColor = AppColors.secondary6;
    final style = context.textStyle.b3(weight: FontWeight.w300);
    final amountStyle = style.copyWith(fontWeight: FontWeight.w500);
    const hPadding = AppFontSizes.px16;

    return Padding(
      padding: context.insets
          .fromLTRBSp(hPadding, AppFontSizes.px24, hPadding, AppFontSizes.px12),
      child: Row(
        children: [
          Expanded(
            child: Text.rich(
              TextSpan(text: "${LocaleKeys.in_.tr()} ", children: [
                TextSpan(
                  text: inflow.asCurrency(symbol),
                  style: amountStyle.copyWith(color: inflowColor),
                ),
                TextSpan(text: "    ${LocaleKeys.out.tr()} "),
                TextSpan(
                  text: outflow.asCurrency(symbol),
                  style: amountStyle.copyWith(color: outFlowColor),
                ),
              ]),
              style: context.textStyle.b3(weight: FontWeight.w300),
            ),
          ),
          currencyPicker,
        ],
      ),
    );
  }
}
