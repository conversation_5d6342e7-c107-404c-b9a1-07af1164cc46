import 'package:day1/day1.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class TransactionStatusPicker extends AppStatefulWidget {
  final TransactionStatus? status;
  final OnChanged<TransactionStatus?> onChanged;

  const TransactionStatusPicker(
    this.status, {
    super.key,
    required this.onChanged,
  });

  @override
  State<StatefulWidget> createState() => _TransactionStatusPickerState();
}

class _TransactionStatusPickerState extends State<TransactionStatusPicker> {
  late OverlayPortalController _overlayController;

  @override
  void initState() {
    super.initState();
    _overlayController = OverlayPortalController();
  }

  String get label {
    if (widget.status == null) return LocaleKeys.all.tr();
    return widget.status?.name.tr() ?? "";
  }

  @override
  Widget build(BuildContext context) {
    final xOffset = context.sizer.width * .925;
    final topMargin = context.sp(AppFontSizes.px20);
    final color = context.raisedBtnBBgColor;
    final options = [
      NamedOptionData(null, name: LocaleKeys.all.tr()),
      ...TransactionStatus.values.mapList(
        (it) => NamedOptionData(it, name: it.name.tr()),
      )
    ];

    return AppOverlayPortal(
      controller: _overlayController,
      overlayOffSet: Offset(-xOffset, topMargin),
      childAnchor: Alignment.bottomRight,
      overlayChild: Align(
        alignment: Alignment.topCenter,
        child: NamedOptionDatasOverlay(
          onSelect: (value) {
            widget.onChanged(value);
            _overlayController.hide();
          },
          options: options,
          selection: widget.status,
        ),
      ),
      child: AppTextButton(
        size: ButtonSize.small,
        color: color,
        onPressed: () {
          HapticFeedback.mediumImpact();
          _overlayController.toggle();
        },
        text: label,
        textAlign: TextAlign.end,
        trailingIcon: AppIcon(AppIcons.navArrowDown, color: color),
      ),
    );
  }
}
