import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

mixin TransactionsModalMixin {
  showPeriodModal(
    BuildContext context, {
    required TransactionPeriod period,
    required OnChanged<(TransactionPeriod, DateTimeRange?)> onSelect,
    DateTimeRange? range,
  }) {
    return AppBottomModal.draggable(
      ctx: context,
      initialChildSize: .6,
      minChildSize: .2,
      maxChildSize: .9,
      padding: EdgeInsets.zero,
      builder: (value) {
        return StatPeriodModal(
          period,
          range: range,
          onSelect: onSelect,
          controller: value,
        );
      },
    );
  }
}
