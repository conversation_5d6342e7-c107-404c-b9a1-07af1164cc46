import 'package:flutter/material.dart';
import 'package:day1/day1.dart';

class TransactionsRoutes implements RouteRegistry {
  static const basePath = "/transactions";
  static const home = "$basePath/home";
  static const statistics = "$basePath/stats";
  static const detail = "$basePath/detail";

  const TransactionsRoutes();

  @override
  Route dynamicRoutes(RouteSettings settings, Route fallbackRoute) {
    return switch (settings.name) {
      home => MaterialPageRoute(builder: (context) {
          return const TransactionsScreen(key: Key(home));
        }),
      statistics => MaterialPageRoute(builder: (context) {
          return const TransactionsScreen(key: Key(statistics), tab: 1);
        }),
      detail => MaterialPageRoute(builder: (context) {
          return TransactionDetailScreen(
            settings.arguments as TransactionResponse,
            key: const Key(detail),
          );
        }),
      _ => fallbackRoute,
    };
  }
}
