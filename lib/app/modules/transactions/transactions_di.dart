import 'package:day1/day1.dart';

void registerTransactionsDI(DoAppConfig config) {
  ///[Services]
  locator.registerFactory<TransactionsService>(() {
    return TransactionsHttpService(locator());
  });

  ///[State]
  locator.registerLazySingleton<TransactionsState>(
    () => TransactionsState(),
  );
  locator.registerLazySingleton<TransactionsStatState>(
    () => TransactionsStatState(locator()),
  );
  locator.registerLazySingleton<TransactionsHistoryState>(
    () => TransactionsHistoryState(locator()),
  );
}

void resetTransactionsDI() {
  locator<TransactionsState>().reset();
  locator<TransactionsStatState>().reset();
  locator<TransactionsHistoryState>().reset();
}
