import 'package:day1/day1.dart';
import 'package:flutter/widgets.dart';

class DashboardState extends StateModel {
  ValueNotifier<int> pageIndex = ValueNotifier(0);
  List<int> pageStack = [0];
  List<NavPageData> pages = [
    const NavPageData(
      page: PaymentScreen(key: PageStorageKey("dash_home_tab")),
      icon: AppIcons.home,
    ),
    NavPageData(
      page: const Offstage(),
      icon: AppIcons.graphUp,
      onTap: () {
        AppRouter.pushNamed(TransactionsRoutes.home);
      },
    ),
    const NavPageData(
      page: CardScreen(key: PageStorageKey("dash_card_tab")),
      icon: AppIcons.mastercardCard,
    ),
    NavPageData(
      page: const Offstage(),
      icon: AppIcons.moreHoriz,
      onTap: () {
        AppRouter.pushNamed(SettingsRoutes.root);
      },
    ),
  ];

  @override
  reset() {
    pageIndex.value = 0;
    pageStack = [pageStack.last];
  }

  updateIndex(int page) {
    if (pageStack.last != page) pageStack.add(page);

    pageIndex.value = page;
  }
}
