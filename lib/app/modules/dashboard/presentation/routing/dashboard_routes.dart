import 'package:flutter/material.dart';
import 'package:day1/day1.dart';

class DashboardRoutes implements RouteRegistry {
  static const basePath = "/dashboard";
  static const home = "$basePath/home";
  static const cards = "$basePath/cards";

  const DashboardRoutes();

  @override
  Route dynamicRoutes(RouteSettings settings, Route fallbackRoute) {
    return switch (settings.name) {
      home => MaterialPageRoute(builder: (context) {
          return const DashboardScreen(key: Key(home));
        }),
      cards => MaterialPageRoute(builder: (context) {
          return const DashboardScreen(key: Key(cards), tab: 2);
        }),
      _ => fallbackRoute,
    };
  }
}
