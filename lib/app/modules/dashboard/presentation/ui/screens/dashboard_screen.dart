import 'package:day1/day1.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

class DashboardScreen extends AppStatefulWidget {
  final int tab;

  const DashboardScreen({this.tab = 0, super.key});

  @override
  State<StatefulWidget> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends State<DashboardScreen> with AppModalMixin {
  late PageController ctrl;
  late DashboardState state;

  @override
  void initState() {
    super.initState();
    ctrl = PageController(initialPage: widget.tab);
    state = context.read<DashboardState>();
    if (widget.tab != state.pageIndex.value) {
      state.updateIndex(widget.tab);
    }
  }

  @override
  Widget build(BuildContext context) {
    final pages = state.pages;

    return AppPopScope(
      onPopRequest: (_, __) async {
        if (state.pageStack.length > 1) {
          state.pageStack.removeLast();
          ctrl.jumpToPage(state.pageStack.last);
        } else {
          confirmAction(
            AppRouter.navigatorKey.currentContext ?? context,
            onContinue: AppRouter.closeApp,
            title: LocaleKeys.closeApp.tr(),
            description: LocaleKeys.closeAppQuestion.tr(),
          );
        }
      },
      child: Scaffold(
        resizeToAvoidBottomInset: false,
        bottomSheet: AppBottomNavBar(
          state.pages,
          controller: ctrl,
          pageIndex: state.pageIndex,
        ),
        body: PageView.builder(
          restorationId: "dashboard_slides",
          physics: const NeverScrollableScrollPhysics(),
          controller: ctrl,
          onPageChanged: state.updateIndex,
          itemBuilder: (context, index) {
            return pages[index].page;
          },
          itemCount: pages.length,
        ),
      ),
    );
  }
}
