import 'package:flutter/material.dart';
import 'package:day1/day1.dart';

class KycRoutes implements RouteRegistry {
  static const basePath = "/kyc";
  static const nationality = "$basePath/nationality";
  static const requirements = "$basePath/requirements";
  static const personalInfo = "$basePath/personal-information";
  static const passportInfo = "$basePath/passport-info";
  static const visa = "$basePath/visa";
  static const liveness = "$basePath/liveness-check";
  static const selfie = "$basePath/selfi-check";
  static const selfieSuccess = "$basePath/selfie-success";
  static const requestSuccess = "$basePath/request-success";
  static const inreview = "$basePath/in-review";
  static const approved = "$basePath/approved";
  static const completed = "$basePath/completed";
  static const status = "$basePath/status";

  const KycRoutes();

  @override
  Route dynamicRoutes(RouteSettings settings, Route fallbackRoute) {
    return switch (settings.name) {
      nationality => MaterialPageRoute(builder: (context) {
          return const KycNationalityScreen(key: Key(nationality));
        }),
      requirements => MaterialPageRoute(builder: (context) {
          return KycRequirementsScreen(
            settings.arguments as KycArguments,
            key: const Key(requirements),
          );
        }),
      personalInfo => MaterialPageRoute(builder: (context) {
          return KycPersonalInfoScreen(
            settings.arguments as KycArguments,
            key: const Key(personalInfo),
          );
        }),
      passportInfo => MaterialPageRoute(builder: (context) {
          return KycPassportInfoScreen(
            settings.arguments as KycArguments,
            key: const Key(passportInfo),
          );
        }),
      visa => MaterialPageRoute(builder: (context) {
          return KycVisaInfoScreen(
            settings.arguments as KycArguments,
            key: const Key(visa),
          );
        }),
      liveness => MaterialPageRoute(builder: (context) {
          return KycLivenessScreen(
            settings.arguments as KycArguments,
            key: const Key(liveness),
          );
        }),
      selfie => MaterialPageRoute(builder: (context) {
          return KycSelfieScreen(
            settings.arguments as KycArguments,
            key: const Key(selfie),
          );
        }),
      selfieSuccess => MaterialPageRoute(
          builder: (context) {
            return KycSelfieSuccessScreen(
              settings.arguments as KycArguments,
              key: const Key(selfieSuccess),
            );
          },
          fullscreenDialog: true,
        ),
      requestSuccess => MaterialPageRoute(
          builder: (context) {
            return KycRequestSuccessScreen(
              settings.arguments as KycArguments,
              key: const Key(requestSuccess),
            );
          },
          fullscreenDialog: true,
        ),
      inreview => MaterialPageRoute(
          builder: (context) {
            return KycInReviewScreen(
              settings.arguments as KycArguments,
              key: const Key(inreview),
            );
          },
          fullscreenDialog: true,
        ),
      approved => MaterialPageRoute(
          builder: (context) {
            return KycApprovedScreen(
              settings.arguments as KycArguments,
              key: const Key(approved),
            );
          },
          fullscreenDialog: true,
        ),
      completed => MaterialPageRoute(
          builder: (context) {
            return KycCompletedScreen(
              settings.arguments as KycArguments,
              key: const Key(completed),
            );
          },
          fullscreenDialog: true,
        ),
      status => MaterialPageRoute(
          builder: (context) {
            return const KycStatusScreen(key: Key(status));
          },
        ),
      _ => fallbackRoute,
    };
  }
}
