import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class KycLivenessScreen extends AppStatefulWidget {
  final KycArguments arguments;
  const KycLivenessScreen(this.arguments, {super.key});

  @override
  State<StatefulWidget> createState() => _KycLivenessScreenState();
}

class _KycLivenessScreenState extends State<KycLivenessScreen> {
  final GlobalKey<FormState> formKey = GlobalKey();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: DoAppBar(
        title: LocaleKeys.selfieCheck.tr(),
      ),
      body: SingleChildScrollView(
        padding: context.insets.defaultAllInsets,
        child: AppStepForm(
          formKey: formKey,
          step: 3,
          steps: 3,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              AppFormHeader(
                title: LocaleKeys.livenessCheck.tr(),
                subTitle: LocaleKeys.youAreAlmostThere.tr(),
              ),
              const AppSvg(AppVectors.viewFinder),
              const AppGap.y32(),
              KycRecommendationCard(
                header: AppText(
                  "${LocaleKeys.steps.tr()}:",
                  style: context.textStyle.b2(weight: FontWeight.w500),
                ),
              ),
              const AppGap.y48(),
              AppButton(
                onPressed: () {
                  AppRouter.pushNamed(
                    KycRoutes.selfieSuccess,
                    arguments: widget.arguments,
                  );
                },
              )
            ],
          ),
        ),
      ),
    );
  }
}
