import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class KycPassportInfoScreen extends AppStatefulWidget {
  final KycArguments arguments;
  const KycPassportInfoScreen(this.arguments, {super.key});

  @override
  State<StatefulWidget> createState() => _KycPassportInfoScreenState();
}

class _KycPassportInfoScreenState extends State<KycPassportInfoScreen> {
  final GlobalKey<FormState> formKey = GlobalKey();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: DoAppBar(
        title: LocaleKeys.passportInfo.tr(),
      ),
      body: SingleChildScrollView(
        padding: context.insets.defaultAllInsets,
        child: AppStepForm(
          formKey: formKey,
          step: 1,
          steps: 3,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              AppFormHeader(
                title: LocaleKeys.passportInfo.tr(),
                subTitle: LocaleKeys.provideInfoOnYourAmericanPass.tr(),
                bottomGap: const AppGap.y32(),
              ),
              FormLabel(label: LocaleKeys.passIssuingCountry.tr()),
              AppTextField(
                hintText: LocaleKeys.unitedStates.tr(),
                autofillHints: const [AutofillHints.countryName],
              ),
              const AppGap.y16(),
              FormLabel(label: LocaleKeys.passNumber.tr()),
              AppTextField(
                hintText: LocaleKeys.enterPassNumber.tr(),
                keyboardType: TextInputType.visiblePassword,
              ),
              const AppGap.y16(),
              FormLabel(label: LocaleKeys.expirationDate.tr()),
              const AppDateField(),
              const AppGap.y16(),
              FormLabel(label: LocaleKeys.fullName.tr()),
              AppTextField(
                hintText: LocaleKeys.enterFullnameLikePassport.tr(),
                autofillHints: const [AutofillHints.name],
              ),
              const AppGap.y16(),
              FormLabel(label: LocaleKeys.dob.tr()),
              const AppDateField(),
              const AppGap.y24(),
              const KycPassportNoticeCard(),
              const AppGap.y24(),
              AppButton(
                onPressed: () {
                  AppRouter.pushNamed(
                    KycRoutes.visa,
                    arguments: widget.arguments,
                  );
                },
              ),
            ],
          ),
        ),
      ),
    );
  }
}
