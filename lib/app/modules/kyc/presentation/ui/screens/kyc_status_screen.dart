import 'package:flutter/material.dart';
import 'package:day1/day1.dart';

class KycStatusScreen extends AppStatelessWidget {
  const KycStatusScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      appBar: DoAppBar(
        padding: context.insets.symmetricSp(horizontal: AppFontSizes.px24),
        title: LocaleKeys.accountVerification.ctr(),
      ),
      body: AppCard(
        margin: context.insets.defaultAllInsets.add(
          context.insets.onlySp(bottom: AppFontSizes.px32),
        ),
        padding: EdgeInsets.zero,
        child: ListView(
          padding: context.insets.defaultCardInsets,
          children: [
            AppFormHeader(
              title: LocaleKeys.acctVerificationStatus.tr(),
              subTitle: LocaleKeys.youHaveCompletedVerificationSteps.tr(
                {"step": "2"},
              ),
              bottomGap: const AppGap.y32(),
            ),
            AppCard(
              padding: EdgeInsets.zero,
              color: context.scaffoldBgColor,
              borderRadius: context.xlBorderRadius,
              child: const Column(
                children: [
                  KycStatusTile(KycStatusStep.email, isDone: true),
                  KycStatusTile(KycStatusStep.phone, isDone: true),
                  KycStatusTile(KycStatusStep.id, isDone: false),
                ],
              ),
            )
          ],
        ),
      ),
    );
  }
}

enum KycStatusStep {
  email(LocaleKeys.emailVerification),
  phone(LocaleKeys.telephoneNumber),
  id(LocaleKeys.idDocument);

  const KycStatusStep(this.name);

  final String name;
}

class KycStatusTile extends AppStatelessWidget {
  final KycStatusStep step;
  final bool isDone;

  const KycStatusTile(this.step, {required this.isDone, super.key});

  @override
  Widget build(BuildContext context) {
    final bgColor = isDone
        ? context.notificationBackgroundColor
        : context.errorNotificationBackgroundColor;
    final textColor = isDone ? null : context.errorColor;
    final text = isDone ? LocaleKeys.verified : LocaleKeys.unVerified;
    Widget? trailing;

    if (!isDone) {
      trailing = AppIcon(
        AppIcons.navArrowRight,
        size: AppFontSizes.px16,
        color: textColor,
      );
    }

    return InkWell(
      child: Padding(
        padding: context.insets.symmetricSp(
          horizontal: AppFontSizes.px16,
          vertical: AppFontSizes.px12,
        ),
        child: Row(
          children: [
            Expanded(child: AppText(step.name.tr())),
            const AppGap.h16(),
            AppPill(
              text.ctr(),
              textColor: textColor,
              bgColor: bgColor,
              trailing: trailing,
            )
          ],
        ),
      ),
    );
  }
}
