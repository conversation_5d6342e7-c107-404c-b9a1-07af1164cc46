import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class KycSelfieSuccessScreen extends AppStatelessWidget {
  final KycArguments arguments;
  const KycSelfieSuccessScreen(this.arguments, {super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: DoAppBar(
        leading: const AppCloseButton(asHero: true),
        title: LocaleKeys.verifyingIdentity.tr(),
      ),
      body: ListView(
        padding: context.insets.defaultHorizontalInsets,
        children: [
          KycStatusCard(
            title: LocaleKeys.selfieCaptured.tr(),
            decription: LocaleKeys.selfieCapturedSuccessfully.tr(),
            action: () {
              AppRouter.pushNamed(
                KycRoutes.requestSuccess,
                arguments: arguments,
              );
            },
          )
        ],
      ),
    );
  }
}
