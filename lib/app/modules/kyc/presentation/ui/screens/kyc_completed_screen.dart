import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class KycCompletedScreen extends AppStatelessWidget {
  final KycArguments arguments;
  const KycCompletedScreen(this.arguments, {super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: DoAppBar(
        leading: const AppCloseButton(asHero: true),
        title: LocaleKeys.verifyingIdentity.tr(),
      ),
      body: ListView(
        padding: context.insets.defaultHorizontalInsets,
        children: [
          KycStatusCard(
            title: LocaleKeys.readyToRoll.tr(),
            decription: LocaleKeys.bankDetailsReady.tr(),
            btnText: LocaleKeys.ok.utr(),
            action: () {
              AppRouter.pushAndRemoveUntil(
                DashboardRoutes.home,
                arguments: arguments,
              );
            },
          )
        ],
      ),
    );
  }
}
