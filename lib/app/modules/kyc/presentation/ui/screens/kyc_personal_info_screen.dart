import 'package:day1/day1.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class KycPersonalInfoScreen extends AppStatefulWidget {
  final KycArguments arguments;
  const KycPersonalInfoScreen(this.arguments, {super.key});

  @override
  State<StatefulWidget> createState() => _KycPersonalInfoScreenState();
}

class _KycPersonalInfoScreenState extends State<KycPersonalInfoScreen> {
  final GlobalKey<FormState> formKey = GlobalKey();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: DoAppBar(
        title: LocaleKeys.personalInfo.tr(),
      ),
      body: SingleChildScrollView(
        padding: context.insets.defaultAllInsets,
        child: AppStepForm(
          formKey: formKey,
          step: 1,
          steps: 3,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              AppFormHeader(
                title: LocaleKeys.tellUsAbtU.tr(),
                subTitle: LocaleKeys.provideInfoOnYourAmericanPass.tr(),
                bottomGap: const AppGap.y32(),
              ),
              FormLabel(label: LocaleKeys.bvnNum.tr()),
              AppNumberField(
                hintText: LocaleKeys.enterBvnNum.tr(),
                formatters: [FilteringTextInputFormatter.digitsOnly],
              ),
              const AppGap.y16(),
              FormLabel(label: LocaleKeys.ninNum.tr()),
              AppNumberField(
                hintText: LocaleKeys.enterNinNum.tr(),
                keyboardType: TextInputType.number,
                formatters: [FilteringTextInputFormatter.digitsOnly],
              ),
              const AppGap.y16(),
              FormLabel(label: LocaleKeys.nigerianAddress.tr()),
              AppTextField(
                hintText: LocaleKeys.enterNigerianAddress.tr(),
                keyboardType: TextInputType.number,
                autofillHints: const [AutofillHints.addressCityAndState],
              ),
              const AppGap.y24(),
              const KycBvnAccessCard(),
              const AppGap.y24(),
              AppButton(
                onPressed: () {
                  AppRouter.pushNamed(
                    KycRoutes.visa,
                    arguments: widget.arguments,
                  );
                },
              ),
            ],
          ),
        ),
      ),
    );
  }
}
