import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class KycVisaInfoScreen extends AppStatefulWidget {
  final KycArguments arguments;
  const KycVisaInfoScreen(this.arguments, {super.key});

  @override
  State<StatefulWidget> createState() => _KycVisaInfoScreenState();
}

class _KycVisaInfoScreenState extends State<KycVisaInfoScreen> {
  final GlobalKey<FormState> formKey = GlobalKey();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: DoAppBar(
        title: LocaleKeys.visaInfo.tr(),
      ),
      body: SingleChildScrollView(
        padding: context.insets.defaultAllInsets,
        child: AppStepForm(
          formKey: formKey,
          step: 2,
          steps: 3,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              AppFormHeader(
                title: LocaleKeys.visaInfo.tr(),
                subTitle: LocaleKeys.pleaseProvideVisaInfo.tr(),
                bottomGap: const AppGap.y32(),
              ),
              FormLabel(label: LocaleKeys.visaReferenceNum.tr()),
              AppTextField(
                hintText: LocaleKeys.enterVisaReferenceNum.tr(),
                keyboardType: TextInputType.visiblePassword,
              ),
              const AppGap.y16(),
              FormLabel(label: LocaleKeys.visaType.tr()),
              AppDropDownField(
                hintText: LocaleKeys.selectAnOption.tr(),
                options: const [],
              ),
              const AppGap.y16(),
              FormLabel(label: LocaleKeys.visaIssueDate.tr()),
              const AppDateField(),
              const AppGap.y16(),
              FormLabel(label: LocaleKeys.visaExpireDate.tr()),
              const AppDateField(),
              const AppGap.y24(),
              AppButton(
                onPressed: () {
                  final route = switch (widget.arguments.country) {
                    KycCountry.foreign => KycRoutes.selfie,
                    _ => KycRoutes.liveness,
                  };
                  AppRouter.pushNamed(route, arguments: widget.arguments);
                },
              ),
            ],
          ),
        ),
      ),
    );
  }
}
