import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class KycRequestSuccessScreen extends AppStatelessWidget {
  final KycArguments arguments;
  const KycRequestSuccessScreen(this.arguments, {super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: DoAppBar(
        leading: const AppCloseButton(asHero: true),
        title: LocaleKeys.verifyingIdentity.tr(),
      ),
      body: ListView(
        padding: context.insets.defaultHorizontalInsets,
        children: [
          KycStatusCard(
            title: LocaleKeys.verificationRequestSubmitted.tr(),
            decription: LocaleKeys.thanksForPatienceYouWillBeNotified.tr(),
            action: () {
              AppRouter.pushNamed(
                arguments.isForeign ? KycRoutes.approved : KycRoutes.inreview,
                arguments: arguments,
              );
            },
          )
        ],
      ),
    );
  }
}
