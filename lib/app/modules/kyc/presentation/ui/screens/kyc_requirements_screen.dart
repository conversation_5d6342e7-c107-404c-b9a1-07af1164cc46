import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class KycRequirementsScreen extends AppStatelessWidget
    with KycRequirementsMixin {
  final KycArguments arguments;

  const KycRequirementsScreen(this.arguments, {super.key});

  bool get _isForeign => arguments.isForeign;

  List<KycRequirementData> get _requirements => getRequirements(_isForeign);

  @override
  Widget build(BuildContext context) {
    List<Widget> widgets = [
      for (final requirement in _requirements) KycRequirementTile(requirement)
    ];
    return Scaffold(
      appBar: DoAppBar(
        title: LocaleKeys.kycSetup.tr(),
      ),
      body: SingleChildScrollView(
        padding: context.insets.defaultAllInsets,
        child: AppCard(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              AppFormHeader(
                title: LocaleKeys.kycVerification.tr(),
                subTitle: LocaleKeys.completKycInQuickSteps.tr(),
              ),
              for (final widget in widgets.intersperse(const AppGap.y12()))
                widget,
              const AppGap.y24(),
              const KycRecommendationCard(),
              const AppGap.y24(),
              AppText(
                LocaleKeys.picturesGoIn90Days.tr(),
                textAlign: TextAlign.center,
                style: context.textStyle.b3(
                  color: context.secondaryTextColor,
                ),
              ),
              const AppGap.y64(),
              AppButton(
                onPressed: () {
                  final route = switch (arguments.country) {
                    KycCountry.foreign => KycRoutes.passportInfo,
                    _ => KycRoutes.personalInfo,
                  };
                  AppRouter.pushNamed(route, arguments: arguments);
                },
              ),
            ],
          ),
        ),
      ),
    );
  }
}
