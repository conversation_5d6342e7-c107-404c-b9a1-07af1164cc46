import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class KycNationalityScreen extends AppStatefulWidget {
  const KycNationalityScreen({super.key});

  @override
  State<StatefulWidget> createState() => _KycNationalityScreenState();
}

class _KycNationalityScreenState extends State<KycNationalityScreen> {
  final ValueNotifier<KycCountry?> _country = ValueNotifier(null);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: DoAppBar(
        title: LocaleKeys.nationality.tr(),
      ),
      body: SingleChildScrollView(
        padding: context.insets.defaultAllInsets,
        child: AppCard(
          child: GenericListener(
            valueListenable: _country,
            builder: (country) {
              final padding = context.insets.symmetricSp(
                vertical: AppFontSizes.px24,
                horizontal: AppFontSizes.px28,
              );
              final flagPadding = context.insets.onlySp(top: AppFontSizes.px5);

              return Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  AppFormHeader(
                    title: LocaleKeys.selectNationality.tr(),
                    subTitle: LocaleKeys.selectNation4Personalisation.tr(),
                    bottomGap: const AppGap.y32(),
                  ),
                  AppRadioCard(
                    value: KycCountry.foreign,
                    padding: padding,
                    groupValue: country,
                    icon: Padding(
                      padding: flagPadding,
                      child: const AppIndicatorAlignedPrefix(
                        child: AppSvg(
                          AppVectors.usa,
                          width: 25,
                          height: 20,
                        ),
                      ),
                    ),
                    title: LocaleKeys.nonNigerian.tr(),
                    rider: LocaleKeys.forTravellers.tr(),
                    onChanged: (value) => _country.value = value,
                  ),
                  const AppGap.y12(),
                  AppRadioCard(
                    value: KycCountry.nigeria,
                    padding: padding,
                    groupValue: country,
                    icon: Padding(
                      padding: flagPadding,
                      child: const AppIndicatorAlignedPrefix(
                        child: AppSvg(
                          AppVectors.nigeria,
                          width: 25,
                          height: 20,
                        ),
                      ),
                    ),
                    title: LocaleKeys.aNigerian.tr(),
                    rider: LocaleKeys.forResidentsFreqTravellers.tr(),
                    onChanged: (value) => _country.value = value,
                  ),
                  const AppGap.y48(),
                  AppButton(
                    isDisabled: country == null,
                    onPressed: () {
                      AppRouter.pushNamed(
                        KycRoutes.requirements,
                        arguments: KycArguments(
                          country: country!,
                        ),
                      );
                    },
                  ),
                ],
              );
            },
          ),
        ),
      ),
    );
  }
}
