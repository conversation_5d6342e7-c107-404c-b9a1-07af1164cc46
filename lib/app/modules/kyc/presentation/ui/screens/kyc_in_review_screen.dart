import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class KycInReviewScreen extends AppStatelessWidget {
  final KycArguments arguments;
  const KycInReviewScreen(this.arguments, {super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: DoAppBar(
        leading: const AppCloseButton(asHero: true),
        title: LocaleKeys.verifyingIdentity.tr(),
      ),
      body: ListView(
        padding: context.insets.defaultHorizontalInsets,
        children: [
          KycStatusCard(
            title: LocaleKeys.detailsUnderReview.tr(),
            decription: LocaleKeys.youWillBeNotifiedAfterReview.tr(),
            icon: AppIcons.searchEngine,
            action: () {
              AppRouter.pushNamed(
                KycRoutes.approved,
                arguments: arguments,
              );
            },
          )
        ],
      ),
    );
  }
}
