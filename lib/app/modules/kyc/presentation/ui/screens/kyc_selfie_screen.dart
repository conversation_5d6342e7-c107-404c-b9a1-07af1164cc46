import 'package:day1/day1.dart';
import 'package:flutter/material.dart';
import 'package:image_cropper/image_cropper.dart';

class KycSelfieScreen extends AppStatefulWidget {
  final KycArguments arguments;
  const KycSelfieScreen(this.arguments, {super.key});

  @override
  State<StatefulWidget> createState() => _KycSelfieScreenState();
}

class _KycSelfieScreenState extends State<KycSelfieScreen> with AppModalMixin {
  final GlobalKey<FormState> formKey = GlobalKey();
  final ValueNotifier<FSResponse?> _image = ValueNotifier(null);

  _choosePhoto() async {
    try {
      final fileData = await AppImagePlugin.pickImage(
        shouldCrop: true,
        cropperUiData: CropperUiData(
          LocaleKeys.selfieCheck.tr(),
          circularCrop: false,
          aspectRatio: CropAspectRatioPreset.ratio16x9,
          aspectRatios: [CropAspectRatioPreset.ratio16x9],
        ),
      );
      if (fileData.hasFile) {
        _image.value = fileData;
      }
    } catch (e, t) {
      AppLogger.severe("$e", stackTrace: t, error: e);
    }
  }

  _selectImage() async {
    confirmAction(
      context,
      title: LocaleKeys.day1NeedCamAccess.tr(),
      description: LocaleKeys.pleaseGrantCamAccess.tr(),
      onContinue: _choosePhoto,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: DoAppBar(
        title: LocaleKeys.selfieCheck.tr(),
      ),
      body: SingleChildScrollView(
        padding: context.insets.defaultAllInsets,
        child: AppStepForm(
          formKey: formKey,
          step: 3,
          steps: 3,
          child: GenericListener(
            valueListenable: _image,
            builder: (image) {
              final hasImage = image != null;
              final btnText =
                  (hasImage ? LocaleKeys.retakePhoto : LocaleKeys.takePhoto);

              return Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  AppFormHeader(
                    title: LocaleKeys.takeAPhotoWithId.tr(),
                    subTitle: LocaleKeys.loookInCamera.tr(),
                  ),
                  KycSelfieImageCard(
                    image: AppImageData(imageData: image?.file),
                    fit: BoxFit.contain,
                  ),
                  if (hasImage) ...[
                    const AppGap.y48(),
                    AppButton(
                      text: LocaleKeys.useThis.tr(),
                      onPressed: () {
                        AppRouter.pushNamed(
                          KycRoutes.selfieSuccess,
                          arguments: widget.arguments,
                        );
                      },
                    ),
                  ],
                  if (hasImage) const AppGap.y16() else const AppGap.y24(),
                  AppOutlineButton(
                    text: btnText.tr(),
                    onPressed: hasImage ? _choosePhoto : _selectImage,
                  ),
                ],
              );
            },
          ),
        ),
      ),
    );
  }
}
