import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class KycApprovedScreen extends AppStatelessWidget {
  final KycArguments arguments;
  const KycApprovedScreen(this.arguments, {super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: DoAppBar(
        leading: const AppCloseButton(asHero: true),
        title: LocaleKeys.verifyingIdentity.tr(),
      ),
      body: ListView(
        padding: context.insets.defaultHorizontalInsets,
        children: [
          KycStatusCard(
            title: LocaleKeys.kycApproved.tr(),
            decription: LocaleKeys.documentsHavBeenVerified.tr(),
            action: () {
              AppRouter.pushNamed(
                KycRoutes.completed,
                arguments: arguments,
              );
            },
          )
        ],
      ),
    );
  }
}
