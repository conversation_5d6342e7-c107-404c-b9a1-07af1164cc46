import 'dart:io';

import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class KycSelfieImageCard extends AppStatelessWidget {
  final AppImageData? image;
  final double? width;
  final double? height;
  final BoxFit? fit;
  final Alignment alignment;
  final GlobalKey<ScaffoldState>? scaffoldKey;

  const KycSelfieImageCard({
    this.image,
    this.scaffoldKey,
    super.key,
    this.alignment = Alignment.center,
    this.fit,
    this.width,
    this.height,
  });

  @override
  Widget build(BuildContext context) {
    final defaultSize = context.sp(AppFontSizes.px40);
    return Align(
      alignment: alignment,
      child: Builder(
        builder: (_) {
          ImageProvider decoration = const AssetImage(AppImages.selfie);
          if (image?.isUrl ?? false) {
            decoration = NetworkImage(image?.fileUrl ?? "");
          }
          if (image?.isString ?? false) {
            decoration = AssetImage(image?.filePath ?? "");
          }
          if (image?.isFile ?? false) {
            decoration = FileImage(image?.file ?? File(""));
          }
          return AspectRatio(
            aspectRatio: 16 / 9,
            child: Container(
              width: width ?? defaultSize,
              height: height ?? defaultSize,
              decoration: BoxDecoration(
                border: Border.all(
                  color: context.cardBorderColor,
                  width: 2,
                ),
                image: DecorationImage(
                  image: decoration,
                  fit: fit ?? BoxFit.cover,
                  alignment: alignment,
                ),
                borderRadius: context.smBorderRadius,
              ),
            ),
          );
        },
      ),
    );
  }
}
