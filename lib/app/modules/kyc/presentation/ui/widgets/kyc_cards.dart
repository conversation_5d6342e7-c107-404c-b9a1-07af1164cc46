import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class KycRecommendationCard extends AppStatelessWidget
    with KycRequirementsMixin {
  final Widget? header;
  const KycRecommendationCard({super.key, this.header});

  @override
  Widget build(BuildContext context) {
    List<Widget> widgets = [
      for (final recommendation in recommendations)
        KycRequirementTile(recommendation)
    ];
    return AppCard(
      padding: context.insets.allSp(AppFontSizes.px24),
      color: context.scaffoldBgColor,
      borderRadius: context.xxlBorderRadius,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          header ?? AppInfoHeader(title: LocaleKeys.weRecommendThat.tr()),
          const AppGap.y8(),
          for (final widget in widgets.intersperse(const AppGap.y12())) widget
        ],
      ),
    );
  }
}

class KycPassportNoticeCard extends AppStatelessWidget {
  const KycPassportNoticeCard({super.key});

  @override
  Widget build(BuildContext context) {
    return AppCard(
      padding: context.insets.allSp(AppFontSizes.px24),
      color: context.dividerColor,
      borderRadius: context.xxlBorderRadius,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          AppInfoHeader(title: "${LocaleKeys.note.tr()}:"),
          const AppGap.y4(),
          AppText(LocaleKeys.onlyAmericanPassportsSupported.tr())
        ],
      ),
    );
  }
}

class KycBvnAccessCard extends AppStatelessWidget {
  const KycBvnAccessCard({super.key});

  static const _accessData = [
    LocaleKeys.fullName,
    LocaleKeys.telephoneNumber,
    LocaleKeys.dob,
  ];

  @override
  Widget build(BuildContext context) {
    final textColor = context.cardBorderColor;
    final style = context.textStyle.b3(color: textColor);

    List<Widget> widgets = [
      for (final data in _accessData) AppText(data.tr(), style: style)
    ];

    return AppCard(
      padding: context.insets.allSp(AppFontSizes.px24),
      color: context.dividerColor,
      borderRadius: context.xxlBorderRadius,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          AppText(
            LocaleKeys.weOnlyHaveAccessTo.tr(),
            style: context.textStyle.b2(
              weight: FontWeight.w500,
            ),
          ),
          const AppGap.y8(),
          for (final widget in widgets.intersperse(const AppGap.y4()))
            Row(
              children: [
                if (widget is AppText) ...[
                  const AppGap.h8(),
                  const AppText(AppStrings.dot),
                  const AppGap.h8(),
                ],
                Expanded(child: widget)
              ],
            ),
          const AppGap.y8(),
          AppText(LocaleKeys.bvnDoesntGiveAccessTo.tr(), style: style),
          const AppGap.y12(),
          AppRichText(LocaleKeys.dialToCheckBvn.tr(), textStyle: style),
        ],
      ),
    );
  }
}
