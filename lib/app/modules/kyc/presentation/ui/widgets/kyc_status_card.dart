import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class KycStatusCard extends AppStatelessWidget with AppModalMixin {
  final String title;
  final String decription;
  final IconData? icon;
  final String? btnText;
  final OnPressed action;
  final EdgeInsetsGeometry? margin;

  const KycStatusCard({
    super.key,
    required this.title,
    required this.decription,
    required this.action,
    this.icon,
    this.btnText,
    this.margin,
  });

  @override
  Widget build(BuildContext context) {
    return AppCard(
      margin: margin,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          const AppGap.y64(),
          const AppGap.y10(),
          AppIcon(
            icon ?? AppIcons.checkCircle,
            size: AppFontSizes.px75,
            color: AppColors.lemon140,
            alignment: Alignment.centerLeft,
          ),
          const AppGap.y32(),
          AppText(title, style: context.textStyle.h5()),
          const AppGap.y8(),
          AppText(
            decription,
            style: context.textStyle.b3(color: context.secondaryTextColor),
          ),
          const AppGap.y48(),
          AppButton(
            text: btnText ?? LocaleKeys.continueText.tr(),
            onPressed: action,
          ),
          const AppGap.y64(),
          const AppGap.y10(),
        ],
      ),
    );
  }
}
