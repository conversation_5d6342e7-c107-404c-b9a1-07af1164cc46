import 'package:day1/day1.dart';

mixin KycRequirementsMixin {
  List<KycRequirementData> getRequirements(bool isForeign) {
    return [
      if (isForeign) ...[
        KycRequirementData(
          title: LocaleKeys.passVerification.tr(),
          icon: AppIcons.pageSearch,
          description: LocaleKeys.pleaseProvideInfoAsInYourId.tr(),
        ),
        KycRequirementData(
          title: LocaleKeys.visaInfo.tr(),
          icon: AppIcons.airplane,
          description: LocaleKeys.addVisaInfo.tr(),
        ),
        KycRequirementData(
          title: LocaleKeys.selfieCheck.tr(),
          icon: AppIcons.userScan,
          description: LocaleKeys.weUseSelfieToVerifyIdentity.tr(),
        ),
      ],
      if (!isForeign) ...[
        KycRequirementData(
          title: LocaleKeys.bvnAndNinCheck.tr(),
          icon: AppIcons.pageSearch,
          description: LocaleKeys.pleaseProvideInfoAsInYourId.tr(),
        ),
        KycRequirementData(
          title: LocaleKeys.livenessCheck.tr(),
          icon: AppIcons.userScan,
        ),
      ]
    ];
  }

  List<KycRequirementData> get recommendations {
    return [
      KycRequirementData(
        icon: AppIcons.lightBulbOn,
        description: LocaleKeys.stayInLitPlace.tr(),
      ),
      KycRequirementData(
        icon: AppIcons.glasses,
        description: LocaleKeys.removeGlassesEtAl.tr(),
      ),
      KycRequirementData(
        icon: AppIcons.userScan,
        description: LocaleKeys.holdPhoneSteady.tr(),
      ),
    ];
  }
}
