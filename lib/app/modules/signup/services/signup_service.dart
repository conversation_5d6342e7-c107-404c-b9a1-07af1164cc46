import 'package:day1/day1.dart';

abstract class SignupService {
  NetworkCallResponse<AuthResponse> register(EmailParam param);
  NetworkCallResponse<AuthResponse> resendEmailOtp(EmailParam param);
  NetworkCallResponse<NoResponse> verifyInfo(VerificationParam param);
  NetworkCallResponse<NoResponse> createPassword(PasswordParam param);
  NetworkCallResponse<NoResponse> addPhoneNumber(PhoneParam param);
  NetworkCallResponse<AuthResponse> resendPhoneOtp(PhoneParam param);
  NetworkCallResponse<AuthResponse> enableBioAuth();
  NetworkCallResponse<NoResponse> addPin(PinParam param);
}
