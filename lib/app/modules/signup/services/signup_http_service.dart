import 'package:day1/day1.dart';

class SignupHttpService
    with AppHttpMixin, AppTaskMixin
    implements SignupService {
  final AppHttpService _service;

  SignupHttpService(this._service);

  @override
  NetworkCallResponse<NoResponse> addPhoneNumber(PhoneParam param) {
    return requestHandler(() async {
      await _service.patch("/add-phone", body: param);
      return const NoResponse();
    });
  }

  @override
  NetworkCallResponse<AuthResponse> enableBioAuth() {
    return requestHandler(() async {
      final request = await _service.post("/enable-biometrics");
      return AuthResponse.fromJson(request.parsedData);
    });
  }

  @override
  NetworkCallResponse<NoResponse> createPassword(PasswordParam param) {
    return requestHandler(() async {
      await _service.patch("/add-password", body: param);
      return const NoResponse();
    });
  }

  @override
  NetworkCallResponse<NoResponse> addPin(PinParam param) {
    return requestHandler(() async {
      await _service.patch("/add-pin", body: param);
      return const NoResponse();
    });
  }

  @override
  NetworkCallResponse<AuthResponse> register(EmailParam param) {
    AppLogger.info("SignupHttpService: register() called with param: ${param.toJson()}");
    return requestHandler(() async {
      AppLogger.info("SignupHttpService: making POST request to /register");
      final request = await _service.post("/register", body: param);
      AppLogger.info("SignupHttpService: received response: ${request.parsedData}");
      return AuthResponse.fromJson(request.parsedData);
    });
  }

  @override
  NetworkCallResponse<AuthResponse> resendEmailOtp(EmailParam param) {
    return requestHandler(() async {
      final request = await _service.patch("/resend-email-otp", body: param);
      return AuthResponse.fromJson(request.parsedData);
    });
  }

  @override
  NetworkCallResponse<AuthResponse> resendPhoneOtp(PhoneParam param) {
    return requestHandler(() async {
      final request = await _service.post("/resend-otp", body: param);
      return AuthResponse.fromJson(request.parsedData);
    });
  }

  @override
  NetworkCallResponse<NoResponse> verifyInfo(VerificationParam param) {
    return requestHandler(() async {
      await _service.patch("/verify-${param.type.name}", body: param);
      return const NoResponse();
    });
  }
}
