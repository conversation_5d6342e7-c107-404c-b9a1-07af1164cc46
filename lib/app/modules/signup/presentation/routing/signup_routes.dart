import 'package:flutter/material.dart';
import 'package:day1/day1.dart';

class SignupRoutes implements RouteRegistry {
  static const basePath = "/signup";
  static const email = "$basePath/email";
  static const emailVerification = "$basePath/email-verify";
  static const password = "$basePath/password";
  static const phoneNumber = "$basePath/phone-number";
  static const phoneVerification = "$basePath/phone-verify";
  static const biometrics = "$basePath/biometrics";
  static const pinSetup = "$basePath/pin";
  static const pinConfirm = "$basePath/pin-confirm";
  static const notification = "$basePath/notification";
  static const location = "$basePath/location";

  const SignupRoutes();

  @override
  Route dynamicRoutes(RouteSettings settings, Route fallbackRoute) {
    final argument =
        (settings.arguments as SignupArguments?) ?? const SignupArguments();

    return switch (settings.name) {
      email => MaterialPageRoute(builder: (context) {
          return SignupEmailScreen(
            argument,
            key: const Key(email),
          );
        }),
      emailVerification => MaterialPageRoute(builder: (context) {
          return SignupEmailVerificationScreen(
            argument,
            key: const Key(emailVerification),
          );
        }),
      password => MaterialPageRoute(builder: (context) {
          return SignupPasswordScreen(
            argument,
            key: const Key(password),
          );
        }),
      phoneNumber => MaterialPageRoute(builder: (context) {
          return SignupPhoneNumberScreen(
            argument,
            key: const Key(phoneNumber),
          );
        }),
      phoneVerification => MaterialPageRoute(builder: (context) {
          return SignupPhoneVerificationScreen(
            argument,
            key: const Key(phoneVerification),
          );
        }),
      biometrics => MaterialPageRoute(builder: (context) {
          return SignupBiometricsScreen(
            argument,
            key: const Key(biometrics),
          );
        }),
      pinSetup => MaterialPageRoute(builder: (context) {
          return SignupPinSetupScreen(
            argument,
            key: const Key(pinSetup),
          );
        }),
      pinConfirm => MaterialPageRoute(builder: (context) {
          return SignupPinSetupScreen(
            argument,
            key: const Key(pinConfirm),
          );
        }),
      notification => MaterialPageRoute(builder: (context) {
          return SignupNotificationScreen(
            argument,
            key: const Key(notification),
          );
        }),
      location => MaterialPageRoute(builder: (context) {
          return SignupLocationScreen(
            argument,
            key: const Key(location),
          );
        }),
      _ => fallbackRoute,
    };
  }
}
