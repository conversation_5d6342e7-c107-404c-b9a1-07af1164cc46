import 'package:day1/day1.dart';

class SignupState extends StateModel {
  final SignupService _service;
  final AppSessionService _sessionService;
  final BiometricAuthService _bioAuthService;
  final AppPermissionService _permissionService;

  SignupState(
    this._service,
    this._sessionService,
    this._bioAuthService,
    this._permissionService,
  );

  SessionCache? get sessionData => _sessionService.sessionData;

  _networkCallDelegate<T>(
    NetworkCallResponse<T> call, {
    required OnChanged<T> onSuccess,
    OnChanged<String>? onError,
  }) async {
    AppLogger.info("SignupState: _networkCallDelegate called");
    AppLogger.info("SignupState: isLoading = $isLoading");
    
    if (isLoading) {
      AppLogger.info("SignupState: already loading, returning early");
      return;
    }

    AppLogger.info("SignupState: setting loading state to true");
    setLoadingState(true);

    AppLogger.info("SignupState: making network call");
    final res = await call;
    AppLogger.info("SignupState: network call completed, hasError = ${res.hasError}");
    
    if (res.hasError) {
      AppLogger.severe("SignupState: network call error: ${res.errorMessage}");
      setLoadingState(false);
      return onError?.call(res.errorMessage!);
    }

    if (res.hasData) {
      AppLogger.info("SignupState: network call success, calling onSuccess");
      onSuccess.call(res.data as T);
    }
  }

  _updateOnboardingState(OnboardingStep step) {
    if (sessionData == null) return;
    final data = sessionData!.copyWith(onboardingState: step.name);
    _sessionService.modifySession(update: data);
  }

  register(
    String email, {
    OnChanged<AuthResponse?>? onSuccess,
    OnChanged<String>? onError,
  }) async {
    AppLogger.info("SignupState: register() called with email: $email");
    await _networkCallDelegate(
      _service.register(EmailParam(email)),
      onError: onError,
      onSuccess: (data) async {
        AppLogger.info("SignupState: register success, creating session");
        await _sessionService.createSession(
          authData: data,
          ephemeral: true,
        );
        AppLogger.info("SignupState: session created, calling onSuccess");
        onSuccess?.call(data);
        setLoadingState(false);
      },
    );
  }

  verify(
    VerificationParam param, {
    OnPressed? onSuccess,
    OnChanged<String>? onError,
  }) async {
    await _networkCallDelegate(
      _service.verifyInfo(param),
      onError: onError,
      onSuccess: (data) {
        final step = switch (param.type) {
          VerificationType.email => OnboardingStep.password,
          _ => OnboardingStep.bioAuth,
        };
        _updateOnboardingState(step);
        onSuccess?.call();
        setLoadingState(false);
      },
    );
  }

  resendEmailOtp(String email, {OnChanged<AuthResponse?>? onSuccess}) async {
    await _networkCallDelegate(
      _service.resendEmailOtp(EmailParam(email)),
      onError: (value) {
        onSuccess?.call(null);
      },
      onSuccess: (data) async {
        onSuccess?.call(data);
        setLoadingState(false);
      },
    );
  }

  resendPhoneOtp(
    PhoneParam param, {
    OnChanged<AuthResponse?>? onSuccess,
  }) async {
    await _networkCallDelegate(
      _service.resendPhoneOtp(param),
      onSuccess: (data) async {
        onSuccess?.call(data);
        setLoadingState(false);
      },
    );
  }

  addPhone(
    PhoneParam param, {
    OnPressed? onSuccess,
    OnChanged<String>? onError,
  }) async {
    await _networkCallDelegate(
      _service.addPhoneNumber(param),
      onSuccess: (data) {
        _updateOnboardingState(OnboardingStep.phoneVerification);
        onSuccess?.call();
        setLoadingState(false);
      },
      onError: onError,
    );
  }

  addPassword(
    PasswordParam param, {
    OnPressed? onSuccess,
    OnChanged<String>? onError,
  }) async {
    await _networkCallDelegate(
      _service.createPassword(param),
      onSuccess: (data) {
        _updateOnboardingState(OnboardingStep.phone);
        onSuccess?.call();
        setLoadingState(false);
      },
      onError: onError,
    );
  }

  enableBiometrics({required OnChanged<bool> onDone}) async {
    final enabled = await _bioAuthService.authenticate(
      title: LocaleKeys.day1NeedBioAccess.tr(),
    );
    onDone(enabled);
  }

  enablePermission(
    Permissions permission, {
    required OnChanged<bool> onDone,
  }) async {
    final enabled = await _permissionService.requestPermission(permission);
    onDone(enabled);
  }

  activateBiometrics({
    OnChanged<AuthResponse?>? onSuccess,
    OnChanged<String>? onError,
  }) async {
    await _networkCallDelegate(
      _service.enableBioAuth(),
      onSuccess: (data) async {
        _updateOnboardingState(OnboardingStep.pin);
        await _sessionService.attachBioAuthData(authData: data);
        onSuccess?.call(data);
        setLoadingState(false);
      },
      onError: onError,
    );
  }

  createPin(
    PinParam param, {
    OnPressed? onSuccess,
    OnChanged<String>? onError,
  }) async {
    await _networkCallDelegate(
      _service.addPin(param),
      onSuccess: (data) async {
        await _sessionService.modifySession(
          update: sessionData?.copyWith(
            onboardingState: OnboardingStep.notification.tag,
            hasEnabledPinAuth: true,
          ),
        );
        onSuccess?.call();
        setLoadingState(false);
      },
      onError: onError,
    );
  }
}
