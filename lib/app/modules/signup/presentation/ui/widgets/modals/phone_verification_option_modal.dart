import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class PhoneVerificationOptionModal extends StatelessWidget {
  final OnChanged<OtpChannel> onSelect;
  const PhoneVerificationOptionModal({super.key, required this.onSelect});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: context.insets.defaultCardInsets,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          AppText(
            LocaleKeys.chooseHowToReceiveOtp.tr(),
            style: context.textStyle.h6(
              color: context.secondaryTextColor,
            ),
          ),
          const AppGap.y24(),
          AppOutlineButton(
            text: LocaleKeys.sms.tr(),
            icon: const AppIcon(AppIcons.messageText),
            onPressed: () {
              AppRouter.popView();
              onSelect(OtpChannel.sms);
            },
          ),
          const AppGap.y16(),
          AppOutlineButton(
            text: LocaleKeys.whatsApp.tr(),
            icon: const AppIcon(AppIcons.chatBubbleCheck),
            onPressed: () {
              AppRouter.popView();
              onSelect(OtpChannel.whatsapp);
            },
          ),
        ],
      ),
    );
  }
}
