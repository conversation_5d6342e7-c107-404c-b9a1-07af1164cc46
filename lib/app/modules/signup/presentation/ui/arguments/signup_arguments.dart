import 'package:day1/day1.dart';

class SignupArguments<T> {
  final AuthResponse? authData;
  final String? pin;
  final OtpChannel? channel;
  final bool isOnboardingSession;
  final OnChanged<T>? onChanged;

  const SignupArguments({
    this.authData,
    this.isOnboardingSession = true,
    this.pin,
    this.channel,
    this.onChanged,
  });

  SignupArguments<T> copyWith({AuthResponse? authData, String? pin}) {
    return SignupArguments(
      isOnboardingSession: isOnboardingSession,
      pin: pin,
      authData: authData,
      onChanged: onChanged,
    );
  }
}
