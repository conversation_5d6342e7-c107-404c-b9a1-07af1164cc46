import 'package:flutter/material.dart';
import 'package:day1/day1.dart';

mixin SignupPhoneNumberMixin<T extends SignupPhoneNumberScreen> on State<T> {
  late final ValueNotifier<bool> formStateEmitter;

  ///Key
  final GlobalKey<FormState> formKey = GlobalKey<FormState>();

  //Controller
  late final TextEditingController telCtrl;
  late final ValueNotifier<SelectionData<Country>?> telCodeCtrl;

  final FocusNode passFocus = FocusNode();
  final FocusNode codeFocus = FocusNode();

  SignupArguments? get argument => widget.arguments;
  AuthResponse? get authData => argument?.authData;

  @override
  void initState() {
    super.initState();
    telCtrl = TextEditingController();
    telCodeCtrl = ValueNotifier(null);

    _trackValidity();

    formStateEmitter = ValueNotifier(_formValidityStatus());
  }

  @override
  void dispose() {
    telCtrl.dispose();
    telCodeCtrl.dispose();
    formStateEmitter.dispose();
    super.dispose();
  }

  _trackValidity() {
    telCtrl.addListener(() {
      formStateEmitter.value = _formValidityStatus();
    });
    telCodeCtrl.addListener(() {
      formStateEmitter.value = _formValidityStatus();
    });
  }

  bool _formValidityStatus() {
    final telIsValid = AppValidators.required(telCtrl.text) == null;
    final codeIsValid = telCodeCtrl.value != null;
    return telIsValid && codeIsValid;
  }

  submit() async {
    if (!context.validateForm(formKey)) return;
    final state = locator<SignupState>();
    final phoneNumber = TelephoneNumber(
      countryCode: telCodeCtrl.value?.label,
      number: telCtrl.text,
    );

    widget.showPhoneVerificationOptionModal(context, onSelect: (channel) {
      state.addPhone(
        PhoneParam(phoneNumber: phoneNumber.combined.value, channel: channel),
        onError: context.showErrorNotification,
        onSuccess: () {
          AppRouter.pushNamed(
            SignupRoutes.phoneVerification,
            arguments: SignupArguments(
              authData: authData?.copyWith(
                user: authData?.user?.copyWith(
                  phoneNumber: phoneNumber.combined,
                ),
              ),
              channel: channel,
            ),
          );
        },
      );
    });
  }
}
