import 'package:flutter/material.dart';
import 'package:day1/day1.dart';

mixin SignupEmailMixin<T extends SignupEmailScreen> on State<T> {
  late final ValueNotifier<bool> formStateEmitter;

  ///Key
  final GlobalKey<FormState> formKey = GlobalKey<FormState>();

  //Controller
  late final TextEditingController emailCtrl;

  SignupArguments? get argument => widget.arguments;
  AuthResponse? get authData => argument?.authData;

  @override
  void initState() {
    super.initState();
    emailCtrl = TextEditingController();

    _trackValidity();

    formStateEmitter = ValueNotifier(_formValidityStatus());
  }

  @override
  void dispose() {
    emailCtrl.dispose();
    formStateEmitter.dispose();
    super.dispose();
  }

  _trackValidity() {
    emailCtrl.addListener(() {
      formStateEmitter.value = _formValidityStatus();
    });
  }

  bool _formValidityStatus() {
    return AppValidators.emailValidator(emailCtrl.text) == null;
  }

  submit() async {
    AppLogger.info("SignupEmailMixin: submit() called");
    AppLogger.info("SignupEmailMixin: email = ${emailCtrl.text}");
    
    if (!context.validateForm(formKey)) {
      AppLogger.info("SignupEmailMixin: form validation failed");
      return;
    }
    
    AppLogger.info("SignupEmailMixin: form validation passed");
    final state = locator<SignupState>();
    AppLogger.info("SignupEmailMixin: calling state.register()");

    state.register(
      emailCtrl.text,
      onError: (error) {
        AppLogger.severe("SignupEmailMixin: registration error: $error");
        context.showErrorNotification(error);
      },
      onSuccess: (value) {
        AppLogger.info("SignupEmailMixin: registration success: $value");
        AppRouter.pushNamed(
          SignupRoutes.emailVerification,
          arguments: SignupArguments(authData: value),
        );
      },
    );
  }
}

mixin SignupEmailVerificationMixin<T extends SignupEmailVerificationScreen>
    on State<T> {
  late final ValueNotifier<bool> formStateEmitter;
  late OtpDetails? otpDetails;
  late final ValueNotifier<Stream<int>> countDownStream;
  bool isResendingOtp = false;

  ///Key
  final GlobalKey<FormState> formKey = GlobalKey<FormState>();

  //Controller
  late final TextEditingController otpCtrl;

  final otpFocus = FocusNode();

  SignupArguments? get argument => widget.arguments;
  AuthResponse? get authData => argument?.authData;

  Stream<int> get _countDownDuration {
    return AppHelpers.countDown(60).asBroadcastStream();
  }

  @override
  void initState() {
    super.initState();

    otpCtrl = TextEditingController();
    otpDetails = authData?.otpDetails;
    countDownStream = ValueNotifier(_countDownDuration);

    _trackValidity();

    formStateEmitter = ValueNotifier(_formValidityStatus());

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _showEmailNotification();
    });
  }

  @override
  void dispose() {
    formStateEmitter.dispose();
    otpCtrl.dispose();
    super.dispose();
  }

  _trackValidity() {
    otpCtrl.addListener(() {
      formStateEmitter.value = _formValidityStatus();
    });
  }

  bool _formValidityStatus() {
    return AppValidators.minLength(otpCtrl.text) == null;
  }

  _showEmailNotification() {
    context.showInfoNotification(
      LocaleKeys.verificationEmailSent.tr(),
      title: LocaleKeys.verifyEmail.tr(),
      duration: 10000,
    );
  }

  resendOtp() {
    if (isResendingOtp) return false;

    otpCtrl.clear();
    isResendingOtp = true;
    final state = locator<SignupState>();

    _showEmailNotification();
    state.resendEmailOtp(authData?.email ?? "", onSuccess: (data) {
      context.showNotification(LocaleKeys.otpResent.tr());
      otpDetails = data?.otpDetails ?? otpDetails;
      countDownStream.value = _countDownDuration;
      isResendingOtp = false;
    });
  }

  submit() async {
    if (!context.validateForm(formKey)) return;
    final state = locator<SignupState>();

    state.verify(
      VerificationParam(
        data: authData?.email ?? "",
        otp: otpCtrl.text,
        otpId: otpDetails?.id ?? "",
        type: VerificationType.email,
      ),
      onError: context.showErrorNotification,
      onSuccess: () {
        AppRouter.pushNamed(
          SignupRoutes.password,
          arguments: SignupArguments(
            authData: authData?.copyWith(
              user: authData?.user?.copyWith(
                emailVerifiedAt: DateTime.now().toIso8601String(),
              ),
            ),
          ),
        );
      },
    );
  }
}
