import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

mixin SignupModalMixin {
  showPhoneVerificationOptionModal(
    BuildContext context, {
    required OnChanged<OtpChannel> onSelect,
  }) {
    AppBottomModal(
      ctx: context,
      isDismissable: false,
      modalWidget: PhoneVerificationOptionModal(
        onSelect: onSelect,
        key: const Key("phone-verification-destination-modal"),
      ),
    );
  }
}
