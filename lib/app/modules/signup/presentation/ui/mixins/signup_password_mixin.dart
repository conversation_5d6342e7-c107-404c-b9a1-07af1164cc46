import 'package:flutter/material.dart';
import 'package:day1/day1.dart';

mixin SignupPasswordMixin<T extends SignupPasswordScreen> on State<T> {
  late final ValueNotifier<bool> formStateEmitter;

  ///Key
  final GlobalKey<FormState> formKey = GlobalKey<FormState>();

  //Controller
  late final TextEditingController passwordCtrl;
  late final TextEditingController passwordConfirmCtrl;

  final FocusNode passFocus = FocusNode();
  final FocusNode confirmFocus = FocusNode();

  SignupArguments? get argument => widget.arguments;
  AuthResponse? get authData => argument?.authData;

  @override
  void initState() {
    super.initState();
    passwordCtrl = TextEditingController();
    passwordConfirmCtrl = TextEditingController();

    _trackValidity();

    formStateEmitter = ValueNotifier(_formValidityStatus());
  }

  @override
  void dispose() {
    passwordCtrl.dispose();
    passwordConfirmCtrl.dispose();
    formStateEmitter.dispose();
    super.dispose();
  }

  _trackValidity() {
    passwordCtrl.addListener(() {
      formStateEmitter.value = _formValidityStatus();
    });
    passwordConfirmCtrl.addListener(() {
      formStateEmitter.value = _formValidityStatus();
    });
  }

  bool _formValidityStatus() {
    final passwordIsValid =
        AppValidators.passwordValidator(passwordCtrl.text) == null;
    final passwordConfirmIsValid =
        AppValidators.passwordValidator(passwordConfirmCtrl.text) == null;
    return passwordIsValid && passwordConfirmIsValid;
  }

  submit() {
    if (!context.validateForm(formKey)) return;
    final state = locator<SignupState>();
    state.addPassword(
      PasswordParam(
        password: passwordCtrl.text,
        confirmPassword: passwordConfirmCtrl.text,
      ),
      onError: context.showErrorNotification,
      onSuccess: () {
        AppRouter.pushNamed(
          SignupRoutes.phoneNumber,
          arguments: SignupArguments(
            authData: authData?.copyWith(
              user: authData?.user?.copyWith(
                passwordSetAt: DateTime.now().toIso8601String(),
              ),
            ),
          ),
        );
      },
    );
  }
}
