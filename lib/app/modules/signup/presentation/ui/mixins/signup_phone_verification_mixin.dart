import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

mixin SignupPhoneVerificationMixin<T extends SignupPhoneVerificationScreen>
    on State<T> {
  late final ValueNotifier<bool> formStateEmitter;
  late OtpDetails? otpDetails;
  late final ValueNotifier<Stream<int>> countDownStream;
  bool isResendingOtp = false;

  ///Key
  final GlobalKey<FormState> formKey = GlobalKey<FormState>();

  //Controller
  late final TextEditingController otpCtrl;

  final otpFocus = FocusNode();

  SignupArguments? get argument => widget.arguments;
  AuthResponse? get authData => argument?.authData;

  @override
  void initState() {
    super.initState();

    otpCtrl = TextEditingController();
    otpDetails = authData?.otpDetails;
    countDownStream = ValueNotifier(_countDownDuration);

    _trackValidity();

    formStateEmitter = ValueNotifier(_formValidityStatus());

    if (argument?.channel == null) sendOtp();
  }

  @override
  void dispose() {
    formStateEmitter.dispose();
    otpCtrl.dispose();
    super.dispose();
  }

  Stream<int> get _countDownDuration {
    return AppHelpers.countDown(60).asBroadcastStream();
  }

  _trackValidity() {
    otpCtrl.addListener(() {
      formStateEmitter.value = _formValidityStatus();
    });
  }

  bool _formValidityStatus() {
    return AppValidators.minLength(otpCtrl.text) == null;
  }

  sendOtp() {
    if (isResendingOtp) return false;

    otpCtrl.clear();
    isResendingOtp = true;
    final state = locator<SignupState>();

    context.showInfoNotification(LocaleKeys.resendingOtp.tr());
    widget.showPhoneVerificationOptionModal(context, onSelect: (channel) {
      state.resendPhoneOtp(
          PhoneParam(
            phoneNumber: authData?.phoneNumber ?? "",
            channel: channel,
          ), onSuccess: (data) {
        context.showNotification(LocaleKeys.otpResent.tr());
        otpDetails = data?.otpDetails ?? otpDetails;
        countDownStream.value = _countDownDuration;
        isResendingOtp = false;
      });
    });
  }

  submit() async {
    if (!context.validateForm(formKey)) return;
    final state = locator<SignupState>();

    state.verify(
      VerificationParam(
        data: authData?.phoneNumber ?? "",
        otp: otpCtrl.text,
        type: VerificationType.phone,
      ),
      onError: context.showErrorNotification,
      onSuccess: () {
        AppRouter.pushNamed(
          SignupRoutes.biometrics,
          arguments: SignupArguments(
            authData: authData?.copyWith(
              user: authData?.user?.copyWith(
                phoneVerifiedAt: DateTime.now().toIso8601String(),
              ),
            ),
          ),
        );
      },
    );
  }
}
