import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

mixin SignupPinSetupMixin<T extends SignupPinSetupScreen> on State<T> {
  late final ValueNotifier<bool> formStateEmitter;

  ///Key
  final GlobalKey<FormState> formKey = GlobalKey<FormState>();

  //Controller
  late final TextEditingController otpCtrl;

  final otpFocus = FocusNode();

  bool get hasPin => widget.pin.hasValue;

  String get title {
    return hasPin ? LocaleKeys.confirmPin : LocaleKeys.createDay1Pin;
  }

  String? get rider {
    return hasPin ? null : LocaleKeys.enter4DigitPin4Transfer;
  }

  @override
  void initState() {
    super.initState();

    otpCtrl = TextEditingController();

    _trackValidity();

    formStateEmitter = ValueNotifier(_formValidityStatus());
  }

  @override
  void dispose() {
    formStateEmitter.dispose();
    otpCtrl.dispose();
    super.dispose();
  }

  _trackValidity() {
    otpCtrl.addListener(() {
      formStateEmitter.value = _formValidityStatus();
    });
  }

  bool _formValidityStatus() {
    return AppValidators.minLength(otpCtrl.text, length: 4) == null;
  }

  submit() async {
    if (!context.validateForm(formKey, notifyOnFailure: !hasPin)) return;

    if (!hasPin) {
      return AppRouter.pushNamed(
        SignupRoutes.pinConfirm,
        arguments: widget.argument.copyWith(pin: otpCtrl.text),
      );
    }

    final state = locator<SignupState>();

    state.createPin(
      PinParam(
        pin: widget.pin.value,
        confirmPin: otpCtrl.text,
      ),
      onError: context.showErrorNotification,
      onSuccess: () {
        AppRouter.pushNamed(
          SignupRoutes.notification,
          arguments: widget.argument,
        );
      },
    );
  }
}
