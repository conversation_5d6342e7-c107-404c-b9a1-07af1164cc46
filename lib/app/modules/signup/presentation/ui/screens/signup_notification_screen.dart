import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class SignupNotificationScreen extends AppStatelessWidget {
  final SignupArguments? arguments;

  const SignupNotificationScreen(this.arguments, {super.key});

  _goToLocationScreen() {
    AppRouter.pushNamed(
      SignupRoutes.location,
      arguments: arguments,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: DoAppBar(title: LocaleKeys.notification.tr()),
      body: ListView(
        padding: context.insets.defaultHorizontalInsets,
        children: [
          AppLocalStateWrapper<SignupState>(
            builder: (state) {
              return AppFeatureCard(
                icon: const AppIcon(
                  AppIcons.bell,
                  alignment: Alignment.centerLeft,
                  size: AppFontSizes.px36,
                ),
                title: LocaleKeys.enableNotifications.tr(),
                onEnable: () {
                  state.enablePermission(
                    Permissions.notification,
                    onDone: (enabled) {
                      if (enabled) _goToLocationScreen();
                    },
                  );
                },
                onSkip: _goToLocationScreen,
                decription: LocaleKeys.keepYourFinHealthNSecurityInCheck.tr(),
                confirmTitle: LocaleKeys.day1NeedsNotificationAccess.tr(),
                confirmSubtitle: LocaleKeys.day1NotificationAccessReason.tr(),
              );
            },
          ),
        ],
      ),
    );
  }
}
