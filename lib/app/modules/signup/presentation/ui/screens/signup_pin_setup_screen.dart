import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class SignupPinSetupScreen extends AppStatefulWidget {
  final SignupArguments argument;

  String? get pin => argument.pin;

  const SignupPinSetupScreen(this.argument, {super.key});

  @override
  State<SignupPinSetupScreen> createState() => _SignupPinSetupScreenState();
}

class _SignupPinSetupScreenState extends State<SignupPinSetupScreen>
    with SignupPinSetupMixin {
  @override
  Widget build(BuildContext context) {
    const p = AppFontSizes.px32;

    return Scaffold(
      appBar: DoAppBar(title: LocaleKeys.setPin.tr()),
      body: AppLocalStateWrapper<SignupState>(builder: (state) {
        return AppForm(
          formKey: formKey,
          child: ListView(
            padding: context.insets.defaultAllInsets,
            children: [
              AppCard(
                padding: context.insets.fromLTRBSp(p, p, p, p / 2),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    AppFormHeader(
                      title: title.tr(),
                      subTitle: rider?.tr(),
                    ),
                    IgnorePointer(
                      child: AppPinInput(
                        length: 4,
                        obscureText: false,
                        isEnabled: !state.isLoading,
                        controller: otpCtrl,
                        fieldSize: const Size(70, 88),
                        onFieldSubmitted: (value) => submit,
                        validator: (text) {
                          if (!hasPin) return null;
                          return AppValidators.matchValidator(
                            text,
                            widget.pin,
                            message: LocaleKeys.pinDontMatch.tr(),
                          );
                        },
                      ),
                    )
                  ],
                ),
              ),
              const AppGap.y12(),
              AppCard(child: AppKeyPad(controller: otpCtrl, limit: 4)),
              const AppGap.y12(),
              BoolListener(
                valueListenable: formStateEmitter,
                builder: (isValid) {
                  return Padding(
                    padding: context.insets.symmetricSp(horizontal: p),
                    child: AppButton(
                      text: LocaleKeys.done.tr(),
                      onPressed: submit,
                      isDisabled: !isValid || state.isLoading,
                      isLoading: state.isLoading,
                    ),
                  );
                },
              )
            ],
          ),
        );
      }),
    );
  }
}
