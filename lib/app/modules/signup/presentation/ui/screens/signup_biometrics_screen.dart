import 'dart:io';

import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class SignupBiometricsScreen extends AppStatelessWidget {
  final SignupArguments? arguments;

  const SignupBiometricsScreen(this.arguments, {super.key});

  _goToPinScreen(bool proceed) {
    if (!proceed) return;
    AppRouter.pushNamed(SignupRoutes.pinSetup, arguments: arguments);
  }

  _goToNotificationScreen() {
    AppRouter.pushNamed(SignupRoutes.notification, arguments: arguments);
  }

  String get title {
    if (Platform.isIOS) {
      return LocaleKeys.enableFaceId.tr();
    }
    return LocaleKeys.enableBiometricLogin.tr();
  }

  IconData get icon {
    if (Platform.isIOS) return AppIcons.faceId;
    return AppIcons.fingerprint;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const DoAppBar(),
      body: ListView(
        padding: context.insets.defaultHorizontalInsets,
        children: [
          AppLocalStateWrapper<SignupState>(
            builder: (state) {
              return AppFeatureCard(
                icon: AppIcon(
                  icon,
                  alignment: Alignment.centerLeft,
                  size: AppFontSizes.px36,
                ),
                title: title,
                isLoading: state.isLoading,
                decription: LocaleKeys.makeLoginFastWithBio.tr(),
                onEnable: () {
                  state.enableBiometrics(onDone: (enabled) {
                    if (!enabled) return;
                    state.activateBiometrics(
                      onError: context.showErrorNotification,
                      onSuccess: (value) {
                        _goToPinScreen(enabled);
                      },
                    );
                  });
                },
                onSkip: _goToNotificationScreen,
              );
            },
          )
        ],
      ),
    );
  }
}
