import 'package:flutter/material.dart';
import 'package:day1/day1.dart';

class SignupPasswordScreen extends AppStatefulWidget {
  final SignupArguments? arguments;

  const SignupPasswordScreen(this.arguments, {super.key});

  @override
  State<SignupPasswordScreen> createState() => _SignupPasswordScreenState();
}

class _SignupPasswordScreenState extends State<SignupPasswordScreen>
    with SignupPasswordMixin {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      appBar: const DoAppBar(),
      body: AppLocalStateWrapper<SignupState>(
        builder: (state) {
          return AppForm(
            formKey: formKey,
            child: AppCard(
              margin: context.insets.defaultAllInsets.add(
                context.insets.onlySp(bottom: AppFontSizes.px32),
              ),
              padding: context.insets.zero,
              child: ListView(
                padding: context.insets.defaultCardInsets,
                children: [
                  AppFormHeader(title: LocaleKeys.createYourPassword.tr()),
                  FormLabel(label: LocaleKeys.choosePassword.tr()),
                  AppPassword<PERSON>ield(
                    controller: passwordCtrl,
                    hintText: LocaleKeys.enterPassword.tr(),
                    focusNode: passFocus,
                    autoFocus: true,
                  ),
                  const AppGap.y24(),
                  FormLabel(label: LocaleKeys.confirmPassword.tr()),
                  AppPasswordField(
                    controller: passwordConfirmCtrl,
                    hintText: LocaleKeys.reEnterPassword.tr(),
                    focusNode: confirmFocus,
                    validator: (value) => AppValidators.matchValidator(
                      value,
                      passwordCtrl.text,
                      message: LocaleKeys.passwordsDoNotMatch.tr(),
                    ),
                    action: TextInputAction.done,
                  ),
                  const AppGap.y24(),
                  PasswordRequirementsList(passwordCtrl),
                  const AppGap.y80(),
                  BoolListener(
                    valueListenable: formStateEmitter,
                    builder: (isValid) {
                      return AppButton(
                        text: LocaleKeys.continueText.tr(),
                        isDisabled: !isValid || state.isLoading,
                        isLoading: state.isLoading,
                        onPressed: submit,
                      );
                    },
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
