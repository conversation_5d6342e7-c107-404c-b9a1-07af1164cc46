import 'package:flutter/material.dart';
import 'package:day1/day1.dart';

class SignupPhoneNumberScreen extends AppStatefulWidget with SignupModalMixin {
  final SignupArguments? arguments;

  const SignupPhoneNumberScreen(this.arguments, {super.key});

  @override
  State<SignupPhoneNumberScreen> createState() =>
      _SignupPhoneNumberScreenState();
}

class _SignupPhoneNumberScreenState extends State<SignupPhoneNumberScreen>
    with SignupPhoneNumberMixin {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      appBar: DoAppBar(title: LocaleKeys.confrimSignUp.tr()),
      body: AppLocalStateWrapper<SignupState>(builder: (state) {
        return AppForm(
          formKey: formKey,
          child: AppCard(
            margin: context.insets.defaultAllInsets.add(
              context.insets.onlySp(bottom: AppFontSizes.px32),
            ),
            padding: context.insets.zero,
            child: ListView(
              padding: context.insets.defaultCardInsets,
              children: [
                AppFormHeader(
                  title: LocaleKeys.whatsYourNumber.tr(),
                  subTitle: LocaleKeys.weWillSendYouACode.tr(),
                ),
                FormLabel(label: LocaleKeys.yourMobileNumber.tr()),
                AppPhoneField(
                  controller: telCtrl,
                  countryCode: telCodeCtrl,
                  focusNode: passFocus,
                  dialFocusNode: codeFocus,
                ),
                const AppGap.y64(),
                BoolListener(
                  valueListenable: formStateEmitter,
                  builder: (isValid) {
                    return AppButton(
                      text: LocaleKeys.sendCode.tr(),
                      isDisabled: !isValid || state.isLoading,
                      isLoading: state.isLoading,
                      onPressed: submit,
                    );
                  },
                ),
              ],
            ),
          ),
        );
      }),
    );
  }
}
