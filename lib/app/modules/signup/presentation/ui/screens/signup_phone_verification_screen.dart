import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class SignupPhoneVerificationScreen extends AppStatefulWidget
    with SignupModalMixin {
  final SignupArguments? arguments;

  const SignupPhoneVerificationScreen(this.arguments, {super.key});

  @override
  State<SignupPhoneVerificationScreen> createState() =>
      _SignupPhoneVerificationScreenState();
}

class _SignupPhoneVerificationScreenState
    extends State<SignupPhoneVerificationScreen>
    with SignupPhoneVerificationMixin {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      appBar: DoAppBar(title: LocaleKeys.confrimSignUp.tr()),
      body: AppLocalStateWrapper<SignupState>(
        builder: (state) {
          return AppForm(
            formKey: formKey,
            child: AppCard(
              margin: context.insets.defaultAllInsets.add(
                context.insets.onlySp(bottom: AppFontSizes.px16),
              ),
              padding: context.insets.zero,
              child: ListView(
                padding: context.insets.defaultCardInsets,
                children: [
                  AppFormHeader(
                    title: LocaleKeys.verifyTelephone.tr(),
                    subTitle: LocaleKeys.weveSentOtp
                        .tr({"tel": authData?.phoneNumber?.lastChars ?? ""}),
                  ),
                  AppPinInput(
                    key: const Key("otp_input"),
                    autoFocus: true,
                    focusNode: otpFocus,
                    length: 6,
                    isEnabled: !state.isLoading,
                    controller: otpCtrl,
                    obscureText: true,
                    keyboardType: TextInputType.number,
                    onFieldSubmitted: (_) => submit(),
                  ),
                  CountDownListener(countDownStream, onResend: sendOtp),
                  BoolListener(
                    valueListenable: formStateEmitter,
                    builder: (isValid) {
                      return AppButton(
                        text: LocaleKeys.verifyAccount.tr(),
                        isDisabled: !isValid || state.isLoading,
                        isLoading: state.isLoading,
                        onPressed: submit,
                      );
                    },
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
