import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class SignupEmailVerificationScreen extends AppStatefulWidget {
  final SignupArguments? arguments;

  const SignupEmailVerificationScreen(this.arguments, {super.key});

  @override
  State<SignupEmailVerificationScreen> createState() =>
      _SignupEmailVerificationScreenState();
}

class _SignupEmailVerificationScreenState
    extends State<SignupEmailVerificationScreen>
    with SignupEmailVerificationMixin {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      appBar: DoAppBar(title: LocaleKeys.confrimSignUp.tr()),
      body: AppLocalStateWrapper<SignupState>(
        builder: (state) {
          return AppForm(
            formKey: formKey,
            child: AppCard(
              margin: context.insets.defaultAllInsets.add(
                context.insets.onlySp(bottom: AppFontSizes.px16),
              ),
              padding: context.insets.zero,
              child: ListView(
                padding: context.insets.defaultCardInsets,
                children: [
                  AppFormHeader(
                    title: LocaleKeys.confirmEmailAddress.tr(),
                    subTitle: LocaleKeys.weHaveSentOtpToYourEmail
                        .tr({"email": authData?.email?.obscuredEmail ?? ""}),
                  ),
                  AppPinInput(
                    key: const Key("otp_input"),
                    autoFocus: true,
                    focusNode: otpFocus,
                    length: 6,
                    controller: otpCtrl,
                    isEnabled: !state.isLoading,
                    obscureText: true,
                    keyboardType: TextInputType.number,
                    onFieldSubmitted: (_) => submit(),
                  ),
                  CountDownListener(countDownStream, onResend: resendOtp),
                  BoolListener(
                    valueListenable: formStateEmitter,
                    builder: (isValid) {
                      return AppButton(
                        text: LocaleKeys.verifyAccount.tr(),
                        isDisabled: !isValid || state.isLoading,
                        isLoading: state.isLoading,
                        onPressed: submit,
                      );
                    },
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
