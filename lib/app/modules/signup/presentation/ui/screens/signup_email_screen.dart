import 'package:flutter/material.dart';
import 'package:day1/day1.dart';

class SignupEmailScreen extends AppStatefulWidget {
  final SignupArguments? arguments;

  const SignupEmailScreen(this.arguments, {super.key});

  @override
  State<SignupEmailScreen> createState() => _SignupEmailScreenState();
}

class _SignupEmailScreenState extends State<SignupEmailScreen>
    with SignupEmailMixin {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      appBar: const DoAppBar(),
      body: AppLocalStateWrapper<SignupState>(
        builder: (state) {
          return AppForm(
            formKey: formKey,
            child: AppCard(
              margin: context.insets.defaultAllInsets.add(
                context.insets.onlySp(
                  bottom: AppFontSizes.px16,
                ),
              ),
              padding: context.insets.zero,
              child: ListView(
                padding: context.insets.defaultCardInsets,
                children: [
                  App<PERSON>orm<PERSON>eader(
                    title: LocaleKeys.welcomeToD1.tr(),
                    subTitle: LocaleKeys.createD1Account.tr(),
                  ),
                  FormLabel(label: LocaleKeys.enterYourEmail.tr()),
                  AppEmailField(
                    controller: emailCtrl,
                    action: TextInputAction.done,
                    isEnabled: !state.isLoading,
                    onFieldSubmitted: (_) => submit(),
                    autoFocus: true,
                  ),
                  SizedBox(height: context.sp(AppFontSizes.px64)),
                  BoolListener(
                    valueListenable: formStateEmitter,
                    builder: (isValid) {
                      return AppButton(
                        text: LocaleKeys.continueText.tr(),
                        isDisabled: !isValid || state.isLoading,
                        onPressed: submit,
                        isLoading: state.isLoading,
                      );
                    },
                  ),
                  const AppGap.y16(),
                  TermsAndConditionTextWidget(
                    termsOfUseText: LocaleKeys.agreeToTermsByContinuing.tr(),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
