import 'package:day1/day1.dart';
import 'package:flutter/material.dart';

class SignupLocationScreen extends AppStatelessWidget {
  final SignupArguments? arguments;

  const SignupLocationScreen(this.arguments, {super.key});

  _goToLoginScreen() {
    AppRouter.pushNamed(AuthRoutes.login);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: DoAppBar(title: LocaleKeys.location.tr()),
      body: ListView(
        padding: context.insets.defaultHorizontalInsets,
        children: [
          AppLocalStateWrapper<SignupState>(
            builder: (state) {
              return AppFeatureCard(
                icon: const AppIcon(
                  AppIcons.mapPin,
                  alignment: Alignment.centerLeft,
                  size: AppFontSizes.px36,
                ),
                title: LocaleKeys.enableLocation.tr(),
                decription: LocaleKeys.shareYourLocation.tr(),
                onEnable: () {
                  state.enablePermission(
                    Permissions.location,
                    onDone: (enabled) {
                      if (enabled) _goToLoginScreen();
                    },
                  );
                },
                onSkip: _goToLoginScreen,
                confirmTitle: LocaleKeys.day1NeedsLocationAccess.tr(),
                confirmSubtitle: LocaleKeys.day1LocationAccessReason.tr(),
              );
            },
          ),
        ],
      ),
    );
  }
}
