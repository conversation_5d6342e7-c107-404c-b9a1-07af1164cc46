import 'package:day1/day1.dart';

void registerSignupDI(DoAppConfig config) {
  ///[Services]
  locator.registerFactory<SignupService>(() {
    return SignupHttpService(locator());
  });

  ///[State]
  locator.registerLazySingleton<SignupState>(() {
    return SignupState(
      locator(),
      locator(),
      locator(),
      locator(),
    );
  });
}

void resetSignupDI() {
  locator<SignupState>().reset();
}
