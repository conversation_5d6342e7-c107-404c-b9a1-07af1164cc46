import 'package:day1/day1.dart';

class EmailParam implements MapCodable {
  final String email;

  const EmailParam(this.email);

  @override
  toJson() => {"email": email.trim()};
}

class PhoneParam implements MapCodable {
  final String phoneNumber;
  final OtpChannel channel;

  const PhoneParam({required this.phoneNumber, required this.channel});

  @override
  toJson() {
    return {"phoneNumber": phoneNumber, "channel": "$channel"};
  }
}

enum VerificationType {
  email("email", "email"),
  phone("phone", 'phoneNumber');

  final String name;
  final String key;

  const VerificationType(this.name, this.key);
}

class VerificationParam implements MapCodable {
  final VerificationType type;
  final String data;
  final String otp;
  final String? otpId;

  const VerificationParam({
    required this.data,
    required this.otp,
    this.otpId,
    required this.type,
  });

  @override
  toJson() {
    final map = {
      "otpId": otpId?.trim(),
      "otp": otp.trim(),
      type.key: data.trim()
    };
    map.removeWhere((k, v) => !v.hasValue);
    return map;
  }
}

class PasswordParam implements MapCodable {
  final String password;
  final String confirmPassword;

  const PasswordParam({required this.password, required this.confirmPassword});

  @override
  toJson() {
    return {
      "password": password.trim(),
      "confirmPassword": confirmPassword.trim(),
    };
  }
}

class PinParam implements MapCodable {
  final String pin;
  final String confirmPin;

  const PinParam({required this.pin, required this.confirmPin});

  @override
  toJson() {
    return {
      "pin": pin.trim(),
      "confirmPin": confirmPin.trim(),
    };
  }
}
