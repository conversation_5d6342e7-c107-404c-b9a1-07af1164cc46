import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:day1/day1.dart';

class DoApp extends StatefulWidget {
  final DoAppConfig config;
  final RootRouteRegistry routes;

  const DoApp(this.config, this.routes, {super.key});

  @override
  State<StatefulWidget> createState() => _DoAppState();
}

class _DoAppState extends State<DoApp> with WidgetsBindingObserver {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    context.observeTheme();
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      AppTextFormatter.locale = context.locale;
    });
    return MaterialApp(
      key: ValueKey(widget.config.appId),
      restorationScopeId: "day1_restoration_bucket",
      theme: context.activeTheme,
      themeMode: ThemeMode.system,
      themeAnimationCurve: Curves.decelerate,
      title: widget.config.appName,
      debugShowCheckedModeBanner: false,
      routes: widget.routes.staticRoutes,
      onGenerateRoute: widget.routes.dynamicRoutes,
      onGenerateInitialRoutes: widget.routes.initialRoutes,
      navigatorKey: AppRouter.navigatorKey,
      localizationsDelegates: context.localizationDelegates,
      supportedLocales: context.supportedLocales,
      locale: context.locale,
      navigatorObservers: [
        AppRouteObserver(),
      ],
      builder: (context, page) => BaseWidget(
        key: ValueKey(page),
        child: page,
      ),
    );
  }
}
