// GENERATED CODE - DO NOT MODIFY BY HAND
// This code was generated by ObjectBox. To update it run the generator again
// with `dart run build_runner build`.
// See also https://docs.objectbox.io/getting-started#generate-objectbox-code

// ignore_for_file: camel_case_types, depend_on_referenced_packages
// coverage:ignore-file

import 'dart:typed_data';

import 'package:flat_buffers/flat_buffers.dart' as fb;
import 'package:objectbox/internal.dart'
    as obx_int; // generated code can access "internal" functionality
import 'package:objectbox/objectbox.dart' as obx;
import 'package:objectbox_flutter_libs/objectbox_flutter_libs.dart';

import 'app/common/services/storage/schemas/cache_schema/cache.dart';
import 'app/common/services/storage/schemas/session_schema/session.dart';
import 'app/common/services/storage/schemas/theme_schema/theme.dart';
import 'app/common/services/storage/schemas/user_schema/user.dart';

export 'package:objectbox/objectbox.dart'; // so that callers only have to import this file

final _entities = <obx_int.ModelEntity>[
  obx_int.ModelEntity(
      id: const obx_int.IdUid(1, 3712845317549324236),
      name: 'AppSelectionResponseCache',
      lastPropertyId: const obx_int.IdUid(4, 1572241121151063295),
      flags: 0,
      properties: <obx_int.ModelProperty>[
        obx_int.ModelProperty(
            id: const obx_int.IdUid(1, 2597724213302654321),
            name: 'id',
            type: 9,
            flags: 2048,
            indexId: const obx_int.IdUid(1, 4475497802173528987)),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(2, 8803209673364266084),
            name: 'index',
            type: 6,
            flags: 1),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(3, 4946487426231902629),
            name: 'isActive',
            type: 1,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(4, 1572241121151063295),
            name: 'name',
            type: 9,
            flags: 0)
      ],
      relations: <obx_int.ModelRelation>[],
      backlinks: <obx_int.ModelBacklink>[]),
  obx_int.ModelEntity(
      id: const obx_int.IdUid(2, 7207097667153489501),
      name: 'CacheCache',
      lastPropertyId: const obx_int.IdUid(5, 6556120847131278040),
      flags: 0,
      properties: <obx_int.ModelProperty>[
        obx_int.ModelProperty(
            id: const obx_int.IdUid(1, 2681005727982569482),
            name: 'id',
            type: 6,
            flags: 1),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(2, 5404173905917963843),
            name: 'data',
            type: 9,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(3, 661961443119417016),
            name: 'byteData',
            type: 27,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(4, 7754238842144774322),
            name: 'uri',
            type: 9,
            flags: 34848,
            indexId: const obx_int.IdUid(2, 1127835597877573256)),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(5, 6556120847131278040),
            name: 'expiresAt',
            type: 10,
            flags: 8,
            indexId: const obx_int.IdUid(3, 4999889854867767910))
      ],
      relations: <obx_int.ModelRelation>[],
      backlinks: <obx_int.ModelBacklink>[]),
  obx_int.ModelEntity(
      id: const obx_int.IdUid(3, 2232319528622886934),
      name: 'ImageCache',
      lastPropertyId: const obx_int.IdUid(6, 3120205711240193872),
      flags: 0,
      properties: <obx_int.ModelProperty>[
        obx_int.ModelProperty(
            id: const obx_int.IdUid(1, 2171202239014423482),
            name: 'id',
            type: 6,
            flags: 1),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(2, 6252635388862524164),
            name: 'uuid',
            type: 9,
            flags: 2048,
            indexId: const obx_int.IdUid(4, 5270774309264438751)),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(3, 5645727601513006053),
            name: 'createdAt',
            type: 9,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(4, 3638584164329311090),
            name: 'name',
            type: 9,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(5, 8959829213104668587),
            name: 'url',
            type: 9,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(6, 3120205711240193872),
            name: 'type',
            type: 9,
            flags: 0)
      ],
      relations: <obx_int.ModelRelation>[],
      backlinks: <obx_int.ModelBacklink>[]),
  obx_int.ModelEntity(
      id: const obx_int.IdUid(4, 6868762395190710332),
      name: 'LocationCache',
      lastPropertyId: const obx_int.IdUid(3, 8650667968744231244),
      flags: 0,
      properties: <obx_int.ModelProperty>[
        obx_int.ModelProperty(
            id: const obx_int.IdUid(1, 4387109304703964791),
            name: 'id',
            type: 6,
            flags: 1),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(2, 7697249962887143341),
            name: 'placeId',
            type: 9,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(3, 8650667968744231244),
            name: 'displayName',
            type: 9,
            flags: 0)
      ],
      relations: <obx_int.ModelRelation>[],
      backlinks: <obx_int.ModelBacklink>[]),
  obx_int.ModelEntity(
      id: const obx_int.IdUid(5, 1066071825613724983),
      name: 'SessionCache',
      lastPropertyId: const obx_int.IdUid(17, 8325086926719649036),
      flags: 0,
      properties: <obx_int.ModelProperty>[
        obx_int.ModelProperty(
            id: const obx_int.IdUid(1, 3249241987546589767),
            name: 'id',
            type: 6,
            flags: 129),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(2, 786064777406375260),
            name: 'accessToken',
            type: 9,
            flags: 34848,
            indexId: const obx_int.IdUid(5, 3928742555453728583)),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(3, 4855809639427102545),
            name: 'refreshToken',
            type: 9,
            flags: 2048,
            indexId: const obx_int.IdUid(6, 7923954639300100374)),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(4, 8405522530865534423),
            name: 'createdAt',
            type: 6,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(5, 2674283578696828888),
            name: 'expiresAt',
            type: 6,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(7, 3564144303851046397),
            name: 'onboardingState',
            type: 9,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(8, 4057236562056340799),
            name: 'lastEmail',
            type: 9,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(9, 6050141837947996242),
            name: 'hasEnabledBioAuth',
            type: 1,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(10, 9184332373292250360),
            name: 'hasEnabled2fa',
            type: 1,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(11, 8622108352346963772),
            name: 'authPublicKey',
            type: 9,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(14, 4050680923259066238),
            name: 'lastPhoneNumber',
            type: 9,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(15, 7058809721617712439),
            name: 'userUuid',
            type: 9,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(16, 7260689454402606908),
            name: 'hasEnabledPinAuth',
            type: 1,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(17, 8325086926719649036),
            name: 'appPublicKey',
            type: 9,
            flags: 0)
      ],
      relations: <obx_int.ModelRelation>[],
      backlinks: <obx_int.ModelBacklink>[]),
  obx_int.ModelEntity(
      id: const obx_int.IdUid(6, 5244052296167536443),
      name: 'TelephoneNumberCache',
      lastPropertyId: const obx_int.IdUid(3, 9079000742058020075),
      flags: 0,
      properties: <obx_int.ModelProperty>[
        obx_int.ModelProperty(
            id: const obx_int.IdUid(1, 7558296146716487399),
            name: 'id',
            type: 6,
            flags: 1),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(2, 3638377099020887639),
            name: 'countryCode',
            type: 9,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(3, 9079000742058020075),
            name: 'number',
            type: 9,
            flags: 0)
      ],
      relations: <obx_int.ModelRelation>[],
      backlinks: <obx_int.ModelBacklink>[]),
  obx_int.ModelEntity(
      id: const obx_int.IdUid(7, 6010729087324264653),
      name: 'ThemeCache',
      lastPropertyId: const obx_int.IdUid(4, 444114950938572228),
      flags: 0,
      properties: <obx_int.ModelProperty>[
        obx_int.ModelProperty(
            id: const obx_int.IdUid(1, 1127178900023012604),
            name: 'id',
            type: 6,
            flags: 129),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(2, 3295688809638728825),
            name: 'inDarkMode',
            type: 1,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(3, 519685389244947430),
            name: 'inLightMode',
            type: 1,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(4, 444114950938572228),
            name: 'inSystemMode',
            type: 1,
            flags: 0)
      ],
      relations: <obx_int.ModelRelation>[],
      backlinks: <obx_int.ModelBacklink>[]),
  obx_int.ModelEntity(
      id: const obx_int.IdUid(8, 7991122869147316633),
      name: 'UserCache',
      lastPropertyId: const obx_int.IdUid(22, 3411140923168221465),
      flags: 0,
      properties: <obx_int.ModelProperty>[
        obx_int.ModelProperty(
            id: const obx_int.IdUid(1, 3474154768916649391),
            name: 'id',
            type: 6,
            flags: 129),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(2, 6863239545440569953),
            name: 'uuid',
            type: 9,
            flags: 2048,
            indexId: const obx_int.IdUid(7, 5208101594132120902)),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(3, 2205465666492976623),
            name: 'email',
            type: 9,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(4, 285087658764003161),
            name: 'displayName',
            type: 9,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(5, 7927998982961257404),
            name: 'firstName',
            type: 9,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(6, 8882632575856413434),
            name: 'description',
            type: 9,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(7, 6716479156462833905),
            name: 'language',
            type: 9,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(8, 1096911734782798370),
            name: 'lastName',
            type: 9,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(9, 1523236474657872902),
            name: 'photo',
            type: 9,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(11, 1480353981465398107),
            name: 'location_Id',
            type: 11,
            flags: 520,
            indexId: const obx_int.IdUid(8, 7913818807779177806),
            relationTarget: 'LocationCache'),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(12, 1034442813426071353),
            name: 'accountNumber',
            type: 9,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(13, 6988372603717617510),
            name: 'plan',
            type: 9,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(14, 8951360676221193380),
            name: 'phoneNumber',
            type: 9,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(15, 2394066332430093532),
            name: 'createdAt',
            type: 12,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(16, 4935934582854000283),
            name: 'updatedAt',
            type: 12,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(17, 4198986452434577181),
            name: 'pinSetAt',
            type: 9,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(18, 1072732210021893416),
            name: 'biometricsSetAt',
            type: 9,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(19, 3714672880221381777),
            name: 'emailVerifiedAt',
            type: 9,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(20, 2187142207093815203),
            name: 'passwordSetAt',
            type: 9,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(21, 8263074333910765743),
            name: 'phoneVerifiedAt',
            type: 9,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(22, 3411140923168221465),
            name: 'stripeCustomerId',
            type: 9,
            flags: 0)
      ],
      relations: <obx_int.ModelRelation>[],
      backlinks: <obx_int.ModelBacklink>[])
];

/// Shortcut for [obx.Store.new] that passes [getObjectBoxModel] and for Flutter
/// apps by default a [directory] using `defaultStoreDirectory()` from the
/// ObjectBox Flutter library.
///
/// Note: for desktop apps it is recommended to specify a unique [directory].
///
/// See [obx.Store.new] for an explanation of all parameters.
///
/// For Flutter apps, also calls `loadObjectBoxLibraryAndroidCompat()` from
/// the ObjectBox Flutter library to fix loading the native ObjectBox library
/// on Android 6 and older.
Future<obx.Store> openStore(
    {String? directory,
    int? maxDBSizeInKB,
    int? maxDataSizeInKB,
    int? fileMode,
    int? maxReaders,
    bool queriesCaseSensitiveDefault = true,
    String? macosApplicationGroup}) async {
  await loadObjectBoxLibraryAndroidCompat();
  return obx.Store(getObjectBoxModel(),
      directory: directory ?? (await defaultStoreDirectory()).path,
      maxDBSizeInKB: maxDBSizeInKB,
      maxDataSizeInKB: maxDataSizeInKB,
      fileMode: fileMode,
      maxReaders: maxReaders,
      queriesCaseSensitiveDefault: queriesCaseSensitiveDefault,
      macosApplicationGroup: macosApplicationGroup);
}

/// Returns the ObjectBox model definition for this project for use with
/// [obx.Store.new].
obx_int.ModelDefinition getObjectBoxModel() {
  final model = obx_int.ModelInfo(
      entities: _entities,
      lastEntityId: const obx_int.IdUid(8, 7991122869147316633),
      lastIndexId: const obx_int.IdUid(8, 7913818807779177806),
      lastRelationId: const obx_int.IdUid(0, 0),
      lastSequenceId: const obx_int.IdUid(0, 0),
      retiredEntityUids: const [],
      retiredIndexUids: const [],
      retiredPropertyUids: const [
        5504126348413455985,
        5702537056496228701,
        6528964882199586615,
        8172486621473470642
      ],
      retiredRelationUids: const [],
      modelVersion: 5,
      modelVersionParserMinimum: 5,
      version: 1);

  final bindings = <Type, obx_int.EntityDefinition>{
    AppSelectionResponseCache:
        obx_int.EntityDefinition<AppSelectionResponseCache>(
            model: _entities[0],
            toOneRelations: (AppSelectionResponseCache object) => [],
            toManyRelations: (AppSelectionResponseCache object) => {},
            getId: (AppSelectionResponseCache object) => object.index,
            setId: (AppSelectionResponseCache object, int id) {
              object.index = id;
            },
            objectToFB: (AppSelectionResponseCache object, fb.Builder fbb) {
              final idOffset =
                  object.id == null ? null : fbb.writeString(object.id!);
              final nameOffset =
                  object.name == null ? null : fbb.writeString(object.name!);
              fbb.startTable(5);
              fbb.addOffset(0, idOffset);
              fbb.addInt64(1, object.index);
              fbb.addBool(2, object.isActive);
              fbb.addOffset(3, nameOffset);
              fbb.finish(fbb.endTable());
              return object.index;
            },
            objectFromFB: (obx.Store store, ByteData fbData) {
              final buffer = fb.BufferContext(fbData);
              final rootOffset = buffer.derefObject(0);
              final isActiveParam = const fb.BoolReader()
                  .vTableGetNullable(buffer, rootOffset, 8);
              final idParam = const fb.StringReader(asciiOptimization: true)
                  .vTableGetNullable(buffer, rootOffset, 4);
              final nameParam = const fb.StringReader(asciiOptimization: true)
                  .vTableGetNullable(buffer, rootOffset, 10);
              final object = AppSelectionResponseCache(
                  isActive: isActiveParam, id: idParam, name: nameParam)
                ..index =
                    const fb.Int64Reader().vTableGet(buffer, rootOffset, 6, 0);

              return object;
            }),
    CacheCache: obx_int.EntityDefinition<CacheCache>(
        model: _entities[1],
        toOneRelations: (CacheCache object) => [],
        toManyRelations: (CacheCache object) => {},
        getId: (CacheCache object) => object.id,
        setId: (CacheCache object, int id) {
          object.id = id;
        },
        objectToFB: (CacheCache object, fb.Builder fbb) {
          final dataOffset =
              object.data == null ? null : fbb.writeString(object.data!);
          final byteDataOffset = object.byteData == null
              ? null
              : fbb.writeListInt64(object.byteData!);
          final uriOffset = fbb.writeString(object.uri);
          fbb.startTable(6);
          fbb.addInt64(0, object.id);
          fbb.addOffset(1, dataOffset);
          fbb.addOffset(2, byteDataOffset);
          fbb.addOffset(3, uriOffset);
          fbb.addInt64(4, object.expiresAt);
          fbb.finish(fbb.endTable());
          return object.id;
        },
        objectFromFB: (obx.Store store, ByteData fbData) {
          final buffer = fb.BufferContext(fbData);
          final rootOffset = buffer.derefObject(0);
          final dataParam = const fb.StringReader(asciiOptimization: true)
              .vTableGetNullable(buffer, rootOffset, 6);
          final byteDataParam =
              const fb.ListReader<int>(fb.Int64Reader(), lazy: false)
                  .vTableGetNullable(buffer, rootOffset, 8);
          final uriParam = const fb.StringReader(asciiOptimization: true)
              .vTableGet(buffer, rootOffset, 10, '');
          final expiresAtParam =
              const fb.Int64Reader().vTableGet(buffer, rootOffset, 12, 0);
          final object = CacheCache(
              data: dataParam,
              byteData: byteDataParam,
              uri: uriParam,
              expiresAt: expiresAtParam)
            ..id = const fb.Int64Reader().vTableGet(buffer, rootOffset, 4, 0);

          return object;
        }),
    ImageCache: obx_int.EntityDefinition<ImageCache>(
        model: _entities[2],
        toOneRelations: (ImageCache object) => [],
        toManyRelations: (ImageCache object) => {},
        getId: (ImageCache object) => object.id,
        setId: (ImageCache object, int id) {
          object.id = id;
        },
        objectToFB: (ImageCache object, fb.Builder fbb) {
          final uuidOffset =
              object.uuid == null ? null : fbb.writeString(object.uuid!);
          final createdAtOffset = object.createdAt == null
              ? null
              : fbb.writeString(object.createdAt!);
          final nameOffset =
              object.name == null ? null : fbb.writeString(object.name!);
          final urlOffset =
              object.url == null ? null : fbb.writeString(object.url!);
          final typeOffset =
              object.type == null ? null : fbb.writeString(object.type!);
          fbb.startTable(7);
          fbb.addInt64(0, object.id);
          fbb.addOffset(1, uuidOffset);
          fbb.addOffset(2, createdAtOffset);
          fbb.addOffset(3, nameOffset);
          fbb.addOffset(4, urlOffset);
          fbb.addOffset(5, typeOffset);
          fbb.finish(fbb.endTable());
          return object.id;
        },
        objectFromFB: (obx.Store store, ByteData fbData) {
          final buffer = fb.BufferContext(fbData);
          final rootOffset = buffer.derefObject(0);
          final uuidParam = const fb.StringReader(asciiOptimization: true)
              .vTableGetNullable(buffer, rootOffset, 6);
          final createdAtParam = const fb.StringReader(asciiOptimization: true)
              .vTableGetNullable(buffer, rootOffset, 8);
          final nameParam = const fb.StringReader(asciiOptimization: true)
              .vTableGetNullable(buffer, rootOffset, 10);
          final urlParam = const fb.StringReader(asciiOptimization: true)
              .vTableGetNullable(buffer, rootOffset, 12);
          final typeParam = const fb.StringReader(asciiOptimization: true)
              .vTableGetNullable(buffer, rootOffset, 14);
          final object = ImageCache(
              uuid: uuidParam,
              createdAt: createdAtParam,
              name: nameParam,
              url: urlParam,
              type: typeParam)
            ..id = const fb.Int64Reader().vTableGet(buffer, rootOffset, 4, 0);

          return object;
        }),
    LocationCache: obx_int.EntityDefinition<LocationCache>(
        model: _entities[3],
        toOneRelations: (LocationCache object) => [],
        toManyRelations: (LocationCache object) => {},
        getId: (LocationCache object) => object.id,
        setId: (LocationCache object, int id) {
          object.id = id;
        },
        objectToFB: (LocationCache object, fb.Builder fbb) {
          final placeIdOffset =
              object.placeId == null ? null : fbb.writeString(object.placeId!);
          final displayNameOffset = object.displayName == null
              ? null
              : fbb.writeString(object.displayName!);
          fbb.startTable(4);
          fbb.addInt64(0, object.id);
          fbb.addOffset(1, placeIdOffset);
          fbb.addOffset(2, displayNameOffset);
          fbb.finish(fbb.endTable());
          return object.id;
        },
        objectFromFB: (obx.Store store, ByteData fbData) {
          final buffer = fb.BufferContext(fbData);
          final rootOffset = buffer.derefObject(0);
          final placeIdParam = const fb.StringReader(asciiOptimization: true)
              .vTableGetNullable(buffer, rootOffset, 6);
          final displayNameParam =
              const fb.StringReader(asciiOptimization: true)
                  .vTableGetNullable(buffer, rootOffset, 8);
          final object = LocationCache(
              placeId: placeIdParam, displayName: displayNameParam)
            ..id = const fb.Int64Reader().vTableGet(buffer, rootOffset, 4, 0);

          return object;
        }),
    SessionCache: obx_int.EntityDefinition<SessionCache>(
        model: _entities[4],
        toOneRelations: (SessionCache object) => [],
        toManyRelations: (SessionCache object) => {},
        getId: (SessionCache object) => object.id,
        setId: (SessionCache object, int id) {
          object.id = id;
        },
        objectToFB: (SessionCache object, fb.Builder fbb) {
          final accessTokenOffset = fbb.writeString(object.accessToken);
          final refreshTokenOffset = fbb.writeString(object.refreshToken);
          final onboardingStateOffset = object.onboardingState == null
              ? null
              : fbb.writeString(object.onboardingState!);
          final lastEmailOffset = object.lastEmail == null
              ? null
              : fbb.writeString(object.lastEmail!);
          final authPublicKeyOffset = object.authPublicKey == null
              ? null
              : fbb.writeString(object.authPublicKey!);
          final lastPhoneNumberOffset = object.lastPhoneNumber == null
              ? null
              : fbb.writeString(object.lastPhoneNumber!);
          final userUuidOffset = object.userUuid == null
              ? null
              : fbb.writeString(object.userUuid!);
          final appPublicKeyOffset = object.appPublicKey == null
              ? null
              : fbb.writeString(object.appPublicKey!);
          fbb.startTable(18);
          fbb.addInt64(0, object.id);
          fbb.addOffset(1, accessTokenOffset);
          fbb.addOffset(2, refreshTokenOffset);
          fbb.addInt64(3, object.createdAt);
          fbb.addInt64(4, object.expiresAt);
          fbb.addOffset(6, onboardingStateOffset);
          fbb.addOffset(7, lastEmailOffset);
          fbb.addBool(8, object.hasEnabledBioAuth);
          fbb.addBool(9, object.hasEnabled2fa);
          fbb.addOffset(10, authPublicKeyOffset);
          fbb.addOffset(13, lastPhoneNumberOffset);
          fbb.addOffset(14, userUuidOffset);
          fbb.addBool(15, object.hasEnabledPinAuth);
          fbb.addOffset(16, appPublicKeyOffset);
          fbb.finish(fbb.endTable());
          return object.id;
        },
        objectFromFB: (obx.Store store, ByteData fbData) {
          final buffer = fb.BufferContext(fbData);
          final rootOffset = buffer.derefObject(0);
          final accessTokenParam =
              const fb.StringReader(asciiOptimization: true)
                  .vTableGet(buffer, rootOffset, 6, '');
          final expiresAtParam =
              const fb.Int64Reader().vTableGet(buffer, rootOffset, 12, 0);
          final onboardingStateParam =
              const fb.StringReader(asciiOptimization: true)
                  .vTableGetNullable(buffer, rootOffset, 16);
          final refreshTokenParam =
              const fb.StringReader(asciiOptimization: true)
                  .vTableGet(buffer, rootOffset, 8, '');
          final appPublicKeyParam =
              const fb.StringReader(asciiOptimization: true)
                  .vTableGetNullable(buffer, rootOffset, 36);
          final hasEnabledBioAuthParam =
              const fb.BoolReader().vTableGet(buffer, rootOffset, 20, false);
          final hasEnabled2faParam =
              const fb.BoolReader().vTableGet(buffer, rootOffset, 22, false);
          final hasEnabledPinAuthParam =
              const fb.BoolReader().vTableGet(buffer, rootOffset, 34, false);
          final lastEmailParam = const fb.StringReader(asciiOptimization: true)
              .vTableGetNullable(buffer, rootOffset, 18);
          final userUuidParam = const fb.StringReader(asciiOptimization: true)
              .vTableGetNullable(buffer, rootOffset, 32);
          final lastPhoneNumberParam =
              const fb.StringReader(asciiOptimization: true)
                  .vTableGetNullable(buffer, rootOffset, 30);
          final authPublicKeyParam =
              const fb.StringReader(asciiOptimization: true)
                  .vTableGetNullable(buffer, rootOffset, 24);
          final object = SessionCache(
              accessToken: accessTokenParam,
              expiresAt: expiresAtParam,
              onboardingState: onboardingStateParam,
              refreshToken: refreshTokenParam,
              appPublicKey: appPublicKeyParam,
              hasEnabledBioAuth: hasEnabledBioAuthParam,
              hasEnabled2fa: hasEnabled2faParam,
              hasEnabledPinAuth: hasEnabledPinAuthParam,
              lastEmail: lastEmailParam,
              userUuid: userUuidParam,
              lastPhoneNumber: lastPhoneNumberParam,
              authPublicKey: authPublicKeyParam)
            ..id = const fb.Int64Reader().vTableGet(buffer, rootOffset, 4, 0)
            ..createdAt =
                const fb.Int64Reader().vTableGet(buffer, rootOffset, 10, 0);

          return object;
        }),
    TelephoneNumberCache: obx_int.EntityDefinition<TelephoneNumberCache>(
        model: _entities[5],
        toOneRelations: (TelephoneNumberCache object) => [],
        toManyRelations: (TelephoneNumberCache object) => {},
        getId: (TelephoneNumberCache object) => object.id,
        setId: (TelephoneNumberCache object, int id) {
          object.id = id;
        },
        objectToFB: (TelephoneNumberCache object, fb.Builder fbb) {
          final countryCodeOffset = object.countryCode == null
              ? null
              : fbb.writeString(object.countryCode!);
          final numberOffset =
              object.number == null ? null : fbb.writeString(object.number!);
          fbb.startTable(4);
          fbb.addInt64(0, object.id);
          fbb.addOffset(1, countryCodeOffset);
          fbb.addOffset(2, numberOffset);
          fbb.finish(fbb.endTable());
          return object.id;
        },
        objectFromFB: (obx.Store store, ByteData fbData) {
          final buffer = fb.BufferContext(fbData);
          final rootOffset = buffer.derefObject(0);
          final countryCodeParam =
              const fb.StringReader(asciiOptimization: true)
                  .vTableGetNullable(buffer, rootOffset, 6);
          final numberParam = const fb.StringReader(asciiOptimization: true)
              .vTableGetNullable(buffer, rootOffset, 8);
          final object = TelephoneNumberCache(
              countryCode: countryCodeParam, number: numberParam)
            ..id = const fb.Int64Reader().vTableGet(buffer, rootOffset, 4, 0);

          return object;
        }),
    ThemeCache: obx_int.EntityDefinition<ThemeCache>(
        model: _entities[6],
        toOneRelations: (ThemeCache object) => [],
        toManyRelations: (ThemeCache object) => {},
        getId: (ThemeCache object) => object.id,
        setId: (ThemeCache object, int id) {
          object.id = id;
        },
        objectToFB: (ThemeCache object, fb.Builder fbb) {
          fbb.startTable(5);
          fbb.addInt64(0, object.id);
          fbb.addBool(1, object.inDarkMode);
          fbb.addBool(2, object.inLightMode);
          fbb.addBool(3, object.inSystemMode);
          fbb.finish(fbb.endTable());
          return object.id;
        },
        objectFromFB: (obx.Store store, ByteData fbData) {
          final buffer = fb.BufferContext(fbData);
          final rootOffset = buffer.derefObject(0);
          final inDarkModeParam =
              const fb.BoolReader().vTableGet(buffer, rootOffset, 6, false);
          final inLightModeParam =
              const fb.BoolReader().vTableGet(buffer, rootOffset, 8, false);
          final inSystemModeParam =
              const fb.BoolReader().vTableGet(buffer, rootOffset, 10, false);
          final object = ThemeCache(
              inDarkModeParam, inLightModeParam, inSystemModeParam)
            ..id = const fb.Int64Reader().vTableGet(buffer, rootOffset, 4, 0);

          return object;
        }),
    UserCache: obx_int.EntityDefinition<UserCache>(
        model: _entities[7],
        toOneRelations: (UserCache object) => [object.location_],
        toManyRelations: (UserCache object) => {},
        getId: (UserCache object) => object.id,
        setId: (UserCache object, int id) {
          object.id = id;
        },
        objectToFB: (UserCache object, fb.Builder fbb) {
          final uuidOffset =
              object.uuid == null ? null : fbb.writeString(object.uuid!);
          final emailOffset =
              object.email == null ? null : fbb.writeString(object.email!);
          final displayNameOffset = object.displayName == null
              ? null
              : fbb.writeString(object.displayName!);
          final firstNameOffset = object.firstName == null
              ? null
              : fbb.writeString(object.firstName!);
          final descriptionOffset = object.description == null
              ? null
              : fbb.writeString(object.description!);
          final languageOffset = object.language == null
              ? null
              : fbb.writeString(object.language!);
          final lastNameOffset = object.lastName == null
              ? null
              : fbb.writeString(object.lastName!);
          final photoOffset =
              object.photo == null ? null : fbb.writeString(object.photo!);
          final accountNumberOffset = object.accountNumber == null
              ? null
              : fbb.writeString(object.accountNumber!);
          final planOffset =
              object.plan == null ? null : fbb.writeString(object.plan!);
          final phoneNumberOffset = object.phoneNumber == null
              ? null
              : fbb.writeString(object.phoneNumber!);
          final pinSetAtOffset = object.pinSetAt == null
              ? null
              : fbb.writeString(object.pinSetAt!);
          final biometricsSetAtOffset = object.biometricsSetAt == null
              ? null
              : fbb.writeString(object.biometricsSetAt!);
          final emailVerifiedAtOffset = object.emailVerifiedAt == null
              ? null
              : fbb.writeString(object.emailVerifiedAt!);
          final passwordSetAtOffset = object.passwordSetAt == null
              ? null
              : fbb.writeString(object.passwordSetAt!);
          final phoneVerifiedAtOffset = object.phoneVerifiedAt == null
              ? null
              : fbb.writeString(object.phoneVerifiedAt!);
          final stripeCustomerIdOffset = object.stripeCustomerId == null
              ? null
              : fbb.writeString(object.stripeCustomerId!);
          fbb.startTable(23);
          fbb.addInt64(0, object.id);
          fbb.addOffset(1, uuidOffset);
          fbb.addOffset(2, emailOffset);
          fbb.addOffset(3, displayNameOffset);
          fbb.addOffset(4, firstNameOffset);
          fbb.addOffset(5, descriptionOffset);
          fbb.addOffset(6, languageOffset);
          fbb.addOffset(7, lastNameOffset);
          fbb.addOffset(8, photoOffset);
          fbb.addInt64(10, object.location_.targetId);
          fbb.addOffset(11, accountNumberOffset);
          fbb.addOffset(12, planOffset);
          fbb.addOffset(13, phoneNumberOffset);
          fbb.addInt64(
              14,
              object.createdAt == null
                  ? null
                  : object.createdAt!.microsecondsSinceEpoch * 1000);
          fbb.addInt64(
              15,
              object.updatedAt == null
                  ? null
                  : object.updatedAt!.microsecondsSinceEpoch * 1000);
          fbb.addOffset(16, pinSetAtOffset);
          fbb.addOffset(17, biometricsSetAtOffset);
          fbb.addOffset(18, emailVerifiedAtOffset);
          fbb.addOffset(19, passwordSetAtOffset);
          fbb.addOffset(20, phoneVerifiedAtOffset);
          fbb.addOffset(21, stripeCustomerIdOffset);
          fbb.finish(fbb.endTable());
          return object.id;
        },
        objectFromFB: (obx.Store store, ByteData fbData) {
          final buffer = fb.BufferContext(fbData);
          final rootOffset = buffer.derefObject(0);
          final createdAtValue =
              const fb.Int64Reader().vTableGetNullable(buffer, rootOffset, 32);
          final updatedAtValue =
              const fb.Int64Reader().vTableGetNullable(buffer, rootOffset, 34);
          final uuidParam = const fb.StringReader(asciiOptimization: true)
              .vTableGetNullable(buffer, rootOffset, 6);
          final emailParam = const fb.StringReader(asciiOptimization: true)
              .vTableGetNullable(buffer, rootOffset, 8);
          final firstNameParam = const fb.StringReader(asciiOptimization: true)
              .vTableGetNullable(buffer, rootOffset, 12);
          final descriptionParam =
              const fb.StringReader(asciiOptimization: true)
                  .vTableGetNullable(buffer, rootOffset, 14);
          final displayNameParam =
              const fb.StringReader(asciiOptimization: true)
                  .vTableGetNullable(buffer, rootOffset, 10);
          final lastNameParam = const fb.StringReader(asciiOptimization: true)
              .vTableGetNullable(buffer, rootOffset, 18);
          final languageParam = const fb.StringReader(asciiOptimization: true)
              .vTableGetNullable(buffer, rootOffset, 16);
          final photoParam = const fb.StringReader(asciiOptimization: true)
              .vTableGetNullable(buffer, rootOffset, 20);
          final phoneNumberParam =
              const fb.StringReader(asciiOptimization: true)
                  .vTableGetNullable(buffer, rootOffset, 30);
          final createdAtParam = createdAtValue == null
              ? null
              : DateTime.fromMicrosecondsSinceEpoch(
                  (createdAtValue / 1000).round());
          final pinSetAtParam = const fb.StringReader(asciiOptimization: true)
              .vTableGetNullable(buffer, rootOffset, 36);
          final updatedAtParam = updatedAtValue == null
              ? null
              : DateTime.fromMicrosecondsSinceEpoch(
                  (updatedAtValue / 1000).round());
          final biometricsSetAtParam =
              const fb.StringReader(asciiOptimization: true)
                  .vTableGetNullable(buffer, rootOffset, 38);
          final emailVerifiedAtParam =
              const fb.StringReader(asciiOptimization: true)
                  .vTableGetNullable(buffer, rootOffset, 40);
          final passwordSetAtParam =
              const fb.StringReader(asciiOptimization: true)
                  .vTableGetNullable(buffer, rootOffset, 42);
          final phoneVerifiedAtParam =
              const fb.StringReader(asciiOptimization: true)
                  .vTableGetNullable(buffer, rootOffset, 44);
          final stripeCustomerIdParam =
              const fb.StringReader(asciiOptimization: true)
                  .vTableGetNullable(buffer, rootOffset, 46);
          final object = UserCache(
              uuid: uuidParam,
              email: emailParam,
              firstName: firstNameParam,
              description: descriptionParam,
              displayName: displayNameParam,
              lastName: lastNameParam,
              language: languageParam,
              photo: photoParam,
              phoneNumber: phoneNumberParam,
              createdAt: createdAtParam,
              pinSetAt: pinSetAtParam,
              updatedAt: updatedAtParam,
              biometricsSetAt: biometricsSetAtParam,
              emailVerifiedAt: emailVerifiedAtParam,
              passwordSetAt: passwordSetAtParam,
              phoneVerifiedAt: phoneVerifiedAtParam,
              stripeCustomerId: stripeCustomerIdParam)
            ..id = const fb.Int64Reader().vTableGet(buffer, rootOffset, 4, 0)
            ..accountNumber = const fb.StringReader(asciiOptimization: true)
                .vTableGetNullable(buffer, rootOffset, 26)
            ..plan = const fb.StringReader(asciiOptimization: true)
                .vTableGetNullable(buffer, rootOffset, 28);
          object.location_.targetId =
              const fb.Int64Reader().vTableGet(buffer, rootOffset, 24, 0);
          object.location_.attach(store);
          return object;
        })
  };

  return obx_int.ModelDefinition(model, bindings);
}

/// [AppSelectionResponseCache] entity fields to define ObjectBox queries.
class AppSelectionResponseCache_ {
  /// See [AppSelectionResponseCache.id].
  static final id = obx.QueryStringProperty<AppSelectionResponseCache>(
      _entities[0].properties[0]);

  /// See [AppSelectionResponseCache.index].
  static final index = obx.QueryIntegerProperty<AppSelectionResponseCache>(
      _entities[0].properties[1]);

  /// See [AppSelectionResponseCache.isActive].
  static final isActive = obx.QueryBooleanProperty<AppSelectionResponseCache>(
      _entities[0].properties[2]);

  /// See [AppSelectionResponseCache.name].
  static final name = obx.QueryStringProperty<AppSelectionResponseCache>(
      _entities[0].properties[3]);
}

/// [CacheCache] entity fields to define ObjectBox queries.
class CacheCache_ {
  /// See [CacheCache.id].
  static final id =
      obx.QueryIntegerProperty<CacheCache>(_entities[1].properties[0]);

  /// See [CacheCache.data].
  static final data =
      obx.QueryStringProperty<CacheCache>(_entities[1].properties[1]);

  /// See [CacheCache.byteData].
  static final byteData =
      obx.QueryIntegerVectorProperty<CacheCache>(_entities[1].properties[2]);

  /// See [CacheCache.uri].
  static final uri =
      obx.QueryStringProperty<CacheCache>(_entities[1].properties[3]);

  /// See [CacheCache.expiresAt].
  static final expiresAt =
      obx.QueryDateProperty<CacheCache>(_entities[1].properties[4]);
}

/// [ImageCache] entity fields to define ObjectBox queries.
class ImageCache_ {
  /// See [ImageCache.id].
  static final id =
      obx.QueryIntegerProperty<ImageCache>(_entities[2].properties[0]);

  /// See [ImageCache.uuid].
  static final uuid =
      obx.QueryStringProperty<ImageCache>(_entities[2].properties[1]);

  /// See [ImageCache.createdAt].
  static final createdAt =
      obx.QueryStringProperty<ImageCache>(_entities[2].properties[2]);

  /// See [ImageCache.name].
  static final name =
      obx.QueryStringProperty<ImageCache>(_entities[2].properties[3]);

  /// See [ImageCache.url].
  static final url =
      obx.QueryStringProperty<ImageCache>(_entities[2].properties[4]);

  /// See [ImageCache.type].
  static final type =
      obx.QueryStringProperty<ImageCache>(_entities[2].properties[5]);
}

/// [LocationCache] entity fields to define ObjectBox queries.
class LocationCache_ {
  /// See [LocationCache.id].
  static final id =
      obx.QueryIntegerProperty<LocationCache>(_entities[3].properties[0]);

  /// See [LocationCache.placeId].
  static final placeId =
      obx.QueryStringProperty<LocationCache>(_entities[3].properties[1]);

  /// See [LocationCache.displayName].
  static final displayName =
      obx.QueryStringProperty<LocationCache>(_entities[3].properties[2]);
}

/// [SessionCache] entity fields to define ObjectBox queries.
class SessionCache_ {
  /// See [SessionCache.id].
  static final id =
      obx.QueryIntegerProperty<SessionCache>(_entities[4].properties[0]);

  /// See [SessionCache.accessToken].
  static final accessToken =
      obx.QueryStringProperty<SessionCache>(_entities[4].properties[1]);

  /// See [SessionCache.refreshToken].
  static final refreshToken =
      obx.QueryStringProperty<SessionCache>(_entities[4].properties[2]);

  /// See [SessionCache.createdAt].
  static final createdAt =
      obx.QueryIntegerProperty<SessionCache>(_entities[4].properties[3]);

  /// See [SessionCache.expiresAt].
  static final expiresAt =
      obx.QueryIntegerProperty<SessionCache>(_entities[4].properties[4]);

  /// See [SessionCache.onboardingState].
  static final onboardingState =
      obx.QueryStringProperty<SessionCache>(_entities[4].properties[5]);

  /// See [SessionCache.lastEmail].
  static final lastEmail =
      obx.QueryStringProperty<SessionCache>(_entities[4].properties[6]);

  /// See [SessionCache.hasEnabledBioAuth].
  static final hasEnabledBioAuth =
      obx.QueryBooleanProperty<SessionCache>(_entities[4].properties[7]);

  /// See [SessionCache.hasEnabled2fa].
  static final hasEnabled2fa =
      obx.QueryBooleanProperty<SessionCache>(_entities[4].properties[8]);

  /// See [SessionCache.authPublicKey].
  static final authPublicKey =
      obx.QueryStringProperty<SessionCache>(_entities[4].properties[9]);

  /// See [SessionCache.lastPhoneNumber].
  static final lastPhoneNumber =
      obx.QueryStringProperty<SessionCache>(_entities[4].properties[10]);

  /// See [SessionCache.userUuid].
  static final userUuid =
      obx.QueryStringProperty<SessionCache>(_entities[4].properties[11]);

  /// See [SessionCache.hasEnabledPinAuth].
  static final hasEnabledPinAuth =
      obx.QueryBooleanProperty<SessionCache>(_entities[4].properties[12]);

  /// See [SessionCache.appPublicKey].
  static final appPublicKey =
      obx.QueryStringProperty<SessionCache>(_entities[4].properties[13]);
}

/// [TelephoneNumberCache] entity fields to define ObjectBox queries.
class TelephoneNumberCache_ {
  /// See [TelephoneNumberCache.id].
  static final id = obx.QueryIntegerProperty<TelephoneNumberCache>(
      _entities[5].properties[0]);

  /// See [TelephoneNumberCache.countryCode].
  static final countryCode =
      obx.QueryStringProperty<TelephoneNumberCache>(_entities[5].properties[1]);

  /// See [TelephoneNumberCache.number].
  static final number =
      obx.QueryStringProperty<TelephoneNumberCache>(_entities[5].properties[2]);
}

/// [ThemeCache] entity fields to define ObjectBox queries.
class ThemeCache_ {
  /// See [ThemeCache.id].
  static final id =
      obx.QueryIntegerProperty<ThemeCache>(_entities[6].properties[0]);

  /// See [ThemeCache.inDarkMode].
  static final inDarkMode =
      obx.QueryBooleanProperty<ThemeCache>(_entities[6].properties[1]);

  /// See [ThemeCache.inLightMode].
  static final inLightMode =
      obx.QueryBooleanProperty<ThemeCache>(_entities[6].properties[2]);

  /// See [ThemeCache.inSystemMode].
  static final inSystemMode =
      obx.QueryBooleanProperty<ThemeCache>(_entities[6].properties[3]);
}

/// [UserCache] entity fields to define ObjectBox queries.
class UserCache_ {
  /// See [UserCache.id].
  static final id =
      obx.QueryIntegerProperty<UserCache>(_entities[7].properties[0]);

  /// See [UserCache.uuid].
  static final uuid =
      obx.QueryStringProperty<UserCache>(_entities[7].properties[1]);

  /// See [UserCache.email].
  static final email =
      obx.QueryStringProperty<UserCache>(_entities[7].properties[2]);

  /// See [UserCache.displayName].
  static final displayName =
      obx.QueryStringProperty<UserCache>(_entities[7].properties[3]);

  /// See [UserCache.firstName].
  static final firstName =
      obx.QueryStringProperty<UserCache>(_entities[7].properties[4]);

  /// See [UserCache.description].
  static final description =
      obx.QueryStringProperty<UserCache>(_entities[7].properties[5]);

  /// See [UserCache.language].
  static final language =
      obx.QueryStringProperty<UserCache>(_entities[7].properties[6]);

  /// See [UserCache.lastName].
  static final lastName =
      obx.QueryStringProperty<UserCache>(_entities[7].properties[7]);

  /// See [UserCache.photo].
  static final photo =
      obx.QueryStringProperty<UserCache>(_entities[7].properties[8]);

  /// See [UserCache.location_].
  static final location_ = obx.QueryRelationToOne<UserCache, LocationCache>(
      _entities[7].properties[9]);

  /// See [UserCache.accountNumber].
  static final accountNumber =
      obx.QueryStringProperty<UserCache>(_entities[7].properties[10]);

  /// See [UserCache.plan].
  static final plan =
      obx.QueryStringProperty<UserCache>(_entities[7].properties[11]);

  /// See [UserCache.phoneNumber].
  static final phoneNumber =
      obx.QueryStringProperty<UserCache>(_entities[7].properties[12]);

  /// See [UserCache.createdAt].
  static final createdAt =
      obx.QueryDateNanoProperty<UserCache>(_entities[7].properties[13]);

  /// See [UserCache.updatedAt].
  static final updatedAt =
      obx.QueryDateNanoProperty<UserCache>(_entities[7].properties[14]);

  /// See [UserCache.pinSetAt].
  static final pinSetAt =
      obx.QueryStringProperty<UserCache>(_entities[7].properties[15]);

  /// See [UserCache.biometricsSetAt].
  static final biometricsSetAt =
      obx.QueryStringProperty<UserCache>(_entities[7].properties[16]);

  /// See [UserCache.emailVerifiedAt].
  static final emailVerifiedAt =
      obx.QueryStringProperty<UserCache>(_entities[7].properties[17]);

  /// See [UserCache.passwordSetAt].
  static final passwordSetAt =
      obx.QueryStringProperty<UserCache>(_entities[7].properties[18]);

  /// See [UserCache.phoneVerifiedAt].
  static final phoneVerifiedAt =
      obx.QueryStringProperty<UserCache>(_entities[7].properties[19]);

  /// See [UserCache.stripeCustomerId].
  static final stripeCustomerId =
      obx.QueryStringProperty<UserCache>(_entities[7].properties[20]);
}
