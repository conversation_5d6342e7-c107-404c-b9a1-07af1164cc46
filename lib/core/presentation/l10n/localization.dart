// DO NOT EDIT. This is code generated via package:easy_localization/generate.dart

abstract class  LocaleKeys {
  static const login = 'login';
  static const register = 'register';
  static const emailAddress = 'emailAddress';
  static const emailProfile = 'emailProfile';
  static const enterYourEmail = 'enterYourEmail';
  static const password = 'password';
  static const rememberMe = 'rememberMe';
  static const continueText = 'continueText';
  static const termsOfUse = 'termsOfUse';
  static const name = 'name';
  static const enterName = 'enterName';
  static const lastName = 'lastName';
  static const enterLastName = 'enterLastName';
  static const selectAnOption = 'selectAnOption';
  static const address = 'address';
  static const enterYourAddress = 'enterYourAddress';
  static const noBeneficiaries = 'noBeneficiaries';
  static const send = 'send';
  static const user = 'user';
  static const search = 'search';
  static const manageNotification = 'manageNotification';
  static const profileSettings = 'profileSettings';
  static const settings = 'settings';
  static const logout = 'logout';
  static const support = 'support';
  static const aboutUs = 'aboutUs';
  static const privacyPolicy = 'privacyPolicy';
  static const filters = 'filters';
  static const searchResults = 'searchResults';
  static const deselectAll = 'deselectAll';
  static const filter = 'filter';
  static const showMoreOptions = 'showMoreOptions';
  static const showLessOptions = 'showLessOptions';
  static const showAll = 'showAll';
  static const language = 'language';
  static const languages = 'languages';
  static const copyrightText = 'copyrightText';
  static const loginError = 'loginError';
  static const signupError = 'signupError';
  static const anErrorOccured = 'anErrorOccured';
  static const retry = 'retry';
  static const oops = 'oops';
  static const pageNotFound = 'pageNotFound';
  static const weFoundNoPage = 'weFoundNoPage';
  static const goBack = 'goBack';
  static const back = 'back';
  static const telephoneNumber = 'telephoneNumber';
  static const dob = 'dob';
  static const selectDate = 'selectDate';
  static const add = 'add';
  static const personalInfo = 'personalInfo';
  static const avatar = 'avatar';
  static const avatarInstructions = 'avatarInstructions';
  static const imageInstructions = 'imageInstructions';
  static const select = 'select';
  static const provideValidEmail = 'provideValidEmail';
  static const save = 'save';
  static const enter = 'enter';
  static const january = 'january';
  static const february = 'february';
  static const march = 'march';
  static const april = 'april';
  static const may = 'may';
  static const june = 'june';
  static const july = 'july';
  static const august = 'august';
  static const september = 'september';
  static const october = 'october';
  static const november = 'november';
  static const december = 'december';
  static const createdAt = 'createdAt';
  static const updatedAt = 'updatedAt';
  static const passwordRequired = 'passwordRequired';
  static const invalidPassword = 'invalidPassword';
  static const fieldRequired = 'fieldRequired';
  static const fieldsDontMatch = 'fieldsDontMatch';
  static const invalidUrl = 'invalidUrl';
  static const invalidPhone = 'invalidPhone';
  static const invalidDate = 'invalidDate';
  static const mustBeNYears = 'mustBeNYears';
  static const startDateMustBeBeforeEnd = 'startDateMustBeBeforeEnd';
  static const endMustBeAfterStart = 'endMustBeAfterStart';
  static const invalidNumber = 'invalidNumber';
  static const invalidAmount = 'invalidAmount';
  static const invalidLink = 'invalidLink';
  static const invalidTag = 'invalidTag';
  static const amountMinimum = 'amountMinimum';
  static const amountMaximum = 'amountMaximum';
  static const changeIt = 'changeIt';
  static const fileTooLarge = 'fileTooLarge';
  static const cropImage = 'cropImage';
  static const unknownError = 'unknownError';
  static const title = 'title';
  static const year = 'year';
  static const years = 'years';
  static const present = 'present';
  static const description = 'description';
  static const forbidden = 'forbidden';
  static const loginToViewPage = 'loginToViewPage';
  static const started = 'started';
  static const ended = 'ended';
  static const edit = 'edit';
  static const delete = 'delete';
  static const view = 'view';
  static const or = 'or';
  static const attach = 'attach';
  static const checkNetwork = 'checkNetwork';
  static const noInternet = 'noInternet';
  static const requestFailedUnexpectedly = 'requestFailedUnexpectedly';
  static const from = 'from';
  static const to = 'to';
  static const ellipsis = 'ellipsis';
  static const bg = 'bg';
  static const cs = 'cs';
  static const da = 'da';
  static const de = 'de';
  static const el = 'el';
  static const en = 'en';
  static const es = 'es';
  static const et = 'et';
  static const fi = 'fi';
  static const fr = 'fr';
  static const ga = 'ga';
  static const hr = 'hr';
  static const hu = 'hu';
  static const it = 'it';
  static const lt = 'lt';
  static const lv = 'lv';
  static const mt = 'mt';
  static const nl = 'nl';
  static const pl = 'pl';
  static const pt = 'pt';
  static const ro = 'ro';
  static const sk = 'sk';
  static const sl = 'sl';
  static const sv = 'sv';
  static const deletingItem = 'deletingItem';
  static const deletedItem = 'deletedItem';
  static const changedPasswordSuccess = 'changedPasswordSuccess';
  static const changedEmailSuccess = 'changedEmailSuccess';
  static const deleteYourAccount = 'deleteYourAccount';
  static const deleteAccount = 'deleteAccount';
  static const deleteAccountSuccess = 'deleteAccountSuccess';
  static const deleteAccountConfirmQuestion = 'deleteAccountConfirmQuestion';
  static const deleteAccountWarning = 'deleteAccountWarning';
  static const changeMailInstruction = 'changeMailInstruction';
  static const changePassword = 'changePassword';
  static const currentPassword = 'currentPassword';
  static const newPassword = 'newPassword';
  static const passwordsDoNotMatch = 'passwordsDoNotMatch';
  static const retypeNewPassword = 'retypeNewPassword';
  static const minimumCharacters = 'minimumCharacters';
  static const updatePassword = 'updatePassword';
  static const deleteProfile = 'deleteProfile';
  static const deleteApplication = 'deleteApplication';
  static const areYouSureYouWantToDeleteApplication = 'areYouSureYouWantToDeleteApplication';
  static const yes = 'yes';
  static const no = 'no';
  static const yesDeleteMyProfile = 'yesDeleteMyProfile';
  static const doYouWantToDeleteProfile = 'doYouWantToDeleteProfile';
  static const deleteProfileBtnText = 'deleteProfileBtnText';
  static const newEmailAddress = 'newEmailAddress';
  static const saving = 'saving';
  static const saved = 'saved';
  static const unsaving = 'unsaving';
  static const unsaved = 'unsaved';
  static const switchTheme = 'switchTheme';
  static const doYouWishToProceed = 'doYouWishToProceed';
  static const thisActionIsIrreversible = 'thisActionIsIrreversible';
  static const defaultEmptyStateText = 'defaultEmptyStateText';
  static const copiedTextToClipboard = 'copiedTextToClipboard';
  static const data = 'data';
  static const notRequired = 'notRequired';
  static const noData = 'noData';
  static const youHaveNo = 'youHaveNo';
  static const changeLanguage = 'changeLanguage';
  static const thisIsNotificationLanguage = 'thisIsNotificationLanguage';
  static const passwordMustHaveNChars = 'passwordMustHaveNChars';
  static const minLength = 'minLength';
  static const maxLength = 'maxLength';
  static const passwordShouldBeDifferent = 'passwordShouldBeDifferent';
  static const category = 'category';
  static const categories = 'categories';
  static const frequentlySearched = 'frequentlySearched';
  static const showAllCategories = 'showAllCategories';
  static const updatedNotifLang = 'updatedNotifLang';
  static const closeApp = 'closeApp';
  static const closeAppQuestion = 'closeAppQuestion';
  static const comments = 'comments';
  static const formInvalid = 'formInvalid';
  static const welcome = 'welcome';
  static const submit = 'submit';
  static const choosePhoto = 'choosePhoto';
  static const userName = 'userName';
  static const hooray = 'hooray';
  static const browseCommunity = 'browseCommunity';
  static const skip = 'skip';
  static const cancel = 'cancel';
  static const selectPhotosVideos = 'selectPhotosVideos';
  static const info = 'info';
  static const photo = 'photo';
  static const updatePhoto = 'updatePhoto';
  static const notifications = 'notifications';
  static const account = 'account';
  static const closeAccount = 'closeAccount';
  static const chooseNotificationStyle = 'chooseNotificationStyle';
  static const sendCode = 'sendCode';
  static const verificationCode = 'verificationCode';
  static const enterCode = 'enterCode';
  static const changePhoto = 'changePhoto';
  static const location = 'location';
  static const updateAddress = 'updateAddress';
  static const themeMode = 'themeMode';
  static const light = 'light';
  static const dark = 'dark';
  static const system = 'system';
  static const logoutOfAccount = 'logoutOfAccount';
  static const chooseNotificationSchedule = 'chooseNotificationSchedule';
  static const type = 'type';
  static const gender = 'gender';
  static const options = 'options';
  static const noNotifications = 'noNotifications';
  static const noNotificationsYet = 'noNotificationsYet';
  static const suchEmpty = 'suchEmpty';
  static const yesterday = 'yesterday';
  static const momentsAgo = 'momentsAgo';
  static const minutesAgo = 'minutesAgo';
  static const anHourAgo = 'anHourAgo';
  static const hoursAgo = 'hoursAgo';
  static const daysAgo = 'daysAgo';
  static const noMessages = 'noMessages';
  static const daysOld = 'daysOld';
  static const weeksOld = 'weeksOld';
  static const monthsOld = 'monthsOld';
  static const yearsOld = 'yearsOld';
  static const wannaLogout = 'wannaLogout';
  static const yourLocationIsPrivate = 'yourLocationIsPrivate';
  static const nameOnlyVisibleOnProfile = 'nameOnlyVisibleOnProfile';
  static const enterNewEmail = 'enterNewEmail';
  static const writeShortDescription = 'writeShortDescription';
  static const enterNewEmailToUpdate = 'enterNewEmailToUpdate';
  static const unlockNigeriaWithYourMoney = 'unlockNigeriaWithYourMoney';
  static const cardReadyOnLanding = 'cardReadyOnLanding';
  static const frotKnoxInPocket = 'frotKnoxInPocket';
  static const spendExperienceMore = 'spendExperienceMore';
  static const yourKeyTonNigerianGems = 'yourKeyTonNigerianGems';
  static const standWhenLanded = 'standWhenLanded';
  static const enjoyEasyFundAccess = 'enjoyEasyFundAccess';
  static const cardAwaitsYourArrival = 'cardAwaitsYourArrival';
  static const enjoyCardConvenience = 'enjoyCardConvenience';
  static const spendMoreGetMore = 'spendMoreGetMore';
  static const spendMoreWithCard = 'spendMoreWithCard';
  static const moneyDataFortified = 'moneyDataFortified';
  static const experienceSecurityUnparalleled = 'experienceSecurityUnparalleled';
  static const discoverNigeriaFromDay1 = 'discoverNigeriaFromDay1';
  static const thereMoreFromNaira = 'thereMoreFromNaira';
  static const welcomeToD1 = 'welcomeToD1';
  static const createD1Account = 'createD1Account';
  static const agreeToTermsByContinuing = 'agreeToTermsByContinuing';
  static const createYourPassword = 'createYourPassword';
  static const choosePassword = 'choosePassword';
  static const confirmPassword = 'confirmPassword';
  static const enterPassword = 'enterPassword';
  static const reEnterPassword = 'reEnterPassword';
  static const eightCharacters = 'eightCharacters';
  static const aLowerChar = 'aLowerChar';
  static const anUpperChar = 'anUpperChar';
  static const aSpecialChar = 'aSpecialChar';
  static const aNumber = 'aNumber';
  static const whatsYourNumber = 'whatsYourNumber';
  static const weWillSendYouACode = 'weWillSendYouACode';
  static const yourMobileNumber = 'yourMobileNumber';
  static const confrimSignUp = 'confrimSignUp';
  static const note = 'note';
  static const sms = 'sms';
  static const whatsApp = 'whatsApp';
  static const chooseHowToReceiveOtp = 'chooseHowToReceiveOtp';
  static const verifyTelephone = 'verifyTelephone';
  static const weveSentOtp = 'weveSentOtp';
  static const didntRecieveCodeResend = 'didntRecieveCodeResend';
  static const verifyAccount = 'verifyAccount';
  static const maybeLater = 'maybeLater';
  static const enable = 'enable';
  static const makeLoginFastWithBio = 'makeLoginFastWithBio';
  static const enableFaceId = 'enableFaceId';
  static const enableBiometricLogin = 'enableBiometricLogin';
  static const biomentricAuth = 'biomentricAuth';
  static const setPin = 'setPin';
  static const createDay1Pin = 'createDay1Pin';
  static const enter4DigitPin4Transfer = 'enter4DigitPin4Transfer';
  static const done = 'done';
  static const confirmPin = 'confirmPin';
  static const enableNotifications = 'enableNotifications';
  static const notification = 'notification';
  static const keepYourFinHealthNSecurityInCheck = 'keepYourFinHealthNSecurityInCheck';
  static const enableLocation = 'enableLocation';
  static const shareYourLocation = 'shareYourLocation';
  static const allow = 'allow';
  static const dontAllow = 'dontAllow';
  static const day1NeedBioAccess = 'day1NeedBioAccess';
  static const day1BioAccessReason = 'day1BioAccessReason';
  static const day1NeedsLocationAccess = 'day1NeedsLocationAccess';
  static const day1LocationAccessReason = 'day1LocationAccessReason';
  static const day1NeedsNotificationAccess = 'day1NeedsNotificationAccess';
  static const pinDontMatch = 'pinDontMatch';
  static const day1NotificationAccessReason = 'day1NotificationAccessReason';
  static const twoFA = 'twoFA';
  static const enter2FACode = 'enter2FACode';
  static const resendVia = 'resendVia';
  static const enterD1Pin = 'enterD1Pin';
  static const enter4DigitCode = 'enter4DigitCode';
  static const welcomeBack = 'welcomeBack';
  static const welcomeBackUser = 'welcomeBackUser';
  static const forgotPassword = 'forgotPassword';
  static const notYourAccount = 'notYourAccount';
  static const money = 'money';
  static const your_balance = 'your_balance';
  static const my_account = 'my_account';
  static const fund_wallet = 'fund_wallet';
  static const send_money = 'send_money';
  static const amount_to_add_to_wallet = 'amount_to_add_to_wallet';
  static const conversion_fee = 'conversion_fee';
  static const send_fee = 'send_fee';
  static const amount_we_will_convert = 'amount_we_will_convert';
  static const todays_rate = 'todays_rate';
  static const amount_in_denom = 'amount_in_denom';
  static const add_payment_method = 'add_payment_method';
  static const credit_cards_debit_cards_are_supported = 'credit_cards_debit_cards_are_supported';
  static const kindly_complete_your_verification_to_perform_transactions = 'kindly_complete_your_verification_to_perform_transactions';
  static const your_account_verification_is_still_pending_so_you = 'your_account_verification_is_still_pending_so_you';
  static const finish_account_setup = 'finish_account_setup';
  static const nationality = 'nationality';
  static const selectNationality = 'selectNationality';
  static const selectNation4Personalisation = 'selectNation4Personalisation';
  static const nonNigerian = 'nonNigerian';
  static const forTravellers = 'forTravellers';
  static const aNigerian = 'aNigerian';
  static const forResidentsFreqTravellers = 'forResidentsFreqTravellers';
  static const selfieCaptured = 'selfieCaptured';
  static const selfieCapturedSuccessfully = 'selfieCapturedSuccessfully';
  static const verificationRequestSubmitted = 'verificationRequestSubmitted';
  static const thanksForPatienceYouWillBeNotified = 'thanksForPatienceYouWillBeNotified';
  static const kycApproved = 'kycApproved';
  static const documentsHavBeenVerified = 'documentsHavBeenVerified';
  static const readyToRoll = 'readyToRoll';
  static const bankDetailsReady = 'bankDetailsReady';
  static const ok = 'ok';
  static const detailsUnderReview = 'detailsUnderReview';
  static const youWillBeNotifiedAfterReview = 'youWillBeNotifiedAfterReview';
  static const stepOfSteps = 'stepOfSteps';
  static const takeAPhotoWithId = 'takeAPhotoWithId';
  static const loookInCamera = 'loookInCamera';
  static const useThis = 'useThis';
  static const retakePhoto = 'retakePhoto';
  static const takePhoto = 'takePhoto';
  static const day1NeedCamAccess = 'day1NeedCamAccess';
  static const pleaseGrantCamAccess = 'pleaseGrantCamAccess';
  static const selfieCheck = 'selfieCheck';
  static const check = 'check';
  static const livenessCheck = 'livenessCheck';
  static const youAreAlmostThere = 'youAreAlmostThere';
  static const steps = 'steps';
  static const stayInLitPlace = 'stayInLitPlace';
  static const removeGlassesEtAl = 'removeGlassesEtAl';
  static const holdPhoneSteady = 'holdPhoneSteady';
  static const visaInfo = 'visaInfo';
  static const pleaseProvideVisaInfo = 'pleaseProvideVisaInfo';
  static const visaNumber = 'visaNumber';
  static const enterVisaNumber = 'enterVisaNumber';
  static const visaType = 'visaType';
  static const visaIssueDate = 'visaIssueDate';
  static const visaExpireDate = 'visaExpireDate';
  static const passportInfo = 'passportInfo';
  static const provideInfoOnYourAmericanPass = 'provideInfoOnYourAmericanPass';
  static const passIssuingCountry = 'passIssuingCountry';
  static const unitedStates = 'unitedStates';
  static const passNumber = 'passNumber';
  static const enterPassNumber = 'enterPassNumber';
  static const expirationDate = 'expirationDate';
  static const enterExpirationDate = 'enterExpirationDate';
  static const fullName = 'fullName';
  static const enterFullName = 'enterFullName';
  static const onlyAmericanPassportsSupported = 'onlyAmericanPassportsSupported';
  static const personalInfoCap = 'personalInfoCap';
  static const tellUsAbtU = 'tellUsAbtU';
  static const pleaseProvideInfoAsInYourId = 'pleaseProvideInfoAsInYourId';
  static const bvnNum = 'bvnNum';
  static const ninNum = 'ninNum';
  static const enterBvnNum = 'enterBvnNum';
  static const enterNinNum = 'enterNinNum';
  static const nigerianAddress = 'nigerianAddress';
  static const enterNigerianAddress = 'enterNigerianAddress';
  static const weOnlyHaveAccessTo = 'weOnlyHaveAccessTo';
  static const bvnDoesntGiveAccessTo = 'bvnDoesntGiveAccessTo';
  static const dialToCheckBvn = 'dialToCheckBvn';
  static const kycSetup = 'kycSetup';
  static const completKycInQuickSteps = 'completKycInQuickSteps';
  static const passVerification = 'passVerification';
  static const provideRequiredPassData = 'provideRequiredPassData';
  static const addVisaInfo = 'addVisaInfo';
  static const weUseSelfieToVerifyIdentity = 'weUseSelfieToVerifyIdentity';
  static const picturesGoIn90Days = 'picturesGoIn90Days';
  static const bvnAndNinCheck = 'bvnAndNinCheck';
  static const kycVerification = 'kycVerification';
  static const weRecommendThat = 'weRecommendThat';
  static const enterFullnameLikePassport = 'enterFullnameLikePassport';
  static const visaReferenceNum = 'visaReferenceNum';
  static const enterVisaReferenceNum = 'enterVisaReferenceNum';
  static const verifyingIdentity = 'verifyingIdentity';
  static const acctStatement = 'acctStatement';
  static const proofOfAcct = 'proofOfAcct';
  static const acctName = 'acctName';
  static const acctNumber = 'acctNumber';
  static const shareAcctDetails = 'shareAcctDetails';
  static const shareVia = 'shareVia';
  static const downloadAcctStatement = 'downloadAcctStatement';
  static const downloadProofOfAcct = 'downloadProofOfAcct';
  static const getUrProofOfAcct = 'getUrProofOfAcct';
  static const downloadPoaSecurely = 'downloadPoaSecurely';
  static const yourPoaData = 'yourPoaData';
  static const formatType = 'formatType';
  static const startDate = 'startDate';
  static const endDate = 'endDate';
  static const invalidCardNumber = 'invalidCardNumber';
  static const invalidCvv = 'invalidCvv';
  static const paymentMethod = 'paymentMethod';
  static const addNewCard = 'addNewCard';
  static const addNewPaymentMethod = 'addNewPaymentMethod';
  static const walletFundingMethod = 'walletFundingMethod';
  static const addCard = 'addCard';
  static const payWithStripe = 'payWithStripe';
  static const fundWithCard = 'fundWithCard';
  static const fundWithPaypal = 'fundWithPaypal';
  static const payWithCard = 'payWithCard';
  static const addCardDetails = 'addCardDetails';
  static const cardNum = 'cardNum';
  static const expiryDate = 'expiryDate';
  static const securityCode = 'securityCode';
  static const addDebitCard = 'addDebitCard';
  static const saveCardForFuture = 'saveCardForFuture';
  static const redirectionMessage = 'redirectionMessage';
  static const youWillBeRedirectedToCardIssuer = 'youWillBeRedirectedToCardIssuer';
  static const proceed = 'proceed';
  static const cardAddedSuccessfully = 'cardAddedSuccessfully';
  static const welldoneUser = 'welldoneUser';
  static const returnToFundWallet = 'returnToFundWallet';
  static const availablePaymentMethods = 'availablePaymentMethods';
  static const creditCard = 'creditCard';
  static const amountAddedToWallet = 'amountAddedToWallet';
  static const totalAmunt = 'totalAmunt';
  static const addAmount = 'addAmount';
  static const confirmAmount = 'confirmAmount';
  static const youAddedAmountToWallet = 'youAddedAmountToWallet';
  static const amountToSend = 'amountToSend';
  static const estimatedDollarValue = 'estimatedDollarValue';
  static const accountDetails = 'accountDetails';
  static const selectAcctToSendMoney = 'selectAcctToSendMoney';
  static const sendMoneyToFavs = 'sendMoneyToFavs';
  static const recent = 'recent';
  static const searchByNameOrAcct = 'searchByNameOrAcct';
  static const viewAll = 'viewAll';
  static const transfer = 'transfer';
  static const searchByName = 'searchByName';
  static const matchedBank = 'matchedBank';
  static const verifyingAccountDetails = 'verifyingAccountDetails';
  static const reviewDetails = 'reviewDetails';
  static const exactAmoountWillReachRecipient = 'exactAmoountWillReachRecipient';
  static const accountName = 'accountName';
  static const bankName = 'bankName';
  static const accountNumber = 'accountNumber';
  static const enterAccountNumber = 'enterAccountNumber';
  static const narration = 'narration';
  static const transferNarration = 'transferNarration';
  static const sendAmount = 'sendAmount';
  static const enterPin = 'enterPin';
  static const completeTransferByPinEntry = 'completeTransferByPinEntry';
  static const paymentSuccessful = 'paymentSuccessful';
  static const youSentFundToUserSuccesfully = 'youSentFundToUserSuccesfully';
  static const saveBeneficiary = 'saveBeneficiary';
  static const shareReceipt = 'shareReceipt';
  static const transactionReceipt = 'transactionReceipt';
  static const transactionDetails = 'transactionDetails';
  static const downloadAsPdf = 'downloadAsPdf';
  static const downloadAsImage = 'downloadAsImage';
  static const status = 'status';
  static const date = 'date';
  static const transactionType = 'transactionType';
  static const senderName = 'senderName';
  static const receiverName = 'receiverName';
  static const receiverAcctNum = 'receiverAcctNum';
  static const refNum = 'refNum';
  static const selectBank = 'selectBank';
  static const paymentWillArriveImmediately = 'paymentWillArriveImmediately';
  static const receipt = 'receipt';
  static const success = 'success';
  static const all = 'all';
  static const favourites = 'favourites';
  static const sureYouWannaDeleteBeneficiary = 'sureYouWannaDeleteBeneficiary';
  static const bankNotFound = 'bankNotFound';
  static const recipientNotFound = 'recipientNotFound';
  static const selectTier = 'selectTier';
  static const tier = 'tier';
  static const originalPricing = 'originalPricing';
  static const pricing = 'pricing';
  static const pricePerMonth = 'pricePerMonth';
  static const pickupInstructions = 'pickupInstructions';
  static const aRepWillMeetYouAtAirport = 'aRepWillMeetYouAtAirport';
  static const contactInfo = 'contactInfo';
  static const contactUsForAssistance = 'contactUsForAssistance';
  static const emergencyAssistance = 'emergencyAssistance';
  static const contactUsInCaseOfEmergency = 'contactUsInCaseOfEmergency';
  static const securityTips = 'securityTips';
  static const securityTipsDetails = 'securityTipsDetails';
  static const usageInformation = 'usageInformation';
  static const usageInformationDetails = 'usageInformationDetails';
  static const getYourPlan = 'getYourPlan';
  static const active = 'active';
  static const tierFeatures = 'tierFeatures';
  static const basic = 'basic';
  static const standard = 'standard';
  static const premium = 'premium';
  static const editProfile = 'editProfile';
  static const inviteFriends = 'inviteFriends';
  static const getAmount = 'getAmount';
  static const accountVerification = 'accountVerification';
  static const stepVerified = 'stepVerified';
  static const changeYourPin = 'changeYourPin';
  static const yourDevices = 'yourDevices';
  static const firstName = 'firstName';
  static const mobileNumber = 'mobileNumber';
  static const day1AcctNumber = 'day1AcctNumber';
  static const accountTier = 'accountTier';
  static const upgrade = 'upgrade';
  static const myProfile = 'myProfile';
  static const emailVerification = 'emailVerification';
  static const idDocument = 'idDocument';
  static const enhancedLoginSecurity = 'enhancedLoginSecurity';
  static const addExtraAuthStep = 'addExtraAuthStep';
  static const setUpNow = 'setUpNow';
  static const aVerificationCodeWillBeSentToYourNumber = 'aVerificationCodeWillBeSentToYourNumber';
  static const your2faSetupIsDone = 'your2faSetupIsDone';
  static const twoFaIsOn = 'twoFaIsOn';
  static const yourWillReceiveCodesByText = 'yourWillReceiveCodesByText';
  static const turnOff = 'turnOff';
  static const inviteAndEarn = 'inviteAndEarn';
  static const earnAmountByReferral = 'earnAmountByReferral';
  static const shareCode = 'shareCode';
  static const howDoesItWork = 'howDoesItWork';
  static const shareYourCode = 'shareYourCode';
  static const startBySharingYourCode = 'startBySharingYourCode';
  static const friendsSignUp = 'friendsSignUp';
  static const whenFriendsSignupTheySelectTier = 'whenFriendsSignupTheySelectTier';
  static const earnRewards = 'earnRewards';
  static const youGetAmountAfterTheySelectTier = 'youGetAmountAfterTheySelectTier';
  static const totalEarnings = 'totalEarnings';
  static const referralRecord = 'referralRecord';
  static const shareNEarnAmount = 'shareNEarnAmount';
  static const viewProfile = 'viewProfile';
  static const passwordUpdated = 'passwordUpdated';
  static const acctVerificationStatus = 'acctVerificationStatus';
  static const youHaveCompletedVerificationSteps = 'youHaveCompletedVerificationSteps';
  static const verified = 'verified';
  static const unVerified = 'unVerified';
  static const enterCurrentPin = 'enterCurrentPin';
  static const enterCurrent4DigitPin = 'enterCurrent4DigitPin';
  static const device = 'device';
  static const inActive = 'inActive';
  static const youHaveCountDevices = 'youHaveCountDevices';
  static const howCanWeHelp = 'howCanWeHelp';
  static const help = 'help';
  static const message = 'message';
  static const sendUsAMessage = 'sendUsAMessage';
  static const searchForHelp = 'searchForHelp';
  static const whatIsServiceFee = 'whatIsServiceFee';
  static const supportChat = 'supportChat';
  static const howDoIWithDrawFunds = 'howDoIWithDrawFunds';
  static const refundPolicy = 'refundPolicy';
  static const letsGetYourCard = 'letsGetYourCard';
  static const getYourDay1CardAsYouLand = 'getYourDay1CardAsYouLand';
  static const orderYourCard = 'orderYourCard';
  static const yourFinancialFreedomInNigeria = 'yourFinancialFreedomInNigeria';
  static const loremIpsum = 'loremIpsum';
  static const startSpendingInstantly = 'startSpendingInstantly';
  static const getDiscounts = 'getDiscounts';
  static const getYourCard = 'getYourCard';
  static const heresWhatToDoNext = 'heresWhatToDoNext';
  static const travelNPickupCard = 'travelNPickupCard';
  static const setupYourPin = 'setupYourPin';
  static const payForCard = 'payForCard';
  static const setupYourCardPin = 'setupYourCardPin';
  static const setA4DigitCardPin = 'setA4DigitCardPin';
  static const payAmount = 'payAmount';
  static const cardRequest = 'cardRequest';
  static const payment = 'payment';
  static const cardCostBreakdown = 'cardCostBreakdown';
  static const cardCost = 'cardCost';
  static const cardDelivery = 'cardDelivery';
  static const processingFee = 'processingFee';
  static const freeDelivery = 'freeDelivery';
  static const addMoneyToSpendLater = 'addMoneyToSpendLater';
  static const allDone = 'allDone';
  static const congratsCardIsActivated = 'congratsCardIsActivated';
  static const goBackHome = 'goBackHome';
  static const goToMyCard = 'goToMyCard';
  static const card = 'card';
  static const thanksForYourSubmission = 'thanksForYourSubmission';
  static const safeJourney = 'safeJourney';
  static const expectedTimeOnNigeriaArrival = 'expectedTimeOnNigeriaArrival';
  static const arrivalAirport = 'arrivalAirport';
  static const flightDetails = 'flightDetails';
  static const enterFlightDetails = 'enterFlightDetails';
  static const alternativeContactNumber = 'alternativeContactNumber';
  static const cardPickupReminder = 'cardPickupReminder';
  static const yourCardAwaitsYou = 'yourCardAwaitsYou';
  static const numDays = 'numDays';
  static const moreInfo = 'moreInfo';
  static const activate = 'activate';
  static const viewDetails = 'viewDetails';
  static const freeze = 'freeze';
  static const setting = 'setting';
  static const cardName = 'cardName';
  static const cardStatus = 'cardStatus';
  static const shareDetails = 'shareDetails';
  static const chooseYourPin = 'chooseYourPin';
  static const cardSettings = 'cardSettings';
  static const manageCard = 'manageCard';
  static const changePin = 'changePin';
  static const cardReplacement = 'cardReplacement';
  static const terminateCard = 'terminateCard';
  static const virtualCard = 'virtualCard';
  static const comingSoon = 'comingSoon';
  static const cardIsFrozen = 'cardIsFrozen';
  static const unFreeze = 'unFreeze';
  static const freezeCard = 'freezeCard';
  static const transactionsWillBeDisables = 'transactionsWillBeDisables';
  static const cardOnItsWay = 'cardOnItsWay';
  static const yourCardWillBeAvailableON = 'yourCardWillBeAvailableON';
  static const hideDetails = 'hideDetails';
  static const frozen = 'frozen';
  static const purpose = 'purpose';
  static const purposeOptions = 'purposeOptions';
  static const deliveryMethod = 'deliveryMethod';
  static const selectDeliveryMethod = 'selectDeliveryMethod';
  static const selectYourPreferredCardDeliveryMethod = 'selectYourPreferredCardDeliveryMethod';
  static const airportPickup = 'airportPickup';
  static const collectCardAtAirport = 'collectCardAtAirport';
  static const placeDelivery = 'placeDelivery';
  static const receiveCardAtYourPlace = 'receiveCardAtYourPlace';
  static const getYourCardAtAirport = 'getYourCardAtAirport';
  static const selectDateOfArrival = 'selectDateOfArrival';
  static const selectPickupAirport = 'selectPickupAirport';
  static const provideTravelInformation = 'provideTravelInformation';
  static const placeOfStayDelivery = 'placeOfStayDelivery';
  static const getUrCardAtPlaceOfStay = 'getUrCardAtPlaceOfStay';
  static const providePlaceOfStayInfo = 'providePlaceOfStayInfo';
  static const dateOfArrival = 'dateOfArrival';
  static const stateOfArrival = 'stateOfArrival';
  static const pickupAirport = 'pickupAirport';
  static const expectedArrivalTimeInNigeria = 'expectedArrivalTimeInNigeria';
  static const placeOfStay = 'placeOfStay';
  static const placeOfStayAddress = 'placeOfStayAddress';
  static const selectArrivalState = 'selectArrivalState';
  static const selectYourPickupAirport = 'selectYourPickupAirport';
  static const enterYourFlightNumber = 'enterYourFlightNumber';
  static const enterPlaceOfStay = 'enterPlaceOfStay';
  static const enterPlaceOfStayAddress = 'enterPlaceOfStayAddress';
  static const travelNPickupDetails = 'travelNPickupDetails';
  static const travleNPickupDetailsRider = 'travleNPickupDetailsRider';
  static const travelNDeliveryDetails = 'travelNDeliveryDetails';
  static const travelNDeliveryDetailsRider = 'travelNDeliveryDetailsRider';
  static const flightNumber = 'flightNumber';
  static const selectTime = 'selectTime';
  static const cardDeliveryReminer = 'cardDeliveryReminer';
  static const pleaseHaveYourPassportAtPickup = 'pleaseHaveYourPassportAtPickup';
  static const pleaseHavePassportAtDelivery = 'pleaseHavePassportAtDelivery';
  static const yourCardWillBeReadyOnArrival = 'yourCardWillBeReadyOnArrival';
  static const otpWillBeSentForPickup = 'otpWillBeSentForPickup';
  static const cardWillBDeliveredAtPlaceOfStay = 'cardWillBDeliveredAtPlaceOfStay';
  static const otpWillBeSentForDelivery = 'otpWillBeSentForDelivery';
  static const transactions = 'transactions';
  static const statistics = 'statistics';
  static const summary = 'summary';
  static const cardPayments = 'cardPayments';
  static const onlinePayments = 'onlinePayments';
  static const distribution = 'distribution';
  static const chart = 'chart';
  static const dailyExpenses = 'dailyExpenses';
  static const expenses = 'expenses';
  static const inflow = 'inflow';
  static const shareTransactionReceipt = 'shareTransactionReceipt';
  static const reportAProblem = 'reportAProblem';
  static const transferToUser = 'transferToUser';
  static const dateTime = 'dateTime';
  static const transactionHistory = 'transactionHistory';
  static const noTransactionRecord = 'noTransactionRecord';
  static const youHaveNoTransactions = 'youHaveNoTransactions';
  static const noTransactionsOfType = 'noTransactionsOfType';
  static const noTransactionsFromPeriod = 'noTransactionsFromPeriod';
  static const noTransactionsOfTypeFromPeriod = 'noTransactionsOfTypeFromPeriod';
  static const in_ = 'in_';
  static const out = 'out';
  static const inAmount = 'inAmount';
  static const outAmount = 'outAmount';
  static const successful = 'successful';
  static const panding = 'panding';
  static const failed = 'failed';
  static const reversed = 'reversed';
  static const walletFund = 'walletFund';
  static const lastNumDays = 'lastNumDays';
  static const customRange = 'customRange';
  static const confirm = 'confirm';
  static const chooseDateRange = 'chooseDateRange';
  static const lastNumWeeks = 'lastNumWeeks';
  static const lastNumMonths = 'lastNumMonths';
  static const lastMonth = 'lastMonth';
  static const lastQuarter = 'lastQuarter';
  static const periodShouldBelessThanYear = 'periodShouldBelessThanYear';
  static const confirmEmailAddress = 'confirmEmailAddress';
  static const weHaveSentOtpToYourEmail = 'weHaveSentOtpToYourEmail';
  static const resendingOtp = 'resendingOtp';
  static const otpResent = 'otpResent';
  static const noAccountRegister = 'noAccountRegister';
  static const verifyEmail = 'verifyEmail';
  static const verificationEmailSent = 'verificationEmailSent';

}
