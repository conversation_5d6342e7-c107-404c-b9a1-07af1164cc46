import 'package:day1/core/core.dart';
import 'package:flutter/material.dart';

class ScreenShotWidget extends StatelessWidget {
  final ScreenShotService service;
  final Widget child;

  const ScreenShotWidget({
    required this.service,
    required this.child,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return RepaintBoundary(
      key: service.screenShotKey,
      child: child,
    );
  }
}
