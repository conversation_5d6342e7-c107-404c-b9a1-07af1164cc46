import 'package:day1/core/core.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

abstract class AppTheme {
  Map<TargetPlatform, PageTransitionsBuilder> get defaultTransitions;
  CupertinoThemeData? get darkThemeIos;
  CupertinoThemeData? get lightThemeIos;
  ThemeData get darkTheme;
  ThemeData get lightTheme;
  
  AppInputStyles inputStyle(BuildContext context);
  AppShadows shadows(BuildContext context);
  AppGradients gradients(BuildContext context);
  AppTextStyles textStyles(BuildContext context);

  Color get scaffoldBgColor;
  Color get shadowColor;
  Color get barrierColor;
  Color get dividerColor;
  Color get switchTrackColor;
  Color get switchTrackInactiveColor;
  Color get switchThumbInactiveColor;
  Color get switchThumbActiveColor;
  Color get inputBgColor;
  Color get inputBorderColor;
  Color get disabledInputBorderColor;
  Color get activeInputBorderColor;
  Color get successCardColor;
  Color get disabledInputFillColor;
  Color get textColor;
  Color get highlightedTextColor;
  Color get secondaryTextColor;
  Color get hintTextColor;
  Color get inactiveIndicatorColor;
  Color get cardColor;
  Color get cardBorderColor;
  Color get pillTextColor;
  Color get pillColor;
  String get font;
  Color get errorTextColor;
  Color get activeIndicatorBorderColor;
  Color get activeIndicatorColor;
  Color get errorColor;
  Color get inActiveIndicatorBorderColor;
  Color get inActiveIndicatorColor;
  Color get disabledBtnColor;
  Color get disabledBtntextColor;
  Color get outlineBtnBorderColor;
  Color get outlineBtnAltBorderColor;
  double get outlineBtnDisabledOpacity;
  double get outlineBtnAltDisabledOpacity;
  Color get outlineBtnBgColor;
  Color get outlineBtnAltBgColor;
  Color get outlineBtnFocusedBgColor;
  Color get outlineBtnAltFocusedBgColor;
  Color get outlineBtnTextColor;
  Color get raisedBtnBorderColor;
  Color get raisedBtnDisabledBorderColor;
  Color get raisedBtnBgColor;
  Color get raisedBtnBBgColor;
  Color get raisedBtnTextColor;
  Color get raisedBtnCBgColor;
  Color get raisedBtnFocusedBgColor;
  Color get raisedBtnBFocusedBgColor;
  Color get raisedBtnCFocusedBgColor;
  double get raisedBtnDisabledOpacity;
  Color get textBtnTextColor;
  Color get textBtnFocusedTextColor;
  Color get textBtnDisabledTextColor;
  Color get appleBtnTextColor;
  Color get neutralTextColor;
  Color get appleBtnColor;
  Color get appleBtnBorderColor;
  Color get googleBtnTextColor;
  Color get googleBtnColor;
  Color get googleBtnBorderColor;
  String get secondaryFont;
  Color get transparent;
  Color get errorInputBorderColor;
  Color get highlightedIconColor;
  Color get iconColor;
  Color get errorNotificationBackgroundColor;
  Color get infoNotificationBackgroundColor;
  Color get warningNotificationBackgroundColor;
  Color get notificationBackgroundColor;
  Color get infoNotificationTextColor;
  Color get warningNotificationTextColor;
  Color get notificationTextColor;
  Color get errorNotificationTextColor;
  Color get infoNotificationBorderColor;
  Color get warningNotificationBorderColor;
  Color get notificationBorderColor;
  Color get errorNotificationBorderColor;
}