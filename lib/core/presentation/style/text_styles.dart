import 'package:flutter/widgets.dart';

abstract class AppTextStyles {
  TextStyle d0({
    TextDecoration? decoration,
    Color? color,
  });

  TextStyle d1({
    TextDecoration? decoration,
    Color? color,
  });

  TextStyle d2({
    TextDecoration? decoration,
    FontWeight? weight,
    Color? color,
  });

  TextStyle d3({
    TextDecoration? decoration,
    FontWeight? weight,
    Color? color,
  });

  TextStyle d4({
    TextDecoration? decoration,
    FontWeight? weight,
    Color? color,
  });

  TextStyle b5({
    TextDecoration? decoration,
    FontWeight? weight,
    Color? color,
  });

  TextStyle b4({
    TextDecoration? decoration,
    FontWeight? weight,
    Color? color,
  });

  TextStyle b3({
    TextDecoration? decoration,
    FontWeight? weight,
    Color? color,
  });

  TextStyle b2({
    TextDecoration? decoration,
    FontWeight? weight,
    Color? color,
  });

  TextStyle b1({
    TextDecoration? decoration,
    FontWeight? weight,
    Color? color,
  });

  TextStyle h6({TextDecoration? decoration, Color? color});

  TextStyle h5({TextDecoration? decoration, Color? color});

  TextStyle h4({TextDecoration? decoration, Color? color});

  TextStyle h3({TextDecoration? decoration, Color? color});

  TextStyle h2({TextDecoration? decoration, Color? color});

  TextStyle h1({TextDecoration? decoration, Color? color});

  TextStyle hintText({
    TextDecoration? decoration,
    FontWeight? weight,
  });

  TextStyle labelText({TextDecoration? decoration, FontWeight? weight});

  TextStyle helperText({TextDecoration? decoration, FontWeight? weight});

  TextStyle errorHelperText({TextDecoration? decoration, FontWeight? weight});

  TextStyle raisedBtn();

  TextStyle raisedBtnMd();

  TextStyle raisedBtnSm();

  TextStyle textBtn({
    TextDecoration? decoration,
    double? height,
    FontWeight? weight,
  });

  TextStyle textBtnMd({
    TextDecoration? decoration,
    double? height,
    FontWeight? weight,
  });

  TextStyle textBtnSm({
    TextDecoration? decoration,
    double? height,
    FontWeight? weight,
  });
}
