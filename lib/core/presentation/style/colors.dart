import 'package:flutter/material.dart';

class ColorSet extends Color {
  final int? _dark;

  const ColorSet(super.value, [this._dark]);

  int get inverted {
    return ((value >> 24) & 0xff) |
        ((value << 8) & 0xff0000) |
        ((value >> 8) & 0xff00) |
        ((value << 24) & 0xff000000);
  }

  ColorSet get dark => ColorSet(_dark ?? inverted);

  ColorSet forTheme(bool inDarkMode) {
    return switch (inDarkMode) {
      true => dark,
      _ => this,
    };
  }
}

abstract class AppGradients {
  LinearGradient get referralGradient;
  LinearGradient get supportGradient;
}

abstract class AppShadows {
  List<BoxShadow> get xxSmall;

  List<BoxShadow> get xSmall;

  List<BoxShadow> get small;

  List<BoxShadow> get medium;

  List<BoxShadow> get large;

  List<BoxShadow> get xLarge;

  List<BoxShadow> get xxLarge;

  List<BoxShadow> get bottomNavShaddow;
}
