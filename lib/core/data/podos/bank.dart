import 'package:day1/core/core.dart';
import 'package:equatable/equatable.dart';

class Bank extends Equatable {
  final String name;
  final String code;
  final String? logo;

  const Bank({
    required this.name,
    required this.code,
    this.logo,
  });

  String? get initials => AppHelpers.getInitials(name);
  String? get accronym => AppHelpers.getAccronym(name);

  factory Bank.fromJson(Map json) => Bank(
        name: json["bank_name"] ?? "",
        code: json["code"] ?? "",
        logo: json["logo"],
      );

  @override
  List<Object?> get props => [code, name.lower];
}
