import 'dart:math';
import 'package:day1/core/core.dart';
import 'package:flutter/material.dart';

extension NumExtension on num {
  String get formattedCurrency {
    return AppTextFormatter.formatCurrency(this);
  }

  String asCurrency(String symbol) {
    return AppTextFormatter.formatCurrency(this, symbol: symbol);
  }

  String asCurrencyShort(String symbol) {
    return AppTextFormatter.formatCurrencyShort(this, symbol: symbol);
  }

  String get maskedCurrency {
    return AppTextFormatter.maskedCurrency(this);
  }

  String get formattedNumber {
    return AppTextFormatter.formatNumber(toString());
  }

  String get formattedNumberLong {
    return AppTextFormatter.formatNumberLong(toString());
  }

  double get asDeg {
    return this * (180 / pi);
  }

  Duration get microDuration {
    return Duration(microseconds: toInt());
  }

  Duration get milliDuration {
    return Duration(milliseconds: toInt());
  }

  Duration get secondDuration {
    return Duration(seconds: toInt());
  }

  Duration get minuteDuration {
    return Duration(minutes: toInt());
  }

  Duration get hourDuration {
    return Duration(hours: toInt());
  }

  Duration get dayDuration {
    return Duration(days: toInt());
  }

  double get asCents => this * 100;
  double get asDollars => this / 100;
}

extension IntExtension on int {
  String get asMonthName {
    return switch (this) {
      DateTime.january => LocaleKeys.january.tr(),
      DateTime.february => LocaleKeys.february.tr(),
      DateTime.march => LocaleKeys.march.tr(),
      DateTime.april => LocaleKeys.april.tr(),
      DateTime.may => LocaleKeys.may.tr(),
      DateTime.june => LocaleKeys.june.tr(),
      DateTime.july => LocaleKeys.july.tr(),
      DateTime.august => LocaleKeys.august.tr(),
      DateTime.september => LocaleKeys.september.tr(),
      DateTime.october => LocaleKeys.october.tr(),
      DateTime.november => LocaleKeys.november.tr(),
      DateTime.december => LocaleKeys.december.tr(),
      _ => "",
    };
  }

  String get asDurationString {
    final duration = "$secondDuration".split(".").first;

    final [hours, minutes, seconds] =
        duration.split(":").mapList((it) => int.tryParse(it) ?? 0);

    final hoursText = hours < 1 ? "" : "${hours < 10 ? '0' : ''}$hours:";
    final minutesText = "${minutes < 10 ? '0' : ''}$minutes:";
    final secondsText = "${seconds < 10 ? '0' : ''}$seconds";

    return "$hoursText$minutesText$secondsText";
  }

  String get asHourName {
    final date = TimeOfDay(hour: this, minute: 0).asDate;
    return date.format("hh");
  }

  String get asMinuteName {
    final date = TimeOfDay(hour: 0, minute: this).asDate;
    return date.format("mm");
  }

  List<int> get monthDays {
    int daysInMonth = switch (this) {
      DateTime.february => 29,
      DateTime.september | DateTime.april | DateTime.june | DateTime.november =>
        30,
      _ => 31,
    };
    return List.generate(daysInMonth, (index) => index + 1, growable: false);
  }
}

int randomInt() {
  return Random().nextInt(10000);
}
