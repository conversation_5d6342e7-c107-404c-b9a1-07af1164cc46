class AppHtmlUtil {
  static String getAdvertHtmlTemplate(
    String html, {
    String title = "Advert Template",
  }) {
    return """
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8" />
          <meta name="viewport" content="width=device-width, initial-scale=1" />
          <link href="//fonts.googleapis.com/css?family=Open+Sans:400,700&amp;subset=latin,latin-ext" rel="stylesheet" type="text/css">
          <script src="https://code.jquery.com/jquery-3.6.0.min.js" integrity="sha256-/xUj+3OJU5yExlq6GSYGSHk7tPXikynS7ogEvDej/m4=" crossorigin="anonymous"></script>
          <script src="https://cdnjs.cloudflare.com/ajax/libs/ResponsiveSlides.js/1.55/responsiveslides.min.js" integrity="sha512-xLb7JAM9BNykuzMmlFtHHgQQAwFMiPVf9IhLV6g/IgQInWqxECLrlqBo64ytFPZH7qeZjahD1TOvl/wp8dL6LA==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
          <style>
            :root {
                box-sizing: border-box;
            }

            *,
            ::before,
            ::after {
                box-sizing: border-box;
            }

            body {
              margin: 0;
              padding: 0;
              font-family: 'Open Sans', Tahoma, Arial, sans-serif;
              font-size: 14px;
            }

            img {
              max-width: 100%;
              height: auto;
            }

            .rslides {
              position: relative;
              list-style: none;
              overflow: hidden;
              width: 100%;
              padding: 0;
              margin: 0;
            }

            .rslides li {
              -webkit-backface-visibility: hidden;
              position: absolute;
              display: none;
              width: 100%;
              left: 0;
              top: 0;
            }

            .rslides li:first-child {
              position: relative;
              display: block;
              float: left;
            }

            .rslides img {
              display: block;
              height: auto;
              float: left;
              width: 100%;
              border: 0;
            }
          </style>
        </head>
        <body id="content-container">
          $html
          <script async defer>
            setTimeout(setTitle, 1000)

            function setTitle() {
              document.title = "$title";
            }
          </script>
        </body>
      </html>
    """;
  }

  static String getPageHtmlTemplate(
    String html, {
    required bool inDarkMode,
    String title = "Page Template",
  }) {
    return """
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8" />
          <meta name="viewport" content="width=device-width, initial-scale=1" />
          <link rel="stylesheet" href="https://poslovac.hr/site.css">
          <link rel="preload" as="style" href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700&family=Source+Sans+Pro:wght@300;400;600;700&display=swap">
          <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700&family=Source+Sans+Pro:wght@300;400;600;700&display=swap">
          <link rel="stylesheet" href="https://static.mojposao.hr/brand/poslovachr/${inDarkMode ? 'dark' : 'light'}.css">
          <link rel="stylesheet" href="https://poslovac.hr/_nuxt/entry.7L9YD7Ng.css">
          <link rel="stylesheet" href="https://poslovac.hr/_nuxt/default.C9wCpa59.css">
          <link rel="stylesheet" href="https://poslovac.hr/_nuxt/_slug_.BfJMzmpq.css">
          <link rel="stylesheet" href="https://poslovac.hr/_nuxt/Grid.DvXnhpX4.css">
          <style>
            :root {
                box-sizing: border-box;
            }

            *,
            ::before,
            ::after {
                box-sizing: border-box;
            }

            body {
              margin: 0;
              padding: 0;
              font-family: 'Inter', Tahoma, Arial, sans-serif;
              font-size: 16px;
            }
          </style>
        </head>
        <body id="content-container">
          $html
          <script async defer>
            setTimeout(setTitle, 1000)

            function setTitle() {
              document.title = "$title";
            }
          </script>
        </body>
      </html>
    """;
  }
}
