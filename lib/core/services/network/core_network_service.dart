import 'package:day1/core/core.dart';
import 'package:dio/dio.dart';

class BaseHttpService {
  late Dio http;
  late Dio downloadHttp;
  late Dio tokenHttp;

  Duration get _timeout => 1.minuteDuration;

  BaseHttpService(String baseUrl) {
    http = Dio(
      BaseOptions(
        baseUrl: baseUrl,
        connectTimeout: _timeout,
        receiveTimeout: _timeout,
        headers: {'Accept': "application/json"},
        contentType: "application/json",
      ),
    )..transformer = BackgroundTransformer();
    downloadHttp = Dio(
      BaseOptions(
        connectTimeout: _timeout,
        receiveTimeout: _timeout,
        responseType: ResponseType.bytes,
      ),
    );
    tokenHttp = Dio(
      BaseOptions(
        baseUrl: baseUrl,
        connectTimeout: _timeout,
        receiveTimeout: _timeout,
        headers: {'Accept': "application/json"},
      ),
    )..transformer = BackgroundTransformer();
  }
}
