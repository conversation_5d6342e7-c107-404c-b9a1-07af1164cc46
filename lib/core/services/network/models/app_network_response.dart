class NetworkResponse<T> {
  final T? data;
  final NetworkError? error;

  const NetworkResponse({this.data, this.error});

  bool get hasError => error != null;

  bool get hasData {
    final isNotNull = data != null;
    bool isNotEmpty = true;

    if (data is Iterable) {
      isNotEmpty = (data as Iterable).isNotEmpty;
    }

    if (data is String) {
      isNotEmpty = (data as String).trim().isNotEmpty;
    }

    return isNotEmpty && isNotNull;
  }

  String? get errorMessage {
    return error?.message;
  }
}

class NoResponse {
  const NoResponse();
}

class NetworkError {
  final String message;
  final int statusCode;
  final int code;
  final dynamic error;

  const NetworkError({
    required this.message,
    this.statusCode = 400,
    this.code = 0,
    this.error,
  });
}
