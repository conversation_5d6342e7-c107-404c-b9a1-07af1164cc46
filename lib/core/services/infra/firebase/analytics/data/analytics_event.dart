import 'package:day1/day1.dart';

enum AppEvent {
  /// NAVIGATION EVENTS
  viewedDrawer("OPENED DRAWER"),
  viewedEndDrawer("OPENED END DRAWER"),
  viewed<PERSON>ro<PERSON>le<PERSON><PERSON>("OPENED PROFILE EDIT FORM"),
  viewedCVScreen("VIEWED CV DETAILS EDIT SCREEN"),
  viewedContactInfoScreen("VIEWED CONTACT INFO SCREEN"),
  viewedTermsAndConditionsScreen("VIEWED TERMS AND CONDITIONS SCREEN"),
  viewedPrivacyPolicyScreen("VIEWED PRIVACY POLICY SCREEN"),
  viewedEmailUpdateScreen("VIEWED EMAIL UPDATE SCREEN"),
  viewedHomeScreen("VIEWED HOME SCREEN"),
  viewedJobListScreen("VIEWED JOB LIST SCREEN"),
  viewedCVPreviewScreen("VIEWED CV DETAILS PREVIEW SCREEN"),
  printedCV("PRINTED CV"),
  viewedApplicationsScreen("VIEWED ALL APPLICATIONS SCREEN"),
  viewedSavedJobsScreen("VIEWED SAVED JOBS SCREEN"),
  viewedDocumentsScreen("VIEWED DOCUMENTS SCREEN"),
  viewedNotificationsScreen("VIEWED NOTIFICATION SETTINGS SCREEN"),
  viewedProfileSettingsScreen("VIEWED PROFILE SETTINGS SCREEN"),
  viewedProfileDeleteScreen("VIEWED PROFILE DELETE SCREEN"),
  viewedEmployersListScreen("VIEWED ORGANISATION LIST SCREEN"),
  viewedAllPositionsScreen("VIEWED POSITION FILTERS LIST SCREEN"),
  viewedAllLocactionScreen("VIEWED LOCATION FILTERS LIST SCREEN"),
  viewedFilterModal("VIEWED FILTER MODAL"),
  viewedAdDetailsScreen("VIEWED ADVERT DETAILS SCREEN"),
  viewedJobApplicationScreen("VIEWED APPLICATION FORM SCREEN"),
  viewedJobApplicationDetails("VIEWED APPLICATION DETAIL MODAL"),
  viewedJobApplicationQuestionnaireScreen(
      "VIEWED APPLICATION QUESTIONNAIRE SCREEN"),
  viewedJobApplicationSummaryScreen("VIEWED APPLICATION SUMMARY SCREEN"),
  viewedJobApplicationSubmissionScreen("VIEWED APPLICATION SUBMIT SCREEN"),
  viewedJobApplicationSuccessScreen("VIEWED APPLICATION SUCCESS SCREEN"),
  viewedAccountCreateScreen("VIEWED ACCOUNT CREATE SCREEN"),

  /// BROWSER EVENTS
  openedLink("OPENED LINK"),
  openedUkraineJobsLink("OPENED UKRAINE JOBS LINK"),
  openedSalaryCalculatorLink("OPENED SALARY CALCULATOR LINK"),
  openedNewsLink("OPENED TIPS LINK"),
  openedEmployersLink("OPENED EMPLOYERS LINK"),
  openedHelpLink("OPENED HELP_SUPPORT LINK"),
  openedAboutUsLink("OPENED ABOUT US LINK"),
  openedCareersLink("OPENED CAREERS LINK"),
  openedPrivacyPolicyLink("OPENED PRIVACY POLICY LINK"),
  openedTnCLink("OPENED TERMS OF USE LINK"),

  /// SESSION EVENTS
  loggedIn("LOGGED IN"),
  loggedOut("LOGGED OUT"),

  /// SWITCH EVENTS
  switchedTheme("SWITCHED THEME"),
  switchedLanguage("SWITCHED THEME"),

  /// CREATE EVERNTS
  createdccount("CREATED NEW ACCOUNT"),

  /// SHARE EVENTS
  sharedJobAdvert("SHARED JOB ADVERT"),

  /// TOOGLE EVENTS
  toggledSearchSubscription("TOGGLED SEARCH SUBSCRIPTION"),
  toggledNewJobs("TOGGLED FOR NEW JOBS"),
  toggledCvVisibility("TOGGLED CV VISIBILITY"),
  toggledAppliedPositionNotification("TOGGLED APPLIED POSITION NOTIFS"),
  toggledNewsletterNotification("TOGGLED NEWS LETTER NOTIFS"),
  toggledAdInviteNotification("TOGGLED AD INVITATION NOTIFS"),
  toggledReminderNotification("TOGGLED INCOMPLETE APPLICATION NOTIFS"),
  toggledSubmenuNotification("TOGGLED SAVED JOBS SUB_MENU NOTIFS"),
  toggledEmailNotification("TOGGLED PREVIOUS SEARCH EMAIL NOTIFS"),
  toggledJobBookmark("TOGGLED JOB BOOKMARK"),
  toggleRememberMe("CLICKED REMEMEMBER ME BUTTON"),

  /// SELECT EVENTS
  selectedCV("SELECTED A CV"),
  selectedCoverLetter("SELECTED A COVER LETTER"),
  selectedDocument("SELECTED A DOCUMENT"),
  selectedDocumentFile("SELECTED DOCUMENT FROM DISK"),
  selectedImageFile("SELECTED IMAgE FROM DISK"),

  // UPLOAD EVENTS
  uploadedImage("UPLOADED AN IMAGE"),
  uploadedDocument("UPLOADED A DOCUMENT"),

  // REMOVAL EVENTS
  removedDocument("REMOVED DOCUMENT"),
  removedJob("REMOVED JOB"),
  removedEducation("REMOVED EDUCATION"),
  removedLanguage("REMOVED LANGUAGE"),
  removedSkill("REMOVED SKILL"),
  removedProject("REMOVED PROJECT"),
  removedAward("REMOVED AWARD"),
  removedConference("REMOVED CONFERENCE"),
  removedPublication("REMOVED PUBLICATION"),
  removedSavedSearch("REMOVED SAVED PUBLICATION"),
  removedAllFilters("REMOVED ALL FILTERS"),

  /// ADD EVENTS
  addedJob("ADDED JOB"),
  addedEducation("ADDED EDUCATION"),
  addedLanguage("ADDED LANGUAGE"),
  addedProject("ADDED PROJECT"),
  addedAward("ADDED AWARD"),
  addedConference("ADDED CONFERENCE"),
  addedPublication("ADDED PUBLICATION"),

  /// UPDATE EVENTS
  updatedJob("UPDATED JOB"),
  updatedEducation("UPDATED EDUCATION"),
  updatedLanguage("UPDATED LANGUAGE"),
  updatedProject("UPDATED PROJECT"),
  updatedAward("UPDATED AWARD"),
  updatedConference("UPDATED CONFERENCE"),
  updatedPublication("UPDATED PUBLICATION"),
  updatedPassword("UPDATED PASSWORD"),
  updatedEmail("UPDATED EMAIL"),
  updatedLicenses("UPDATED LICENSES"),
  updatedIdealJob("UPDATED IDEAL JOB"),
  updatedSkills("UPDATED SKILLS"),
  updatedProfile("UPDATED PROFILE"),

  /// SEARCH EVENTS
  searchedJobs("SEARCHED JOBS"),
  searchedRecommendedJobs("SEARCHED RECOMMENDED JOBS"),

  /// CLICK EVENTS
  clickedBtn("CLICKED BUTTON"),
  clickedSearchBtn("CLICKED SEARCH BUTTON"),
  clickedDeleteProfileBtn("CLICKED DELETE PROFILE BUTTON"),
  clickedDeleteApplication("CLICKED DELETE APPLICATION BUTTON"),
  clickedApplyBtn("CLICKED APPLY BUTTON"),
  clickedSaveApplicationBtn("CLICKED SAVE APPLICATION BUTTON"),
  clickedSubmitApplicationQuestionnaireBtn(
      "CLICKED SUBMIT APPLICATION QUESTIONNAIRE BUTTON"),
  clickedSearchFilterBtn("CLICKED SEARCH FILTER BUTTON"),
  clickedRemoveSavedSearchBtn("CLICKED REMOVE SAVED SEARCH BUTTON"),
  clickedCancelChangeEmailBtn("CLICKED CANCEL EMAIL CHANGE BUTTON"),
  clickedChangePasswordBtn("CLICKED CHANGE PASSWORD BUTTON"),
  clickedCancelProfileDelete("CLICKED CANCEL PROFILE DELETE BUTTON"),
  clickedUploadDocumentBtn("CLICKED UPLOAD DOCUMENT BUTTON"),
  clickedUploadImageBtn("CLICKED UPLOAD IMAGE BUTTON"),
  clickedEditJobBtn("CLICKED EDIT JOB BUTTON"),
  clickedAddJobBtn("CLICKED ADD JOB BUTTON"),
  clickedEditEduBtn("CLICKED EDIT EDUCATION BUTTON"),
  clickedAddEduBtn("CLICKED ADD EDUCATION BUTTON"),
  clickedAddSkillBtn("CLICKED ADD SKILL BUTTON"),
  clickedAddLanguageBtn("CLICKED ADD LANGUAGE BUTTON"),
  clickedEditLanguageBtn("CLICKED EDIT LANGUAGE BUTTON"),
  clickedAddLicenseBtn("CLICKED ADD DRIVER LICENSE BUTTON"),
  clickedAddProjectBtn("CLICKED ADD PROJECT BUTTON"),
  clickedEditProjectBtn("CLICKED EDIT PROJECT BUTTON"),
  clickedAddAwardBtn("CLICKED ADD AWARD BUTTON"),
  clickedEditAwardBtn("CLICKED EDIT AWARD BUTTON"),
  clickedAddConferenceBtn("CLICKED ADD CONFERENCE BUTTON"),
  clickedEditConferenceBtn("CLICKED EDIT CONFERENCE BUTTON"),
  clickedAddPublicationBtn("CLICKED ADD PUBLICATION BUTTON"),
  clickedEditPublicationBtn("CLICKED EDIT PUBLICATION BUTTON"),
  clickedRemoteJobBtn("CLICKED REMOTE JOBS BUTTON"),
  clickedRetireeJobBtn("CLICKED RETIREE JOBS BUTTON"),
  clickedForeignJobBtn("CLICKED FOREIGN JOBS BUTTON"),
  clickedBackBtn("CLICKED BACK BUTTON"),
  clickedCancelBtn("CLICKED CANCEL BUTTON"),
  clickedCloseBtn("CLICKED CLOSE BUTTON"),
  clickedMenuBtn("CLICKED MENU BUTTON"),
  clickedLoginBtn("CLICKED LOGIN BUTTON"),
  clickedForgotPasswordBtn("CLICKED REMEMEMBER ME BUTTON"),
  clickedGoogleBtn("CLICKED GOOGLE LOGIN BUTTON"),
  clickedFbBtn("CLICKED FACEBOOK LOGIN BUTTON"),
  clickedAppleBtn("CLICKED APPLE LOGIN BUTTON"),
  clickedLanguageSwitchBtn("CLICKED LANGUAGE SWITCH BUTTON"),
  clickedAutoUpdateCVInfoBtn("CLICKED AUTO UPDATE CV DATA BUTTON"),
  clickedJobApplicationBtn("CLICKED JOB APPLIATION BUTTON"),

  /// NETWORK EVENTS
  apiRequest("API REQUEST");

  const AppEvent(this.name);

  final String name;
}

class AppAnalyticsData extends Codable {
  final AppEvent event;
  final String? description;
  final dynamic value;

  AppAnalyticsData(this.event, {this.description, this.value});

  @override
  Map<String, dynamic> toJson() {
    final data = {
      "description": description ?? event.name.toLowerCase(),
      "value": "$value",
      "executedAt": DateTime.now().millisecondsSinceEpoch
    };
    return data;
  }
}
