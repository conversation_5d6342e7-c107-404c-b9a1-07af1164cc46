// import 'package:day1/day1.dart';
// import 'package:firebase_messaging/firebase_messaging.dart';

// class AppFcmServiceImpl implements AppFcmService {
//   final AppCrashlyticsService _crashlyticsService;

//   AppFcmServiceImpl(this._crashlyticsService);

//   FirebaseMessaging get _fcm => FirebaseMessaging.instance;

//   _reportError(Object e, StackTrace t) {
//     _crashlyticsService.trackError("$e", error: e, trace: t);
//   }

//   @override
//   Future<String?> get token async {
//     try {
//       return await _fcm.getToken();
//     } catch (e, t) {
//       _reportError(e, t);
//       return null;
//     }
//   }

//   @override
//   Future<RemoteMessage?> getInitialMessage() async {
//     return await _fcm.getInitialMessage();
//   }

//   @override
//   Future<void> initialiseMessaging() async {
//     try {
//       NotificationSettings settings =
//           await _fcm.requestPermission(provisional: true);

//       if (settings.authorizationStatus == AuthorizationStatus.authorized) {
//         FirebaseMessaging.onBackgroundMessage(_onBackgroundMessage);
//         FirebaseMessaging.onMessage.listen(_onMessageReceived);
//         FirebaseMessaging.onMessageOpenedApp.listen(_onMessageReceived);
//       }
//     } catch (e, t) {
//       _reportError(e, t);
//     }
//   }

//   @override
//   Future<void> disableMessaging() async {
//     try {
//       await _fcm.deleteToken();
//     } catch (e, t) {
//       _reportError(e, t);
//     }
//   }

//   _onMessageReceived(RemoteMessage data) async {
//     AppLogger.info("COMMAND PAYLOAD:: ${data.data}");
//   }

//   @override
//   Future<void> unwatchTopic({required String topic}) async {
//     try {
//       _fcm.unsubscribeFromTopic(topic);
//     } catch (e, t) {
//       _reportError(e, t);
//     }
//   }

//   @override
//   Future<void> watchTopic({required String topic}) async {
//     try {
//       _fcm.subscribeToTopic(topic);
//     } catch (e, t) {
//       _reportError(e, t);
//     }
//   }
// }

// Future<void> _onBackgroundMessage(RemoteMessage data) async {
//   AppLogger.info("COMMAND PAYLOAD:: ${data.data}");
// }
