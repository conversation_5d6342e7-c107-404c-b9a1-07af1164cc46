import 'package:day1/day1.dart';

class AppPermissionServiceMock implements AppPermissionService {
  @override
  Future<bool> isPermissionGranted(Permissions permissions) async {
    return AppHelpers.randomBool;
  }

  @override
  Future<bool> requestPermission(Permissions permissions) async {
    return AppHelpers.randomBool;
  }

  @override
  Future<bool> openAppPermissionsSettings() async {
    return AppHelpers.randomBool;
  }

  @override
  Future<bool> requestPermissions(List<Permissions> permissions) async {
    return AppHelpers.randomBool;
  }
}
