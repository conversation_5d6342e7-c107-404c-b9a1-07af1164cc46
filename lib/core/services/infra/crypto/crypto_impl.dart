import 'dart:convert';
import 'dart:math';
import 'dart:typed_data';
import 'package:crypto/crypto.dart';
import 'package:day1/core/core.dart';
import 'package:encrypt/encrypt.dart';
import 'package:flutter/foundation.dart';

class AppCryptoServiceImpl implements AppCryptoService {
  // Use a secure key derivation function instead of simple string repetition
  Key _deriveKeyFromSecret(String secret, Uint8List salt) {
    // Use PBKDF2 for key derivation with SHA-256 and 10000 iterations
    final keyBytes = pbkdf2(
      password: utf8.encode(secret),
      salt: salt,
      iterations: 10000,
      keyLength: 32, // AES-256 requires 32 bytes
      macAlgorithm: Hmac(sha256),
    );

    return Key(keyBytes);
  }

  // Generate a secure random IV for each encryption operation
  IV _generateSecureIV() {
    final random = Random.secure();
    final ivData = List<int>.generate(16, (_) => random.nextInt(256));
    return IV(Uint8List.fromList(ivData));
  }

  Encrypter _getEncrypter(Key key) {
    return Encrypter(
      AES(key, mode: AESMode.cbc, padding: 'PKCS7'),
    );
  }

  @override
  String decrypt({required String key, required String value}) {
    try {
      // Extract the salt and IV from the encrypted value
      final parts = value.split('.');
      if (parts.length != 3) {
        throw FormatException('Invalid encrypted format');
      }

      final salt = base64Decode(parts[0]);
      final iv = IV(base64Decode(parts[1]));
      final encryptedData = Encrypted.from64(parts[2]);

      // Derive the key using the same salt
      final derivedKey = _deriveKeyFromSecret(key, salt);
      final encrypter = _getEncrypter(derivedKey);

      // Decrypt the data
      final decrypted = encrypter.decrypt(encryptedData, iv: iv);
      return decrypted;
    } catch (e, t) {
      AppLogger.severe('Decryption error: $e', stackTrace: t);
      // Don't return the original value on error as it might be sensitive
      return "";
    }
  }

  @override
  String encrypt({required String key, required String value}) {
    try {
      // Generate a random salt for each encryption
      final salt = Uint8List.fromList(
        List<int>.generate(16, (_) => Random.secure().nextInt(256))
      );

      // Generate a secure random IV
      final iv = _generateSecureIV();

      // Derive the key using PBKDF2
      final derivedKey = _deriveKeyFromSecret(key, salt);
      final encrypter = _getEncrypter(derivedKey);

      // Encrypt the data
      final encrypted = encrypter.encrypt(value, iv: iv);

      // Combine salt, IV, and encrypted data with a separator
      final saltBase64 = base64Encode(salt);
      final ivBase64 = base64Encode(iv.bytes);

      // Return the combined string
      return '$saltBase64.$ivBase64.${encrypted.base64}';
    } catch (e, t) {
      AppLogger.severe('Encryption error: $e', stackTrace: t);
      return "";
    }
  }
}
