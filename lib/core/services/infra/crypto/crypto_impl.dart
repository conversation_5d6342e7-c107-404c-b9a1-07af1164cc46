import 'package:day1/core/core.dart';
import 'package:encrypt/encrypt.dart';

class AppCryptoServiceImpl implements AppCryptoService {
  String _expandSecretToSize(String secret, [int size = 32]) {
    if (secret.length >= size) return secret.substring(0, size);

    final length = secret.length;
    final reps = ((size - length) / length).ceil();

    secret = secret + (secret * reps);

    return secret.substring(0, size);
  }

  Encrypter getEncrypter(Key key) {
    return Encrypter(
      AES(key, mode: AESMode.ctr, padding: null),
    );
  }

  (IV, Key) _getIvKey(String secret) {
    secret = _expandSecretToSize(secret);
    final key = Key.fromUtf8(secret);
    final iv = IV.fromLength(16);

    return (iv, key);
  }

  @override
  String decrypt({required String key, required String value}) {
    try {
      final (iv, cipher) = _getIvKey(key);
      final encrypter = getEncrypter(cipher);

      final encrypted = Encrypted.from64(value);
      final decrypted = encrypter.decrypt(encrypted, iv: iv);

      return decrypted;
    } catch (e, t) {
      AppLogger.severe('$e', stackTrace: t);
      return value;
    }
  }

  @override
  String encrypt({required String key, required String value}) {
    try {
      final (iv, cipher) = _getIvKey(key);
      final encrypter = getEncrypter(cipher);

      final encrypted = encrypter.encrypt(value, iv: iv);

      return encrypted.base64;
    } catch (e, t) {
      AppLogger.severe('$e', stackTrace: t);
      return "";
    }
  }
}
