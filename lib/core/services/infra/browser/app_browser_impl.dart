import 'package:day1/day1.dart';
// import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:url_launcher/url_launcher.dart' as ul;
import 'package:url_launcher/url_launcher_string.dart';

class AppBrowserImpl extends AppBrowser {
  @override
  Future<void> openUrl(String url) async {
    // final defaultSettings = ChromeSafariBrowserSettings(
    //   toolbarBackgroundColor: theme.scaffoldBgColor,
    //   barCollapsingEnabled: true,
    //   preferredBarTintColor: theme.scaffoldBgColor,
    //   preferredControlTintColor: theme.textColor,
    //   displayMode: TrustedWebActivityImmersiveDisplayMode(isSticky: true),
    //   noHistory: true,
    // );

    if (!await canLaunchUrlString(url)) return;
    final uri = Uri.tryParse(url);
    if (uri == null) return;
    ul.launchUrl(uri);
  }
}
