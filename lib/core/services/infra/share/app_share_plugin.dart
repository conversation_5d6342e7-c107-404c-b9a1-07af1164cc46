import 'dart:io';

import 'package:flutter/material.dart';
import 'package:share_plus/share_plus.dart';

class AppSharePlugin {
  static Rect _getContextRect(BuildContext context) {
    final RenderBox box = context.findRenderObject() as RenderBox;
    final offset = box.localToGlobal(Offset.zero);
    final size = box.size;

    return Rect.fromLTWH(
      offset.dx,
      offset.dy,
      size.width,
      size.height,
    );
  }

  static shareText(
    BuildContext context, {
    String? title,
    required String text,
  }) {
    Share.share(
      text,
      subject: title,
      sharePositionOrigin: _getContextRect(context),
    );
  }

  static shareFile(
    BuildContext context, {
    String? title,
    String? text,
    required List<File> files,
  }) {
    Share.shareXFiles(
      [...files.map((it) => XFile(it.path))],
      text: text,
      subject: title,
      sharePositionOrigin: _getContextRect(context),
    );
  }
}
