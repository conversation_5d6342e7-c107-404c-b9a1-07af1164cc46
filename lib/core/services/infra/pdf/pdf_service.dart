import 'dart:typed_data';

import 'package:day1/core/core.dart';
import 'package:flutter/material.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;

class PdfService {
  static Future<Uint8List> pdfFromImageBytes(
    Uint8List data, {
    OnChanged? onError,
    Color? color,
  }) async {
    try {
      final pdf = pw.Document(
        pageMode: PdfPageMode.fullscreen,
      );

      pdf.addPage(
        pw.Page(
          pageTheme: const pw.PageTheme(margin: pw.EdgeInsets.zero),
          build: (context) {
            return pw.Container(
              alignment: pw.Alignment.center,
              decoration: pw.BoxDecoration(
                color: PdfColor.fromInt(color?.value ?? 0),
                image: pw.DecorationImage(
                  fit: pw.BoxFit.contain,
                  image: pw.MemoryImage(data),
                ),
              ),
            );
          },
        ),
      );

      return await pdf.save();
    } catch (e, t) {
      AppLogger.severe("$e", error: e, stackTrace: t);
      rethrow;
    }
  }
}
