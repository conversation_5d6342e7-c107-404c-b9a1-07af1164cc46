class PaymentIntentResponse {
  final String? id;
  final String? transactionTypeId;
  final String? transactionStatusId;
  final String? stripeTransactionStatus;
  final String stripeClientSecret;
  final double? transactionAmount;
  final double? baseTransactionAmount;
  final String? transactionId;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final String? userId;

  PaymentIntentResponse({
    this.id,
    this.transactionTypeId,
    this.transactionStatusId,
    this.stripeTransactionStatus,
    required this.stripeClientSecret,
    this.transactionAmount,
    this.baseTransactionAmount,
    this.transactionId,
    this.createdAt,
    this.updatedAt,
    this.userId,
  });

  factory PaymentIntentResponse.fromJson(Map<String, dynamic> json) {
    return PaymentIntentResponse(
      id: "${json["id"]}",
      transactionTypeId: "${json["transactionTypeId"]}",
      transactionStatusId: "${json["transactionStatusId"]}",
      stripeTransactionStatus: json["stripeTransactionStatus"],
      stripeClientSecret: json["stripeClientSecret"] ?? "",
      transactionAmount: double.tryParse(json["transactionAmount"]),
      baseTransactionAmount: double.tryParse(json["baseTransactionAmount"]),
      transactionId: json["transactionId"],
      createdAt: json["createdAt"] == null
          ? null
          : DateTime.tryParse(json["createdAt"]),
      updatedAt: json["updatedAt"] == null
          ? null
          : DateTime.tryParse(json["updatedAt"]),
      userId: json["userId"],
    );
  }
}
