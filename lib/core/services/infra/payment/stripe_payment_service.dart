import 'package:day1/core/core.dart';
import 'package:flutter_stripe/flutter_stripe.dart';

class StripePaymentService {
  late final Stripe _service;
  final String _pubKey;

  StripePaymentService(this._pubKey) {
    Stripe.publishableKey = _pubKey;
    _service = Stripe.instance;
  }

  Future<({String? errorMessage, bool status})> makePayment({
    required PaymentIntentResponse intent,
    required AppTheme theme,
  }) async {
    try {
      await _service.initPaymentSheet(
        paymentSheetParameters: SetupPaymentSheetParameters(
          paymentIntentClientSecret: intent.stripeClientSecret,
          merchantDisplayName: "DayOne",
          customerId: intent.userId,
          appearance: PaymentSheetAppearance(
            colors: PaymentSheetAppearanceColors(
              background: theme.scaffoldBgColor,
              primary: theme.raisedBtnBgColor,
              componentBorder: theme.inputBorderColor,
              componentText: theme.textColor,
              componentBackground: theme.cardColor,
              primaryText: theme.textColor,
              secondaryText: theme.secondaryTextColor,
              placeholderText: theme.hintTextColor,
              icon: theme.raisedBtnCBgColor,
              error: theme.errorColor,
            ),
            shapes: PaymentSheetShape(
              shadow: PaymentSheetShadowParams(
                color: theme.shadowColor,
                opacity: .4,
              ),
            ),
          ),
        ),
      );
      await _service.presentPaymentSheet(
        options: PaymentSheetPresentOptions(
          timeout: 2.minuteDuration.inMilliseconds,
        ),
      );
      return (errorMessage: null, status: true);
    } on StripeException catch (e) {
      return (errorMessage: e.error.localizedMessage, status: false);
    } catch (e, t) {
      AppLogger.severe("$e", error: e, stackTrace: t);
      return (errorMessage: "$e", status: false);
    }
  }
}
