import 'package:app_links/app_links.dart';
import 'package:day1/day1.dart';
import 'package:flutter/foundation.dart';

class DeeplinkServiceImpl implements DeeplinkService {
  final AppCrashlyticsService _crashlyticsService;

  @override
  final List<String> hosts;
  final _appLinks = AppLinks();

  DeeplinkServiceImpl(this.hosts, this._crashlyticsService);

  bool _isValidLink(String? link) {
    if (link == null) return false;
    return hosts.any((it) {
      final nornalisedLink = link.toLowerCase();
      return nornalisedLink.contains(it);
    });
  }

  @override
  String? extractLink(String? url) {
    if (!url.hasValue) return null;
    final link = url!;
    final host = hosts.firstWhere(
      (it) => link.includes(it),
      orElse: () => "",
    );
    final scheme = config.scheme;

    if (!host.hasValue && !(link.startsWith(scheme))) return null;

    if (!url.includes(host)) {
      final matches = AppRegex.customSchemeRegex.firstMatch(url);
      return matches?.namedGroup('path');
    }
    final parts = url.split(host);
    return parts.tryLast?.trim();
  }

  @override
  Stream<String> watchLinks() async* {
    if (kIsWeb) return;
    final initialLink = await _appLinks.getInitialAppLinkString();

    if (_isValidLink(initialLink)) yield initialLink ?? "";

    final stream = _appLinks.allStringLinkStream.handleError((e) {
      _crashlyticsService.trackError("$e", error: e);
    });
    await for (final link in stream) {
      if (!_isValidLink(link)) continue;
      yield link;
    }
  }

  @override
  parsePath({
    required String? path,
    required String? currentRoute,
    required OnChanged<String> onCompletion,
  }) async {
    if (kIsWeb || !path.hasValue) return;

    final parsedPath = extractLink(path);

    if (!parsedPath.hasValue) return;

    if (parsedPath == currentRoute) return;

    onCompletion(parsedPath!);
  }

  @override
  Future<String?> get latestLink async {
    try {
      final link = await _appLinks.getInitialAppLinkString();
      final validLink = extractLink(link);
      return validLink;
    } catch (_) {
      return null;
    }
  }
}
