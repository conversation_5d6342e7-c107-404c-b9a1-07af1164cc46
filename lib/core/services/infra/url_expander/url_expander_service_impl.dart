import 'dart:io';

import 'package:day1/day1.dart';

class UrlExpanderServiceImpl implements UrlExpanderService {
  final AppCrashlyticsService _crashlyticsService;

  UrlExpanderServiceImpl(this._crashlyticsService);

  @override
  Future<String> getFullUrl(
    String shortenedUrl, {
    required String expectedBaseUrl,
  }) async {
    try {
      if (_isValidLink(shortenedUrl, expectedBaseUrl)) return shortenedUrl;
      final client = HttpClient();
      final uri = Uri.parse(shortenedUrl);
      final request = await client.headUrl(uri);
      request.followRedirects = false;
      final response = await request.close();

      final location = response.headers.value(HttpHeaders.locationHeader) ?? "";

      if ((location.isEmpty) && !response.isRedirect) return "";

      final isValidUrl = _isValidLink(
        location,
        expectedBaseUrl,
      );

      if (!isValidUrl && response.isRedirect) {
        return await getFullUrl(
          location,
          expectedBaseUrl: expectedBaseUrl,
        );
      }

      return location;
    } catch (e, t) {
      _crashlyticsService.trackError("$e", error: e, trace: t);
      return "";
    }
  }

  bool _isValidLink(String? link, String expectedBaseUrl) {
    if (!link.hasValue) return false;

    return link!.toLowerCase().startsWith(expectedBaseUrl.toLowerCase());
  }
}
