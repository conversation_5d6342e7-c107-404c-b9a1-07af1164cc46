import 'dart:io';

import 'package:day1/day1.dart';
import 'package:flutter/material.dart';
import 'package:image_cropper/image_cropper.dart';

class CropperUiData {
  final String title;
  final Color? toolbarColor;
  final Color? toolbarTextColor;
  final bool circularCrop;
  final bool lockAspectRatio;
  final CropAspectRatioPreset? aspectRatio;
  final List<CropAspectRatioPreset>? aspectRatios;

  const CropperUiData(
    this.title, {
    this.toolbarColor,
    this.toolbarTextColor,
    this.aspectRatios,
    this.aspectRatio,
    this.circularCrop = true,
    this.lockAspectRatio = false,
  });
}

class AppImagePlugin {
  static final ImageCropper _cropper = ImageCropper();

  static Future<File?> _cropImage(
    File imageFile, {
    CropperUiData? cropperUiData,
  }) async {
    try {
      final cropStyle = (cropperUiData?.circularCrop ?? false)
          ? CropStyle.circle
          : CropStyle.rectangle;
      if (imageFile.existsSync()) {
        const defaultRatios = [
          CropAspectRatioPreset.original,
          CropAspectRatioPreset.square,
          CropAspectRatioPreset.ratio3x2,
          CropAspectRatioPreset.ratio4x3,
          CropAspectRatioPreset.ratio16x9
        ];
        final croppedImage = await _cropper.cropImage(
          sourcePath: imageFile.path,
          uiSettings: [
            IOSUiSettings(
              title: cropperUiData?.title ?? 'Crop photo',
              aspectRatioLockEnabled: cropperUiData?.lockAspectRatio ?? false,
              cropStyle: cropStyle,
              aspectRatioPresets: cropperUiData?.aspectRatios ?? defaultRatios,
              minimumAspectRatio: 16 / 9,
            ),
            AndroidUiSettings(
              toolbarTitle: cropperUiData?.title ?? 'Crop photo',
              toolbarColor: cropperUiData?.toolbarColor,
              toolbarWidgetColor: cropperUiData?.toolbarTextColor,
              lockAspectRatio: cropperUiData?.lockAspectRatio ?? false,
              aspectRatioPresets: cropperUiData?.aspectRatios ?? defaultRatios,
              initAspectRatio: cropperUiData?.aspectRatio,
              cropStyle: cropStyle,
            )
          ],
        );
        if (croppedImage == null) return null;
        return File(croppedImage.path);
      }
      return imageFile;
    } catch (_) {
      return imageFile;
    }
  }

  static Future<FSResponse> pickImage({
    bool shouldCrop = true,
    CropperUiData? cropperUiData,
    FSDocumentType documentType = FSDocumentType.image,
  }) async {
    final fileResponse = await AppFilePlugin.pickFile(
      documentType: documentType,
      maxSizeInMb: 5,
    );

    if (fileResponse.hasError) return fileResponse;

    if (!shouldCrop) return fileResponse;

    final File file = fileResponse.file!;

    final croppedFile = await _cropImage(
      file,
      cropperUiData: cropperUiData,
    );
    if (croppedFile == null) FSResponse(file: file, type: documentType);
    return FSResponse(file: croppedFile, type: documentType);
  }

  static Future<List<FSResponse>> pickImages({
    bool shouldCrop = true,
    CropperUiData? cropperUiData,
    FSDocumentType documentType = FSDocumentType.image,
  }) async {
    final fileResponses = await AppFilePlugin.pickFiles(
      documentType: documentType,
      maxSizeInMb: 5,
    );

    return fileResponses.whereList((it) => it.hasFile);
  }
}
