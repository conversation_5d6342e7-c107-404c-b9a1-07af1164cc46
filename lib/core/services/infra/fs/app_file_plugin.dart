import 'dart:io';
import 'dart:typed_data';

import 'package:day1/day1.dart';
import 'package:file_picker/file_picker.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart';

class AppFilePlugin {
  static final FilePicker _picker = FilePicker.platform;

  static Future<FSResponse> pickFile({
    String? title,
    FSDocumentType documentType = FSDocumentType.document,
    double maxSizeInMb = 10,
  }) async {
    try {
      final pickedFile = await _picker.pickFiles(
        allowedExtensions: documentType.extensions,
        dialogTitle: title,
        type: documentType.type,
      );

      if (pickedFile == null) {
        return FSResponse(
          type: documentType,
          error: const FSError(type: FSErrorType.empty),
        );
      }

      final choiceFile = pickedFile.files.first;

      if (choiceFile.path == null) {
        return FSResponse(
          type: documentType,
          error: const FSError(type: FSErrorType.empty),
        );
      }

      final File file = File(choiceFile.path!);

      if (AppHelpers.fileSizeInMb(file) > maxSizeInMb) {
        return FSResponse(
          error: const FSError(type: FSErrorType.oversized),
          type: documentType,
        );
      }
      return FSResponse(
        file: file,
        name: choiceFile.name,
        type: documentType,
      );
    } catch (e, t) {
      AppLogger.severe("$e", stackTrace: t, error: e);
      return FSResponse(
        error: const FSError(type: FSErrorType.unknown),
        type: documentType,
      );
    }
  }

  static Future<List<FSResponse>> pickFiles({
    String? title,
    FSDocumentType documentType = FSDocumentType.document,
    double maxSizeInMb = 10,
  }) async {
    try {
      final pickedFile = await _picker.pickFiles(
        allowedExtensions: documentType.extensions,
        allowMultiple: true,
        dialogTitle: title,
        type: documentType.type,
      );

      if (pickedFile == null) {
        return [
          FSResponse(
            type: documentType,
            error: const FSError(type: FSErrorType.empty),
          )
        ];
      }
      final choiceFiles = pickedFile.files.where((it) => it.path != null);

      if (choiceFiles.isEmpty) {
        return [
          FSResponse(
            type: documentType,
            error: const FSError(type: FSErrorType.empty),
          )
        ];
      }

      List<FSResponse> files = [];

      for (final file in choiceFiles) {
        if (file.path == null) continue;

        final parsedFile = File(file.path!);

        final isLarge = AppHelpers.fileSizeInMb(parsedFile) > maxSizeInMb;

        files.tryAdd(
          FSResponse(
            file: isLarge ? null : parsedFile,
            name: file.name,
            error: isLarge ? const FSError(type: FSErrorType.oversized) : null,
            type: documentType,
          ),
        );
      }

      return files;
    } catch (e, t) {
      return [
        FSResponse(
          error: FSError(
            type: FSErrorType.unknown,
            error: e,
            stackTrace: t,
          ),
          type: documentType,
        )
      ];
    }
  }

  static Future<FSResponse> getFileFromString(
    String fileData, {
    required String ext,
  }) async {
    try {
      final documentDir = await getTemporaryDirectory();
      final name = join(documentDir.path, "generated_file.$ext");
      final file = File(name);

      final overWrittenFile = await file.writeAsString(fileData);
      return FSResponse(
        file: overWrittenFile,
        name: name,
        type: FSDocumentType.document,
      );
    } catch (e, t) {
      return FSResponse(
        error: FSError(
          type: FSErrorType.unknown,
          error: e,
          stackTrace: t,
        ),
        type: FSDocumentType.document,
      );
    }
  }

  static Future<FSResponse> saveFile(
    Uint8List bytes, {
    required String filename,
    String? dialogTitle,
    required String ext,
    FileType fileType = FileType.any,
  }) async {
    try {
      final docDir = await getApplicationDocumentsDirectory();
      if (!filename.endsWith(ext)) filename = "$filename.$ext";

      final filePath = await _picker.saveFile(
        bytes: bytes,
        dialogTitle: dialogTitle,
        fileName: filename,
        type: fileType,
        allowedExtensions: [ext],
        initialDirectory: docDir.path,
      );

      if (filePath == null) {
        return const FSResponse(
          type: FSDocumentType.document,
          error: FSError(type: FSErrorType.empty),
        );
      }

      return const FSResponse(type: FSDocumentType.document);
    } catch (e, t) {
      return FSResponse(
        error: FSError(
          type: FSErrorType.unknown,
          error: e,
          stackTrace: t,
        ),
        type: FSDocumentType.document,
      );
    }
  }
}
