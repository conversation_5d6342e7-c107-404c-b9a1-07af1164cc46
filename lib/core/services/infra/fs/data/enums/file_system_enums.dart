import 'package:file_picker/file_picker.dart';

//["png", "jpg", "jpeg", "heic", "aae", "webp", "avif", "heif"];

enum FSErrorType {
  unknown,
  oversized,
  empty,
}

enum FSDocumentType {
  document(FileType.custom),
  image(FileType.image),
  video(FileType.video),
  imageVideo(FileType.media);

  const FSDocumentType(this.type);
  final FileType type;

  List<String>? get extensions {
    switch (type) {
      case FileType.custom:
        return ["pdf", "doc", "docx"];
      case FileType.media:
      // return [
      //   "jpg",
      //   "jpeg",
      //   "png",
      //   "gif",
      //   "webp",
      //   "tiff",
      //   "heif",
      //   "heic",
      //   "mp4",
      //   "mov",
      //   "flv"
      // ];
      case FileType.video:
      case FileType.image:
      default:
        return null;
    }
  }
}
