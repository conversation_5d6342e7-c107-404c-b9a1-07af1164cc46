{"_note1": "KEEP THIS FILE! Check it into a version control system (VCS) like git.", "_note2": "ObjectBox manages crucial IDs for your object model. See docs for details.", "_note3": "If you have VCS merge conflicts, you must resolve them according to ObjectBox docs.", "entities": [{"id": "1:3712845317549324236", "lastPropertyId": "4:1572241121151063295", "name": "AppSelectionResponseCache", "properties": [{"id": "1:2597724213302654321", "name": "id", "type": 9, "flags": 2048, "indexId": "1:4475497802173528987"}, {"id": "2:8803209673364266084", "name": "index", "type": 6, "flags": 1}, {"id": "3:4946487426231902629", "name": "isActive", "type": 1}, {"id": "4:1572241121151063295", "name": "name", "type": 9}], "relations": []}, {"id": "2:7207097667153489501", "lastPropertyId": "5:6556120847131278040", "name": "<PERSON><PERSON><PERSON><PERSON>", "properties": [{"id": "1:2681005727982569482", "name": "id", "type": 6, "flags": 1}, {"id": "2:5404173905917963843", "name": "data", "type": 9}, {"id": "3:661961443119417016", "name": "byteData", "type": 27}, {"id": "4:7754238842144774322", "name": "uri", "type": 9, "flags": 34848, "indexId": "2:1127835597877573256"}, {"id": "5:6556120847131278040", "name": "expiresAt", "type": 10, "flags": 8, "indexId": "3:4999889854867767910"}], "relations": []}, {"id": "3:2232319528622886934", "lastPropertyId": "6:3120205711240193872", "name": "ImageCache", "properties": [{"id": "1:2171202239014423482", "name": "id", "type": 6, "flags": 1}, {"id": "2:6252635388862524164", "name": "uuid", "type": 9, "flags": 2048, "indexId": "4:5270774309264438751"}, {"id": "3:5645727601513006053", "name": "createdAt", "type": 9}, {"id": "4:3638584164329311090", "name": "name", "type": 9}, {"id": "5:8959829213104668587", "name": "url", "type": 9}, {"id": "6:3120205711240193872", "name": "type", "type": 9}], "relations": []}, {"id": "4:6868762395190710332", "lastPropertyId": "3:8650667968744231244", "name": "LocationCache", "properties": [{"id": "1:4387109304703964791", "name": "id", "type": 6, "flags": 1}, {"id": "2:7697249962887143341", "name": "placeId", "type": 9}, {"id": "3:8650667968744231244", "name": "displayName", "type": 9}], "relations": []}, {"id": "5:1066071825613724983", "lastPropertyId": "17:8325086926719649036", "name": "SessionCache", "properties": [{"id": "1:3249241987546589767", "name": "id", "type": 6, "flags": 129}, {"id": "2:786064777406375260", "name": "accessToken", "type": 9, "flags": 34848, "indexId": "5:3928742555453728583"}, {"id": "3:4855809639427102545", "name": "refreshToken", "type": 9, "flags": 2048, "indexId": "6:7923954639300100374"}, {"id": "4:8405522530865534423", "name": "createdAt", "type": 6}, {"id": "5:2674283578696828888", "name": "expiresAt", "type": 6}, {"id": "7:3564144303851046397", "name": "onboardingState", "type": 9}, {"id": "8:4057236562056340799", "name": "lastEmail", "type": 9}, {"id": "9:6050141837947996242", "name": "hasEnabledBioAuth", "type": 1}, {"id": "10:9184332373292250360", "name": "hasEnabled2fa", "type": 1}, {"id": "11:8622108352346963772", "name": "authPublicKey", "type": 9}, {"id": "14:4050680923259066238", "name": "lastPhoneNumber", "type": 9}, {"id": "15:7058809721617712439", "name": "userUuid", "type": 9}, {"id": "16:7260689454402606908", "name": "hasEnabledPinAuth", "type": 1}, {"id": "17:8325086926719649036", "name": "appPublicKey", "type": 9}], "relations": []}, {"id": "6:5244052296167536443", "lastPropertyId": "3:9079000742058020075", "name": "TelephoneNumberCache", "properties": [{"id": "1:7558296146716487399", "name": "id", "type": 6, "flags": 1}, {"id": "2:3638377099020887639", "name": "countryCode", "type": 9}, {"id": "3:9079000742058020075", "name": "number", "type": 9}], "relations": []}, {"id": "7:6010729087324264653", "lastPropertyId": "4:444114950938572228", "name": "ThemeCache", "properties": [{"id": "1:1127178900023012604", "name": "id", "type": 6, "flags": 129}, {"id": "2:3295688809638728825", "name": "inDarkMode", "type": 1}, {"id": "3:519685389244947430", "name": "inLightMode", "type": 1}, {"id": "4:444114950938572228", "name": "inSystemMode", "type": 1}], "relations": []}, {"id": "8:7991122869147316633", "lastPropertyId": "22:3411140923168221465", "name": "UserCache", "properties": [{"id": "1:3474154768916649391", "name": "id", "type": 6, "flags": 129}, {"id": "2:6863239545440569953", "name": "uuid", "type": 9, "flags": 2048, "indexId": "7:5208101594132120902"}, {"id": "3:2205465666492976623", "name": "email", "type": 9}, {"id": "4:285087658764003161", "name": "displayName", "type": 9}, {"id": "5:7927998982961257404", "name": "firstName", "type": 9}, {"id": "6:8882632575856413434", "name": "description", "type": 9}, {"id": "7:6716479156462833905", "name": "language", "type": 9}, {"id": "8:1096911734782798370", "name": "lastName", "type": 9}, {"id": "9:1523236474657872902", "name": "photo", "type": 9}, {"id": "11:1480353981465398107", "name": "location_Id", "type": 11, "flags": 520, "indexId": "8:7913818807779177806", "relationTarget": "LocationCache"}, {"id": "12:1034442813426071353", "name": "accountNumber", "type": 9}, {"id": "13:6988372603717617510", "name": "plan", "type": 9}, {"id": "14:8951360676221193380", "name": "phoneNumber", "type": 9}, {"id": "15:2394066332430093532", "name": "createdAt", "type": 12}, {"id": "16:4935934582854000283", "name": "updatedAt", "type": 12}, {"id": "17:4198986452434577181", "name": "pinSetAt", "type": 9}, {"id": "18:1072732210021893416", "name": "biometricsSetAt", "type": 9}, {"id": "19:3714672880221381777", "name": "emailVerifiedAt", "type": 9}, {"id": "20:2187142207093815203", "name": "passwordSetAt", "type": 9}, {"id": "21:8263074333910765743", "name": "phoneVerifiedAt", "type": 9}, {"id": "22:3411140923168221465", "name": "stripeCustomerId", "type": 9}], "relations": []}], "lastEntityId": "8:7991122869147316633", "lastIndexId": "8:7913818807779177806", "lastRelationId": "0:0", "lastSequenceId": "0:0", "modelVersion": 5, "modelVersionParserMinimum": 5, "retiredEntityUids": [], "retiredIndexUids": [], "retiredPropertyUids": [**********413455985, 5702537056496228701, 6528964882199586615, 8172486621473470642], "retiredRelationUids": [], "version": 1}