import 'dart:io';

import 'package:day1/day1.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:provider/provider.dart';

void main() async {
  HttpOverrides.global = AppHttpOverrides();

  WidgetsFlutterBinding.ensureInitialized();
  await EasyLocalization.ensureInitialized();

  // Load configuration from environment variables and non-sensitive config from JSON file
  final configFile = const bool.fromEnvironment('USE_MOCK_CONFIG') ? 'mock_config.json' : 'd1_config.json';
  final appConfig = await DoAppConfig.fromConfigFile(configFile);

  print("appconfig");
  print(appConfig);
  setUpAppDI(appConfig: appConfig, routeRegistry: AppRoutes());

  await locator<AppDbClient>().init();

  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  final config = locator<DoAppConfig>();

  runApp(
    EasyLocalization(
      supportedLocales: config.supportedLocales,
      path: 'assets/translations',
      fallbackLocale: config.defaultLocale,
      startLocale: config.defaultLocale,
      useOnlyLangCode: true,
      child: AppStateWrapper(
        providers: [
          ChangeNotifierProvider<ThemeState>(
            create: (_) => locator(),
            lazy: true,
          ),
          ChangeNotifierProvider<CountryState>(
            create: (_) => locator(),
            lazy: true,
          ),
          ChangeNotifierProvider<LauncherState>(
            create: (_) => locator(),
            lazy: true,
          ),
          ChangeNotifierProvider<BankState>(
            create: (_) => locator(),
            lazy: true,
          ),
          ChangeNotifierProvider<AvatarState>(
            create: (_) => locator(),
            lazy: true,
          ),
          ChangeNotifierProvider<LoginState>(
            create: (_) => locator(),
            lazy: true,
          ),
          ChangeNotifierProvider<AuthSettingsState>(
            create: (_) => locator(),
            lazy: true,
          ),
          ChangeNotifierProvider<DashboardState>(
            create: (_) => locator(),
            lazy: true,
          ),
          ChangeNotifierProvider<PaymentWalletState>(
            create: (_) => locator(),
            lazy: true,
          ),
        ],
        child: DoApp(
          locator(),
          locator(),
          key: ValueKey(config.appId),
        ),
      ),
    ),
  );
}
