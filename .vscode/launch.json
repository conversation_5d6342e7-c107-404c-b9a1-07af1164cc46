{
  // Use IntelliSense to learn about possible attributes.
  // Hover to view descriptions of existing attributes.
  // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "configurations": [
    {
      "name": "day1",
      "request": "launch",
      "type": "dart",
      "toolArgs": ["--dart-define-from-file", "d1_config.json", "--verbose"]
    },
    {
      "name": "day1 (mock)",
      "request": "launch",
      "type": "dart",
      "toolArgs": ["--dart-define-from-file", "mock_config.json", "--verbose"]
    },
    {
      "name": "day1 (profile mode)",
      "request": "launch",
      "type": "dart",
      "toolArgs": ["--dart-define-from-file", "d1_config.json", "--verbose"],
      "flutterMode": "profile"
    },
    {
      "name": "day1 (release mode)",
      "request": "launch",
      "type": "dart",
      "toolArgs": ["--dart-define-from-file", "d1_config.json", "--verbose"],
      "flutterMode": "release"
    }
  ]
}
