# Changelog

## Version 0.11.0

* (`selderee`) Escape sequences in selectors.

## Version 0.10.0

* Targeting Node.js version 14 and ES2020;
* Bump dependencies.

## Version 0.9.0

* Bump dependencies - fix "./core module cannot be found" issue.

## Version 0.8.1

* Sync with `selderee` package version. Now all dependencies are TypeScript, dual CommonJS/ES module packages;
* Use `rollup-plugin-cleanup` to condition published files.

## Version 0.7.0

* Drop Node.js version 10 support. At least 12.22.x is required.

## Version 0.6.0

* `selderee` 0.6.0 - [changelog](https://github.com/mxxii/selderee/blob/main/packages/selderee/CHANGELOG.md).

## Version 0.5.0

Initial release.

Aiming at Node.js version 10 and up.
