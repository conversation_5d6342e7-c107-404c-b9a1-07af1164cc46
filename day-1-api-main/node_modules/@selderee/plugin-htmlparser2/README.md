# selderee

![lint status badge](https://github.com/mxxii/selderee/workflows/lint/badge.svg)
![test status badge](https://github.com/mxxii/selderee/workflows/test/badge.svg)
[![License: MIT](https://img.shields.io/badge/license-MIT-green.svg)](https://github.com/mxxii/selderee/blob/main/LICENSE)

[selderee](https://github.com/mxxii/selderee) plugin - selectors decision tree builder for [htmlparser2](https://github.com/fb55/htmlparser2) DOM AST.

(Technically, the package depends not on `htmlparser2` but on [domhandler](https://github.com/fb55/domhandler), underlying package of `htmlparser2`.)

----

[Changelog](https://github.com/mxxii/selderee/blob/main/packages/plugin-htmlparser2/CHANGELOG.md).

See [main README file](https://github.com/mxxii/selderee/blob/main/README.md) for more info.
