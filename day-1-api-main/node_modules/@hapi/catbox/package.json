{"name": "@hapi/catbox", "description": "Multi-strategy object caching service", "version": "12.1.1", "repository": "git://github.com/hapijs/catbox", "main": "lib/index.js", "types": "lib/index.d.ts", "files": ["lib"], "keywords": ["cache", "generic", "adapter"], "eslintConfig": {"extends": ["plugin:@hapi/module"]}, "dependencies": {"@hapi/boom": "^10.0.1", "@hapi/hoek": "^11.0.2", "@hapi/podium": "^5.0.0", "@hapi/validate": "^2.0.1"}, "devDependencies": {"@hapi/code": "^9.0.3", "@hapi/eslint-plugin": "*", "@hapi/lab": "^25.1.2", "@types/node": "^18.11.9", "typescript": "^4.9.3"}, "scripts": {"test": "lab -a @hapi/code -t 100 -L -m 5000 -Y", "test-cov-html": "lab -a @hapi/code -r html -o coverage.html"}, "license": "BSD-3-<PERSON><PERSON>"}