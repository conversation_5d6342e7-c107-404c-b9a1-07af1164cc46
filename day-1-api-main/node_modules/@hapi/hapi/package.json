{"name": "@hapi/hapi", "description": "HTTP Server framework", "homepage": "https://hapi.dev", "version": "21.3.10", "repository": "git://github.com/hapijs/hapi", "main": "lib/index.js", "types": "lib/index.d.ts", "engines": {"node": ">=14.15.0"}, "files": ["lib"], "keywords": ["framework", "http", "api", "web"], "eslintConfig": {"extends": ["plugin:@hapi/module"]}, "dependencies": {"@hapi/accept": "^6.0.1", "@hapi/ammo": "^6.0.1", "@hapi/boom": "^10.0.1", "@hapi/bounce": "^3.0.1", "@hapi/call": "^9.0.1", "@hapi/catbox": "^12.1.1", "@hapi/catbox-memory": "^6.0.2", "@hapi/heavy": "^8.0.1", "@hapi/hoek": "^11.0.2", "@hapi/mimos": "^7.0.1", "@hapi/podium": "^5.0.1", "@hapi/shot": "^6.0.1", "@hapi/somever": "^4.1.1", "@hapi/statehood": "^8.1.1", "@hapi/subtext": "^8.1.0", "@hapi/teamwork": "^6.0.0", "@hapi/topo": "^6.0.1", "@hapi/validate": "^2.0.1"}, "devDependencies": {"@hapi/code": "^9.0.3", "@hapi/eslint-plugin": "*", "@hapi/inert": "^7.0.1", "@hapi/joi-legacy-test": "npm:@hapi/joi@^15.0.0", "@hapi/lab": "^25.1.2", "@hapi/vision": "^7.0.1", "@hapi/wreck": "^18.0.1", "@types/node": "^18.11.9", "handlebars": "^4.7.4", "joi": "^17.0.0", "legacy-readable-stream": "npm:readable-stream@^1.0.34", "typescript": "^4.9.4"}, "scripts": {"test": "lab -a @hapi/code -t 100 -L -m 5000 -Y", "test-tap": "lab -a @hapi/code -r tap -o tests.tap -m 5000", "test-cov-html": "lab -a @hapi/code -r html -o coverage.html -m 5000"}, "license": "BSD-3-<PERSON><PERSON>"}