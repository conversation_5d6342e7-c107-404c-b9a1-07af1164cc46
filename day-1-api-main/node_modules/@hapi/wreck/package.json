{"name": "@hapi/wreck", "description": "HTTP Client Utilities", "version": "18.1.0", "repository": "git://github.com/hapijs/wreck", "main": "lib/index", "types": "lib/index.d.ts", "keywords": ["utilities", "http", "client"], "files": ["lib"], "dependencies": {"@hapi/boom": "^10.0.1", "@hapi/bourne": "^3.0.0", "@hapi/hoek": "^11.0.2"}, "devDependencies": {"@hapi/code": "^9.0.3", "@hapi/lab": "^25.1.2", "@types/node": "^17.0.31", "typescript": "~4.6.4"}, "scripts": {"test": "lab -t 100 -L -a @hapi/code -m 10000 -Y", "test-cov-html": "lab -r html -o coverage.html -a @hapi/code -m 10000"}, "license": "BSD-3-<PERSON><PERSON>"}