{"name": "@hapi/ammo", "description": "HTTP Range processing utilities", "version": "6.0.1", "repository": "git://github.com/hapijs/ammo", "main": "lib/index.js", "types": "lib/index.d.ts", "keywords": ["http", "range", "utilities"], "files": ["lib"], "eslintConfig": {"extends": ["plugin:@hapi/module"]}, "dependencies": {"@hapi/hoek": "^11.0.2"}, "devDependencies": {"@hapi/code": "^9.0.3", "@hapi/eslint-plugin": "*", "@hapi/lab": "^25.1.2", "@hapi/wreck": "^18.0.1", "@types/node": "^17.0.31", "typescript": "~4.6.4"}, "scripts": {"test": "lab -a @hapi/code -t 100 -L -Y", "test-cov-html": "lab -a @hapi/code -r html -o coverage.html"}, "license": "BSD-3-<PERSON><PERSON>"}