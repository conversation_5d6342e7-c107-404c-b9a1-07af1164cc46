{"name": "@hapi/iron", "description": "Encapsulated tokens (encrypted and mac'ed objects)", "version": "7.0.1", "repository": "git://github.com/hueniverse/iron", "main": "lib/index.js", "types": "lib/index.d.ts", "files": ["lib"], "keywords": ["authentication", "encryption", "data integrity"], "eslintConfig": {"extends": ["plugin:@hapi/module"]}, "dependencies": {"@hapi/hoek": "^11.0.2", "@hapi/b64": "^6.0.1", "@hapi/boom": "^10.0.1", "@hapi/bourne": "^3.0.0", "@hapi/cryptiles": "^6.0.1"}, "devDependencies": {"@hapi/code": "^9.0.3", "@hapi/eslint-plugin": "*", "@hapi/lab": "^25.1.2", "@types/node": "^17.0.31", "typescript": "~4.6.4"}, "scripts": {"test": "lab -a @hapi/code -t 100 -L -Y", "test-cov-html": "lab -a @hapi/code -t 100 -L -r html -o coverage.html"}, "license": "BSD-3-<PERSON><PERSON>"}