{"name": "@hapi/mimos", "description": "Mime database interface", "version": "7.0.1", "repository": "git://github.com/hapijs/mimos", "main": "lib/index.js", "types": "lib/index.d.ts", "files": ["lib"], "keywords": ["mime", "database", "content-type"], "eslintConfig": {"extends": ["plugin:@hapi/module"]}, "dependencies": {"@hapi/hoek": "^11.0.2", "mime-db": "^1.52.0"}, "devDependencies": {"@hapi/code": "^9.0.0", "@hapi/eslint-plugin": "*", "@hapi/lab": "^25.0.1", "@types/node": "^17.0.31", "typescript": "~4.6.4"}, "scripts": {"test": "lab -m 5000 -t 100 -L -a @hapi/code -Y", "test-cov-html": "lab -a @hapi/code -r html -o coverage.html -L"}, "license": "BSD-3-<PERSON><PERSON>"}