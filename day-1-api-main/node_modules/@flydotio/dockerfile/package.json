{"name": "@flydotio/dockerfile", "version": "0.5.9", "description": "Dockerfile generator", "main": "./index.js", "bin": {"dockerfile": "index.js"}, "type": "module", "scripts": {"eslint": "eslint .", "eslint:fix": "eslint --fix .", "test": "mocha  --spec test/test.js", "test:capture": "TEST_CAPTURE=1 mocha"}, "author": "<PERSON>", "license": "MIT", "engines": {"node": ">=16.0.0"}, "dependencies": {"chalk": "^5.3.0", "diff": "^5.1.0", "ejs": "^3.1.9", "shell-quote": "^1.8.1", "yargs": "^17.7.2"}, "repository": {"type": "git", "url": "git+https://github.com/fly-apps/dockerfile-node.git"}, "publishConfig": {"access": "public"}, "devDependencies": {"chai": "^4.3.7", "eslint": "^8.47.0", "eslint-config-standard": "^17.1.0", "eslint-plugin-import": "^2.28.1", "eslint-plugin-promise": "^6.1.1", "mocha": "^10.2.0"}, "files": ["index.js", "gdf.js", "fly.js", "templates"]}