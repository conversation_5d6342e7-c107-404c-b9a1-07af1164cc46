{"version": 3, "file": "referenceTransformer.js", "sourceRoot": "", "sources": ["../../../src/metadataGeneration/transformer/referenceTransformer.ts"], "names": [], "mappings": ";;;AAGA,+CAA4C;AAC5C,uDAAoD;AACpD,kDAA+C;AAC/C,8CAAsD;AACtD,+DAAmE;AAEnE,MAAa,oBAAqB,SAAQ,yBAAW;IAC5C,MAAM,CAAC,KAAK,CAAC,cAAoC;QACtD,IAAI,cAAc,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAChC,OAAO,cAAc,CAAC,CAAC,CAAC,CAAC;QAC3B,CAAC;QAED,IAAI,cAAc,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,QAAQ,KAAK,SAAS,CAAC,EAAE,CAAC;YACpE,qEAAqE;YACrE,OAAO,iCAAe,CAAC,SAAS,CAAC,cAAoC,CAAC,CAAC;QACzE,CAAC;QAED,IAAI,cAAc,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,QAAQ,KAAK,WAAW,CAAC,EAAE,CAAC;YACtE,qEAAqE;YACrE,OAAO,IAAI,CAAC,eAAe,CAAC,cAAsC,CAAC,CAAC;QACtE,CAAC;QAED,MAAM,IAAI,kCAAqB,CAAC,oDAAoD,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,EAAE,CAAC,CAAC;IACxH,CAAC;IAEM,MAAM,CAAC,eAAe,CAAC,IAA0B;QACtD,IAAI,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;QAChD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC;YACrC,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7C,CAAC;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAEM,MAAM,CAAC,WAAW,CAAC,KAAyB,EAAE,MAA0B;QAC7E,MAAM,WAAW,GAAG,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,WAAW,KAAK,MAAM,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,WAAW,CAAC;QAEtJ,MAAM,UAAU,GAAG,KAAK,CAAC,UAAU,IAAI,MAAM,CAAC,UAAU,CAAC;QACzD,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO,IAAI,MAAM,CAAC,OAAO,CAAC;QAEhD,MAAM,UAAU,GAAG,CAAC,GAAG,KAAK,CAAC,UAAU,EAAE,GAAG,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,UAAU,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE,CAAC,SAAS,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAEjJ,MAAM,oBAAoB,GAAG,CAAC,KAAgB,EAAE,MAAiB,EAAa,EAAE;YAC9E,OAAO;gBACL,QAAQ,EAAE,OAAO;gBACjB,KAAK,EAAE,CAAC,KAAK,EAAE,MAAM,CAAC;aACvB,CAAC;QACJ,CAAC,CAAC;QAEF,MAAM,oBAAoB,GAAG,KAAK,CAAC,oBAAoB;YACrD,CAAC,CAAC,MAAM,CAAC,oBAAoB;gBAC3B,CAAC,CAAC,oBAAoB,CAAC,KAAK,CAAC,oBAAoB,EAAE,MAAM,CAAC,oBAAoB,CAAC;gBAC/E,CAAC,CAAC,KAAK,CAAC,oBAAoB;YAC9B,CAAC,CAAC,MAAM,CAAC,oBAAoB,CAAC;QAEhC,MAAM,MAAM,GAAuB;YACjC,QAAQ,EAAE,WAAW;YACrB,WAAW;YACX,UAAU;YACV,oBAAoB;YACpB,OAAO,EAAE,KAAK,CAAC,OAAO;YACtB,UAAU;YACV,OAAO;SACR,CAAC;QAEF,OAAO,MAAM,CAAC;IAChB,CAAC;IAEM,SAAS,CAAC,WAAiC,EAAE,WAAmB,EAAE,UAAiB;QACxF,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;QAE1D,MAAM,aAAa,GAAuB;YACxC,QAAQ,EAAE,UAAU;YACpB,OAAO,EAAE,2BAAY,CAAC,UAAU,CAAC,WAAW,CAAC;YAC7C,WAAW,EAAE,IAAI,CAAC,QAAQ,CAAC,kBAAkB,CAAC,WAAW,CAAC;YAC1D,OAAO,EAAE,WAAW;YACpB,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,WAAW,CAAC;YAChD,IAAI,EAAE,IAAI,2BAAY,CAAC,WAAW,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,WAAW,EAAE,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC,UAAU,IAAI,UAAU,CAAC,CAAC,OAAO,EAAE;YACrJ,UAAU,EAAE,IAAA,sCAAqB,EAAC,WAAW,CAAC,IAAI,EAAE;YACpD,GAAG,CAAC,OAAO,IAAI,EAAE,OAAO,EAAE,CAAC;SAC5B,CAAC;QACF,OAAO,aAAa,CAAC;IACvB,CAAC;CACF;AA5ED,oDA4EC"}