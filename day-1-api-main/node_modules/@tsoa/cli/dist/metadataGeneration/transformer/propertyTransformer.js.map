{"version": 3, "file": "propertyTransformer.js", "sourceRoot": "", "sources": ["../../../src/metadataGeneration/transformer/propertyTransformer.ts"], "names": [], "mappings": ";;;AAWA,2CAQoB;AAGpB,+CAA4C;AAC5C,8CAAsD;AACtD,kDAA+C;AAC/C,4DAA2D;AAC3D,+DAAmE;AACnE,uDAAyD;AACzD,+DAAyD;AACzD,qDAAoD;AAIpD,MAAa,mBAAoB,SAAQ,yBAAW;IAC3C,SAAS,CAAC,IAA6C,EAAE,aAA6B;QAC3F,MAAM,SAAS,GAAG,CAAC,CAA6B,EAAE,EAAE;YAClD,IAAI,MAAM,GAAG,IAAA,4BAAe,EAAC,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,KAAK,QAAQ,CAAC,CAAC;YACtE,MAAM,GAAG,MAAM,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,sBAAS,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC;YAC9D,OAAO,MAAM,CAAC;QAChB,CAAC,CAAC;QAEF,kBAAkB;QAClB,IAAI,IAAA,mCAAsB,EAAC,IAAI,CAAC,EAAE,CAAC;YACjC,OAAO,IAAI,CAAC,OAAO;iBAChB,MAAM,CAAC,CAAC,MAAM,EAA+B,EAAE,CAAC,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,IAAA,gCAAmB,EAAC,MAAM,CAAC,CAAC;iBAClG,GAAG,CAAC,CAAC,MAAyB,EAAE,EAAE,CAAC,IAAI,CAAC,qBAAqB,CAAC,MAAM,EAAE,aAAa,CAAC,CAAC,CAAC;QAC3F,CAAC;QAED,MAAM,UAAU,GAAsD,EAAE,CAAC;QACzE,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YAClC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,IAAA,kCAAqB,EAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,EAAE,CAAC;gBAC7H,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAC1B,CAAC;QACH,CAAC;QAED,MAAM,gBAAgB,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,IAAA,qCAAwB,EAAC,MAAM,CAAC,CAA2B,CAAC;QAEjH,IAAI,gBAAgB,IAAI,gBAAgB,CAAC,UAAU,EAAE,CAAC;YACpD,MAAM,qBAAqB,GAAG,gBAAgB,CAAC,UAAU,CAAC,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,IAAI,CAAC,qBAAqB,CAAC,SAAS,CAAC,CAAC,CAAC;YAErH,UAAU,CAAC,IAAI,CAAC,GAAG,qBAAqB,CAAC,CAAC;QAC5C,CAAC;QAED,OAAO,UAAU,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC,uBAAuB,CAAC,QAAQ,EAAE,aAAa,CAAC,CAAC,CAAC;IAC3F,CAAC;IAEO,qBAAqB,CAAC,iBAAoC,EAAE,aAA6B;QAC/F,IAAA,uBAAW,EAAC,iBAAiB,CAAC,IAAI,EAAE,IAAI,kCAAqB,CAAC,+CAA+C,CAAC,CAAC,CAAC;QAEhH,IAAI,QAAQ,GAAG,CAAC,iBAAiB,CAAC,aAAa,CAAC;QAChD,IAAI,aAAa,IAAI,aAAa,CAAC,IAAI,KAAK,uBAAU,CAAC,UAAU,EAAE,CAAC;YAClE,QAAQ,GAAG,IAAI,CAAC;QAClB,CAAC;aAAM,IAAI,aAAa,IAAI,aAAa,CAAC,IAAI,KAAK,uBAAU,CAAC,aAAa,EAAE,CAAC;YAC5E,QAAQ,GAAG,KAAK,CAAC;QACnB,CAAC;QAED,MAAM,GAAG,GAAG,2BAAY,CAAC,UAAU,CAAC,iBAAiB,CAAC,CAAC;QAEvD,MAAM,QAAQ,GAAkB;YAC9B,OAAO,EAAE,GAAG;YACZ,WAAW,EAAE,IAAI,CAAC,QAAQ,CAAC,kBAAkB,CAAC,iBAAiB,CAAC;YAChE,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,iBAAiB,CAAC;YACxD,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,iBAAiB,CAAC;YACtD,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,iBAAiB,CAAC;YACtD,QAAQ;YACR,IAAI,EAAE,IAAI,2BAAY,CAAC,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,iBAAiB,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE;YACrI,UAAU,EAAE,IAAA,sCAAqB,EAAC,iBAAiB,CAAC,IAAI,EAAE;YAC1D,UAAU,EAAE,IAAA,4BAAe,EAAC,iBAAiB,EAAE,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,KAAK,YAAY,CAAC;YACxF,UAAU,EAAE,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,iBAAiB,CAAC;SAC9D,CAAC;QACF,OAAO,QAAQ,CAAC;IAClB,CAAC;IAEO,uBAAuB,CAAC,mBAA+D,EAAE,aAA6B;QAC5H,IAAI,QAAQ,GAAG,mBAAmB,CAAC,IAAI,CAAC;QAExC,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,WAAW,CAAC,iBAAiB,CAAC,mBAAmB,CAAC,CAAC;QAExF,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,2BAA2B;YAC3B,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,WAAW,CAAC,cAAc,CAAC,MAAM,EAAE,SAAS,EAAE,6BAAgB,CAAC,YAAY,CAAE,CAAC;QACjH,CAAC;QAED,MAAM,IAAI,GAAG,IAAI,2BAAY,CAAC,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,mBAAmB,EAAE,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,OAAO,EAAE,CAAC;QAE7H,IAAI,QAAQ,GAAG,CAAC,mBAAmB,CAAC,aAAa,IAAI,CAAC,mBAAmB,CAAC,WAAW,CAAC;QACtF,IAAI,aAAa,IAAI,aAAa,CAAC,IAAI,KAAK,uBAAU,CAAC,UAAU,EAAE,CAAC;YAClE,QAAQ,GAAG,IAAI,CAAC;QAClB,CAAC;aAAM,IAAI,aAAa,IAAI,aAAa,CAAC,IAAI,KAAK,uBAAU,CAAC,aAAa,EAAE,CAAC;YAC5E,QAAQ,GAAG,KAAK,CAAC;QACnB,CAAC;QACD,IAAI,GAAG,GAAG,IAAA,uCAAmB,EAAC,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;QAClG,IAAI,GAAG,KAAK,SAAS,EAAE,CAAC;YACtB,GAAG,GAAG,2BAAY,CAAC,UAAU,CAAC,mBAAmB,CAAC,CAAC;QACrD,CAAC;QAED,MAAM,QAAQ,GAAkB;YAC9B,OAAO,EAAE,GAAG;YACZ,WAAW,EAAE,IAAI,CAAC,QAAQ,CAAC,kBAAkB,CAAC,mBAAmB,CAAC;YAClE,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,mBAAmB,CAAC;YAC1D,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,mBAAmB,CAAC;YACxD,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,mBAAmB,CAAC;YACxD,QAAQ;YACR,IAAI;YACJ,UAAU,EAAE,IAAA,sCAAqB,EAAC,mBAAmB,CAAC,IAAI,EAAE;YAC5D,yGAAyG;YACzG,UAAU,EAAE,IAAA,4BAAe,EAAC,mBAAmB,EAAE,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,KAAK,YAAY,CAAC,IAAI,IAAA,4BAAW,EAAC,mBAAmB,EAAE,UAAU,CAAC,EAAE,CAAC,UAAU,CAAC,IAAI,KAAK,YAAY,CAAC;YAC9K,UAAU,EAAE,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,mBAAmB,CAAC;SAChE,CAAC;QACF,OAAO,QAAQ,CAAC;IAClB,CAAC;CACF;AAlGD,kDAkGC"}