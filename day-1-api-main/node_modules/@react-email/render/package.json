{"name": "@react-email/render", "version": "0.0.17", "description": "Transform React components into HTML email templates", "sideEffects": false, "main": "./dist/browser/index.js", "module": "./dist/browser/index.mjs", "types": "./dist/browser/index.d.ts", "files": ["dist/**"], "exports": {".": {"node": {"import": {"types": "./dist/node/index.d.mts", "default": "./dist/node/index.mjs"}, "require": {"types": "./dist/node/index.d.ts", "default": "./dist/node/index.js"}}, "deno": {"import": {"types": "./dist/browser/index.d.mts", "default": "./dist/browser/index.mjs"}, "require": {"types": "./dist/browser/index.d.ts", "default": "./dist/browser/index.js"}}, "worker": {"import": {"types": "./dist/browser/index.d.mts", "default": "./dist/browser/index.mjs"}, "require": {"types": "./dist/browser/index.d.ts", "default": "./dist/browser/index.js"}}, "browser": {"import": {"types": "./dist/browser/index.d.mts", "default": "./dist/browser/index.mjs"}, "require": {"types": "./dist/browser/index.d.ts", "default": "./dist/browser/index.js"}}, "default": {"import": {"types": "./dist/node/index.d.mts", "default": "./dist/node/index.mjs"}, "require": {"types": "./dist/node/index.d.ts", "default": "./dist/node/index.js"}}}}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/resend/react-email.git", "directory": "packages/render"}, "keywords": ["react", "email"], "engines": {"node": ">=18.0.0"}, "dependencies": {"html-to-text": "9.0.5", "js-beautify": "^1.14.11", "react-promise-suspense": "0.3.4"}, "peerDependencies": {"react": "^18.2.0", "react-dom": "^18.2.0"}, "devDependencies": {"@babel/preset-react": "7.23.3", "@edge-runtime/vm": "3.1.8", "@types/html-to-text": "9.0.4", "@types/js-beautify": "1.14.3", "jsdom": "23.0.1", "tsup": "7.2.0", "typescript": "5.1.6", "vitest": "1.1.2", "eslint-config-custom": "0.0.0", "tsconfig": "0.0.0"}, "publishConfig": {"access": "public"}, "scripts": {"build": "tsup-node", "clean": "rm -rf dist", "dev": "tsup-node --watch", "lint": "eslint .", "test": "vitest run", "test:watch": "vitest"}}