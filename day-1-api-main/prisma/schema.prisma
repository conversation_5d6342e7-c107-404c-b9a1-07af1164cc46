// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider  = "postgresql"
  url       = env("DATABASE_URL")
  directUrl = env("DIRECT_URL")
}

model User {
  id               String              @id @default(uuid())
  email            String              @unique @db.VarChar(200)
  password         String?             @db.VarChar(100)
  pin              String?             @db.VarChar(100)
  phoneNumber      String?             @unique @db.VarChar(20)
  phoneVerifiedAt  DateTime?           @db.Timestamptz(3)
  emailVerifiedAt  DateTime?           @db.Timestamptz(3)
  passwordSetAt    DateTime?           @db.Timestamptz(3)
  pinSetAt         DateTime?           @db.Timestamptz(3)
  biometricsSetAt  DateTime?           @db.Timestamptz(3)
  stripeCustomerId String?             @db.VarChar(250)
  updatedAt        DateTime            @updatedAt @db.Timestamptz(3)
  createdAt        DateTime            @default(now()) @db.Timestamptz(3)
  Location         Location?
  Wallet           UserWallet?
  Beneficiaries    UserBeneficiaries[]
  Transactions     Transaction[]

  VisaDetails     VisaKycDetails[]
  PassportDetails PassportKycDetails[]

  @@index([email])
}

model Location {
  id        Int      @id @default(autoincrement())
  userId    String   @unique
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  createdAt DateTime @default(now()) @db.Timestamptz(3)
  updatedAt DateTime @updatedAt @db.Timestamptz(3)
}

model EmailOTP {
  id        String   @id @default(cuid())
  otp       String   @db.VarChar(200)
  createdAt DateTime @default(now()) @db.Timestamptz(3)
  updatedAt DateTime @updatedAt @db.Timestamptz(3)
  expiredAt DateTime @db.Timestamptz(3)
  email     String   @db.VarChar(200)
}

model UserWallet {
  id             Int             @id @default(autoincrement())
  userId         String          @unique
  user           User            @relation(fields: [userId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  createdAt      DateTime        @default(now()) @db.Timestamptz(3)
  updatedAt      DateTime        @updatedAt @db.Timestamptz(3)
  balance        Decimal         @default(0.00) @db.Decimal(40, 2)
  baseBalance    Decimal         @default(0.00) @db.Decimal(15, 2)
  balanceHistory WalletBalance[]
}

model WalletBalance {
  id            Int         @id @default(autoincrement())
  walletId      Int
  wallet        UserWallet  @relation(fields: [walletId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  balance       Decimal     @default(0.00) @db.Decimal(40, 2)
  baseBalance   Decimal     @default(0.00) @db.Decimal(15, 2)
  transactionId Int         @unique
  transaction   Transaction @relation(fields: [transactionId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  timestamp     DateTime    @default(now()) @db.Timestamptz(3)
}

model Transaction {
  id                        Int               @id @default(autoincrement())
  transactionType           TransactionType   @relation(fields: [transactionTypeId], references: [id])
  transactionTypeId         Int
  transactionStatus         TransactionStatus @relation(fields: [transactionStatusId], references: [id])
  transactionStatusId       Int
  stripeTransactionStatus   String?
  paystackTransactionStatus String?

  stripeClientSecret   String?
  paystackTransferCode String?
  paystackReference    String?

  transactionAmount     Decimal            @default(0.00) @db.Decimal(40, 2)
  baseTransactionAmount Decimal            @default(0.00) @db.Decimal(15, 2)
  transactionId         String             @unique
  createdAt             DateTime           @default(now()) @db.Timestamptz(3)
  updatedAt             DateTime           @updatedAt @db.Timestamptz(3)
  walletBalance         WalletBalance?
  exchangeRate          Json
  userId                String
  user                  User               @relation(fields: [userId], references: [id], onDelete: Cascade)
  beneficiaryId         Int?
  beneficiary           UserBeneficiaries? @relation(fields: [beneficiaryId], references: [id], onDelete: Cascade)
}

model TransactionType {
  id           Int           @id @default(autoincrement())
  typeName     String        @unique
  transactions Transaction[]
}

model TransactionStatus {
  id           Int           @id @default(autoincrement())
  statusName   String        @unique
  transactions Transaction[]
}

model UserBeneficiaries {
  id                  Int                @id @default(autoincrement())
  userId              String
  User                User               @relation(fields: [userId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  beneficiaryDetailId Int
  BeneficiaryDetails  BeneficiaryDetails @relation(fields: [beneficiaryDetailId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  createdAt           DateTime           @default(now()) @db.Timestamptz(3)
  lastUsedAt          DateTime           @db.Timestamptz(3)
  NoOfTransactions    Int
  transactions        Transaction[]
  isFavorite          Boolean            @default(false)
  deletedAt           DateTime?          @db.Timestamptz(3)
}

model BeneficiaryDetails {
  id                  Int                 @id @default(autoincrement())
  bankName            String              @db.VarChar(100)
  bankCode            String
  createdAt           DateTime            @default(now()) @db.Timestamptz(3)
  paystackRecipientId String
  accountName         String
  accountNumber       String
  UserBeneficiaries   UserBeneficiaries[]

  @@index([paystackRecipientId, accountName, bankCode, accountNumber])
}

model VisaKycAttempts {
  id             Int      @id @default(autoincrement())
  userId         String
  visaNumber     String   @db.VarChar(256)
  visaType       String   @db.VarChar(256)
  visaIssueDate  DateTime
  visaExpiryDate DateTime
  sessionId      String
  firstName      String   @db.VarChar(256)
  lastName       String   @db.VarChar(256)
  dateOfBirth    DateTime
  createdAt      DateTime @default(now()) @db.Timestamptz(3)
  updatedAt      DateTime @updatedAt @db.Timestamptz(3)
  country        String   @db.VarChar(2)
  status         String?  @db.VarChar(256)
  reason         String?  @db.VarChar(256)

  verifiedDetails VisaKycDetails?

  @@index([sessionId])
}

model VisaKycDetails {
  id                     Int             @id @default(autoincrement())
  userId                 String
  User                   User            @relation(fields: [userId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  attemptId              Int             @unique
  Attempt                VisaKycAttempts @relation(fields: [attemptId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  gender                 String?         @db.VarChar(1)
  idNumber               String?         @db.VarChar(256)
  lastName               String?         @db.VarChar(256)
  firstName              String?         @db.VarChar(256)
  nationality            String?         @db.VarChar(256)
  dateOfBirth            DateTime?
  yearOfBirth            String?
  placeOfBirth           String?         @db.VarChar(256)
  occupation             String?         @db.VarChar(256)
  extraNames             String?         @db.VarChar(256)
  employer               String?         @db.VarChar(256)
  riskScore              Decimal?        @db.Decimal(3, 2)
  riskLabels             Json?           @db.JsonB
  additionalVerifiedData Json?           @db.JsonB
  document               Json?           @db.JsonB
  ip                     String?         @db.VarChar(16)
  decisionTime           DateTime?       @db.Timestamptz(3)
  acceptanceTime         DateTime?       @db.Timestamptz(3)
  createdAt              DateTime        @default(now()) @db.Timestamptz(3)
  updatedAt              DateTime        @updatedAt @db.Timestamptz(3)
}

model PassportKycAttempts {
  id             Int      @id @default(autoincrement())
  userId         String
  sessionId      String
  firstName      String   @db.VarChar(256)
  lastName       String   @db.VarChar(256)
  dateOfBirth    DateTime
  documentNumber String   @db.VarChar(256)
  documentType   String   @default("PASSPORT") @db.VarChar(256)
  reason         String?  @db.VarChar(256)
  expiryDate     DateTime

  country         String              @db.VarChar(2)
  createdAt       DateTime            @default(now()) @db.Timestamptz(3)
  updatedAt       DateTime            @updatedAt @db.Timestamptz(3)
  status          String?             @db.VarChar(256)
  verifiedDetails PassportKycDetails?

  @@index([sessionId])
}

model PassportKycDetails {
  id                     Int                 @id @default(autoincrement())
  userId                 String
  User                   User                @relation(fields: [userId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  attemptId              Int                 @unique
  Attempt                PassportKycAttempts @relation(fields: [attemptId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  gender                 String?             @db.VarChar(1)
  idNumber               String?             @db.VarChar(256)
  lastName               String?             @db.VarChar(256)
  firstName              String?             @db.VarChar(256)
  nationality            String?             @db.VarChar(256)
  dateOfBirth            DateTime?
  placeOfBirth           String?             @db.VarChar(256)
  occupation             String?             @db.VarChar(256)
  extraNames             String?             @db.VarChar(256)
  employer               String?             @db.VarChar(256)
  riskScore              Decimal?            @db.Decimal(3, 2)
  riskLabels             Json?               @db.JsonB
  additionalVerifiedData Json?               @db.JsonB
  document               Json?               @db.JsonB
  ip                     String?             @db.VarChar(16)
  decisionTime           DateTime?           @db.Timestamptz(3)
  acceptanceTime         DateTime?           @db.Timestamptz(3)
  createdAt              DateTime            @default(now()) @db.Timestamptz(3)
  updatedAt              DateTime            @updatedAt @db.Timestamptz(3)
}
