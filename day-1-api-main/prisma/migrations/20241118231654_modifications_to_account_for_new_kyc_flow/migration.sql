/*
  Warnings:

  - You are about to drop the `UserKycAttempts` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `UserKycDetails` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `VisaDetails` table. If the table is not empty, all the data it contains will be lost.

*/
-- DropForeignKey
ALTER TABLE "UserKycAttempts" DROP CONSTRAINT "UserKycAttempts_userId_fkey";

-- DropForeignKey
ALTER TABLE "UserKycDetails" DROP CONSTRAINT "UserKycDetails_attemptId_fkey";

-- DropForeignKey
ALTER TABLE "UserKycDetails" DROP CONSTRAINT "UserKycDetails_userId_fkey";

-- DropForeignKey
ALTER TABLE "VisaDetails" DROP CONSTRAINT "VisaDetails_userId_fkey";

-- DropTable
DROP TABLE "UserKycAttempts";

-- DropTable
DROP TABLE "UserKycDetails";

-- DropTable
DROP TABLE "VisaDetails";

-- CreateTable
CREATE TABLE "VisaKycAttempts" (
    "id" SERIAL NOT NULL,
    "userId" TEXT NOT NULL,
    "visaNumber" VARCHAR(256) NOT NULL,
    "visaType" VARCHAR(256) NOT NULL,
    "visaIssueDate" TIMESTAMP(3) NOT NULL,
    "visaExpiryDate" TIMESTAMP(3) NOT NULL,
    "sessionId" TEXT NOT NULL,
    "firstName" VARCHAR(256) NOT NULL,
    "lastName" VARCHAR(256) NOT NULL,
    "dateOfBirth" TIMESTAMP(3) NOT NULL,
    "createdAt" TIMESTAMPTZ(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMPTZ(3) NOT NULL,
    "country" VARCHAR(2) NOT NULL,
    "status" VARCHAR(256),

    CONSTRAINT "VisaKycAttempts_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "VisaKycDetails" (
    "id" SERIAL NOT NULL,
    "userId" TEXT NOT NULL,
    "attemptId" INTEGER NOT NULL,
    "gender" VARCHAR(1),
    "idNumber" VARCHAR(256),
    "lastName" VARCHAR(256),
    "firstName" VARCHAR(256),
    "nationality" VARCHAR(256),
    "dateOfBirth" TIMESTAMP(3),
    "yearOfBirth" TEXT,
    "placeOfBirth" VARCHAR(256),
    "occupation" VARCHAR(256),
    "extraNames" VARCHAR(256),
    "employer" VARCHAR(256),
    "riskScore" DECIMAL(3,2),
    "riskLabels" JSONB,
    "additionalVerifiedData" JSONB,
    "document" JSONB,
    "ip" VARCHAR(16),
    "decisionTime" TIMESTAMPTZ(3),
    "acceptanceTime" TIMESTAMPTZ(3),
    "createdAt" TIMESTAMPTZ(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMPTZ(3) NOT NULL,

    CONSTRAINT "VisaKycDetails_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "PassportKycAttempts" (
    "id" SERIAL NOT NULL,
    "userId" TEXT NOT NULL,
    "sessionId" TEXT NOT NULL,
    "firstName" VARCHAR(256) NOT NULL,
    "lastName" VARCHAR(256) NOT NULL,
    "dateOfBirth" TIMESTAMP(3) NOT NULL,
    "documentNumber" VARCHAR(256) NOT NULL,
    "documentType" VARCHAR(256) NOT NULL,
    "reason" VARCHAR(256),
    "country" VARCHAR(2) NOT NULL,
    "createdAt" TIMESTAMPTZ(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMPTZ(3) NOT NULL,
    "status" VARCHAR(256),

    CONSTRAINT "PassportKycAttempts_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "PassportKycDetails" (
    "id" SERIAL NOT NULL,
    "userId" TEXT NOT NULL,
    "attemptId" INTEGER NOT NULL,
    "gender" VARCHAR(1),
    "idNumber" VARCHAR(256),
    "lastName" VARCHAR(256),
    "firstName" VARCHAR(256),
    "nationality" VARCHAR(256),
    "dateOfBirth" TIMESTAMP(3),
    "yearOfBirth" TEXT,
    "placeOfBirth" VARCHAR(256),
    "occupation" VARCHAR(256),
    "extraNames" VARCHAR(256),
    "employer" VARCHAR(256),
    "riskScore" DECIMAL(3,2),
    "riskLabels" JSONB,
    "additionalVerifiedData" JSONB,
    "document" JSONB,
    "ip" VARCHAR(16),
    "decisionTime" TIMESTAMPTZ(3),
    "acceptanceTime" TIMESTAMPTZ(3),
    "createdAt" TIMESTAMPTZ(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMPTZ(3) NOT NULL,

    CONSTRAINT "PassportKycDetails_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "VisaKycDetails_userId_key" ON "VisaKycDetails"("userId");

-- CreateIndex
CREATE UNIQUE INDEX "VisaKycDetails_attemptId_key" ON "VisaKycDetails"("attemptId");

-- CreateIndex
CREATE UNIQUE INDEX "PassportKycDetails_userId_key" ON "PassportKycDetails"("userId");

-- CreateIndex
CREATE UNIQUE INDEX "PassportKycDetails_attemptId_key" ON "PassportKycDetails"("attemptId");

-- AddForeignKey
ALTER TABLE "VisaKycDetails" ADD CONSTRAINT "VisaKycDetails_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "VisaKycDetails" ADD CONSTRAINT "VisaKycDetails_attemptId_fkey" FOREIGN KEY ("attemptId") REFERENCES "VisaKycAttempts"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PassportKycDetails" ADD CONSTRAINT "PassportKycDetails_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PassportKycDetails" ADD CONSTRAINT "PassportKycDetails_attemptId_fkey" FOREIGN KEY ("attemptId") REFERENCES "PassportKycAttempts"("id") ON DELETE CASCADE ON UPDATE CASCADE;
