/*
  Warnings:

  - You are about to alter the column `balance` on the `UserWallet` table. The data in that column could be lost. The data in that column will be cast from `Decimal(20,2)` to `Decimal(10,2)`.
  - You are about to alter the column `baseBalance` on the `UserWallet` table. The data in that column could be lost. The data in that column will be cast from `Decimal(20,2)` to `Decimal(10,2)`.
  - You are about to drop the column `beneficiaryId` on the `WalletBalance` table. All the data in the column will be lost.
  - You are about to drop the column `exchangeRate` on the `WalletBalance` table. All the data in the column will be lost.
  - You are about to drop the column `transactionStatus` on the `WalletBalance` table. All the data in the column will be lost.
  - You are about to drop the column `transactionType` on the `WalletBalance` table. All the data in the column will be lost.
  - You are about to alter the column `balance` on the `WalletBalance` table. The data in that column could be lost. The data in that column will be cast from `Decimal(20,2)` to `Decimal(10,2)`.
  - You are about to alter the column `baseBalance` on the `WalletBalance` table. The data in that column could be lost. The data in that column will be cast from `Decimal(20,2)` to `Decimal(10,2)`.
  - A unique constraint covering the columns `[transactionId]` on the table `WalletBalance` will be added. If there are existing duplicate values, this will fail.
  - Changed the type of `transactionId` on the `WalletBalance` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.

*/
-- DropForeignKey
ALTER TABLE "WalletBalance" DROP CONSTRAINT "WalletBalance_beneficiaryId_fkey";

-- AlterTable
ALTER TABLE "User" ADD COLUMN     "firstName" VARCHAR(200),
ADD COLUMN     "lastName" VARCHAR(200);

-- AlterTable
ALTER TABLE "UserWallet" ALTER COLUMN "balance" SET DATA TYPE DECIMAL(10,2),
ALTER COLUMN "baseBalance" SET DATA TYPE DECIMAL(10,2);

-- AlterTable
ALTER TABLE "WalletBalance" DROP COLUMN "beneficiaryId",
DROP COLUMN "exchangeRate",
DROP COLUMN "transactionStatus",
DROP COLUMN "transactionType",
ALTER COLUMN "balance" SET DATA TYPE DECIMAL(10,2),
ALTER COLUMN "baseBalance" SET DATA TYPE DECIMAL(10,2),
DROP COLUMN "transactionId",
ADD COLUMN     "transactionId" INTEGER NOT NULL;

-- DropEnum
DROP TYPE "TransactionStatus";

-- DropEnum
DROP TYPE "TransactionType";

-- CreateTable
CREATE TABLE "Transaction" (
    "id" SERIAL NOT NULL,
    "transactionTypeId" INTEGER NOT NULL,
    "transactionStatusId" INTEGER NOT NULL,
    "transactionAmount" DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    "baseTransactionAmount" DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    "transactionId" TEXT NOT NULL,
    "createdAt" TIMESTAMPTZ(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMPTZ(3) NOT NULL,
    "exchangeRate" JSONB NOT NULL,
    "userId" TEXT NOT NULL,
    "beneficiaryId" INTEGER,

    CONSTRAINT "Transaction_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "TransactionType" (
    "id" SERIAL NOT NULL,
    "statusName" TEXT NOT NULL,

    CONSTRAINT "TransactionType_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "TransactionStatus" (
    "id" SERIAL NOT NULL,
    "statusName" TEXT NOT NULL,

    CONSTRAINT "TransactionStatus_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "Transaction_transactionId_key" ON "Transaction"("transactionId");

-- CreateIndex
CREATE UNIQUE INDEX "TransactionType_statusName_key" ON "TransactionType"("statusName");

-- CreateIndex
CREATE UNIQUE INDEX "TransactionStatus_statusName_key" ON "TransactionStatus"("statusName");

-- CreateIndex
CREATE INDEX "User_email_idx" ON "User"("email");

-- CreateIndex
CREATE UNIQUE INDEX "WalletBalance_transactionId_key" ON "WalletBalance"("transactionId");

-- AddForeignKey
ALTER TABLE "WalletBalance" ADD CONSTRAINT "WalletBalance_transactionId_fkey" FOREIGN KEY ("transactionId") REFERENCES "Transaction"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Transaction" ADD CONSTRAINT "Transaction_transactionTypeId_fkey" FOREIGN KEY ("transactionTypeId") REFERENCES "TransactionType"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Transaction" ADD CONSTRAINT "Transaction_transactionStatusId_fkey" FOREIGN KEY ("transactionStatusId") REFERENCES "TransactionStatus"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Transaction" ADD CONSTRAINT "Transaction_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Transaction" ADD CONSTRAINT "Transaction_beneficiaryId_fkey" FOREIGN KEY ("beneficiaryId") REFERENCES "UserBeneficiaries"("id") ON DELETE CASCADE ON UPDATE CASCADE;
