/*
  Warnings:

  - You are about to drop the column `accountName` on the `UserBeneficiaries` table. All the data in the column will be lost.
  - You are about to drop the column `bankCode` on the `UserBeneficiaries` table. All the data in the column will be lost.
  - You are about to drop the column `bankName` on the `UserBeneficiaries` table. All the data in the column will be lost.
  - You are about to drop the column `paystackRecipientId` on the `UserBeneficiaries` table. All the data in the column will be lost.
  - Added the required column `beneficiaryDetailId` to the `UserBeneficiaries` table without a default value. This is not possible if the table is not empty.

*/
-- DropIndex
DROP INDEX "UserBeneficiaries_paystackRecipientId_idx";

-- AlterTable
ALTER TABLE "UserBeneficiaries" DROP COLUMN "accountName",
DROP COLUMN "bankCode",
DROP COLUMN "bankName",
DROP COLUMN "paystackRecipientId",
ADD COLUMN     "beneficiaryDetailId" INTEGER NOT NULL;

-- CreateTable
CREATE TABLE "BeneficiaryDetails" (
    "id" SERIAL NOT NULL,
    "bankName" VARCHAR(100) NOT NULL,
    "bankCode" INTEGER NOT NULL,
    "createdAt" TIMESTAMPTZ(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "paystackRecipientId" TEXT NOT NULL,
    "accountName" TEXT NOT NULL,

    CONSTRAINT "BeneficiaryDetails_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "BeneficiaryDetails_paystackRecipientId_accountName_bankCode_idx" ON "BeneficiaryDetails"("paystackRecipientId", "accountName", "bankCode");

-- AddForeignKey
ALTER TABLE "UserBeneficiaries" ADD CONSTRAINT "UserBeneficiaries_beneficiaryDetailId_fkey" FOREIGN KEY ("beneficiaryDetailId") REFERENCES "BeneficiaryDetails"("id") ON DELETE CASCADE ON UPDATE CASCADE;
