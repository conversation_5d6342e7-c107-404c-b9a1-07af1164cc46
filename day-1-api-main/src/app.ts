import helmet from 'helmet'
import rTracer from 'cls-rtracer'
import morgan from 'morgan'
import { createStream } from 'rotating-file-stream'
import cors from 'cors'
import express, { Response as ExResponse, Request as ExRequest } from 'express'
import swaggerUi from 'swagger-ui-express'
import { RegisterRoutes } from '../build/routes'
import { errorHandler } from './middleware/errorhandler.middleware'
import { NotFountMiddleware } from './middleware/notfound.middleware'
import path from 'path'
import { safeString } from './utils/utils'

const app = express()

const accessLogStream = createStream('access.log', {
  size: '10M', // rotate every 10 MegaBytes written
  interval: '7d', // rotate every 7 days
  compress: 'gzip', // compress rotated files
  path: path.join(__dirname, '../logs'),
})

app.use(express.urlencoded({ extended: true }))
app.use(helmet())
app.use(cors())
app.use(rTracer.expressMiddleware())

const morganFormat =
  '[:requestId] :remote-addr - :remote-user [:date[clf]] ":method :url HTTP/:http-version" :status :res[content-length] ":referrer" ":user-agent"'
app.use(morgan(morganFormat, { stream: accessLogStream }))
app.use(morgan(morganFormat))
morgan.token('requestId', () => safeString(rTracer.id(), ''))

app.use('/docs', swaggerUi.serve, async (_: ExRequest, res: ExResponse) => {
  return res.send(swaggerUi.generateHTML(await import('../build/swagger.json')))
})

app.get('/', (_, res) => {
  return res.status(200).json({
    status: 'success',
    message: 'Server says hello',
  })
})
app.get('/health-check', (_, res) => {
  return res.status(200).json({
    status: 'success',
    message: 'Server is healthy',
  })
})
app.get('/dummy', (_, res) => {
  return res.status(200).json({
    status: 'success',
    message: 'Dummy endpoint',
  })
})
RegisterRoutes(app)

app.use(NotFountMiddleware)
app.use(errorHandler)

export { app }
