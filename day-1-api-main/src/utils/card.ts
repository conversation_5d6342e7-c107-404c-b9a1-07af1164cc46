/**
 * Validates strings with <PERSON><PERSON>'s algorithm to ensure they are valid card numbers
 * @param {string} cardNumber - The card number to validate
 * @return {boolean} description
 */
export const isValidCardNumber = (cardNumber: string): boolean => {
  if (isNaN(+cardNumber)) {
    return false
  }

  const arr = cardNumber
    .split('')
    .reverse()
    .map((x) => parseInt(x))
  const lastDigit = arr.splice(0, 1)[0]
  let sum = arr.reduce(
    (acc, val, i) => (i % 2 !== 0 ? acc + val : acc + ((val * 2) % 9) || 9),
    0,
  )
  sum += lastDigit
  return sum % 10 === 0
}
