import { PaginationData } from '../types/req.types'

export const getPagination = (params: { page: number; size: number }) => {
  const { size, page } = params
  return {
    take: size,
    skip: page > 0 ? size * (page - 1) : 0,
  }
}

export const paginationData = <T extends Array<unknown>>(params: {
  data: T
  page: number
  size: number
  total: number
  paginate: boolean
}): PaginationData<T> => {
  const { data, page, size, total, paginate } = params
  const totalPages = paginate ? Math.ceil(total / size) : 1

  return {
    paginationDetails: {
      totalPages: paginate ? totalPages : 1,
      totalItems: total,
      currentPage: paginate ? page : 1,
      hitsPerPage: paginate ? size : total,
    },
    data,
  }
}
