import { CardsService } from './cards.service'
import { inject, injectable } from 'tsyringe'
import {
  Controller,
  Route,
  Post,
  SuccessResponse,
  Body,
  Tags,
  Security,
  Request,
  Get,
  Path,
  Middlewares,
} from 'tsoa'
import { ZodErrorToValidateError } from '../../middleware/errorhandler.middleware'
import { AuthenticatedRequest } from '../../types/req.types'
import { UnauthenticatedError } from '../../errors/UnauthenticatedError'
import {
  AddCardBodyDto,
  AddCardBodySchema,
  RetrieveCardDto,
  RetrieveCardSchema,
} from '../../types/dtos.types'
import express from 'express'

@injectable()
@Tags('Cards Controller')
@Route('cards')
@Middlewares(express.json())
export class CardsController extends Controller {
  constructor(@inject('cardsService') private cardsService: CardsService) {
    super()
  }

  @Post('add')
  @Security('jwt')
  @SuccessResponse('201', 'Created')
  public async AddCard(
    @Request() request: AuthenticatedRequest,
    @Body() body: AddCardBodyDto,
  ) {
    const check = await AddCardBodySchema.safeParseAsync(body)
    if (!check.success) {
      throw new ZodErrorToValidateError<AddCardBodyDto>(check.error, 'body')
    }
    const user = request.user

    if (!user) {
      throw new UnauthenticatedError('Unauthenticated')
    }
    const response = await this.cardsService.addCard(body, user)
    this.setStatus(response.statusCode)
    return response
  }

  @Get('all')
  @Security('jwt')
  @SuccessResponse('200', 'OK')
  public async GetAllCardsForUser(@Request() request: AuthenticatedRequest) {
    const user = request.user

    if (!user) {
      throw new UnauthenticatedError('Unauthenticated')
    }

    const response = await this.cardsService.getAllCardsForUser(user)
    this.setStatus(response.statusCode)
    return response
  }
  @Get(':cardId')
  @Security('jwt')
  @SuccessResponse('200', 'OK')
  public async GetSingleCardForUser(
    @Request() request: AuthenticatedRequest,
    @Path() cardId: string,
  ) {
    const check = await RetrieveCardSchema.safeParseAsync({ cardId })
    if (!check.success) {
      throw new ZodErrorToValidateError<RetrieveCardDto>(check.error, 'path')
    }
    const user = request.user

    if (!user) {
      throw new UnauthenticatedError('Unauthenticated')
    }

    const response = await this.cardsService.getCard(user, { cardId })
    this.setStatus(response.statusCode)
    return response
  }
}
