import {
  Body,
  Controller,
  Delete,
  Get,
  Middlewares,
  Patch,
  Path,
  Post,
  Query,
  Request,
  Route,
  Security,
  SuccessResponse,
  Tags,
} from 'tsoa'
import { inject, injectable } from 'tsyringe'
import { TransfersService } from './transfer.service'
import { AuthenticatedRequest } from '../../types/req.types'
import {
  InitiateTransferDto,
  InitiateTransferSchema,
  RetrieveTransactionDto,
  RetrieveTransactionSchema,
  PaginatedBeneficiaryQueryDto,
  PaginatedBeneficiaryQuerySchema,
  ToggleFavoriteBeneficiariesBodySchema,
  ToggleFavoriteBeneficiariesBodyDTO,
} from '../../types/dtos.types'
import { ZodErrorToValidateError } from '../../middleware/errorhandler.middleware'
import { UnauthenticatedError } from '../../errors/UnauthenticatedError'
import express from 'express'

@injectable()
@Route('transfers')
@Tags('Transfers Controller')
@Middlewares(express.json())
export class TransferController extends Controller {
  constructor(
    @inject('transfersService') private transfersService: TransfersService,
  ) {
    super()
  }

  @Post('create')
  @Security('jwt')
  @SuccessResponse('201', 'OK')
  public async InitializePaystackTransfer(
    @Request() request: AuthenticatedRequest,
    @Body() body: InitiateTransferDto,
  ) {
    const check = await InitiateTransferSchema.safeParseAsync(body)
    if (!check.success) {
      throw new ZodErrorToValidateError<InitiateTransferDto>(
        check.error,
        'body',
      )
    }
    const user = request.user

    if (!user) {
      throw new UnauthenticatedError('Unauthenticated')
    }

    const response = await this.transfersService.initiateTransfer(body, user)
    this.setStatus(response.statusCode)
    return response
  }

  @Get('beneficiary/all')
  @Security('jwt')
  @SuccessResponse('200', 'OK')
  public async ListBeneficiaries(
    @Request() request: AuthenticatedRequest,
    @Query() searchString?: string,
    @Query() page?: string,
    @Query() hitsPerPage?: string,
    @Query() isFavorite?: boolean,
  ) {
    const req = {
      searchString,
      page,
      hitsPerPage,
      isFavorite,
    }

    if (!req.hitsPerPage) delete req.hitsPerPage
    if (!req.page) delete req.page
    if (!req.searchString) delete req.searchString
    if (!req.isFavorite) delete req.isFavorite
    const check = await PaginatedBeneficiaryQuerySchema.safeParseAsync(req)
    if (!check.success) {
      throw new ZodErrorToValidateError<PaginatedBeneficiaryQueryDto>(
        check.error,
        'query',
      )
    }
    const user = request.user

    if (!user) {
      throw new UnauthenticatedError('Unauthenticated')
    }

    const response = await this.transfersService.retrieveBeneficiaries(
      req,
      user,
    )
    this.setStatus(response.statusCode)
    return response
  }

  @Patch('beneficiary/:id')
  @Security('jwt')
  @SuccessResponse('200', 'OK')
  public async ToggleFavoriteBeneficiaries(
    @Request() request: AuthenticatedRequest,
    @Path() id: string,
    @Body() body: ToggleFavoriteBeneficiariesBodyDTO,
  ) {
    const check =
      await ToggleFavoriteBeneficiariesBodySchema.safeParseAsync(body)
    if (!check.success) {
      throw new ZodErrorToValidateError<ToggleFavoriteBeneficiariesBodyDTO>(
        check.error,
        'body',
      )
    }
    const user = request.user
    if (!user) {
      throw new UnauthenticatedError('Unauthenticated')
    }
    const response = await this.transfersService.toggleBeneficiaryFavorite(
      {
        ...body,
        beneficiaryId: id,
      },
      user,
    )
    this.setStatus(response.statusCode)
    return response
  }

  @Delete('beneficiary/:id')
  @Security('jwt')
  @SuccessResponse('200', 'OK')
  public async DetachBeneficiaryFromUser(
    @Request() request: AuthenticatedRequest,
    @Path() id: string,
  ) {
    const user = request.user
    if (!user) {
      throw new UnauthenticatedError('Unauthenticated')
    }
    const response = await this.transfersService.detachBeneficiaryFromUser(
      id,
      user,
    )
    this.setStatus(response.statusCode)
    return response
  }

  @Get(':transferId')
  @Security('jwt')
  @SuccessResponse('200', 'OK')
  public async GetTransfer(
    @Request() request: AuthenticatedRequest,
    @Path() transferId: string,
  ) {
    const check = await RetrieveTransactionSchema.safeParseAsync({
      transactionId: transferId,
    })
    if (!check.success) {
      throw new ZodErrorToValidateError<RetrieveTransactionDto>(
        check.error,
        'body',
      )
    }
    const user = request.user

    if (!user) {
      throw new UnauthenticatedError('Unauthenticated')
    }

    const response = await this.transfersService.retrieveTransfer(
      transferId,
      user,
    )
    this.setStatus(response.statusCode)
    return response
  }
}
