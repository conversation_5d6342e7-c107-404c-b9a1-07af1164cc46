import { singleton } from 'tsyringe'
import { prisma } from '../../config/prisma.config'
import {
  internalResponse,
  InternalResponseReturns,
} from '../../helpers/response-helpers'
import { PrismaClient } from '@prisma/client'
import {
  CreateUserBodyDto,
  AddPhoneDto,
  VerifyPhoneDto,
  VerifyEmailBodyDto,
  AddPasswordBodyDto,
  EmailOTP,
  AddPin,
} from '../../types/dtos.types'
import { hashPassword } from '../../utils/password-utils'
import { JwtService, TokenDetails, UserPayloadFromUser } from '../../utils/auth'
import { BadRequestError } from '../../errors/BadRequestError'
import { logger } from '../../helpers/logger'
import { BiometricsKeyPayload, User, UserPayload } from '../../types/req.types'
import {
  PrismaClientKnownRequestError,
  PrismaClientUnknownRequestError,
} from '@prisma/client/runtime/library'
import { HTTPError } from '../../errors/HTTPError'
// import { EmailOTPService } from '../../config/resend.config'
import { PaymentsManager } from '../../config/stripe.config'
import { BiometricKeyService } from '../../services/biometric-key.service'
import { EnvVars } from '../../constants/env'
import { OTPService } from '../../config/twilio.config'

@singleton()
export class OnboardingService {
  private createResponse: typeof internalResponse
  private db: PrismaClient
  // private emailOTP: EmailOTPService
  private paymentsManager: PaymentsManager
  private biometricsKeyService: BiometricKeyService
  constructor() {
    this.createResponse = internalResponse
    this.db = prisma
    this.paymentsManager = new PaymentsManager()
    // this.emailOTP = new EmailOTPService()
    this.biometricsKeyService = new BiometricKeyService({
      encryptionKey: EnvVars.bioSecret,
    })
  }

  public async registerUser(body: CreateUserBodyDto): Promise<
    InternalResponseReturns<{
      user: User
      tokens: {
        accessToken: TokenDetails
        refreshToken: TokenDetails
      }
      OTPDetails: Omit<EmailOTP, 'otp'>
    }>
  > {
    const { email } = body

    let user = await this.db.user.findUnique({
      where: { email },
      select: {
        id: true,
        email: true,
        phoneNumber: true,
        createdAt: true,
        updatedAt: true,
        password: false,
        biometricsSetAt: true,
        emailVerifiedAt: true,
        passwordSetAt: true,
        phoneVerifiedAt: true,
        stripeCustomerId: true,
        pinSetAt: true,
      },
    })

    if (user && user.passwordSetAt) {
      throw new BadRequestError('User exists, please login')
    }

    try {
      if (!user) {
        user = await this.db.user.create({
          data: {
            email,
            Wallet: {
              create: {},
            },
          },
          select: {
            id: true,
            email: true,
            phoneNumber: true,
            createdAt: true,
            updatedAt: true,
            password: false,
            biometricsSetAt: true,
            emailVerifiedAt: true,
            passwordSetAt: true,
            phoneVerifiedAt: true,
            stripeCustomerId: true,
            pinSetAt: true,
          },
        })
      }

      if (!user.stripeCustomerId) {
        const { data: data_, error: error_ } =
          await this.paymentsManager.createCustomer(email)

        if (data_) {
          logger(module).info(
            `Stripe customer created with data: 
            ${JSON.stringify(data_, null, 2)}`,
          )
          await this.db.user.update({
            where: { email },
            data: { stripeCustomerId: data_.id },
          })
          user.stripeCustomerId = data_.id
        }
        if (error_) {
          logger(module).error(
            `Stripe customer creation failed with error: 
            ${JSON.stringify(error_, null, 2)}`,
          )
        }
      }
      // const otp = this.emailOTP.generateOTP()

      const otp = '123456'

      // const { data, error } = await this.emailOTP.sendOTP({
      //   otp,
      //   recipient: email,
      // })

      // if (data) {
      //   logger(module).info(
      //     `email otp sent successfully to ${email}. Returned payload:
      //     ${JSON.stringify(data, null, 2)}`,
      //   )
      // }

      // if (error) {
      //   logger(module).error(
      //     `email otp to ${email} failed. Error:
      //     ${JSON.stringify(error, null, 2)}`,
      //   )
      // }

      const expiredAt = new Date()
      expiredAt.setTime(expiredAt.getTime() + 10 * 60 * 1000)
      const OTPDetails = await this.db.emailOTP.create({
        data: {
          otp,
          expiredAt: expiredAt.toISOString(),
          email,
        },
        select: {
          otp: false,
          createdAt: true,
          expiredAt: true,
          email: true,
          id: true,
          updatedAt: true,
        },
      })

      const accessToken = JwtService.generateToken(UserPayloadFromUser(user))
      const refreshToken = JwtService.generateToken(
        UserPayloadFromUser(user),
        'refresh',
      )

      return this.createResponse(201, 'success', 'User created successfully', {
        user,
        tokens: {
          refreshToken,
          accessToken,
        },
        OTPDetails,
      })
    } catch (error) {
      if (error instanceof PrismaClientKnownRequestError) {
        throw new BadRequestError(error.message)
      }
      if (error instanceof PrismaClientUnknownRequestError) {
        throw new BadRequestError(error.message)
      }
      if (error instanceof HTTPError) {
        throw error
      }
      logger(module).error(`Unknown error:  ${JSON.stringify(error, null, 2)}`)
      throw error
    }
  }

  public async resendEmailOTP(body: CreateUserBodyDto): Promise<
    InternalResponseReturns<{
      OTPDetails: Omit<EmailOTP, 'otp'>
    }>
  > {
    try {
      const { email } = body

      const otps = await this.db.emailOTP.findMany({
        where: {
          email,
          expiredAt: {
            gt: new Date().toISOString(),
          },
        },
        orderBy: {
          expiredAt: 'desc',
        },
      })

      if (otps.length < 1) {
        // const otp = this.emailOTP.generateOTP()
        const otp = '123456'
        // const { data, error } = await this.emailOTP.sendOTP({
        //   otp,
        //   recipient: email,
        // })

        // if (data) {
        //   logger(module).info(
        //     `email otp sent successfully to ${email}. Returned payload:,
        //     ${JSON.stringify(data, null, 2)}`,
        //   )
        // }

        // if (error) {
        //   logger(module).error(
        //     `email otp to ${email} failed. Error:
        //     ${JSON.stringify(error, null, 2)}`,
        //   )
        // }
        const expiredAt = new Date()
        expiredAt.setTime(expiredAt.getTime() + 10 * 60 * 1000)
        const OTPDetails = await this.db.emailOTP.create({
          data: {
            otp,
            email,
            expiredAt,
          },
          select: {
            otp: false,
            createdAt: true,
            expiredAt: true,
            email: true,
            id: true,
            updatedAt: true,
          },
        })
        return this.createResponse(200, 'success', 'OTP resent successfully', {
          OTPDetails,
        })
      }

      const mostRecentOTP = otps[0]

      /**
       * TODO: move to an event emitter
       */
      const redundantOtps = otps.slice(1)

      for (const otp of redundantOtps) {
        await this.db.emailOTP.delete({ where: { id: otp.id } })
      }

      // const { data, error } = await this.emailOTP.sendOTP({
      //   otp: mostRecentOTP.otp,
      //   recipient: email,
      // })
      // if (data) {
      //   logger(module).info(
      //     `email otp sent successfully to ${email}. Returned payload:
      //     ${JSON.stringify(data, null, 2)}`,
      //   )
      // }

      // if (error) {
      //   logger(module).error(
      //     `email otp to ${email} failed. Error:
      //     ${JSON.stringify(error, null, 2)}`,
      //   )
      // }
      const expiredAt = new Date()
      expiredAt.setTime(expiredAt.getTime() + 10 * 60 * 1000)
      const OTPDetails = await this.db.emailOTP.update({
        where: { id: mostRecentOTP.id },
        data: {
          // otp: mostRecentOTP.otp,
          expiredAt: expiredAt.toISOString(),
          email,
          otp: '123456',
        },
        select: {
          otp: false,
          createdAt: true,
          expiredAt: true,
          email: true,
          id: true,
          updatedAt: true,
        },
      })
      return this.createResponse(200, 'success', 'OTP resent successfully', {
        OTPDetails,
      })
    } catch (error) {
      if (error instanceof PrismaClientKnownRequestError) {
        throw new BadRequestError(error.message)
      }
      if (error instanceof PrismaClientUnknownRequestError) {
        throw new BadRequestError(error.message)
      }
      if (error instanceof HTTPError) {
        throw error
      }
      logger(module).error(`Unknown error: ${JSON.stringify(error, null, 2)}`)
      throw error
    }
  }

  public async verifyEmailOTP(
    body: VerifyEmailBodyDto,
  ): Promise<InternalResponseReturns<undefined>> {
    try {
      const { email, otp, otpId } = body

      const userExists = await this.db.user.findUnique({
        where: { email },
      })

      if (!userExists) {
        throw new BadRequestError('Invalid email')
      }

      const foundOTP = await this.db.emailOTP.findUnique({
        where: {
          id: otpId,
          email,
          expiredAt: {
            gt: new Date().toISOString(),
          },
          otp,
        },
      })

      if (!foundOTP) {
        throw new BadRequestError('Invalid OTP')
      }
      await this.db.user.update({
        where: {
          email,
        },
        data: {
          emailVerifiedAt: new Date().toISOString(),
        },
      })
      await this.db.emailOTP.delete({ where: { id: foundOTP.id } })

      return this.createResponse(200, 'success', 'OTP verified successfully')
    } catch (error) {
      if (error instanceof PrismaClientKnownRequestError) {
        throw new BadRequestError(error.message)
      }
      if (error instanceof PrismaClientUnknownRequestError) {
        throw new BadRequestError(error.message)
      }
      if (error instanceof HTTPError) {
        throw error
      }
      logger(module).error(`Unknown error: ${JSON.stringify(error, null, 2)}`)
      throw error
    }
  }

  public async addPassword(
    body: AddPasswordBodyDto,
    user: UserPayload,
  ): Promise<InternalResponseReturns<undefined>> {
    const { password } = body
    const { id } = user

    try {
      const userExists = await this.db.user.findUnique({
        where: { id },
      })

      if (!userExists) {
        throw new BadRequestError('Invalid user')
      }
      if (userExists.password) {
        throw new BadRequestError('Password already set')
      }
      const hash = await hashPassword(password)
      await this.db.user.update({
        where: { id },
        data: {
          password: hash,
          passwordSetAt: new Date().toISOString(),
        },
      })

      return this.createResponse(200, 'success', 'Password added successfully')
    } catch (error) {
      if (error instanceof PrismaClientKnownRequestError) {
        throw new BadRequestError(error.message)
      }
      if (error instanceof PrismaClientUnknownRequestError) {
        throw new BadRequestError(error.message)
      }
      if (error instanceof HTTPError) {
        throw error
      }
      logger(module).error(`Unknown error: ${JSON.stringify(error, null, 2)}`)
      throw error
    }
  }

  public async addPhone(
    body: AddPhoneDto,
    user: UserPayload,
  ): Promise<InternalResponseReturns<undefined>> {
    const { phoneNumber, channel } = body

    // const response = await OTPService.sendOTP({
    //   to: phoneNumber,
    //   via: channel,
    // })

    try {
      const phoneExists = await this.db.user.findUnique({
        where: { phoneNumber: phoneNumber },
      })

      if (phoneExists) {
        throw new BadRequestError('Phone number has been taken')
      }

      // if (
      //   !response.response ||
      //   response.error ||
      //   response.response?.status !== 'pending'
      // ) {
      //   logger(module).error(
      //     `Error sending otp ${JSON.stringify(response, null, 2)}`,
      //   )
      //   type Response = {
      //     status: number
      //     code: number
      //     moreInfo: number
      //   }
      //   if ((response.error as Response).code === 60223) {
      //     throw new BadRequestError('Unsupported channel')
      //   }

      //   throw new BadRequestError('Invalid phone number')
      // }

      await this.db.user.update({
        where: { id: user.id },
        data: {
          phoneNumber,
        },
      })

      return this.createResponse(200, 'success', 'OTP sent successfully')
    } catch (error) {
      if (error instanceof PrismaClientKnownRequestError) {
        throw new BadRequestError(error.message)
      }
      if (error instanceof PrismaClientUnknownRequestError) {
        throw new BadRequestError(error.message)
      }
      if (error instanceof HTTPError) {
        throw error
      }
      logger(module).error(`Unknown error: ${JSON.stringify(error, null, 2)}`)
      throw error
    }
  }

  public async resendOTP(
    body: AddPhoneDto,
  ): Promise<InternalResponseReturns<undefined>> {
    const { phoneNumber, channel } = body

    // const response = await OTPService.sendOTP({
    //   to: phoneNumber,
    //   via: channel,
    // })

    // if (
    //   !response.response ||
    //   response.error ||
    //   response.response?.status !== 'pending'
    // ) {
    //   logger(module).error(
    //     `Error sending otp ${JSON.stringify(response, null, 2)}`,
    //   )
    //   type Response = {
    //     status: number
    //     code: number
    //     moreInfo: number
    //   }
    //   if ((response.error as Response).code === 60223) {
    //     throw new BadRequestError('Unsupported channel')
    //   }
    //   throw new BadRequestError('Invalid phone number')
    // }
    return this.createResponse(200, 'success', 'OTP resent successfully')
  }
  public async verifyPhone(
    body: VerifyPhoneDto,
    user: UserPayload,
  ): Promise<InternalResponseReturns<undefined>> {
    const { phoneNumber, otp } = body

    try {
      // const response = await OTPService.verifyOTP({
      //   to: phoneNumber,
      //   code: otp,
      // })

      if (otp !== '123456') {
        throw new BadRequestError('Invalid otp')
      }

      // if (
      //   !response.response ||
      //   response.error ||
      //   response.response?.status !== 'approved'
      // ) {
      //   logger(module).error(JSON.stringify(response, null, 2))
      //   throw new BadRequestError('Invalid otp')
      // }
      const updatedUser = await this.db.user.update({
        where: { id: user.id, phoneNumber },
        data: {
          phoneVerifiedAt: new Date().toISOString(),
        },
      })

      if (!updatedUser) {
        logger(module).error(
          `Twilio otp verification error for payload: \n
          ${JSON.stringify(body, null, 2)}
           \n and user: 
          ${user.id}`,
        )
        throw new BadRequestError('Invalid user')
      }

      return this.createResponse(200, 'success', 'OTP verified successfully')
    } catch (error) {
      if (error instanceof PrismaClientKnownRequestError) {
        throw new BadRequestError(error.message)
      }
      if (error instanceof PrismaClientUnknownRequestError) {
        throw new BadRequestError(error.message)
      }
      if (error instanceof HTTPError) {
        throw error
      }
      logger(module).error(`Unknown error: ${JSON.stringify(error, null, 2)}`)
      throw error
    }
  }

  public async enableBiometrics(
    user: UserPayload,
  ): Promise<InternalResponseReturns<{ user: User; publicKey: string }>> {
    const { id } = user

    try {
      // TODO: tie biometrics to devices
      const user = await this.db.user.update({
        where: {
          id,
        },
        data: {
          biometricsSetAt: new Date().toISOString(),
        },
        select: {
          id: true,
          email: true,
          phoneNumber: true,
          createdAt: true,
          updatedAt: true,
          password: false,
          biometricsSetAt: true,
          emailVerifiedAt: true,
          passwordSetAt: true,
          stripeCustomerId: true,
          phoneVerifiedAt: true,
          pinSetAt: true,
        },
      })

      if (!user) {
        throw new BadRequestError('invalid user')
      }

      const publicKey =
        this.biometricsKeyService.generateToken<BiometricsKeyPayload>({
          email: user.email,
          phoneNumber: user.phoneNumber,
        })

      return this.createResponse(
        200,
        'success',
        'Biometrics added successfully',
        { user, publicKey },
      )
    } catch (error) {
      if (error instanceof PrismaClientKnownRequestError) {
        throw new BadRequestError(error.message)
      }
      if (error instanceof PrismaClientUnknownRequestError) {
        throw new BadRequestError(error.message)
      }
      if (error instanceof HTTPError) {
        throw error
      }
      logger(module).error(`Unknown error: ${JSON.stringify(error, null, 2)}`)
      throw error
    }
  }

  public async addPin(
    user: UserPayload,
    body: AddPin,
  ): Promise<InternalResponseReturns<undefined>> {
    try {
      const { id } = user
      const { pin } = body

      const userExists = await this.db.user.findUnique({
        where: { id },
      })

      if (!userExists) {
        throw new BadRequestError('Invalid user')
      }
      if (userExists.pin) {
        throw new BadRequestError('Pin already set')
      }
      const hash = await hashPassword(pin)
      await this.db.user.update({
        where: { id },
        data: {
          pin: hash,
          pinSetAt: new Date().toISOString(),
        },
      })

      return this.createResponse(200, 'success', 'Pin added successfully')
    } catch (error) {
      if (error instanceof PrismaClientKnownRequestError) {
        throw new BadRequestError(error.message)
      }
      if (error instanceof PrismaClientUnknownRequestError) {
        throw new BadRequestError(error.message)
      }
      if (error instanceof HTTPError) {
        throw error
      }
      logger(module).error(`Unknown error: ${JSON.stringify(error, null, 2)}`)
      throw error
    }
  }
}
