import { PrismaClient } from '@prisma/client'
import {
  internalResponse,
  InternalResponseReturns,
} from '../../helpers/response-helpers'
import { singleton } from 'tsyringe'
import { prisma } from '../../config/prisma.config'
import {
  StartPassportVerificationSessionType,
  StartVisaVerificationSessionType,
  SupportedVerifDocumentType,
} from '../../types/dtos.types'
import {
  UserPayload,
  VisaKycAttemptResponse,
  PassportKycAttemptResponse,
} from '../../types/req.types'
import {
  PrismaClientKnownRequestError,
  PrismaClientUnknownRequestError,
} from '@prisma/client/runtime/library'
import { BadRequestError } from '../../errors/BadRequestError'
import { HTTPError } from '../../errors/HTTPError'
import { logger } from '../../helpers/logger'
import { VerifService, VerifSessionResponse } from '../../config/verif.config'
import { safeString } from '../../utils/utils'

@singleton()
export class KYCService {
  private createResponse: typeof internalResponse
  private db: PrismaClient
  private kycProvider: VerifService
  constructor() {
    this.createResponse = internalResponse
    this.db = prisma
    this.kycProvider = new VerifService()
  }

  async createPassportKYCSession(
    body: StartPassportVerificationSessionType,
    user: UserPayload,
  ): Promise<InternalResponseReturns<VerifSessionResponse>> {
    const { id, email } = user
    try {
      const hasCompletedKyc = await this.db.passportKycDetails.findFirst({
        where: {
          userId: id,
          Attempt: {
            expiryDate: {
              gt: new Date(),
            },
          },
        },
      })
      if (hasCompletedKyc) {
        throw new BadRequestError('User has completed kyc')
      }

      const kycSessionResponse = await this.kycProvider.createSession(
        this.kycProvider.generateCreatePassportSessionPayload({
          ...body,
          email,
          userId: id,
        }),
      )

      if (!kycSessionResponse.data || kycSessionResponse.error) {
        logger(module).error(
          `Unable to create kyc session. Response Payload: ${JSON.stringify(kycSessionResponse, null, 2)}`,
        )

        const errorMessage =
          typeof kycSessionResponse.error === 'string'
            ? kycSessionResponse.error
            : 'Failed to create session, try again later'
        throw new BadRequestError(errorMessage)
      }
      if (kycSessionResponse.data.status !== 'success') {
        logger(module).error(
          `Unable to create kyc session. Response Payload: ${JSON.stringify(kycSessionResponse, null, 2)}`,
        )
        throw new BadRequestError('Failed to create session, try again later')
      }

      logger(module).info(
        `KYC session created. Response Payload: ${JSON.stringify(kycSessionResponse.data, null, 2)}`,
      )

      const {
        verification: { id: sessionId, status },
      } = kycSessionResponse.data

      await this.db.passportKycAttempts.create({
        data: {
          sessionId,
          status,
          userId: id,
          firstName: body.firstName,
          lastName: body.lastName,
          dateOfBirth: new Date(body.dateOfBirth),
          documentNumber: body.documentNumber,
          expiryDate: new Date(body.expiryDate),
          country: body.country,
        },
      })
      return this.createResponse(
        201,
        'success',
        'Verif session created successfully',
        {
          ...kycSessionResponse.data,
        },
      )
    } catch (error) {
      if (error instanceof PrismaClientKnownRequestError) {
        throw new BadRequestError(error.message)
      }
      if (error instanceof PrismaClientUnknownRequestError) {
        throw new BadRequestError(error.message)
      }
      if (error instanceof HTTPError) {
        throw error
      }
      logger(module).error(`Unknown error: ${JSON.stringify(error, null, 2)}`)
      throw error
    }
  }

  async createVisaKYCSession(
    body: StartVisaVerificationSessionType,
    user: UserPayload,
  ): Promise<InternalResponseReturns<VerifSessionResponse>> {
    const { id, email } = user
    try {
      const hasCompletedKyc = await this.db.visaKycDetails.findFirst({
        where: {
          userId: id,
          Attempt: {
            visaExpiryDate: {
              gt: new Date(),
            },
          },
        },
      })

      if (hasCompletedKyc) {
        throw new BadRequestError('User has completed kyc')
      }

      const kycSessionResponse = await this.kycProvider.createSession(
        this.kycProvider.generateCreateVisaSessionPayload({
          ...body,
          email,
          userId: id,
        }),
      )

      if (!kycSessionResponse.data || kycSessionResponse.error) {
        logger(module).error(
          `Unable to create kyc session. Response Payload: ${JSON.stringify(kycSessionResponse, null, 2)}`,
        )
        const errorMessage =
          typeof kycSessionResponse.error === 'string'
            ? kycSessionResponse.error
            : 'Failed to create session, try again later'
        throw new BadRequestError(errorMessage)
      }
      if (kycSessionResponse.data.status !== 'success') {
        logger(module).error(
          `Unable to create kyc session. Response Payload: ${JSON.stringify(kycSessionResponse, null, 2)}`,
        )
        throw new BadRequestError('Failed to create session, try again later')
      }

      logger(module).info(
        `KYC session created. Response Payload: ${JSON.stringify(kycSessionResponse.data, null, 2)}`,
      )

      const {
        verification: { id: sessionId, status },
      } = kycSessionResponse.data

      await this.db.visaKycAttempts.create({
        data: {
          userId: sessionId,
          sessionId: id,
          status,
          country: body.country,
          dateOfBirth: new Date(body.dateOfBirth),
          firstName: body.firstName,
          lastName: body.lastName,
          visaExpiryDate: new Date(body.visaExpiryDate),
          visaIssueDate: new Date(body.visaIssueDate),
          visaNumber: body.visaNumber,
          visaType: body.visaType,
        },
      })
      return this.createResponse(
        201,
        'success',
        'Verif session created successfully',
        {
          ...kycSessionResponse.data,
        },
      )
    } catch (error) {
      if (error instanceof PrismaClientKnownRequestError) {
        throw new BadRequestError(error.message)
      }
      if (error instanceof PrismaClientUnknownRequestError) {
        throw new BadRequestError(error.message)
      }
      if (error instanceof HTTPError) {
        throw error
      }
      logger(module).error(`Unknown error: ${JSON.stringify(error, null, 2)}`)
      throw error
    }
  }
  async getKYCStatus(
    sessionId: string,
    idType: SupportedVerifDocumentType,
    user: UserPayload,
  ): Promise<
    InternalResponseReturns<VisaKycAttemptResponse | PassportKycAttemptResponse>
  > {
    const { id } = user
    try {
      const sessionDetails:
        | VisaKycAttemptResponse
        | PassportKycAttemptResponse
        | null =
        idType === 'PASSPORT'
          ? await this.db.passportKycAttempts.findFirst({
              where: { sessionId, userId: id },
            })
          : await this.db.visaKycAttempts.findFirst({
              where: { sessionId, userId: id },
            })

      if (!sessionDetails) {
        throw new BadRequestError(`No session found with id ${sessionId}`)
      }

      return this.createResponse(
        200,
        'success',
        'session status retrieved successfully',
        {
          ...sessionDetails,
        },
      )
    } catch (error) {
      if (error instanceof PrismaClientKnownRequestError) {
        throw new BadRequestError(error.message)
      }
      if (error instanceof PrismaClientUnknownRequestError) {
        throw new BadRequestError(error.message)
      }
      if (error instanceof HTTPError) {
        throw error
      }
      logger(module).error(`Unknown error: ${JSON.stringify(error, null, 2)}`)
      throw error
    }
  }
  async getUserKYC(user: UserPayload): Promise<InternalResponseReturns<any>> {
    const { id } = user
    try {
      const passportKycs = await this.db.passportKycDetails.findMany({
        where: {
          userId: id,
        },
        include: {
          Attempt: true,
        },
      })

      const visaKycs = await this.db.visaKycDetails.findMany({
        where: {
          userId: id,
        },
        include: {
          Attempt: true,
        },
      })

      const kycDetails = {
        passport:
          passportKycs.find(
            (passport) =>
              new Date(
                safeString(
                  passport.document?.[
                    'validUntil' as keyof typeof passport.document
                  ],
                ),
              ).getDate() > new Date().getDate(),
          ) ?? null,

        visa:
          visaKycs.find(
            (visa) =>
              new Date(
                safeString(
                  visa.document?.['validUntil' as keyof typeof visa.document],
                ),
              ).getDate() > new Date().getDate(),
          ) ?? null,
      }

      return this.createResponse(
        200,
        'success',
        'Kyc details retrieved successfully',
        {
          ...kycDetails,
        },
      )
    } catch (error) {
      if (error instanceof PrismaClientKnownRequestError) {
        throw new BadRequestError(error.message)
      }
      if (error instanceof PrismaClientUnknownRequestError) {
        throw new BadRequestError(error.message)
      }
      if (error instanceof HTTPError) {
        throw error
      }
      logger(module).error(`Unknown error: ${JSON.stringify(error, null, 2)}`)
      throw error
    }
  }
}
