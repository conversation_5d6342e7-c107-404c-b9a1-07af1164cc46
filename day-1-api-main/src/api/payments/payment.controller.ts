import { inject, injectable } from 'tsyringe'
import {
  Controller,
  Route,
  Post,
  SuccessResponse,
  Body,
  Tags,
  Security,
  Request,
  Get,
  Path,
  Middlewares,
} from 'tsoa'
import { PaymentService } from './payment.service'
import { AuthenticatedRequest } from '../../types/req.types'
import { UnauthenticatedError } from '../../errors/UnauthenticatedError'
import {
  InitializePaymentIntentBodyDto,
  InitializePaymentIntentBodySchema,
  RetrieveTransactionDto,
  RetrieveTransactionSchema,
} from '../../types/dtos.types'
import { ZodErrorToValidateError } from '../../middleware/errorhandler.middleware'
import express from 'express'
@injectable()
@Tags('Payments and Wallets Controller')
@Route('payments')
@Middlewares(express.json())
export class PaymentsController extends Controller {
  constructor(
    @inject('paymentService') private paymentsService: PaymentService,
  ) {
    super()
  }

  @Post('create/credit')
  @Security('jwt')
  @SuccessResponse('201', 'OK')
  public async InitializeStripePaymentIntent(
    @Request() request: AuthenticatedRequest,
    @Body() body: InitializePaymentIntentBodyDto,
  ) {
    const check = await InitializePaymentIntentBodySchema.safeParseAsync(body)
    if (!check.success) {
      throw new ZodErrorToValidateError<InitializePaymentIntentBodyDto>(
        check.error,
        'body',
      )
    }
    const user = request.user

    if (!user) {
      throw new UnauthenticatedError('Unauthenticated')
    }

    const response = await this.paymentsService.initializeStripePaymentIntent(
      body,
      user,
    )
    this.setStatus(response.statusCode)
    return response
  }

  @Get('wallet')
  @Security('jwt')
  @SuccessResponse('200', 'OK')
  public async GetWallet(@Request() request: AuthenticatedRequest) {
    const user = request.user

    if (!user) {
      throw new UnauthenticatedError('Unauthenticated')
    }

    const response = await this.paymentsService.getWallet(user)
    this.setStatus(response.statusCode)
    return response
  }

  @Get(':id')
  @Security('jwt')
  @SuccessResponse('200', 'OK')
  public async ValidateStripePaymentIntent(
    @Request() request: AuthenticatedRequest,
    @Path() id: string,
  ) {
    const check = await RetrieveTransactionSchema.safeParseAsync({
      transactionId: id,
    })
    if (!check.success) {
      throw new ZodErrorToValidateError<RetrieveTransactionDto>(
        check.error,
        'body',
      )
    }
    const user = request.user

    if (!user) {
      throw new UnauthenticatedError('Unauthenticated')
    }

    const response = await this.paymentsService.retrievePayment(id, user)
    this.setStatus(response.statusCode)
    return response
  }
}
