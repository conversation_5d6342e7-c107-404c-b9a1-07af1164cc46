import {
  Body,
  Controller,
  Hidden,
  Middlewares,
  Post,
  Request,
  Route,
  Tags,
} from 'tsoa'
import express from 'express'

import { inject, injectable } from 'tsyringe'
import { WebhookService } from './webhooks.service'
import { VerifSessionDecisionResponse } from '../../config/verif.config'
import { rawRequestMiddleware } from '../../middleware/rawRequestMiddleware'

@Tags('Webhook controller')
@Route('webhook')
@injectable()
@Hidden()
export class WebhookController extends Controller {
  constructor(
    @inject('webhookService') private webhookService: WebhookService,
  ) {
    super()
  }

  @Post('verif/events')
  @Middlewares(express.json())
  public async VerifEventsWebhook(
    @Body() body: any,
    @Request() request: Request,
  ) {
    // @ts-expect-error Mismatch interface
    const signature = request?.headers?.['x-hmac-signature']

    if (!signature) {
      this.setStatus(401)
      return
    }

    const response = await this.webhookService.verifEvents(
      body as VerifSessionDecisionResponse,
      signature,
    )

    this.setStatus(response)
    return
  }
  @Post('verif/decision')
  @Middlewares(express.json())
  public async VerifDecisionWebhook(
    @Body() body: any,
    @Request() request: Request,
  ) {
    // @ts-expect-error Mismatch interface
    const signature = request?.headers?.['x-hmac-signature']

    if (!signature) {
      this.setStatus(401)
      return
    }

    const response = await this.webhookService.verifDecisions(
      body as VerifSessionDecisionResponse,
      signature,
    )
    this.setStatus(response)
    return
  }

  @Post('stripe/payment-intent')
  @Middlewares(rawRequestMiddleware)
  public async HandlePaymentIntentWebhook(
    @Body() body: any,
    @Request() request: any,
  ) {
    const signature = request?.headers?.['stripe-signature']

    if (!signature) {
      this.setStatus(401)
      return
    }

    const response = await this.webhookService.handlePaymentIntentResponse(
      body,
      signature,
    )

    this.setStatus(response)
    return
  }

  @Post('paystack/transfer')
  @Middlewares(express.json())
  public async HandleTransferWebhook(
    @Body() body: any,
    @Request() request: Request,
  ) {
    // @ts-expect-error Mismatch interface
    const signature = request?.headers?.['x-paystack-signature']

    if (!signature) {
      this.setStatus(401)
      return
    }

    const response = await this.webhookService.handlePaystackTransferResponse(
      body,
      signature,
    )
    this.setStatus(response)
    return
  }
}
