import { app } from './app'
import { EnvVars } from './constants/env'
import { logger } from './helpers/logger'
import { AuthEncryptionHandler } from './services/encrypt-decrypt-service/auth-encrypt.service'
const startServer = () => {
  function onListening() {
    logger(module).info(`Listening on port ${EnvVars.port}`)
  }

  function onError(err: unknown) {
    if (err instanceof Error) {
      if ('errno' in err && err.errno === 'EADDRINUSE') {
        logger(module).error(`Port ${EnvVars.port} is busy`)
      } else {
        logger(module).error(`Error in starting server: ${err}`)
      }
    } else {
      logger(module).error(`Error in starting server: ${err}`)
    }

    process.exit(1)
  }

  const hostname = '0.0.0.0'
  const serverInstance = app.listen(+EnvVars.port, hostname, onListening)
  serverInstance.timeout = 50000

  serverInstance.on('error', onError)

  process.on('SIGTERM', () => {
    logger(module).info('SIGTERM signal received: closing HTTP server')
    serverInstance.close(() => {
      logger(module).info('HTTP server closed')
    })
  })
}

const generateKeys = () => {
  const keyService = new AuthEncryptionHandler()
  keyService.generateAuthKeys()
}
try {
  startServer()
  generateKeys()
} catch (error) {
  console.log(error)
}
