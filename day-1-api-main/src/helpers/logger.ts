import { createLogger, format, transports } from 'winston'
import rTracer from 'cls-rtracer'
import path from 'path'
import fs from 'fs'
import { safeString } from '../utils/utils'

const { combine, timestamp, label, printf } = format

const getLogLabel = (callingModule: typeof module) => {
  const parts = callingModule.filename.split(path.sep)
  return path.join(parts[parts.length - 2], safeString(parts.pop()))
}

const formatDate = () => {
  const d = new Date()
  let month = '' + (d.getMonth() + 1),
    day = '' + d.getDate()

  const year = d.getFullYear()

  if (month.length < 2) month = '0' + month
  if (day.length < 2) day = '0' + day

  return `${year}${month}${day}`
}

const getFile = (type: string) => {
  const d = formatDate()
  const filename = `logs/${d}${type}.log`
  fs.open(filename, 'r', function (err) {
    if (err) {
      fs.writeFile(filename, '', function (err) {
        if (err) {
          return `logs/${type}.log`
        }
        return filename
      })
    } else {
      return filename
    }
    return ''
  })
  return filename
}

/**
 * Creates a Winston logger object.
 * ### Log Format
 * *| timestamp | request-id | module/filename | log level | log message |*
 *
 * @param {Module} callingModule the module from which the logger is called
 */
const logger = (callingModule: typeof module) =>
  createLogger({
    format: combine(
      label({ label: getLogLabel(callingModule) }),
      timestamp(),
      printf((info) => {
        const rid = rTracer.id()

        return rid
          ? `| ${info.timestamp} | ${rid} | ${info.label} | ${info.level} | ${info.message} |`
          : `| ${info.timestamp} | ${info.label} | ${info.level} | ${info.message} |`
      }),
    ),
    transports: [
      new transports.Console(),
      new transports.File({ filename: './logs/debug.log', level: 'debug' }),
      new transports.File({ filename: getFile('info'), level: 'info' }),
      new transports.File({ filename: getFile('error'), level: 'error' }),
    ],
    exitOnError: false,
  })

export { logger }
