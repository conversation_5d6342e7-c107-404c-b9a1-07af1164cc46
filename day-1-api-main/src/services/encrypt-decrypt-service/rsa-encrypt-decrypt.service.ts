import crypto from 'crypto'
import path from 'path'
import fs from 'fs/promises'
import { logger } from '../../helpers/logger'
interface KeyPair {
  publicKey: string
  privateKey: string
}

interface KeyPaths {
  publicKeyPath: string
  privateKeyPath: string
}
export class RSAEncryptDecryptService {
  private readonly keyDir: string

  constructor() {
    this.keyDir = path.join(__dirname, '../../keys')
  }

  /**
   * Ensures the key directory exists
   */
  private async ensureKeyDirectory(): Promise<string> {
    try {
      await fs.access(this.keyDir)
    } catch {
      await fs.mkdir(this.keyDir, { recursive: true })
    }
    return this.keyDir
  }

  private generateKeys(): KeyPair {
    const keys = crypto.generateKeyPairSync('rsa', {
      modulusLength: 2048,
      publicKeyEncoding: {
        type: 'spki',
        format: 'pem',
      },
      privateKeyEncoding: {
        type: 'pkcs8',
        format: 'pem',
      },
    })

    return {
      publicKey: keys.publicKey,
      privateKey: keys.privateKey,
    }
  }

  /**
   * Saves a key pair to the file system
   */
  public async saveKeys(keyName: string): Promise<KeyPaths> {
    const keys = this.generateKeys()
    await this.ensureKeyDirectory()

    const publicKeyPath = path.join(this.keyDir, `${keyName}.public.pem`)
    const privateKeyPath = path.join(this.keyDir, `${keyName}.private.pem`)

    try {
      await Promise.allSettled([
        fs.writeFile(publicKeyPath, keys.publicKey, 'utf8'),
        fs.writeFile(privateKeyPath, keys.privateKey, 'utf8'),
      ])

      return {
        publicKeyPath,
        privateKeyPath,
      }
    } catch {
      throw {
        publicKeyPath: '',
        privateKeyPath: '',
      }
    }
  }

  public async readPublicKeyFromFileSystem(keyName: string): Promise<string> {
    try {
      const publicKeyPath = path.join(this.keyDir, `${keyName}.public.pem`)
      return await fs.readFile(publicKeyPath, 'utf8')
    } catch {
      return ''
    }
  }

  /**
   * Reads the private key from the file system
   */
  public async readPrivateKeyFromFileSystem(keyName: string): Promise<string> {
    try {
      const privateKeyPath = path.join(this.keyDir, `${keyName}.private.pem`)
      return await fs.readFile(privateKeyPath, 'utf8')
    } catch {
      return ''
    }
  }

  /**
   * Lists all available key pairs
   */
  public async listKeyPairs(): Promise<string[]> {
    try {
      await this.ensureKeyDirectory()
      const files = await fs.readdir(this.keyDir)

      const keyPairs = files
        .filter((file) => file.endsWith('.pem'))
        .reduce<Record<string, string[]>>((acc, file) => {
          const keyName = file.split('.')[0]
          if (!acc[keyName]) {
            acc[keyName] = []
          }
          acc[keyName].push(file)
          return acc
        }, {})

      return Object.keys(keyPairs)
    } catch {
      return []
    }
  }

  /**
   * Encrypts data using the public key
   */
  public async encryptData<T extends Record<string, unknown>>(
    data: T,
    keyName: string,
  ): Promise<string> {
    try {
      const publicKey = await this.readPublicKeyFromFileSystem(keyName)
      const bufferData = Buffer.from(JSON.stringify(data), 'utf8')

      const encrypted = crypto.publicEncrypt(
        {
          key: publicKey,
          padding: crypto.constants.RSA_PKCS1_OAEP_PADDING,
          oaepHash: 'sha256',
        },
        bufferData,
      )

      return encrypted.toString('base64')
    } catch {
      return ''
    }
  }

  /**
   * Decrypts data using the private key
   */
  public async decryptData<T extends Record<string, unknown>>(
    encryptedData: string,
    keyName: string,
  ): Promise<T | null> {
    try {
      const privateKey = await this.readPrivateKeyFromFileSystem(keyName)
      const bufferData = Buffer.from(encryptedData, 'base64')

      const decrypted = crypto.privateDecrypt(
        {
          key: privateKey,
          padding: crypto.constants.RSA_PKCS1_OAEP_PADDING,
          oaepHash: 'sha256',
        },
        bufferData,
      )

      return JSON.parse(decrypted.toString('utf8'))
    } catch {
      return null
    }
  }

  /**
   * Deletes a key pair from the file system
   */
  public async deleteKeyPair(keyName: string): Promise<void> {
    try {
      const publicKeyPath = path.join(this.keyDir, `${keyName}.public.pem`)
      const privateKeyPath = path.join(this.keyDir, `${keyName}.private.pem`)

      await Promise.allSettled([
        fs.unlink(publicKeyPath),
        fs.unlink(privateKeyPath),
      ])
    } catch {
      logger(module).error('Failed to delete key')
    }
  }
}
