import * as z from 'zod'

export const emailSchema = z.string().email('email must be an email')
export const otpSchema = z
  .string()
  .length(6, { message: 'OTP must have a length of 6' })
export const passwordSchema = z
  .string({
    invalid_type_error: 'password must be a string',
    required_error: 'Password is required',
  })
  .min(8, { message: 'Password must have at least 8 characters' })
  .regex(/[$&+,:;=?@#|'<>.^*()%!-]/, {
    message: 'Password must contain one special character',
  })
  .regex(/[A-Z]/, {
    message: 'Password must contain one uppercase character',
  })
  .regex(/[0-9]/, {
    message: 'Password must contain one number',
  })
  .regex(/[a-z]/, {
    message: 'Password must contain one lowercase character',
  })
export const phoneSchema = z
  .string({
    invalid_type_error: 'phone number must be a string',
    required_error: 'phone number is required',
  })
  .trim()
  .startsWith('+', { message: 'phone must begin with +' })
  .min(10, { message: 'phone number must have at least 10 characters' })
  .max(15, { message: 'phone number must have at most 13 characters' })
export const channelSchema = z.enum(['sms', 'whatsapp'], {
  message: 'Only sms and whatsapp channels are supported',
})
export const pinSchema = z
  .string({
    invalid_type_error: 'pin must be a string',
    required_error: 'Pin is required',
  })
  .length(4, { message: 'pin must have 4 characters' })
  .regex(/[0-9]/, {
    message: 'Pin must contain only numbers',
  })

export const CreateUserBodySchema = z.object({
  email: emailSchema,
})

export type CreateUserBodyDto = {
  email: string
}

export const VerifyEmailBodySchema = z.object({
  email: emailSchema,
  otp: otpSchema,
  otpId: z.string({ message: 'OTP id is required' }),
})

export type VerifyEmailBodyDto = {
  email: string
  otp: string
  otpId: string
}

export const AddPasswordBodySchema = z
  .object({
    password: passwordSchema,
    confirmPassword: z.string({ message: 'Confirm password is required' }),
  })
  .superRefine(({ confirmPassword, password }, ctx) => {
    if (confirmPassword !== password) {
      ctx.addIssue({
        code: 'custom',
        message: 'The passwords did not match',
        path: ['confirmPassword', 'password'],
      })
    }
  })
export type AddPasswordBodyDto = {
  password: string
  confirmPassword: string
}

export type ResendEmailOTP = {
  email: string
}

export const ResendEmailBodySchema = z.object({
  email: emailSchema,
})

export type OTPChannels = 'whatsapp' | 'sms'

export type AddPhoneDto = {
  phoneNumber: string
  channel: OTPChannels
}

export const AddPhoneNumberSchema = z.object({
  phoneNumber: phoneSchema,
  channel: channelSchema,
})

export type AddPin = {
  pin: string
  confirmPin: string
}

export const AddPinSchema = z
  .object({
    pin: pinSchema,
    confirmPin: pinSchema,
  })
  .superRefine(({ confirmPin, pin }, ctx) => {
    if (confirmPin !== pin) {
      ctx.addIssue({
        code: 'custom',
        message: 'The pins did not match',
        path: ['confirmPin', 'pin'],
      })
    }
  })

export type VerifyPhoneDto = { phoneNumber: string; otp: string }
export const VerifyPhoneNumberSchema = z.object({
  otp: otpSchema,
  phoneNumber: phoneSchema,
})

export const loginSchema = z.object({
  email: emailSchema,
  password: z.string({ message: 'password is required' }),
})

export type LoginDto = {
  email: string
  password: string
}

export const pinLoginSchema = z.object({
  pin: pinSchema,
  email: emailSchema,
})

export type PinLoginDto = {
  pin: string
  email: string
}

export type twoFADto = {
  otp: string
}

export const twoFASchema = z.object({
  otp: otpSchema,
})

export type EmailOTP = {
  email: string

  otp: string
  id: string
  createdAt: Date
  updatedAt: Date
  expiredAt: Date
}

export type ResetPasswordDto = {
  resetToken: string
  password: string
  confirmPassword: string
  email: string
}

export type PublicKeyLoginDto = {
  publicKey: string
  email: string
}
export const PublicKeyBodySchema = z.object({
  publicKey: z.string({ required_error: 'Public key is required' }),
  email: z.string({ required_error: 'Email is required' }),
})

export const ResetPasswordSchema = z
  .object({
    password: passwordSchema,
    confirmPassword: z.string({ message: 'Confirm password is required' }),
    resetToken: z.string({ message: 'Reset token is required' }),
    email: emailSchema,
  })
  .superRefine(({ confirmPassword, password }, ctx) => {
    if (confirmPassword !== password) {
      ctx.addIssue({
        code: 'custom',
        message: 'The passwords did not match',
        path: ['confirmPassword', 'password'],
      })
    }
  })

export const RefreshTokenSchema = z.object({
  refreshToken: z.string({ required_error: 'Refresh token is required' }),
})

export type RefreshTokenDto = {
  refreshToken: string
}

export type ChangePasswordDto = {
  password: string
  newPassword: string
  confirmNewPassword: string
}

export const ChangePasswordSchema = z
  .object({
    password: z.string({ message: 'Password is required' }),
    newPassword: passwordSchema,
    confirmNewPassword: z.string({ message: 'Password is required' }),
  })
  .superRefine(({ confirmNewPassword, newPassword }, ctx) => {
    if (confirmNewPassword !== newPassword) {
      ctx.addIssue({
        code: 'custom',
        message: 'The passwords did not match',
        path: ['confirmNewPassword', 'newPassword'],
      })
    }
  })

export type ConfirmPasswordDto = {
  password: string
}
export const ConfirmPasswordSchema = z.object({
  password: z.string({ message: 'Password is required' }),
})

export type ChangePhoneDTO = {
  newPhone: string
  channel: OTPChannels
}

export const ChangePhoneSchema = z.object({
  newPhone: phoneSchema,
  channel: channelSchema,
})

export type VerifyChangePhoneDTO = {
  newPhone: string
  otp: string
}

export const VerifyChangePhoneSchema = z.object({
  phone: phoneSchema,
  otp: otpSchema,
})

export type VerifyChangeEmailOTP = {
  otpId: string
  otp: string
}

export const VerifyChangeEmailOTPSchema = z.object({
  otpId: z.string({ message: 'Otp Id is required' }),
  otp: otpSchema,
})

export type ChangeEmailDto = {
  password: string
  email: string
  confirmEmail: string
}

export const ChangeEmailSchema = z
  .object({
    email: emailSchema,
    confirmEmail: emailSchema,
    password: z.string({ message: 'Password is required' }),
  })
  .superRefine(({ email, confirmEmail }, ctx) => {
    if (email !== confirmEmail) {
      ctx.addIssue({
        code: 'custom',
        message: 'Emails do not match',
        path: ['email', 'confirmEmail'],
      })
    }
  })

export type AddCardBodyDto = {
  token: string
}

export const AddCardBodySchema = z.object({
  token: z.string({ message: 'token is required' }),
})

export type RetrieveCardDto = {
  cardId: string
}
export const RetrieveCardSchema = z.object({
  cardId: z.string({ message: 'Card id is required' }),
})

export type FundWalletBodyDto = {
  amount: number
  cardId: string
}

export const FundWalletBodySchema = z.object({
  amount: z
    .number({ message: 'amount is required' })
    .min(1000, { message: 'Amount must be at least at least $10' })
    .max(500000, { message: 'Amount must be at most $5000' }),
  cardId: z.string({ message: 'Please select a card' }),
})

export type InitializePaymentIntentBodyDto = {
  amount: number
}
export const InitializePaymentIntentBodySchema = z.object({
  amount: z
    .number({ message: 'amount is required' })
    .min(1000, { message: 'Amount must be at least at least $10' })
    .max(500000, { message: 'Amount must be at most $5000' }),
})

export const GetKYCSessionStatusSchema = z.string({
  message: 'session id is required',
  required_error: 'session id is required',
})

export type VerifyAccountDetailsDto = {
  bankCode: string
  accountNumber: string
}
export const VerifyAccountDetailsSchema = z.object({
  bankCode: z
    .string({ message: 'Bank code is required' })
    .min(3, { message: 'Bank code cannot be less than 3 digits' }),
  accountNumber: z
    .string({ message: 'Account number is required' })
    .length(10, { message: 'Account number must be 10 digits long' })
    .regex(/^[0-9]{10}$/, {
      message: 'Account number must contain only digits',
    }),
})

export type PredictAccountDetailsDto = {
  accountNumber: string
}
export const PredictAccountDetailsSchema = z.object({
  accountNumber: z
    .string({ message: 'Account number is required' })
    .length(10, { message: 'Account number must be 10 digits long' })
    .regex(/^[0-9]{10}$/, {
      message: 'Account number must contain only digits',
    }),
})

export const dateSchema = z
  .string()
  .regex(/^\d{4}-\d{2}-\d{2}$/, {
    message: 'Date must be in the format YYYY-MM-DD',
  })

  .superRefine((date, ctx) => {
    const parsed = new Date(date)
    if (isNaN(parsed.getTime())) {
      ctx.addIssue({
        code: 'custom',
        message: 'Invalid date',
      })
    }
  })

export const countrySchema = z.enum(['US', 'NG'])

export type SupportedCountriesPassport = 'US' | 'NG'

export const SupportedVerifDocumentSchema = z.enum(['PASSPORT', 'VISA'])
export type SupportedVerifDocumentType = 'PASSPORT' | 'VISA'

export type StartVisaVerificationSessionType = {
  visaNumber: string
  visaType: string
  visaIssueDate: string
  visaExpiryDate: string
  firstName: string
  lastName: string
  dateOfBirth: string
  country: SupportedCountriesPassport
}
export const StartVisaVerificationSessionSchema = z.object({
  firstName: z.string(),
  lastName: z.string(),
  dateOfBirth: dateSchema.superRefine((date, ctx) => {
    const parsedYear = new Date(date).getFullYear()
    const currentYear = new Date().getFullYear()
    if (currentYear - parsedYear < 18) {
      ctx.addIssue({
        code: 'custom',
        message: 'User must be at least 18 to proceed',
      })
    }

    const parsed = new Date(date).getTime()
    const currentDate = Date.now()
    if (parsed > currentDate) {
      ctx.addIssue({
        code: 'custom',
        message: 'date of birth must be a past date',
      })
    }
  }),
  country: countrySchema,

  visaNumber: z
    .string({
      required_error: 'Visa number is required',
      message: 'Visa number is required',
    })
    .regex(/[0-9a-zA-Z]/, {
      message: 'Visa number must contain only alphanumeric characters',
    }),
  visaType: z.string({
    required_error: 'Visa type is required',
    message: 'Visa type is required',
  }),
  visaIssueDate: dateSchema.superRefine((date, ctx) => {
    const parsed = new Date(date).getTime()
    const currentDate = Date.now()
    if (parsed > currentDate) {
      ctx.addIssue({
        code: 'custom',
        message: 'Issue date must be a past date',
      })
    }
  }),
  visaExpiryDate: dateSchema.superRefine((date, ctx) => {
    const parsed = new Date(date).getTime()
    const currentDate = Date.now()
    if (parsed < currentDate) {
      ctx.addIssue({
        code: 'custom',
        message: 'Expiry date must be a future date',
      })
    }
  }),
})

export const StartPassportVerificationSessionSchema = z.object({
  firstName: z.string(),
  lastName: z.string(),
  dateOfBirth: dateSchema.superRefine((date, ctx) => {
    const parsedYear = new Date(date).getFullYear()
    const currentYear = new Date().getFullYear()
    if (currentYear - parsedYear < 18) {
      ctx.addIssue({
        code: 'custom',
        message: 'User must be at least 18 to proceed',
      })
    }

    const parsed = new Date(date).getTime()
    const currentDate = Date.now()
    if (parsed > currentDate) {
      ctx.addIssue({
        code: 'custom',
        message: 'date of birth must be a past date',
      })
    }
  }),
  country: countrySchema,
  documentNumber: z.string().regex(/[0-9a-zA-Z]/, {
    message: 'Visa number must contain only alphanumeric characters',
  }),
  expiryDate: dateSchema.superRefine((date, ctx) => {
    const parsed = new Date(date).getTime()
    const currentDate = Date.now()
    if (parsed < currentDate) {
      ctx.addIssue({
        code: 'custom',
        message: 'Expiry date must be a future date',
      })
    }
  }),
})

export type StartPassportVerificationSessionType = {
  firstName: string
  lastName: string
  dateOfBirth: string
  country: SupportedCountriesPassport
  documentNumber: string
  expiryDate: string
}

export type RetrieveTransactionDto = {
  transactionId: string
}
export const RetrieveTransactionSchema = z.object({
  transactionId: z.string({ message: 'Transaction id is required' }),
})

export type InitiateTransferDto =
  | {
      name: string
      accountNumber: string
      bankCode: string
      reason?: string
      amount: number
      auth: 'pin'
      pin: string
    }
  | {
      name: string
      accountNumber: string
      bankCode: string
      reason?: string
      amount: number
      auth: 'biometrics'
      publicKey: string
    }
export const InitiateTransferSchema = z.discriminatedUnion('auth', [
  z.object({
    name: z.string({
      message: 'Please enter beneficiary name',
      required_error: 'Please enter beneficiary name',
      invalid_type_error: 'Beneficiary must be a string',
    }),
    auth: z.literal('pin'),
    pin: pinSchema,
    accountNumber: z
      .string({ message: 'Account number is required' })
      .length(10, { message: 'Account number must be 10 digits long' })
      .regex(/^[0-9]{10}$/, {
        message: 'Account number must contain only digits',
      }),
    bankCode: z
      .string({ message: 'Bank code is required' })
      .min(3, { message: 'Bank code cannot be less than 3 digits' }),
    amount: z
      .number({ message: 'amount is required' })
      .min(1000000, { message: 'Amount must be at least at least ₦10000' })
      .max(*********, { message: 'Amount must be at most ₦1000000' }),
    reason: z
      .string({
        message: 'Please enter transfer reason',
        invalid_type_error: 'Reason must be a string',
      })
      .optional(),
  }),
  z.object({
    name: z.string({
      message: 'Please enter beneficiary name',
      required_error: 'Please enter beneficiary name',
      invalid_type_error: 'Beneficiary must be a string',
    }),
    auth: z.literal('biometrics'),
    publicKey: z.string(),
    accountNumber: z
      .string({ message: 'Account number is required' })
      .length(10, { message: 'Account number must be 10 digits long' })
      .regex(/^[0-9]{10}$/, {
        message: 'Account number must contain only digits',
      }),
    bankCode: z
      .string({ message: 'Bank code is required' })
      .min(3, { message: 'Bank code cannot be less than 3 digits' }),
    amount: z
      .number({ message: 'amount is required' })
      .min(1000000, { message: 'Amount must be at least at least ₦10000' })
      .max(*********, { message: 'Amount must be at most ₦1000000' }),
    reason: z
      .string({
        message: 'Please enter transfer reason',
        invalid_type_error: 'Reason must be a string',
      })
      .optional(),
  }),
])

export type PaginatedQueryDto = {
  searchString?: string
  page?: string
  hitsPerPage?: string
}

export const PaginatedQuerySchema = z.object({
  searchString: z
    .string({
      message: 'Please enter search string',
      required_error: 'Please enter search string',
      invalid_type_error: 'Search string must be a string',
    })
    .optional(),
  page: z
    .string({
      message: 'Please enter page number',
      required_error: 'Please enter page number',
    })
    .regex(/[0-9]/, {
      message: 'Page must contain only numbers',
    })
    .optional(),
  hitsPerPage: z
    .string({
      message: 'Please enter page size',
      required_error: 'Please enter page size',
    })
    .regex(/[0-9]/, {
      message: 'Page size must contain only numbers',
    })
    .optional(),
})

export type PaginatedBeneficiaryQueryDto = PaginatedQueryDto & {
  isFavorite?: boolean
}

export const PaginatedBeneficiaryQuerySchema = PaginatedQuerySchema.extend({
  isFavorite: z.boolean().optional(),
})

export type ToggleFavoriteBeneficiariesBodyDTO = {
  isFavorite: boolean
}
export const ToggleFavoriteBeneficiariesBodySchema = z.object({
  isFavorite: z.boolean(),
})
