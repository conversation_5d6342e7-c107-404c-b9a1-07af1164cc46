import { Request } from 'express'
import {
  JwtService,
  isAuthEndpoint,
  isOnboardingEndpoint,
  userHasCompletedRegistrationProcess,
} from '../utils/auth'
import { UnauthenticatedError } from '../errors/UnauthenticatedError'
import { UnauthorizedError } from '../errors/UnauthorizedError'
import { prisma } from '../config/prisma.config'

export async function expressAuthentication(
  request: Request,
  securityName: string,
) {
  if (securityName === 'jwt') {
    const token = request.headers['authorization']?.split(' ')[1]
    if (!token) {
      return Promise.reject(new UnauthenticatedError('Unauthenticated'))
    }
    const decoded = JwtService.verifyToken(token)

    if (!decoded.verified || !decoded.user || decoded.error) {
      return Promise.reject(new UnauthenticatedError(decoded.error as string))
    }
    if (isAuthEndpoint(request.url) || isOnboardingEndpoint(request.url)) {
      return Promise.resolve(decoded.user)
    }
    const user = await prisma.user.findUnique({
      where: { id: decoded.user.id },
      select: {
        id: true,
        email: true,
        phoneNumber: true,
        createdAt: true,
        updatedAt: true,
        password: false,
        biometricsSetAt: true,
        emailVerifiedAt: true,
        passwordSetAt: true,
        phoneVerifiedAt: true,
        stripeCustomerId: true,
        pinSetAt: true,
      },
    })

    if (!user) {
      return Promise.reject(new UnauthenticatedError('User not found'))
    }

    if (!userHasCompletedRegistrationProcess(user)) {
      return Promise.reject(
        new UnauthorizedError('Please complete your registration process'),
      )
    }

    return Promise.resolve(decoded.user)
  }
  return Promise.reject(new UnauthenticatedError('Invalid signature'))
}
