import { NextFunction, Request, Response } from 'express'
import { internalResponse } from '../helpers/response-helpers'
import { AuthEncryptionHandler } from '../services/encrypt-decrypt-service/auth-encrypt.service'
import { XAuthChallenge } from '../types/req.types'
export async function ValidateRequestMiddleware(
  req: Request,
  res: Response,
  next: NextFunction,
) {
  const encryptService = new AuthEncryptionHandler()
  const challenge = req.headers['x-auth-challenge'] as string

  if (!challenge) {
    return internalResponse(400, 'error', 'Missing challenge')
  }
  try {
    const decryptedData =
      await encryptService.decryptData<XAuthChallenge>(challenge)

    if (!decryptedData) {
      return internalResponse(400, 'error', 'Invalid challenge')
    }
    const currentTime = Date.now()
    const fiveMinutesAgo = currentTime - 5 * 60 * 1000

    if (
      decryptedData.timestamp > fiveMinutesAgo &&
      decryptedData.timestamp <= currentTime
    ) {
      next()
    }
    return internalResponse(400, 'error', 'Expired challenge')
  } catch {
    return internalResponse(400, 'error', 'Invalid challenge')
  }

  next()
}
