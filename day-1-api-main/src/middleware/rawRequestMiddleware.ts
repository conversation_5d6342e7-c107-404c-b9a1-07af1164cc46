import { NextFunction, Request, Response } from 'express'
import { logger } from '../helpers/logger'
const bufferReader = (req: Request) => {
  return new Promise((resolve, reject) => {
    const chunks: any[] = []

    // Listen for data events
    req.on('data', (chunk) => {
      chunks.push(chunk)
    })

    // Listen for end event
    req.on('end', () => {
      try {
        // Concatenate all chunks into a single buffer
        const buffer = Buffer.concat(chunks)

        resolve(buffer)
      } catch (err) {
        logger(module).error(
          `Error reading buffer from request: ${JSON.stringify(err, null, 2)}`,
        )

        reject(err)
      }
    })

    req.on('error', (err) => {
      logger(module).error(`On error event: ${JSON.stringify(err, null, 2)}`)

      reject(err)
    })
  })
}
export async function rawRequestMiddleware(
  req: Request,
  _: Response,
  next: NextFunction,
) {
  try {
    req.body = await bufferReader(req)
    next()
  } catch (error) {
    logger(module).error(
      `rawRequestMiddleware error: ${JSON.stringify(error, null, 2)}`,
    )

    req.body = {}
    next()
  }
}
