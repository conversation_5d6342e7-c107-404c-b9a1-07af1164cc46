import { PrismaClient } from '@prisma/client'
import { logger } from '../helpers/logger'
import { transactionStatus, transactionType } from '../types/req.types'
export const prisma = new PrismaClient()

async function seedDatabase() {
  const transactionStatuses: transactionStatus[] = [
    'pending',
    'success',
    'failed',
  ]
  const transactionTypes: transactionType[] = [
    'credit',
    'withdrawal',
    'transfer',
  ]

  try {
    await prisma.$connect()

    for (const status of transactionStatuses) {
      await prisma.transactionStatus.upsert({
        where: { statusName: status },
        update: {},
        create: { statusName: status },
      })
    }

    for (const type of transactionTypes) {
      await prisma.transactionType.upsert({
        where: {
          typeName: type,
        },
        update: {},
        create: { typeName: type },
      })
    }
    logger(module).info('database seeded successfully')
  } catch (error) {
    logger(module).error(JSON.stringify(error, null, 2))
  } finally {
    await prisma.$disconnect()
  }
}

seedDatabase()
