import {
  SupportedVerifDocumentType,
  StartPassportVerificationSessionType,
  StartVisaVerificationSessionType,
} from '../types/dtos.types'
import { EnvVars } from '../constants/env'
import { logger } from '../helpers/logger'
import { createHmac } from 'crypto'

export type VerifSessionPayload = {
  verification: {
    person: {
      firstName: string
      lastName: string
      dateOfBirth: string
    }
    document: {
      number: string
      type: SupportedVerifDocumentType
      country: string
    }
    vendorData: string
    endUserId: string
  }
}

export type VerifErrorResponse = {
  status: 'fail' | 'success'
  code: number
  message: string
}

export type VerifSessionResponse = {
  status: string
  verification: {
    id: string
    url: string
    vendorData: string
    endUserId: string
    host: string
    status: string
    sessionToken: string
  }
}

export type VerifAddress = {
  fullAddress: string
  parsedAddress: {
    city: string | null
    unit: string | null
    state: string | null
    street: string | null
    country: string | null
    postcode: string | null
    houseNumber: string | null
    firstIssue: string | null
    issueNumber: string | null
    issuedBy: string | null
    nfcValidated: boolean
    residencePermitType: string
    signatureIsVisible: boolean
  }
}
export type VerifVerifiedDocument = {
  number: string | null
  type: SupportedVerifDocumentType
  country: string | null
  validFrom: string | null
  validUntil: string | null
  state: string | null

  remarks?: string
  placeOfIssue?: string | null
}
export type VerifSessionDecisionResponse = {
  status: string
  verification: {
    id: string
    /**
     * Verification response code.
     */
    code: 9001 | 9102 | 9103 | 9104 | 9121
    /**
     *  Verified person
     */
    person: {
      firstName: string | null
      lastName: string | null
      /**
       * @deprecated
       */
      citizenship: null
      idNumber: string | null
      gender: 'M' | 'F' | null
      dateOfBirth: string
      yearOfBirth: string | null
      placeOfBirth: string | null
      nationality: string | null
      pepSanctionMatch: string | null

      occupation?: string | null
      employer?: string | null
      foreginerStatus?: string | null
      extraNames?: string | null
      addresses?: Array<VerifAddress>
    }
    reason: string | null
    status:
      | 'approved'
      | 'resubmission_requested'
      | 'review'
      | 'declined'
      | 'expired'
      | 'abandoned'
    /**
     * @deprecated
     */
    comments: Array<unknown>
    document: VerifVerifiedDocument
    reasonCode: 102 | 103 | 104 | 105 | 106 | 108 | 109 | 110 | 112 | 113 | null
    vendorData: string
    endUserId: string | null
    decisionTime: string
    acceptanceTime: string
    additionalVerifiedData?: any
    riskScore?: {
      /**
       * A float in the range of 0.0–1.0.
       */
      score: number
    }
    riskLabels?: any
    biometricAuthentication?: any
  }
  technicalData: {
    ip: string | null
  }
}

export class VerifService {
  private baseUrl: string = 'https://stationapi.veriff.com/v1'
  private apiKey: string = EnvVars.verifApiKey
  private apiSignatureKey: string = EnvVars.verifSignatureKey
  constructor() {}
  public async createHMACHeader(payload: unknown) {
    return createHmac('sha256', this.apiSignatureKey)
      .update(JSON.stringify(payload))
      .digest('hex')
  }

  public async validateHMACHeader({
    payload,
    signature,
  }: {
    payload: any
    signature: string
  }) {
    if (payload.constructor === Object) {
      payload = JSON.stringify(payload)
    }

    if (payload.constructor !== Buffer) {
      payload = Buffer.from(payload, 'utf8')
    }

    const digest = createHmac('sha256', this.apiSignatureKey)
      .update(Buffer.from(payload, 'utf8'))
      .digest('hex')
      .toLowerCase()

    return digest === signature.toLowerCase()
  }

  public generateCreatePassportSessionPayload(
    body: StartPassportVerificationSessionType & {
      email: string
      userId: string
    },
  ): VerifSessionPayload {
    return {
      verification: {
        endUserId: body.userId,
        vendorData: body.email,
        document: {
          country: body.country,
          number: body.documentNumber,
          type: 'PASSPORT',
        },
        person: {
          dateOfBirth: body.dateOfBirth,
          firstName: body.firstName,
          lastName: body.lastName,
        },
      },
    }
  }

  public generateCreateVisaSessionPayload(
    body: StartVisaVerificationSessionType & {
      email: string
      userId: string
    },
  ): VerifSessionPayload {
    return {
      verification: {
        endUserId: body.userId,
        vendorData: body.email,
        document: {
          country: body.country,
          number: body.visaNumber,
          type: 'VISA',
        },
        person: {
          dateOfBirth: body.dateOfBirth,
          firstName: body.firstName,
          lastName: body.lastName,
        },
      },
    }
  }

  public async createSession(
    body: VerifSessionPayload,
  ): Promise<{ data: null | VerifSessionResponse; error: null | unknown }> {
    try {
      const response = await fetch(`${this.baseUrl}/sessions`, {
        method: 'POST',
        headers: {
          'X-AUTH-CLIENT': this.apiKey,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(body),
      })
      if (!response.ok) {
        logger(module).error(JSON.stringify(response, null, 2))
        const error: VerifErrorResponse = await response.json()
        logger(module).error(JSON.stringify(error, null, 2))
        return {
          data: null,
          error: error.message,
        }
      }
      const data = await response.json()
      return { data, error: null }
    } catch (error) {
      return { data: null, error }
    }
  }
  public async getSessionStatus(sessionId: string) {
    try {
      const response = await fetch(
        `${this.baseUrl}/sessions/${sessionId}/decision`,
        {
          method: 'GET',
          headers: {
            'X-AUTH-CLIENT': this.apiKey,
            'Content-Type': 'application/json',
            'X-HMAC-SIGNATURE': await this.createHMACHeader(sessionId),
          },
        },
      )

      if (!response.ok) {
        logger(module).error(JSON.stringify(response, null, 2))

        const error = await response.json()
        logger(module).error(JSON.stringify(error, null, 2))

        return {
          data: null,
          error: new Error(`${response.status}: could not retrieve status`),
        }
      }

      const data = await response.json()
      return { data, error: null }
    } catch (error) {
      return { data: null, error }
    }
  }
  public async getKYCDetails() {}
}
