import { Resend } from 'resend'
import { EnvVars } from '../constants/env'
import { randomBytes, randomInt } from 'crypto'
import { Emails } from '../constants/emails'

export class EmailService {
  // protected emailer: Resend = new Resend(EnvVars.resendOutgoingKey)
  constructor() {}
}

export class EmailOTPService extends EmailService {
  constructor() {
    super()
  }
  generateOTP(length: number = 6) {
    return Array.from({ length }, () => randomInt(10)).join('')
  }
  generateToken(length: number) {
    return randomBytes(Math.ceil(length / 2))
      .toString('hex')
      .slice(0, length)
  }
  // async sendOTP({ recipient, otp }: { recipient: string; otp: string }) {
  //   return await this.emailer.emails.send({
  //     from: `Onboarding <${Emails.onboarding}>`,
  //     to: [recipient],
  //     subject: 'Welcome to Day One, Please verify your email',
  //     html: `Your OTP is <strong>${otp}</strong>`,
  //   })
  // }

  // async sendResetToken({
  //   recipient,
  //   token,
  // }: {
  //   recipient: string
  //   token: string
  // }) {
  //   return await this.emailer.emails.send({
  //     from: `Reset Password <${Emails.onboarding}>`,
  //     to: [recipient],
  //     subject: 'Day One, Reset Password',
  //     html: `Hi, ${recipient} Your token is <strong>${token}</strong>`,
  //   })
  // }
}
