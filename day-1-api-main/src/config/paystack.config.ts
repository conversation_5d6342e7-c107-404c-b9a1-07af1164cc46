import { logger } from '../helpers/logger'
import { EnvVars } from '../constants/env'
import { createHmac, randomUUID } from 'crypto'

export type InitializeTransferArgs = {
  reason: string
  amount: number
  recipient: string
}

export type FinalizeTransferArgs = {
  transfer_code: string
  /**
   * 6 digit number
   */
  otp: string
}
export type FetchTransfersArgs = {
  hitsPerPage: number
  page: number
  recipient: string
  /**
   * Valid date string in YYYY-MM-DD format eg 2020-12-12
   */
  from: string
  /**
   * Valid date string in YYYY-MM-DD format eg 2020-12-12
   */
  to: string
}

export type FetchTransferArgs = {
  transfer_code: string
}

export type CreateTransferRecipientArgs = {
  name: string
  accountNumber: string
  bankCode: string
}
export type VerifyTransferArgs = {
  reference: string
}

export type PaystackResponse<T extends Record<string, unknown>> = {
  status: boolean
  message: string
  data: T
}

export type CreateRecipientResponse = {
  active: boolean
  createdAt: string
  currency: 'NGN'
  domain: string
  id: number
  integration: number
  name: string
  recipient_code: string
  type: string
  updatedAt: string
  is_deleted: false
  details: {
    authorization_code: null | string | number
    account_number: string
    account_name: string
    bank_code: string
    bank_name: string
  }
}

export type InitializeTransferResponse = {
  integration: number
  domain: string
  amount: number
  currency: string
  source: string
  reason?: string
  recipient: number
  status: string
  transfer_code: string
  id: number
  createdAt: string
  updatedAt: string
  reference: string
}
export class PaystackService {
  baseHeaders: Record<string, string> = {}
  constructor(
    private baseURL = 'https://api.paystack.co',
    private secretKey = EnvVars.paystackSecretKey,
  ) {
    this.baseHeaders = {
      Authorization: `Bearer ${this.secretKey}`,
      'Content-Type': 'application/json',
    }
  }

  async initializeTransfer(args: InitializeTransferArgs): Promise<{
    data: PaystackResponse<InitializeTransferResponse> | null
    error: PaystackResponse<InitializeTransferResponse> | unknown | null
  }> {
    const { reason, amount, recipient } = args

    try {
      const requestBody = {
        reason,
        amount,
        recipient,
        reference: randomUUID(),
        source: 'balance',
      }

      const response = await fetch(`${this.baseURL}/transfer`, {
        method: 'POST',
        body: JSON.stringify(requestBody),
        headers: {
          ...this.baseHeaders,
        },
      })

      if (!response.ok) {
        logger(module).error(
          `Error initializing paystack transfer: ${JSON.stringify(response, null, 2)}`,
        )
        return { data: null, error: await response.json() }
      }
      const data = await response.json()
      return { data, error: null }
    } catch (error) {
      logger(module).error(
        `Error initializing paystack transfer: ${JSON.stringify(error, null, 2)}`,
      )
      return { data: null, error }
    }
  }

  public async validateHMACHeader({
    payload,
    signature,
  }: {
    payload: unknown
    signature: string
  }) {
    const hash = createHmac('sha512', this.secretKey)
      .update(JSON.stringify(payload))
      .digest('hex')

    return hash === signature
  }
  async verifyTransfer() {}
  async createRecipient(args: CreateTransferRecipientArgs): Promise<{
    data: PaystackResponse<CreateRecipientResponse> | null
    error: PaystackResponse<CreateRecipientResponse> | unknown | null
  }> {
    const { name, accountNumber, bankCode } = args
    try {
      const requestBody = {
        type: 'nuban',
        name,
        account_number: accountNumber,
        bank_code: bankCode,
        currency: 'NGN',
      }

      const response = await fetch(`${this.baseURL}/transferrecipient`, {
        method: 'POST',
        body: JSON.stringify(requestBody),
        headers: {
          ...this.baseHeaders,
        },
      })

      if (!response.ok) {
        logger(module).error(
          `Error creating paystack recipient: ${JSON.stringify(response, null, 2)}`,
        )
        return { data: null, error: await response.json() }
      }
      const data = await response.json()
      return { data, error: null }
    } catch (error) {
      logger(module).error(
        `Error creating paystack recipient: ${JSON.stringify(error, null, 2)}`,
      )
      return { data: null, error }
    }
  }

  public async getAllBanks() {
    try {
      const response = await fetch(`${this.baseURL}/bank`)
      if (!response.ok) {
        logger(module).error(
          `Error getting paystack bank list: ${JSON.stringify(response, null, 2)}`,
        )

        const error = await response.json()
        return {
          data: null,
          error,
        }
      }

      const data = await response.json()
      if (data.error) {
        logger(module).error(JSON.stringify(response, null, 2))
        return {
          data: null,
          error: data,
        }
      }
      return { data, error: null }
    } catch (error) {
      logger(module).error(
        `Error getting paystack bank list: ${JSON.stringify(error, null, 2)}`,
      )
      return { data: null, error }
    }
  }
}
