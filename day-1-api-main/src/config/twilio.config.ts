import twilio from 'twilio'
import { EnvVars } from '../constants/env'
import { logger } from '../helpers/logger'
export class OTPService {
  private static twilioClient: twilio.Twilio = twilio(
    EnvVars.twilioAccountSid,
    EnvVars.twilioAuthToken,
  )

  constructor() {}

  static sendOTP = async (body: { to: string; via: 'sms' | 'whatsapp' }) => {
    const { to, via } = body
    try {
      const response = await OTPService.twilioClient.verify.v2
        .services(EnvVars.twilioVerifySid)
        .verifications.create({
          to: to,
          channel: via,
        })
      return { response, error: null }
    } catch (error) {
      logger(module).error(JSON.stringify(error, null, 2))
      return { error, response: null }
    }
  }
  static verifyOTP = async (body: { to: string; code: string }) => {
    try {
      const response = await OTPService.twilioClient.verify.v2
        .services(EnvVars.twilioVerifySid)
        .verificationChecks.create(body)
      return { response, error: null }
    } catch (error) {
      logger(module).error(JSON.stringify(error, null, 2))

      return { error, response: null }
    }
  }
}
