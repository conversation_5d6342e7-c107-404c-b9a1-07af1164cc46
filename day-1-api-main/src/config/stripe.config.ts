import { EnvVars } from '../constants/env'
import Stripe from 'stripe'

export class PaymentsManager {
  private stripe: Stripe = new Stripe(EnvVars.stripeSecretKey)
  constructor() {}

  async initializePaymentIntent(params: {
    amount: number
    customerId: string
    userId: string
  }) {
    const { amount, customerId, userId } = params
    try {
      const paymentIntent = await this.stripe.paymentIntents.create({
        amount,
        currency: 'usd',
        customer: customerId,
        setup_future_usage: 'on_session',
        metadata: {
          userId,
        },
      })

      return { data: paymentIntent, error: null }
    } catch (error) {
      return { error, data: null }
    }
  }

  constructWebhookEvent(params: { signature: string; body: string }) {
    const { signature, body } = params

    return this.stripe.webhooks.constructEvent(
      body,
      signature,
      EnvVars.stripeWebHookSecret,
    )
  }

  async addCard(token: string, stripeCustomerId: string) {
    try {
      const card = await this.stripe.customers.createSource(stripeCustomerId, {
        source: token,
      })

      return { data: card, error: null }
    } catch (error) {
      return { error, data: null }
    }
  }
  async listCards(stripeCustomerId: string) {
    try {
      const cards = await this.stripe.customers.listSources(stripeCustomerId, {
        object: 'card',
      })

      return { data: cards, error: null }
    } catch (error) {
      return { error, data: null }
    }
  }

  async getCard(body: { stripeCustomerId: string; cardId: string }) {
    const { stripeCustomerId, cardId } = body
    try {
      const card = await this.stripe.customers.retrieveSource(
        stripeCustomerId,
        cardId,
      )
      return { data: card, error: null }
    } catch (error) {
      return { error, data: null }
    }
  }
  async payWithCard(body: {
    amount: number
    currency: string
    stripeCustomerId: string
    cardId: string
  }) {
    const { amount, currency, stripeCustomerId, cardId } = body
    try {
      const charge = await this.stripe.charges.create({
        amount,
        currency,
        customer: stripeCustomerId,
        source: cardId,
      })
      return { data: charge, error: null }
    } catch (error) {
      return { error, data: null }
    }
  }
  async createCustomer(email: string) {
    try {
      const customer = await this.stripe.customers.create({
        email,
      })
      return { error: null, data: customer }
    } catch (error) {
      return { error, data: null }
    }
  }
}
