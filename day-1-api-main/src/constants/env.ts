import { safeString } from '../utils/utils'
import dotenv from 'dotenv'
dotenv.config()

export type supportedEnvironments = 'staging' | 'development' | 'production'
export const EnvVars = {
  port: (process.env.PORT || process.env.port) ?? 3000,
  nodeEnvironment: safeString(
    process.env.NODE_ENV,
    'development',
  ) as supportedEnvironments,

  // Password & pin vars
  saltRounds: process.env.SALT_ROUNDS_FOR_HASH ?? 9,

  // Access token vars
  jwtExpiry: process.env.JWT_EXPIRY ?? '5m',
  jwtSecret: process.env.JWT_SECRET_KEY ?? 'i-am-a-random-string',

  // Biometrics vars
  bioSecret: process.env.BIO_SECRET_KEY ?? 'i-am-a-random-string',

  // Refresh token vars
  refreshSecret: process.env.RT_SECRET_KEY ?? 'i-am-another-random-string',
  refreshTokenExpiry: process.env.RT_EXPIRY ?? '10m',

  // Twilio vars
  twilioAccountSid: process.env.TWILIO_ACCOUNT_SID ?? '',
  twilioAuthToken: process.env.TWILIO_AUTH_TOKEN ?? '',
  twilioVerifySid: process.env.TWILIO_VERIFY_SERVICE_SID ?? '',

  // Resend vars
  resendOutgoingKey: process.env.RESEND_OUTGOING_KEY ?? '',

  // Stripe  vars
  stripePublishableKey: process.env.STRIPE_PUBLISHABLE_KEY ?? '',
  stripeSecretKey: process.env.STRIPE_SECRET_KEY ?? '',
  stripeWebHookSecret: process.env.STRIPE_WEBHOOK_SECRET ?? '',

  // Nuban vars
  nubanKey: process.env.NUBAN_API_KEY ?? '',

  // Verif vars
  verifApiKey: process.env.VERIF_API_KEY ?? '',
  verifSignatureKey: process.env.VERIF_MASTER_SIGNATURE_KEY ?? '',

  // paystack
  paystackSecretKey: process.env.PAYSTACK_SECRET_KEY ?? '',
}
