# Project Documentation

## 1) Introduction
Day One is a secure and scalable Node.js based REST API service engineered for high-performance financial operations.  
Key technical features include:
- Modular service design to allow horizontal scaling
- Robust middleware for error handling and input validation
- Secure token-based authentication (JWT) employing asymmetric key encryption
- Real-time payment processing with optimized asynchronous workflows
- Comprehensive audit logging and rate limiting to prevent abuse

## 2) Tech Stack
### Backend Core
- Node.js v14+ (Asynchronous event-driven runtime)
- Express.js (Minimalist web framework enabling rapid REST API development)
- TypeScript (Provides compile-time type safety and improved maintainability)
- TSOA (For automatic API documentation and route generation, enforcing strong contract definitions)
- Prisma (Type-safe ORM for efficient database querying and schema migrations)

### Database & Caching
- PostgreSQL (Relational database with support for advanced features like JSON columns)
- Native JSON support for flexible data schemas
- Decimal type to ensure precise financial calculations

### Security
- Helmet (Secures HTTP headers)
- CORS middleware with strict policy definition
- Rate limiting using Express-rate-limit to mitigate DDoS attacks
- JWT for stateless authentication
- Bcrypt for secure password hashing

### External Services
- Stripe: RESTful integration for payment processing
- Paystack: API integration for bank transfer operations in Nigerian markets
- Twilio: Utilized for programmatic SMS-based verification
- Resend: Implements email service for transactional communications
- KYC services: Integrated for automatic identity verification using Visa and Passport data

### Development Tools
- ESLint & Prettier for code quality and uniform coding style
- Husky for managing git hooks ensuring pre-commit checks
- Winston & Morgan for advanced logging and HTTP request tracking
- Swagger UI integrated via TSOA for dynamic API documentation

## 3) Backend Architecture
### Layered Architecture
1. **Presentation Layer**  
   - REST controllers handling incoming HTTP requests
   - Request/Response DTOs ensuring contract validation
   - Integrated input validation and custom error handling middleware
   - Automated Swagger API documentation through TSOA

2. **Business Layer**  
   - Service classes encapsulating core business logic and transaction management
   - Integration points for external REST APIs and third-party services
   - Event-driven handlers for asynchronous processing

3. **Data Access Layer**  
   - Prisma ORM for database interactions
   - Structured database migrations and schema versioning
   - Query optimization strategies and connection pooling

### Technical Patterns
- Dependency Injection for service decoupling
- Use of middleware for layered request processing
- Circuit breaker pattern for robust external service handling

## 4) Naming Conventions
### Code Style
- Variables and Functions: camelCase for consistency and clarity (e.g., userDetails, processPayment)
- Classes and Interfaces: PascalCase with interfaces prefixed by an "I" (e.g., IUserResponse)
- Constants: UPPER_SNAKE_CASE to denote immutability (e.g., MAX_ATTEMPTS)

### File Structure
```
src/
├── controllers/      // REST endpoint handlers with clear responsibility separation
├── services/         // Business logic and interactions with data sources or external APIs
├── models/           // Type definitions and ORM models
├── utils/            // Utility functions for common operations (e.g., encryption, validation)
```

## 5) Table Names
| Table Name           | Description                              | Primary Key   | Notable Fields                                |
|----------------------|------------------------------------------|---------------|-----------------------------------------------|
| User                 | Unique user records with authentication  | id (UUID)     | email, phoneNumber, stripeCustomerId          |
| UserWallet           | User wallet details for financial data   | id (Int)      | balance, baseBalance, userId                   |
| Transaction          | Log of all payment transactions          | id (Int)      | transactionId, amount, status                 |
| BeneficiaryDetails   | Beneficiary bank details                 | id (Int)      | accountName, bankCode, paystackRecipientId     |
| VisaKycDetails       | Data for Visa-based KYC verification     | id (Int)      | documentNumber, verificationStatus             |
| PassportKycDetails   | Passport details for KYC purposes        | id (Int)      | documentNumber, nationality                    |
| WalletBalance        | Historical records of wallet balances    | id (Int)      | balance, transactionId                        |

## 6) Infra Diagram
```
                                    ┌─────────────┐
                                    │   Client    │
                                    └──────┬──────┘
                                           │
                                    ┌──────▼──────┐
                           ┌────────► API Gateway ◄────────┐
                           │        └──────┬──────┘        │  -- Load Balancer handling ingress traffic
                           │               │               │
                     ┌─────┴────┐   ┌─────┴────┐   ┌─────┴────┐
                     │ Server 1  │   │ Server 2  │   │ Server 3  │  -- Multiple stateless API servers for scalability
                     └─────┬────┘   └─────┬────┘   └─────┬────┘
                           │              │              │
             ┌────────────►│◄────────────►│◄────────────►│◄────────────┐
             │             │              │              │             │
        ┌────┴────┐   ┌────┴────┐   ┌────┴────┐   ┌────┴────┐  ┌────┴────┐
        │ Postgres │   │  Redis   │   │ Stripe   │   │ Paystack │  │  Twilio  │
        └─────────┘   └─────────┘   └─────────┘   └─────────┘  └─────────┘
```
Key comments:
- Load balancing, high availability, and distributed caching (Redis) are integral for optimal system performance.
- The API Gateway manages incoming requests and forwards them to multiple instances of the API servers for fault tolerance.

## 7) Code Repository (Main Branch Structure)
```
/
├── src/
│   ├── controllers/      # REST API endpoints with well-defined routes and error handling
│   ├── services/         # Encapsulates business logic and external service integrations
│   ├── models/           # ORM models and TypeScript interfaces defining the data layer
│   ├── middleware/       # Custom Express middleware (e.g., authentication, logging, error capturing)
│   ├── utils/            # Helper utilities (e.g., encryption util, validation functions)
│   └── config/           # Environment-based configuration and application settings
├── prisma/
│   ├── schema.prisma     # Database schema with relations and indexing details
│   └── migrations/       # Detailed migration logs for incremental schema changes
├── tests/
│   ├── unit/            # Unit tests ensuring correctness of isolated functions and classes
│   └── integration/     # Integration tests validating end-to-end scenarios and third-party interactions
├── logs/                # Persistent logs for audit tracking and debugging (rotate frequently)
├── docs/                # Additional technical documentation and API specs
└── build/               # Compiled JavaScript output targeting production environments
```
Additional CI/CD considerations:
- Automated tests run on every commit with integration to code quality tools
- Branching model supports feature branches, hotfixes, and a stable main branch
